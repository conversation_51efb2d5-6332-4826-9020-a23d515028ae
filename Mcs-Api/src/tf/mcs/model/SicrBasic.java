/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@Table
public class SicrBasic implements Serializable{
	
	private java.lang.String serid;
	private java.lang.String reportid;
	private java.lang.String reportTime;
	private java.lang.String cliname;
	private java.lang.String certtype;
	private java.lang.String certtypeCode;
	private java.lang.String certno;
	private java.lang.String inst;
	private java.lang.String reason;
	private java.lang.String reasonCode;
	private java.lang.String objectionInfo;
	private java.lang.String warnFlg;
	private java.lang.String warnFlgCode;
	private java.lang.String warnContent;
	private java.lang.String warnBegin;
	private java.lang.String warnEnd;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setSerid(java.lang.String value) {
		this.serid = value;
	}
	
	@Id
	@Column(length=32,desc="流水号")
	public java.lang.String getSerid() {
		return this.serid;
	}
	
	public void setReportid(java.lang.String value) {
		this.reportid = value;
	}
	
	
	@Column(length=50,desc="报告编号")
	public java.lang.String getReportid() {
		return this.reportid;
	}
	
	public void setReportTime(java.lang.String value) {
		this.reportTime = value;
	}
	
	
	@Column(length=50,desc="报告时间")
	public java.lang.String getReportTime() {
		return this.reportTime;
	}
	
	public void setCliname(java.lang.String value) {
		this.cliname = value;
	}
	
	
	@Column(length=96,desc="被查询者姓名")
	public java.lang.String getCliname() {
		return this.cliname;
	}
	
	public void setCerttype(java.lang.String value) {
		this.certtype = value;
	}
	
	
	@Column(length=32,desc="被查询者证件类型")
	public java.lang.String getCerttype() {
		return this.certtype;
	}
	
	public void setCerttypeCode(java.lang.String value) {
		this.certtypeCode = value;
	}
	
	
	@Column(length=2,desc="被查询者证件类型编码")
	public java.lang.String getCerttypeCode() {
		return this.certtypeCode;
	}
	
	public void setCertno(java.lang.String value) {
		this.certno = value;
	}
	
	
	@Column(length=64,desc="被查询者证件号码")
	public java.lang.String getCertno() {
		return this.certno;
	}
	
	public void setInst(java.lang.String value) {
		this.inst = value;
	}
	
	
	@Column(length=64,desc="查询机构")
	public java.lang.String getInst() {
		return this.inst;
	}
	
	public void setReason(java.lang.String value) {
		this.reason = value;
	}
	
	
	@Column(length=64,desc="查询原因")
	public java.lang.String getReason() {
		return this.reason;
	}
	
	public void setReasonCode(java.lang.String value) {
		this.reasonCode = value;
	}
	
	
	@Column(length=2,desc="查询原因编码")
	public java.lang.String getReasonCode() {
		return this.reasonCode;
	}
	
	public void setObjectionInfo(java.lang.String value) {
		this.objectionInfo = value;
	}
	
	
	@Column(length=512,desc="异议信息提示")
	public java.lang.String getObjectionInfo() {
		return this.objectionInfo;
	}
	
	public void setWarnFlg(java.lang.String value) {
		this.warnFlg = value;
	}
	
	
	@Column(length=12,desc="防欺诈警示标志")
	public java.lang.String getWarnFlg() {
		return this.warnFlg;
	}
	
	public void setWarnFlgCode(java.lang.String value) {
		this.warnFlgCode = value;
	}
	
	
	@Column(length=1,desc="防欺诈警示标志编码：0：否；1：是；")
	public java.lang.String getWarnFlgCode() {
		return this.warnFlgCode;
	}
	
	public void setWarnContent(java.lang.String value) {
		this.warnContent = value;
	}
	
	
	@Column(length=200,desc="防欺诈警示内容")
	public java.lang.String getWarnContent() {
		return this.warnContent;
	}
	
	public void setWarnBegin(java.lang.String value) {
		this.warnBegin = value;
	}
	
	
	@Column(length=12,desc="防欺诈警示生效日期")
	public java.lang.String getWarnBegin() {
		return this.warnBegin;
	}
	
	public void setWarnEnd(java.lang.String value) {
		this.warnEnd = value;
	}
	
	
	@Column(length=12,desc="防欺诈警示截止日期")
	public java.lang.String getWarnEnd() {
		return this.warnEnd;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"流水号="+serid+ " , "+ "报告编号="+reportid+ " , "+ "报告时间="+reportTime+ " , "+ "被查询者姓名="+cliname+ " , "+ "被查询者证件类型="+certtype+ " , "+ "被查询者证件类型编码="+certtypeCode+ " , "+ "被查询者证件号码="+certno+ " , "+ "查询机构="+inst+ " , "+ "查询原因="+reason+ " , "+ "查询原因编码="+reasonCode+ " , "+ "异议信息提示="+objectionInfo+ " , "+ "防欺诈警示标志="+warnFlg+ " , "+ "防欺诈警示标志编码：0：否；1：是；="+warnFlgCode+ " , "+ "防欺诈警示内容="+warnContent+ " , "+ "防欺诈警示生效日期="+warnBegin+ " , "+ "防欺诈警示截止日期="+warnEnd+ " } ";
	}
	

}


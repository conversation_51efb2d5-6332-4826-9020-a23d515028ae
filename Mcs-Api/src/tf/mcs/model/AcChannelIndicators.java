/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012
 */

package tf.mcs.model;

import org.codehaus.xfire.aegis.type.java5.XmlElement;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table
public class AcChannelIndicators implements Serializable {

	private java.lang.Long channelIndicatorsId;
	private java.lang.String recommendationCode;
	private java.lang.String prdtNo;
	private java.lang.String parType;
	private java.lang.String dataMonth;
	private java.lang.Double amtSum;
	private java.lang.Double overBal;
	private java.lang.Integer overNum;
	private java.lang.Double indicatorsVal;
	private java.lang.String createDate;
	private java.lang.String updateDate;
	private java.lang.String createTime;
	private java.lang.String updateTime;

	//---------------------------------Extra attributes below which don't belong to database table--------------------

	public void setChannelIndicatorsId(java.lang.Long value) {
		this.channelIndicatorsId = value;
	}

	@XmlElement(nillable = true)
	@Id
	@Column(length = 19, desc = "渠道放款指标主键id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	public java.lang.Long getChannelIndicatorsId() {
		return this.channelIndicatorsId;
	}

	public void setRecommendationCode(java.lang.String value) {
		this.recommendationCode = value;
	}


	@Column(length = 10, desc = "渠道推荐码")
	public java.lang.String getRecommendationCode() {
		return this.recommendationCode;
	}

	public void setPrdtNo(java.lang.String value) {
		this.prdtNo = value;
	}


	@Column(length = 16, desc = "产品编号")
	public java.lang.String getPrdtNo() {
		return this.prdtNo;
	}

	public void setParType(java.lang.String value) {
		this.parType = value;
	}


	@Column(length = 8, desc = "par指标类型(Par0=0,Par10=10,Par20=20,Par30=30……)")
	public java.lang.String getParType() {
		return this.parType;
	}

	public void setDataMonth(java.lang.String value) {
		this.dataMonth = value;
	}


	@Column(length = 8, desc = "数据月份(yyyyMM)")
	public java.lang.String getDataMonth() {
		return this.dataMonth;
	}

	public void setAmtSum(java.lang.Double value) {
		this.amtSum = value;
	}

	@XmlElement(nillable = true)
	@Column(length=18,desc="放款总额")
	public java.lang.Double getAmtSum() {
		return this.amtSum;
	}

	public void setOverBal(java.lang.Double value) {
		this.overBal = value;
	}

	@XmlElement(nillable = true)
	@Column(length = 18, desc = "逾期余额")
	public java.lang.Double getOverBal() {
		return this.overBal;
	}

	public void setOverNum(java.lang.Integer value) {
		this.overNum = value;
	}

	@XmlElement(nillable = true)
	@Column(length = 8, desc = "逾期笔数")
	public java.lang.Integer getOverNum() {
		return this.overNum;
	}

	public void setIndicatorsVal(java.lang.Double value) {
		this.indicatorsVal = value;
	}

	@XmlElement(nillable = true)
	@Column(length = 18, desc = "指标值")
	public java.lang.Double getIndicatorsVal() {
		return this.indicatorsVal;
	}

	public void setCreateDate(java.lang.String value) {
		this.createDate = value;
	}


	@Column(length = 8, desc = "创建日期")
	public java.lang.String getCreateDate() {
		return this.createDate;
	}

	public void setUpdateDate(java.lang.String value) {
		this.updateDate = value;
	}


	@Column(length = 8, desc = "更新日期")
	public java.lang.String getUpdateDate() {
		return this.updateDate;
	}

	public void setCreateTime(java.lang.String value) {
		this.createTime = value;
	}


	@Column(length = 24, desc = "创建时间 yyyyMMdd HH:mi:ss")
	public java.lang.String getCreateTime() {
		return this.createTime;
	}

	public void setUpdateTime(java.lang.String value) {
		this.updateTime = value;
	}


	@Column(length = 24, desc = "更新时间 yyyyMMdd HH:mi:ss")
	public java.lang.String getUpdateTime() {
		return this.updateTime;
	}


	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------


	public String toDescString() {
		return " { " +
				"渠道放款指标主键id=" + channelIndicatorsId + " , " +
				"渠道推荐码=" + recommendationCode + " , " +
				"产品编号=" + prdtNo + " , " +
				"par指标类型(Par0=0,Par10=10,Par20=20,Par30=30……)=" + parType + " , " +
				"数据月份(yyyyMM)=" + dataMonth + " , " +
				"放款总额=" + amtSum + " , " +
				"逾期余额=" + overBal + " , " +
				"逾期笔数=" + overNum + " , " +
				"指标值=" + indicatorsVal + " , " +
				"创建日期=" + createDate + " , " +
				"更新日期=" + updateDate + " , " +
				"创建时间 yyyyMMdd HH:mi:ss=" + createTime + " , " +
				"更新时间 yyyyMMdd HH:mi:ss=" + updateTime +
				" } ";
	}
}
/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(CnDeductionsSerialPrdtPK.class)@Table
public class CnDeductionsSerialPrdt implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String prdtNo;
	private java.lang.String begDate;
	private java.lang.String endDate;
	private java.lang.String serial;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=20,desc="法人机构号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setPrdtNo(java.lang.String value) {
		this.prdtNo = value;
	}
	
	@Id
	@Column(length=5,desc="产品编号")
	public java.lang.String getPrdtNo() {
		return this.prdtNo;
	}
	
	public void setBegDate(java.lang.String value) {
		this.begDate = value;
	}
	
	@Id
	@Column(length=8,desc="有效起始日")
	public java.lang.String getBegDate() {
		return this.begDate;
	}
	
	public void setEndDate(java.lang.String value) {
		this.endDate = value;
	}
	
	@Id
	@Column(length=8,desc="有效结束日")
	public java.lang.String getEndDate() {
		return this.endDate;
	}
	
	public void setSerial(java.lang.String value) {
		this.serial = value;
	}
	
	
	@Column(length=32,desc="扣款顺序")
	public java.lang.String getSerial() {
		return this.serial;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	

}


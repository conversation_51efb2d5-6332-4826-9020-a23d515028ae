/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(CreditSearchMainPK.class)@Table
public class CreditSearchMain implements Serializable{
	
	private java.lang.String applyno;
	private java.lang.String queryPerson;
	private java.lang.String brno;
	private java.lang.String bankAccount;
	private java.lang.String bankPassword;
	private java.lang.String bankOrgCode;
	private java.lang.Long effectiveDate;
	private java.lang.String reportType;
	private java.lang.String optDate;
	private java.lang.Long bankid;
	private java.lang.String flag;
	private java.lang.String cretype;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setApplyno(java.lang.String value) {
		this.applyno = value;
	}
	
	@Id
	@Column(length=32,desc="征信查询编号")
	public java.lang.String getApplyno() {
		return this.applyno;
	}
	
	public void setQueryPerson(java.lang.String value) {
		this.queryPerson = value;
	}
	
	
	@Column(length=32,desc="查询人")
	public java.lang.String getQueryPerson() {
		return this.queryPerson;
	}
	
	public void setBrno(java.lang.String value) {
		this.brno = value;
	}
	
	
	@Column(length=32,desc="机构")
	public java.lang.String getBrno() {
		return this.brno;
	}
	
	public void setBankAccount(java.lang.String value) {
		this.bankAccount = value;
	}
	
	
	@Column(length=32,desc="人行登陆账户")
	public java.lang.String getBankAccount() {
		return this.bankAccount;
	}
	
	public void setBankPassword(java.lang.String value) {
		this.bankPassword = value;
	}
	
	
	@Column(length=32,desc="人行登陆密码")
	public java.lang.String getBankPassword() {
		return this.bankPassword;
	}
	
	public void setBankOrgCode(java.lang.String value) {
		this.bankOrgCode = value;
	}
	
	
	@Column(length=64,desc="人行登陆账户机构代码")
	public java.lang.String getBankOrgCode() {
		return this.bankOrgCode;
	}
	
	public void setEffectiveDate(java.lang.Long value) {
		this.effectiveDate = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="有效期")
	public java.lang.Long getEffectiveDate() {
		return this.effectiveDate;
	}
	
	public void setReportType(java.lang.String value) {
		this.reportType = value;
	}
	
	
	@Column(length=1,desc="报告类型")
	public java.lang.String getReportType() {
		return this.reportType;
	}
	
	public void setOptDate(java.lang.String value) {
		this.optDate = value;
	}
	
	
	@Column(length=12,desc="查询日期")
	public java.lang.String getOptDate() {
		return this.optDate;
	}
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="法人实体号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setFlag(java.lang.String value) {
		this.flag = value;
	}
	
	
	@Column(length=8,desc="结束标志 0否1是 2日终未结束")
	public java.lang.String getFlag() {
		return this.flag;
	}
	
	public void setCretype(java.lang.String value) {
		this.cretype = value;
	}
	
	
	@Column(length=2,desc="1微业贷征信")
	public java.lang.String getCretype() {
		return this.cretype;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"征信查询编号="+applyno+ " , "+ "查询人="+queryPerson+ " , "+ "机构="+brno+ " , "+ "人行登陆账户="+bankAccount+ " , "+ "人行登陆密码="+bankPassword+ " , "+ "人行登陆账户机构代码="+bankOrgCode+ " , "+ "有效期="+effectiveDate+ " , "+ "报告类型="+reportType+ " , "+ "查询日期="+optDate+ " , "+ "法人实体号="+bankid+ " , "+ "结束标志 0否1是 2日终未结束="+flag+ " , "+ "1微业贷征信="+cretype+ " } ";
	}
	

}


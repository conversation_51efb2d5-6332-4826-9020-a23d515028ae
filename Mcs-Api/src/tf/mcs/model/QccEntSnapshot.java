/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(QccEntSnapshotPK.class)@Table
public class QccEntSnapshot implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String serid;
	private java.lang.String ssurl;
	private java.lang.String isdownload;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="法人机构号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setSerid(java.lang.String value) {
		this.serid = value;
	}
	
	@Id
	@Column(length=32,desc="流水号")
	public java.lang.String getSerid() {
		return this.serid;
	}
	
	public void setSsurl(java.lang.String value) {
		this.ssurl = value;
	}
	
	
	@Column(length=256,desc="公司名称")
	public java.lang.String getSsurl() {
		return this.ssurl;
	}
	
	public void setIsdownload(java.lang.String value) {
		this.isdownload = value;
	}
	
	
	@Column(length=1,desc="是否已下载")
	public java.lang.String getIsdownload() {
		return this.isdownload;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"法人机构号="+bankid+ " , "+ "流水号="+serid+ " , "+ "公司名称="+ssurl+ " , "+ "是否已下载="+isdownload+ " } ";
	}
	

}


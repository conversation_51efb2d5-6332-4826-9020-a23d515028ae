/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(AcGuarRelativePK.class)@Table
public class AcGuarRelative implements Serializable{
	
	private java.lang.String serid;
	private java.lang.Long bankid;
	private java.lang.String applyno;
	private java.lang.String contno;
	private java.lang.String gccontno;
	private java.lang.String reltype;
	private java.lang.String relcolNo;
	private java.lang.String relserid;
	private java.lang.String cifid;
	private java.lang.String cliname;
	private java.lang.String certtype;
	private java.lang.String certno;
	private java.lang.String guarform;
	private java.lang.Double guarsum;
	private java.lang.Double guarLtv;
	private java.lang.String guarcurr;
	private java.lang.String status;
	private java.lang.String relation;
	private java.lang.String guarRel;
	private java.lang.String isInvalid;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setSerid(java.lang.String value) {
		this.serid = value;
	}
	
	@Id
	@Column(length=32,desc="流水号")
	public java.lang.String getSerid() {
		return this.serid;
	}
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="法人机构号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setApplyno(java.lang.String value) {
		this.applyno = value;
	}
	
	
	@Column(length=32,desc="申请编号")
	public java.lang.String getApplyno() {
		return this.applyno;
	}
	
	public void setContno(java.lang.String value) {
		this.contno = value;
	}
	
	
	@Column(length=32,desc="合同编号")
	public java.lang.String getContno() {
		return this.contno;
	}
	
	public void setGccontno(java.lang.String value) {
		this.gccontno = value;
	}
	
	
	@Column(length=32,desc="担保合同编号")
	public java.lang.String getGccontno() {
		return this.gccontno;
	}
	
	public void setReltype(java.lang.String value) {
		this.reltype = value;
	}
	
	
	@Column(length=16,desc="关联类型：10抵押、20质押、30保证、60协议、70其他")
	public java.lang.String getReltype() {
		return this.reltype;
	}
	
	public void setRelcolNo(java.lang.String value) {
		this.relcolNo = value;
	}
	
	
	@Column(length=16,desc="关联押品类型")
	public java.lang.String getRelcolNo() {
		return this.relcolNo;
	}
	
	public void setRelserid(java.lang.String value) {
		this.relserid = value;
	}
	
	
	@Column(length=32,desc="关联押品/保证人/第三方担保协议号")
	public java.lang.String getRelserid() {
		return this.relserid;
	}
	
	public void setCifid(java.lang.String value) {
		this.cifid = value;
	}
	
	
	@Column(length=32,desc="关联客户编号")
	public java.lang.String getCifid() {
		return this.cifid;
	}
	
	public void setCliname(java.lang.String value) {
		this.cliname = value;
	}
	
	
	@Column(length=128,desc="关联客户名称")
	public java.lang.String getCliname() {
		return this.cliname;
	}
	
	public void setCerttype(java.lang.String value) {
		this.certtype = value;
	}
	
	
	@Column(length=8,desc="关联客户证件类型")
	public java.lang.String getCerttype() {
		return this.certtype;
	}
	
	public void setCertno(java.lang.String value) {
		this.certno = value;
	}
	
	
	@Column(length=32,desc="关联客户证件号码")
	public java.lang.String getCertno() {
		return this.certno;
	}
	
	public void setGuarform(java.lang.String value) {
		this.guarform = value;
	}
	
	
	@Column(length=8,desc="保证担保形式")
	public java.lang.String getGuarform() {
		return this.guarform;
	}
	
	public void setGuarsum(java.lang.Double value) {
		this.guarsum = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="担保金额")
	public java.lang.Double getGuarsum() {
		return this.guarsum;
	}
	
	public void setGuarLtv(java.lang.Double value) {
		this.guarLtv = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=12,desc="抵押率")
	public java.lang.Double getGuarLtv() {
		return this.guarLtv;
	}
	
	public void setGuarcurr(java.lang.String value) {
		this.guarcurr = value;
	}
	
	
	@Column(length=8,desc="担保币种")
	public java.lang.String getGuarcurr() {
		return this.guarcurr;
	}
	
	public void setStatus(java.lang.String value) {
		this.status = value;
	}
	
	
	@Column(length=8,desc="担保状态：1待生效、2生效、3失效")
	public java.lang.String getStatus() {
		return this.status;
	}
	
	public void setRelation(java.lang.String value) {
		this.relation = value;
	}
	
	
	@Column(length=8,desc="与借款人关系")
	public java.lang.String getRelation() {
		return this.relation;
	}
	
	public void setGuarRel(java.lang.String value) {
		this.guarRel = value;
	}
	
	
	@Column(length=8,desc="担保关系：11共同借款人、20抵质押权属人、30担保自然人、40担保公司")
	public java.lang.String getGuarRel() {
		return this.guarRel;
	}
	
	public void setIsInvalid(java.lang.String value) {
		this.isInvalid = value;
	}
	
	
	@Column(length=8,desc="是否有效：0否、1是")
	public java.lang.String getIsInvalid() {
		return this.isInvalid;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	

}


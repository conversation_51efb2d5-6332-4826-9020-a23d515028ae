/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(InMstPK.class)@Table
public class InMst implements Serializable{
	
	private java.lang.String acNo;
	private java.lang.String opnBrNo;
	private java.lang.Long acId;
	private java.lang.Integer acSeqn;
	private java.lang.String prdtNo;
	private java.lang.Double bal;
	private java.lang.Double ysBal;
	private java.lang.Double hstBal;
	private java.lang.String sts;
	private java.lang.String opnDate;
	private java.lang.String icDate;
	private java.lang.String endDate;
	private java.lang.String lstDate;
	private java.lang.String clsDate;
	private java.lang.String intstKnd;
	private java.lang.String rateType;
	private java.lang.Double rate;
	private java.lang.Double overRate;
	private java.lang.Double intstAcm;
	private java.lang.Long hstCnt;
	private java.lang.Long hstPg;
	private java.lang.Long hstPrt;
	private java.lang.Double holdAmt;
	private java.lang.String odInd;
	private java.lang.String acType;
	private java.lang.String calCode;
	private java.lang.String mac;
	private java.lang.Long intstAcId;
	private java.lang.Double overAcm;
	private java.lang.String overUsed;
	private java.lang.Long bankid;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setAcNo(java.lang.String value) {
		this.acNo = value;
	}
	
	
	@Column(length=32,desc="账号")
	public java.lang.String getAcNo() {
		return this.acNo;
	}
	
	public void setOpnBrNo(java.lang.String value) {
		this.opnBrNo = value;
	}
	
	
	@Column(length=12,desc="开户机构")
	public java.lang.String getOpnBrNo() {
		return this.opnBrNo;
	}
	
	public void setAcId(java.lang.Long value) {
		this.acId = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="账户ID")
	public java.lang.Long getAcId() {
		return this.acId;
	}
	
	public void setAcSeqn(java.lang.Integer value) {
		this.acSeqn = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=4,desc="序号")
	public java.lang.Integer getAcSeqn() {
		return this.acSeqn;
	}
	
	public void setPrdtNo(java.lang.String value) {
		this.prdtNo = value;
	}
	
	
	@Column(length=10,desc="产品号")
	public java.lang.String getPrdtNo() {
		return this.prdtNo;
	}
	
	public void setBal(java.lang.Double value) {
		this.bal = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="余额")
	public java.lang.Double getBal() {
		return this.bal;
	}
	
	public void setYsBal(java.lang.Double value) {
		this.ysBal = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="昨日余额")
	public java.lang.Double getYsBal() {
		return this.ysBal;
	}
	
	public void setHstBal(java.lang.Double value) {
		this.hstBal = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="最后明细余额")
	public java.lang.Double getHstBal() {
		return this.hstBal;
	}
	
	public void setSts(java.lang.String value) {
		this.sts = value;
	}
	
	
	@Column(length=2,desc="状态1正常*销户")
	public java.lang.String getSts() {
		return this.sts;
	}
	
	public void setOpnDate(java.lang.String value) {
		this.opnDate = value;
	}
	
	
	@Column(length=16,desc="开户日期")
	public java.lang.String getOpnDate() {
		return this.opnDate;
	}
	
	public void setIcDate(java.lang.String value) {
		this.icDate = value;
	}
	
	
	@Column(length=16,desc="起息日期")
	public java.lang.String getIcDate() {
		return this.icDate;
	}
	
	public void setEndDate(java.lang.String value) {
		this.endDate = value;
	}
	
	
	@Column(length=16,desc="有效结束日")
	public java.lang.String getEndDate() {
		return this.endDate;
	}
	
	public void setLstDate(java.lang.String value) {
		this.lstDate = value;
	}
	
	
	@Column(length=16,desc="上笔发生日")
	public java.lang.String getLstDate() {
		return this.lstDate;
	}
	
	public void setClsDate(java.lang.String value) {
		this.clsDate = value;
	}
	
	
	@Column(length=16,desc="未启用")
	public java.lang.String getClsDate() {
		return this.clsDate;
	}
	
	public void setIntstKnd(java.lang.String value) {
		this.intstKnd = value;
	}
	
	
	@Column(length=2,desc="计息类型")
	public java.lang.String getIntstKnd() {
		return this.intstKnd;
	}
	
	public void setRateType(java.lang.String value) {
		this.rateType = value;
	}
	
	
	@Column(length=2,desc="计息类型:0不计息")
	public java.lang.String getRateType() {
		return this.rateType;
	}
	
	public void setRate(java.lang.Double value) {
		this.rate = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="利率")
	public java.lang.Double getRate() {
		return this.rate;
	}
	
	public void setOverRate(java.lang.Double value) {
		this.overRate = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="逾期利率")
	public java.lang.Double getOverRate() {
		return this.overRate;
	}
	
	public void setIntstAcm(java.lang.Double value) {
		this.intstAcm = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="利息积数")
	public java.lang.Double getIntstAcm() {
		return this.intstAcm;
	}
	
	public void setHstCnt(java.lang.Long value) {
		this.hstCnt = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="明细笔数")
	public java.lang.Long getHstCnt() {
		return this.hstCnt;
	}
	
	public void setHstPg(java.lang.Long value) {
		this.hstPg = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="满页页数")
	public java.lang.Long getHstPg() {
		return this.hstPg;
	}
	
	public void setHstPrt(java.lang.Long value) {
		this.hstPrt = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="帐页已打笔次")
	public java.lang.Long getHstPrt() {
		return this.hstPrt;
	}
	
	public void setHoldAmt(java.lang.Double value) {
		this.holdAmt = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="冻结金额未启用")
	public java.lang.Double getHoldAmt() {
		return this.holdAmt;
	}
	
	public void setOdInd(java.lang.String value) {
		this.odInd = value;
	}
	
	
	@Column(length=2,desc="是否可透支标志")
	public java.lang.String getOdInd() {
		return this.odInd;
	}
	
	public void setAcType(java.lang.String value) {
		this.acType = value;
	}
	
	
	@Column(length=2,desc="帐户类型")
	public java.lang.String getAcType() {
		return this.acType;
	}
	
	public void setCalCode(java.lang.String value) {
		this.calCode = value;
	}
	
	
	@Column(length=20,desc="指标提示代码:未启用")
	public java.lang.String getCalCode() {
		return this.calCode;
	}
	
	public void setMac(java.lang.String value) {
		this.mac = value;
	}
	
	
	@Column(length=32,desc="密押")
	public java.lang.String getMac() {
		return this.mac;
	}
	
	public void setIntstAcId(java.lang.Long value) {
		this.intstAcId = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="还息账户")
	public java.lang.Long getIntstAcId() {
		return this.intstAcId;
	}
	
	public void setOverAcm(java.lang.Double value) {
		this.overAcm = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=21,desc="逾期积数")
	public java.lang.Double getOverAcm() {
		return this.overAcm;
	}
	
	public void setOverUsed(java.lang.String value) {
		this.overUsed = value;
	}
	
	
	@Column(length=2,desc="逾期标志未启用")
	public java.lang.String getOverUsed() {
		return this.overUsed;
	}
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="法人机构号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"账号="+acNo+ " , "+ "开户机构="+opnBrNo+ " , "+ "账户ID="+acId+ " , "+ "序号="+acSeqn+ " , "+ "产品号="+prdtNo+ " , "+ "余额="+bal+ " , "+ "昨日余额="+ysBal+ " , "+ "最后明细余额="+hstBal+ " , "+ "状态1正常*销户="+sts+ " , "+ "开户日期="+opnDate+ " , "+ "起息日期="+icDate+ " , "+ "有效结束日="+endDate+ " , "+ "上笔发生日="+lstDate+ " , "+ "未启用="+clsDate+ " , "+ "计息类型="+intstKnd+ " , "+ "计息类型:0不计息="+rateType+ " , "+ "利率="+rate+ " , "+ "逾期利率="+overRate+ " , "+ "利息积数="+intstAcm+ " , "+ "明细笔数="+hstCnt+ " , "+ "满页页数="+hstPg+ " , "+ "帐页已打笔次="+hstPrt+ " , "+ "冻结金额未启用="+holdAmt+ " , "+ "是否可透支标志="+odInd+ " , "+ "帐户类型="+acType+ " , "+ "指标提示代码:未启用="+calCode+ " , "+ "密押="+mac+ " , "+ "还息账户="+intstAcId+ " , "+ "逾期积数="+overAcm+ " , "+ "逾期标志未启用="+overUsed+ " , "+ "法人机构号="+bankid+ " } ";
	}
	

}


/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model.key;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;

public class FsOthReceivablesCopyPK implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String serid;
	private java.lang.String cifid;
	private java.lang.String fsno;

	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	public void setSerid(java.lang.String value) {
		this.serid = value;
	}
	
	public java.lang.String getSerid() {
		return this.serid;
	}
	public void setCifid(java.lang.String value) {
		this.cifid = value;
	}
	
	public java.lang.String getCifid() {
		return this.cifid;
	}
	public void setFsno(java.lang.String value) {
		this.fsno = value;
	}
	
	public java.lang.String getFsno() {
		return this.fsno;
	}
	

}


/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model.key;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;

public class ComParmPK implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String parmCode;
	private java.lang.Long parmSeqn;

	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	public void setParmCode(java.lang.String value) {
		this.parmCode = value;
	}
	
	public java.lang.String getParmCode() {
		return this.parmCode;
	}
	public void setParmSeqn(java.lang.Long value) {
		this.parmSeqn = value;
	}
	
	public java.lang.Long getParmSeqn() {
		return this.parmSeqn;
	}
	

}


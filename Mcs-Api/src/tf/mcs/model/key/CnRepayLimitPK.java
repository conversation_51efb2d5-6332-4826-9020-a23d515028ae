/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model.key;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;

public class CnRepayLimitPK implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String repayType;
	private java.lang.String bankcode;

	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	public void setRepayType(java.lang.String value) {
		this.repayType = value;
	}
	
	public java.lang.String getRepayType() {
		return this.repayType;
	}
	public void setBankcode(java.lang.String value) {
		this.bankcode = value;
	}
	
	public java.lang.String getBankcode() {
		return this.bankcode;
	}
	

}


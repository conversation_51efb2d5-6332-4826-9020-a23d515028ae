/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model.key;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;

public class BusAffrimCollateralPK implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String applyno;
	private java.lang.String guarNo;

	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	public void setApplyno(java.lang.String value) {
		this.applyno = value;
	}
	
	public java.lang.String getApplyno() {
		return this.applyno;
	}
	public void setGuarNo(java.lang.String value) {
		this.guarNo = value;
	}
	
	public java.lang.String getGuarNo() {
		return this.guarNo;
	}
	

}


/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@Table
public class ActLotteryPrize implements Serializable{
	
	private java.lang.Long id;
	private java.lang.Long lotteryId;
	private java.lang.String prizeType;
	private java.lang.String grantType;
	private java.lang.String prizeGrade;
	private java.lang.String prizeName;
	private java.lang.Long prizeNumber;
	private java.lang.Double prizeSigleCost;
	private java.lang.Double prizeAllCost;
	private java.lang.Double prizeMailCost;
	private java.lang.Double prizeRadix;
	private java.lang.String createTime;
	private java.lang.String createDate;
	private java.lang.String updateTime;
	private java.lang.String updateDate;
	private java.lang.String status;
	private java.lang.Long prizeRemainNumber;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setId(java.lang.Long value) {
		this.id = value;
	}
	@XmlElement(nillable = true)
	@Id
	//在主键上增加注解
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(length=19,desc="主键id")
	public java.lang.Long getId() {
		return this.id;
	}
	
	public void setLotteryId(java.lang.Long value) {
		this.lotteryId = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="活动编号")
	public java.lang.Long getLotteryId() {
		return this.lotteryId;
	}
	
	public void setPrizeType(java.lang.String value) {
		this.prizeType = value;
	}
	
	
	@Column(length=2,desc="奖品类型 1实物奖品2虚拟卡券奖品")
	public java.lang.String getPrizeType() {
		return this.prizeType;
	}
	
	public void setGrantType(java.lang.String value) {
		this.grantType = value;
	}
	
	
	@Column(length=2,desc="发放方式 1快递邮寄2添加微信领取")
	public java.lang.String getGrantType() {
		return this.grantType;
	}
	
	public void setPrizeGrade(java.lang.String value) {
		this.prizeGrade = value;
	}
	
	
	@Column(length=2,desc="奖品等级 1一等奖2二等奖3三等奖4四等奖5五等奖6六等奖")
	public java.lang.String getPrizeGrade() {
		return this.prizeGrade;
	}
	
	public void setPrizeName(java.lang.String value) {
		this.prizeName = value;
	}
	
	
	@Column(length=128,desc="奖品名称")
	public java.lang.String getPrizeName() {
		return this.prizeName;
	}
	
	public void setPrizeNumber(java.lang.Long value) {
		this.prizeNumber = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="奖品数量")
	public java.lang.Long getPrizeNumber() {
		return this.prizeNumber;
	}
	
	public void setPrizeSigleCost(java.lang.Double value) {
		this.prizeSigleCost = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="奖品单件成本")
	public java.lang.Double getPrizeSigleCost() {
		return this.prizeSigleCost;
	}
	
	public void setPrizeAllCost(java.lang.Double value) {
		this.prizeAllCost = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="奖品成本")
	public java.lang.Double getPrizeAllCost() {
		return this.prizeAllCost;
	}
	
	public void setPrizeMailCost(java.lang.Double value) {
		this.prizeMailCost = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="邮寄成本")
	public java.lang.Double getPrizeMailCost() {
		return this.prizeMailCost;
	}
	
	public void setPrizeRadix(java.lang.Double value) {
		this.prizeRadix = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=18,desc="prize_radix")
	public java.lang.Double getPrizeRadix() {
		return this.prizeRadix;
	}
	
	public void setCreateTime(java.lang.String value) {
		this.createTime = value;
	}
	
	
	@Column(length=32,desc="创建时间")
	public java.lang.String getCreateTime() {
		return this.createTime;
	}
	
	public void setCreateDate(java.lang.String value) {
		this.createDate = value;
	}
	
	
	@Column(length=8,desc="创建日期")
	public java.lang.String getCreateDate() {
		return this.createDate;
	}
	
	public void setUpdateTime(java.lang.String value) {
		this.updateTime = value;
	}
	
	
	@Column(length=32,desc="更新时间")
	public java.lang.String getUpdateTime() {
		return this.updateTime;
	}
	
	public void setUpdateDate(java.lang.String value) {
		this.updateDate = value;
	}
	
	
	@Column(length=8,desc="更新日期")
	public java.lang.String getUpdateDate() {
		return this.updateDate;
	}
	
	public void setStatus(java.lang.String value) {
		this.status = value;
	}
	
	
	@Column(length=2,desc="数据状态 0无效1有效")
	public java.lang.String getStatus() {
		return this.status;
	}
	
	public void setPrizeRemainNumber(java.lang.Long value) {
		this.prizeRemainNumber = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=19,desc="奖品剩余数量")
	public java.lang.Long getPrizeRemainNumber() {
		return this.prizeRemainNumber;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"主键id="+id+ " , "+ "活动编号="+lotteryId+ " , "+ "奖品类型 1实物奖品2虚拟卡券奖品="+prizeType+ " , "+ "发放方式 1快递邮寄2添加微信领取="+grantType+ " , "+ "奖品等级 1一等奖2二等奖3三等奖4四等奖5五等奖6六等奖="+prizeGrade+ " , "+ "奖品名称="+prizeName+ " , "+ "奖品数量="+prizeNumber+ " , "+ "奖品单件成本="+prizeSigleCost+ " , "+ "奖品成本="+prizeAllCost+ " , "+ "邮寄成本="+prizeMailCost+ " , "+ "prize_radix="+prizeRadix+ " , "+ "创建时间="+createTime+ " , "+ "创建日期="+createDate+ " , "+ "更新时间="+updateTime+ " , "+ "更新日期="+updateDate+ " , "+ "数据状态 0无效1有效="+status+ " , "+ "奖品剩余数量="+prizeRemainNumber+ " } ";
	}
	

}


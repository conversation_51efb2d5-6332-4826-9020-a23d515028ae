/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(PfTellevelPK.class)@Table
public class PfTellevel implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String stalvl;
	private java.lang.Double levelmoney;
	private java.lang.Double afterloan;
	private java.lang.Double actApp;
	private java.lang.Double actResearch;
	private java.lang.Double grantSum;
	private java.lang.Double mgrBal;
	private java.lang.Double teamGoal;
	private java.lang.String staname;
	private java.lang.String updUsr;
	private java.lang.String updDate;
	private java.lang.String staid;
	private java.lang.String instcode;
	private java.lang.Double actGrant;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="BANKID")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setStalvl(java.lang.String value) {
		this.stalvl = value;
	}
	
	@Id
	@Column(length=2,desc="STALVL")
	public java.lang.String getStalvl() {
		return this.stalvl;
	}
	
	public void setLevelmoney(java.lang.Double value) {
		this.levelmoney = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="LEVELMONEY")
	public java.lang.Double getLevelmoney() {
		return this.levelmoney;
	}
	
	public void setAfterloan(java.lang.Double value) {
		this.afterloan = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="AFTERLOAN")
	public java.lang.Double getAfterloan() {
		return this.afterloan;
	}
	
	public void setActApp(java.lang.Double value) {
		this.actApp = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="ACT_APP")
	public java.lang.Double getActApp() {
		return this.actApp;
	}
	
	public void setActResearch(java.lang.Double value) {
		this.actResearch = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="ACT_RESEARCH")
	public java.lang.Double getActResearch() {
		return this.actResearch;
	}
	
	public void setGrantSum(java.lang.Double value) {
		this.grantSum = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="GRANT_SUM")
	public java.lang.Double getGrantSum() {
		return this.grantSum;
	}
	
	public void setMgrBal(java.lang.Double value) {
		this.mgrBal = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="MGR_BAL")
	public java.lang.Double getMgrBal() {
		return this.mgrBal;
	}
	
	public void setTeamGoal(java.lang.Double value) {
		this.teamGoal = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="TEAM_GOAL")
	public java.lang.Double getTeamGoal() {
		return this.teamGoal;
	}
	
	public void setStaname(java.lang.String value) {
		this.staname = value;
	}
	
	
	@Column(length=32,desc="STANAME")
	public java.lang.String getStaname() {
		return this.staname;
	}
	
	public void setUpdUsr(java.lang.String value) {
		this.updUsr = value;
	}
	
	
	@Column(length=5,desc="UPD_USR")
	public java.lang.String getUpdUsr() {
		return this.updUsr;
	}
	
	public void setUpdDate(java.lang.String value) {
		this.updDate = value;
	}
	
	
	@Column(length=8,desc="UPD_DATE")
	public java.lang.String getUpdDate() {
		return this.updDate;
	}
	
	public void setStaid(java.lang.String value) {
		this.staid = value;
	}
	
	@Id
	@Column(length=10,desc="STAID")
	public java.lang.String getStaid() {
		return this.staid;
	}
	
	public void setInstcode(java.lang.String value) {
		this.instcode = value;
	}
	
	@Id
	@Column(length=12,desc="INSTCODE")
	public java.lang.String getInstcode() {
		return this.instcode;
	}
	
	public void setActGrant(java.lang.Double value) {
		this.actGrant = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=16,desc="ACT_GRANT")
	public java.lang.Double getActGrant() {
		return this.actGrant;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	

}


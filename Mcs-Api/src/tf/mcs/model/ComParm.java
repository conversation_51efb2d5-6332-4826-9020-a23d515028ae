/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(ComParmPK.class)@Table
public class ComParm implements Serializable{
	
	private java.lang.Long bankid;
	private java.lang.String parmCode;
	private java.lang.Long parmSeqn;
	private java.lang.String parmName;
	private java.lang.String valType;
	private java.lang.String val;
	private java.lang.String valid;
	private java.lang.String modified;
	private java.lang.String visible;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=19,desc="法人机构号")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	public void setParmCode(java.lang.String value) {
		this.parmCode = value;
	}
	
	@Id
	@Column(length=5,desc="参数码")
	public java.lang.String getParmCode() {
		return this.parmCode;
	}
	
	public void setParmSeqn(java.lang.Long value) {
		this.parmSeqn = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=16,desc="参数序号")
	public java.lang.Long getParmSeqn() {
		return this.parmSeqn;
	}
	
	public void setParmName(java.lang.String value) {
		this.parmName = value;
	}
	
	
	@Column(length=400,desc="参数名称")
	public java.lang.String getParmName() {
		return this.parmName;
	}
	
	public void setValType(java.lang.String value) {
		this.valType = value;
	}
	
	
	@Column(length=1,desc="VAL_TYPE")
	public java.lang.String getValType() {
		return this.valType;
	}
	
	public void setVal(java.lang.String value) {
		this.val = value;
	}
	
	
	@Column(length=100,desc="VAL")
	public java.lang.String getVal() {
		return this.val;
	}
	
	public void setValid(java.lang.String value) {
		this.valid = value;
	}
	
	
	@Column(length=1,desc="可用标志：0，不可用；1，可用")
	public java.lang.String getValid() {
		return this.valid;
	}
	
	public void setModified(java.lang.String value) {
		this.modified = value;
	}
	
	
	@Column(length=1,desc="修改标志：0，不可改；1，可修改")
	public java.lang.String getModified() {
		return this.modified;
	}
	
	public void setVisible(java.lang.String value) {
		this.visible = value;
	}
	
	
	@Column(length=1,desc="页面可见：0，不可见；1，可见")
	public java.lang.String getVisible() {
		return this.visible;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	

}


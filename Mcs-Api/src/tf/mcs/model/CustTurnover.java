/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.mcs.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.mcs.model.key.*;

@Entity
@IdClass(CustTurnoverPK.class)@Table
public class CustTurnover implements Serializable{
	
	private java.lang.Long id;
	private java.lang.String ispreloan;
	private java.lang.String applyno;
	private java.lang.String accountName;
	private java.lang.String bankName;
	private java.lang.String begindate;
	private java.lang.String enddate;
	private java.lang.String isDubious;
	private java.lang.String serid;
	private java.lang.String createDate;
	private java.lang.String createTime;
	private java.lang.String updateDate;
	private java.lang.String updateTime;
	private java.lang.String preloanMessage;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setId(java.lang.Long value) {
		this.id = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=11,desc="序号.1-5")
	public java.lang.Long getId() {
		return this.id;
	}
	
	public void setIspreloan(java.lang.String value) {
		this.ispreloan = value;
	}
	
	@Id
	@Column(length=2,desc="是否贷前")
	public java.lang.String getIspreloan() {
		return this.ispreloan;
	}
	
	public void setApplyno(java.lang.String value) {
		this.applyno = value;
	}
	
	@Id
	@Column(length=64,desc="申请号")
	public java.lang.String getApplyno() {
		return this.applyno;
	}
	
	public void setAccountName(java.lang.String value) {
		this.accountName = value;
	}
	
	
	@Column(length=64,desc="账户名")
	public java.lang.String getAccountName() {
		return this.accountName;
	}
	
	public void setBankName(java.lang.String value) {
		this.bankName = value;
	}
	
	
	@Column(length=64,desc="开户银行")
	public java.lang.String getBankName() {
		return this.bankName;
	}
	
	public void setBegindate(java.lang.String value) {
		this.begindate = value;
	}
	
	
	@Column(length=64,desc="流水起始日期")
	public java.lang.String getBegindate() {
		return this.begindate;
	}
	
	public void setEnddate(java.lang.String value) {
		this.enddate = value;
	}
	
	
	@Column(length=64,desc="流水截止日期")
	public java.lang.String getEnddate() {
		return this.enddate;
	}
	
	public void setIsDubious(java.lang.String value) {
		this.isDubious = value;
	}
	
	
	@Column(length=2,desc="是否有可疑交易")
	public java.lang.String getIsDubious() {
		return this.isDubious;
	}
	
	public void setSerid(java.lang.String value) {
		this.serid = value;
	}
	
	
	@Column(length=128,desc="对应的月度流水")
	public java.lang.String getSerid() {
		return this.serid;
	}
	
	public void setCreateDate(java.lang.String value) {
		this.createDate = value;
	}
	
	
	@Column(length=16,desc="创建年月日")
	public java.lang.String getCreateDate() {
		return this.createDate;
	}
	
	public void setCreateTime(java.lang.String value) {
		this.createTime = value;
	}
	
	
	@Column(length=16,desc="创建时分秒")
	public java.lang.String getCreateTime() {
		return this.createTime;
	}
	
	public void setUpdateDate(java.lang.String value) {
		this.updateDate = value;
	}
	
	
	@Column(length=16,desc="修改年月日")
	public java.lang.String getUpdateDate() {
		return this.updateDate;
	}
	
	public void setUpdateTime(java.lang.String value) {
		this.updateTime = value;
	}
	
	
	@Column(length=16,desc="修改时分秒")
	public java.lang.String getUpdateTime() {
		return this.updateTime;
	}
	
	public void setPreloanMessage(java.lang.String value) {
		this.preloanMessage = value;
	}
	
	
	@Column(length=1024,desc="贷前确认信息")
	public java.lang.String getPreloanMessage() {
		return this.preloanMessage;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	
	
	public String toDescString(){
		return " { "+"序号.1-5="+id+ " , "+ "是否贷前="+ispreloan+ " , "+ "申请号="+applyno+ " , "+ "账户名="+accountName+ " , "+ "开户银行="+bankName+ " , "+ "流水起始日期="+begindate+ " , "+ "流水截止日期="+enddate+ " , "+ "是否有可疑交易="+isDubious+ " , "+ "对应的月度流水="+serid+ " , "+ "创建年月日="+createDate+ " , "+ "创建时分秒="+createTime+ " , "+ "修改年月日="+updateDate+ " , "+ "修改时分秒="+updateTime+ " , "+ "贷前确认信息="+preloanMessage+ " } ";
	}
	

}


//系统级数据总线
tf = Ext.emptyFn;
tf.sysdb = Ext.emptyFn;
tf.sysdb.suId;
tf.sysdb.suName;
tf.sysdb.branchId;
tf.sysdb.branchName;
tf.sysdb.workDate;
tf.sysdb.bankid;
tf.sysdb.optel;
tf.sysdb.instType;
tf.sysdb.sysId;

Ext.onReady(function () {
    TF.ExtHelper.call("/TccSsoUri/sessionIsEnable", function (data) {
        if (data.isSuccess) {
            tf.sysdb.suId = data.refObj.suId;
            tf.sysdb.suName = data.refObj.suName;
            tf.sysdb.branchId = data.refObj.branchId;
            tf.sysdb.branchName = data.refObj.branchName;
            tf.sysdb.workDate = data.refObj.workDate;
            tf.sysdb.bankid = data.refObj.bankid;
            tf.sysdb.sysId = TF.SYS.SYS_ID;
            if (!Ext.isEmpty(data.refObj.attrMap)) {
                if (data.refObj.attrMap.optel) {
                    tf.sysdb.optel = data.refObj.attrMap.optel;
                }
                tf.sysdb.instType=data.refObj.attrMap.instType;
            }
        } else {
            top.location.href = systemDir + "/Logout.html";
        }
    }, null, false);
});
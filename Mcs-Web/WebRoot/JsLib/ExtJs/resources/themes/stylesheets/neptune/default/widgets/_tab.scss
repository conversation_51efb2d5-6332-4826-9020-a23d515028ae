/**
  * @class Ext.Tab
  */
@mixin extjs-tab {
//    @include x-frame(
//        $cls: 'tab',
//        $ui: 'default-top',
//        $border-radius: $tab-top-border-radius,
//        $border-width: $tab-top-border-width,
//        $background-color: $tab-base-color,
//        $background-gradient: $tab-background-gradient,
//        $background-direction: top,
//        $table: true
//    );

//    @include x-frame(
//        $cls: 'tab',
//        $ui: 'default-bottom',
//        $border-radius: $tab-bottom-border-radius,
//        $border-width: $tab-bottom-border-width,
//        $background-color: $tab-base-color,
//        $background-gradient: $tab-background-gradient,
//        $background-direction: bottom,
//        $table: true
//    );

    .#{$prefix}tab {
        z-index: 1;
        margin: 0 $tab-spacing 0 0;
        display: inline-block;
        // @tag iezoomhack
        *display: inline;
//        height: $tab-height;
//        border-color: $tab-border-color;
        @extend .mr-white-glove;

        em {
            display: block;
            padding: 7px 22px 8px 22px;
            line-height: 1px;
        }

        button {
            background: none;
            border: 0;
            padding: 0;
            margin: 0;
            -webkit-appearance: none;

            color: $tab-color;

            @extend .outline-none;
            @extend .ellipsis;

            &::-moz-focus-inner {
                border: 0;
                padding: 0;
            }

            .#{$prefix}tab-inner {
                font-size: $tab-font-size;

                @if $tab-font-weight {
                    font-weight: $tab-font-weight;
                }
                @if $tab-font-family {
                    font-family: $tab-font-family;
                }
                
                background-color: transparent;
                background-repeat: no-repeat;
                background-position: 0 -2px;
                display: block;
                text-align: center;
                white-space: nowrap;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                overflow: hidden;
            }
        }

        img {
            display: none;
        }
    }

    .#{$prefix}tab-close-btn {
        @include pictos('*', $size: 12px, $include-states: false);

        display: block;
        position: absolute !important;
        top: 5px;
        right: 2px;

        width: 13px;
        height: 13px;

        text-decoration: none;

        @include opacity(.6);
    }

    .#{$prefix}tab-close-btn:hover {
        @include opacity(1);
    }

    .x-panel-default-framed .x-tab-default-top {
        @include border-radius($tab-top-border-radius);
    }

    .x-panel-default-framed .x-tab-default-bottom {
        @include border-radius($tab-bottom-border-radius);
    }

    .#{$prefix}tab-disabled {
        @include opacity(1);
    }

    .#{$prefix}border-box {
        .#{$prefix}tab-default-top {
//            height: $tab-height + top($tabbar-top-strip-border-width);
        }
        .#{$prefix}tab-default-bottom {
//            height: $tab-height + bottom($tabbar-bottom-strip-border-width);
        }
    }

    @if $include-ie {
        * html .#{$prefix}ie {
            .#{$prefix}tab button {
                width: 1px;
            }
        }

        .#{$prefix}strict .#{$prefix}ie6,
        .#{$prefix}strict .#{$prefix}ie7 {
            .#{$prefix}tab .#{$prefix}frame-mc {
                height: 100%;
            }
        }

        .#{$prefix}ie .#{$prefix}tab-active button:active {
            position: relative;
            top: -1px;
            left: -1px;
        }
    }

    $framepad: max(top($tab-top-border-radius), right($tab-top-border-radius)) - top($tab-top-border-width);

    .#{$prefix}tab-default-top {
        @if $tab-inner-border {
            @include inner-border(
                $width: $tab-top-inner-border-width,
                $color: $tab-inner-border-color
            );
        }

//        border-bottom: 1px solid $tabbar-strip-border-color !important;

//        em {
//            padding-bottom: $framepad;
//        }

        button,
        .#{$prefix}tab-inner {
//            height: $tab-height - $framepad * 2 - top($tab-top-border-width);
//            line-height: $tab-height - $framepad * 2 - top($tab-top-border-width);
        }
    }

    .#{$prefix}nbr .#{$prefix}tab-default-top {
//        border-bottom-width: 1px !important;
    }

    .#{$prefix}tab-default-top-active {
//        border-bottom-color: $tabbar-strip-background-color !important;
    }

    $framepad: max(bottom($tab-bottom-border-radius), left($tab-bottom-border-radius)) - bottom($tab-bottom-border-width);

    .#{$prefix}tab-default-bottom {
//        @if $tab-inner-border {
//            @include inner-border(
//                $width: $tab-bottom-inner-border-width,
//                $color: $tab-inner-border-color
//            );
//        }

//        border-top: 1px solid $tabbar-strip-border-color !important;

//        @include inner-border(
//            $width: $tab-bottom-inner-border-width,
//            $color: $tab-inner-border-color
//        );

        em {
//            padding-top: $framepad;
        }

        button,
        .#{$prefix}tab-inner {
//            height: $tab-height - $framepad * 2 - bottom($tab-bottom-border-width);
//            line-height: $tab-height - $framepad * 2 - bottom($tab-bottom-border-width);
        }
    }

    .#{$prefix}nbr .#{$prefix}tab-default-bottom {
//        border-top-width: 1px !important;
    }

    .#{$prefix}tab-default-bottom-active {
//        border-top-color: $tabbar-strip-background-color !important;
    }

    .#{$prefix}tab-default-disabled {
        &, & * {
            cursor: default;
        }

        border-color: $tab-border-color-disabled;
        @include background-gradient($tab-base-color-disabled, $tab-background-gradient-disabled);

        button {
            color: $tab-color-disabled !important;
        }
    }

    .#{$prefix}tab-icon-text-left {
        .#{$prefix}tab-inner {
            padding-left: 24px;
        }
    }

    .#{$prefix}tab {
        background: rgba(255,255,255,.2);
        
        button, a {
            position: relative;

            .#{$prefix}tab-icon {
                position: absolute;
                background-repeat: no-repeat;
                top: 0;
                left: 0;
                right: auto;
                bottom: 0;
                width: $tab-icon-size;
                height: $tab-icon-size;
            }
        }

        &.x-icon {
            em {
//                padding: 7px 10px 8px;
            }

            button {
                width: $tab-icon-size;
                height: $tab-icon-size;
            }
        }

        &.x-icon-text-left {
            em {
//                padding-left: 10px;
            }
        }
    }

    .#{$prefix}tab-default-plain {
        background: transparent
    }

    //over
    .#{$prefix}tab-over {
        @if $tab-border-color-over != $tab-border-color {
            border-color: $tab-border-color-over;
        }

        button {
            @if $tab-color-over != $tab-color {
                color: $tab-color-over;
            }
            @if $tab-font-weight-over != $tab-font-weight {
                font-weight: $tab-font-weight-over;
            }
            @if $tab-font-size-over != $tab-font-size {
                font-size: $tab-font-size-over;
            }
            @if $tab-font-family-over != $tab-font-family {
                font-family: $tab-font-family-over;
            }
        }
    }

    // @tag question: Tabs not getting these classes properly?
    .#{$prefix}tab-top-over, .#{$prefix}tab-over {
        @include background-gradient($tab-base-color-over, $tab-background-gradient-over, top);
    }
    .#{$prefix}tab-bottom-over {
        @include background-gradient($tab-base-color-over, $tab-background-gradient-over, bottom);
    }

    //active
    .#{$prefix}tab-active {
        z-index: 3;
        @if $tab-border-color-active != $tab-border-color {
            border-color: $tab-border-color-active;
        }

        button {
            @if $tab-color-active != $tab-color {
                color: $tab-color-active;
            }
            @if $tab-font-weight-active != $tab-font-weight {
                font-weight: $tab-font-weight-active;
            }
            @if $tab-font-size-active != $tab-font-size {
                font-size: $tab-font-size-active;
            }
            @if $tab-font-family-active != $tab-font-family {
                font-family: $tab-font-family-active;
            }
        }

        .#{$prefix}tab-close-btn {
            &:after {
                @include background-gradient(#CCCCCC, null);
            }
        }

        .#{$prefix}tab-icon {
            &:after {
                color: $tab-color-active;
            }
        }
    }

    .#{$prefix}tab-top-active {
        @include background-gradient($tab-base-color-active, $tab-background-gradient-active, top);
    }
    .#{$prefix}tab-bottom-active {
        @include background-gradient($tab-base-color-active, $tab-background-gradient-active, bottom);
    }

    //disabled
    .#{$prefix}tab-disabled {
        @if $tab-border-color-disabled != $tab-border-color {
            border-color: $tab-border-color-disabled;
        }

        button {
            @if $tab-color-disabled != $tab-color {
                color: $tab-color-disabled;
            }
            @if $tab-font-weight-disabled != $tab-font-weight {
                font-weight: $tab-font-weight-disabled;
            }
            @if $tab-font-size-disabled != $tab-font-size {
                font-size: $tab-font-size-disabled;
            }
            @if $tab-font-family-disabled != $tab-font-family {
                font-family: $tab-font-family-disabled;
            }
        }

        .#{$prefix}tab-icon {
            &:after {
                opacity: .5;
            }
        }
    }

    .#{$prefix}tab-top-disabled {
//        @include background-gradient($tab-base-color-disabled, $tab-background-gradient-disabled, top);
    }
    .#{$prefix}tab-bottom-disabled {
//        @include background-gradient($tab-base-color-disabled, $tab-background-gradient-disabled, bottom);
    }

    @if not $supports-gradients or $compile-all {
        .#{$prefix}nlg {
            @if $tab-background-gradient != null {
                .#{$prefix}tab-top { background-image: theme-background-image($theme-name, 'tab/tab-default-top-bg.gif'); }
                .#{$prefix}tab-bottom { background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-bg.gif'); }
            }

            @if $tab-background-gradient-over != null {
                .#{$prefix}tab-top-over { background-image: theme-background-image($theme-name, 'tab/tab-default-top-over-bg.gif'); }
                .#{$prefix}tab-bottom-over { background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-over-bg.gif'); }
            }

            @if $tab-background-gradient-active != null {
                .#{$prefix}tab-top-active { background-image: theme-background-image($theme-name, 'tab/tab-default-top-active-bg.gif'); }
                .#{$prefix}tab-bottom-active { background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-active-bg.gif'); }
            }

            @if $tab-background-gradient-disabled != null {
                .#{$prefix}tab-top-disabled { background-image: theme-background-image($theme-name, 'tab/tab-default-top-disabled-bg.gif') !important; }
                .#{$prefix}tab-bottom-disabled { background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-disabled-bg.gif') !important; }
            }
        }
    }

    .#{$prefix}tab-closable em {
//        padding-right: $tab-closable-icon-width + 3;
    }

    @if not $supports-border-radius or $compile-all {
        .#{$prefix}nbr {
            .#{$prefix}tab-top-over {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-over-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-over-sides.gif');
                }
                @if $tab-background-gradient-over != null {
                    .#{$prefix}frame-mc {
                        background-color: $tab-base-color-over;
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-top-over-bg.gif');
                    }
                }
            }

            .#{$prefix}tab-bottom-over {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-over-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-over-sides.gif');
                }
                @if $tab-background-gradient-over != null {
                    .#{$prefix}frame-mc {
                        background-color: $tab-base-color-over;
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-over-bg.gif');
                    }
                }
            }

            .#{$prefix}tab-top-active {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-active-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-active-sides.gif');
                }
                @if $tab-background-gradient-active != null {
                    .#{$prefix}frame-mc {
                        background-color: $tab-base-color-active;
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-top-active-bg.gif');
                    }
                }
            }

            .#{$prefix}tab-bottom-active {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-active-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-active-sides.gif');
                }
                @if $tab-background-gradient-active != null {
                    .#{$prefix}frame-mc {
                        background-color: $tab-base-color-active;
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-active-bg.gif');
                    }
                }
            }

            .#{$prefix}tab-top-disabled {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-disabled-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-top-disabled-sides.gif');
                }
                @if $tab-background-gradient-disabled != null {
                    .#{$prefix}frame-mc {
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-top-disabled-bg.gif');
                    }
                }
            }

            .#{$prefix}tab-bottom-disabled {
                .#{$prefix}frame-tl,
                .#{$prefix}frame-bl,
                .#{$prefix}frame-tr,
                .#{$prefix}frame-br,
                .#{$prefix}frame-tc,
                .#{$prefix}frame-bc {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-disabled-corners.gif');
                }
                .#{$prefix}frame-ml,
                .#{$prefix}frame-mr {
                    background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-disabled-sides.gif');
                }
                @if $tab-background-gradient-disabled != null {
                    .#{$prefix}frame-mc {
                        background-repeat: repeat-x;
                        background-image: theme-background-image($theme-name, 'tab/tab-default-bottom-disabled-bg.gif');
                    }
                }
            }
        }
    }
}

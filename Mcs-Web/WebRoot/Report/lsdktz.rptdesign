<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.2.2.v************ Build &lt;4.2.2.v20130206-1509></property>
    <property name="units">in</property>
    <method name="initialize"><![CDATA[var cliname = params["cliname"].value;
params["cliname"].value=decodeURI(cliname);]]></method>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="layoutPreference">auto layout</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="bankid" id="139">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">100000</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="instcode" id="140">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">*********</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="begindate" id="141">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="beginmaxdate" id="142">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="loanacNo" id="697">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="cliname" id="698">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="tjoperid" id="699">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="vchsts" id="701">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="endmindate" id="710">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="enddate" id="711">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="txdate" id="941">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="bratetype" id="980">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="数据源" id="143">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledContentBidiFormatStr</name>
                </ex-property>
            </list-property>
            <property name="odaDriverClass">oracle.jdbc.driver.OracleDriver</property>
            <property name="odaURL">jdbc:oracle:thin:@*************:1521:mcs</property>
            <property name="odaUser">mcsjn</property>
            <encrypted-property name="odaPassword" encryptionID="base64">bWNzam4=</encrypted-property>
            <property name="odaJndiName">java:comp/env/jdbc/bankreport</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集" id="144">
            <list-property name="columnHints"/>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">bankid</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">1</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_3</property>
                    <property name="paramName">begindate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">2</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">beginmaxdate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">3</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_7</property>
                    <property name="paramName">endmindate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">4</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_8</property>
                    <property name="paramName">enddate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">5</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_12</property>
                    <property name="paramName">loanacNo</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">6</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_13</property>
                    <property name="paramName">cliname</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">7</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_4</property>
                    <property name="paramName">tjoperid</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">8</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_5</property>
                    <property name="paramName">instcode</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">9</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_6</property>
                    <property name="paramName">instcode</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">10</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_14</property>
                    <property name="paramName">vchsts</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">11</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_10</property>
                    <property name="paramName">bratetype</property>
                    <property name="dataType">string</property>
                    <property name="position">12</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_9</property>
                    <property name="paramName">txdate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">13</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">CLINAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">CONTNO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">VCHNO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">LOANAC_NO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">PRDTNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">CURR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">BUS_SUM</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">BUS_BAL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">CORE_PRDT_NO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">FLOAT_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">RATE_FLOAT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">MARATE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">YARATE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">BEGINDATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">ENDDATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">GUAR_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">OTH_GUAR_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">NORM_BAL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">OVER_BAL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">PARTOVER_BAL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">FLOW_BAL</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">IN_DEBT_INT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">OUT_DEBT_INT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">AC_STS</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">OVERDUE_DAY</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">INDUSTRYTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">INDUSTRYTYPENAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">IS_PEASANT</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">IS_FARM</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">PAY_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">REPAY_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">PURPOSE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">RPT_FIVE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">LST_RPT_FIVE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">CERTTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">CERTNO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">RELATION</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">ISSELF</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">39</property>
                        <property name="name">FAMADDR</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">40</property>
                        <property name="name">MTEL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">41</property>
                        <property name="name">OCCURTYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">42</property>
                        <property name="name">MANAGE_OPERID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">43</property>
                        <property name="name">MANAGE_INSTCODE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">44</property>
                        <property name="name">BAL_INSTCODE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">45</property>
                        <property name="name">CUST_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">46</property>
                        <property name="name">REGIONNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">47</property>
                        <property name="name">FARM_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">48</property>
                        <property name="name">TX_DATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">49</property>
                        <property name="name">YWPZ</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">50</property>
                        <property name="name">IRATE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">51</property>
                        <property name="name">BASE_RATE_TYPE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">52</property>
                        <property name="name">RATE_POINT</property>
                        <property name="dataType">decimal</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">CLINAME</property>
                    <property name="nativeName">CLINAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">CONTNO</property>
                    <property name="nativeName">CONTNO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">VCHNO</property>
                    <property name="nativeName">VCHNO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">LOANAC_NO</property>
                    <property name="nativeName">LOANAC_NO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">PRDTNAME</property>
                    <property name="nativeName">PRDTNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">CURR</property>
                    <property name="nativeName">CURR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">BUS_SUM</property>
                    <property name="nativeName">BUS_SUM</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">BUS_BAL</property>
                    <property name="nativeName">BUS_BAL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">CORE_PRDT_NO</property>
                    <property name="nativeName">CORE_PRDT_NO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">FLOAT_TYPE</property>
                    <property name="nativeName">FLOAT_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">RATE_FLOAT</property>
                    <property name="nativeName">RATE_FLOAT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">MARATE</property>
                    <property name="nativeName">MARATE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">YARATE</property>
                    <property name="nativeName">YARATE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">BEGINDATE</property>
                    <property name="nativeName">BEGINDATE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">ENDDATE</property>
                    <property name="nativeName">ENDDATE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">GUAR_TYPE</property>
                    <property name="nativeName">GUAR_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">OTH_GUAR_TYPE</property>
                    <property name="nativeName">OTH_GUAR_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">NORM_BAL</property>
                    <property name="nativeName">NORM_BAL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">OVER_BAL</property>
                    <property name="nativeName">OVER_BAL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">PARTOVER_BAL</property>
                    <property name="nativeName">PARTOVER_BAL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">FLOW_BAL</property>
                    <property name="nativeName">FLOW_BAL</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">IN_DEBT_INT</property>
                    <property name="nativeName">IN_DEBT_INT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">OUT_DEBT_INT</property>
                    <property name="nativeName">OUT_DEBT_INT</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">AC_STS</property>
                    <property name="nativeName">AC_STS</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">OVERDUE_DAY</property>
                    <property name="nativeName">OVERDUE_DAY</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">INDUSTRYTYPE</property>
                    <property name="nativeName">INDUSTRYTYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">INDUSTRYTYPENAME</property>
                    <property name="nativeName">INDUSTRYTYPENAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">IS_PEASANT</property>
                    <property name="nativeName">IS_PEASANT</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">IS_FARM</property>
                    <property name="nativeName">IS_FARM</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">PAY_TYPE</property>
                    <property name="nativeName">PAY_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">REPAY_TYPE</property>
                    <property name="nativeName">REPAY_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">PURPOSE</property>
                    <property name="nativeName">PURPOSE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">RPT_FIVE</property>
                    <property name="nativeName">RPT_FIVE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">LST_RPT_FIVE</property>
                    <property name="nativeName">LST_RPT_FIVE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">CERTTYPE</property>
                    <property name="nativeName">CERTTYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">CERTNO</property>
                    <property name="nativeName">CERTNO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">RELATION</property>
                    <property name="nativeName">RELATION</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">ISSELF</property>
                    <property name="nativeName">ISSELF</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">39</property>
                    <property name="name">FAMADDR</property>
                    <property name="nativeName">FAMADDR</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">40</property>
                    <property name="name">MTEL</property>
                    <property name="nativeName">MTEL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">41</property>
                    <property name="name">OCCURTYPE</property>
                    <property name="nativeName">OCCURTYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">42</property>
                    <property name="name">MANAGE_OPERID</property>
                    <property name="nativeName">MANAGE_OPERID</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">43</property>
                    <property name="name">MANAGE_INSTCODE</property>
                    <property name="nativeName">MANAGE_INSTCODE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">44</property>
                    <property name="name">BAL_INSTCODE</property>
                    <property name="nativeName">BAL_INSTCODE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">45</property>
                    <property name="name">CUST_TYPE</property>
                    <property name="nativeName">CUST_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">46</property>
                    <property name="name">REGIONNAME</property>
                    <property name="nativeName">REGIONNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">47</property>
                    <property name="name">FARM_TYPE</property>
                    <property name="nativeName">FARM_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">48</property>
                    <property name="name">TX_DATE</property>
                    <property name="nativeName">TX_DATE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">49</property>
                    <property name="name">YWPZ</property>
                    <property name="nativeName">YWPZ</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">50</property>
                    <property name="name">IRATE</property>
                    <property name="nativeName">IRATE</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">51</property>
                    <property name="name">BASE_RATE_TYPE</property>
                    <property name="nativeName">BASE_RATE_TYPE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">52</property>
                    <property name="name">RATE_POINT</property>
                    <property name="nativeName">RATE_POINT</property>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[/*贷款台账*/
select t1.cliname,t1.contno,t1.vchno,t1.loanac_no,
TF_GET_PRDTNAME(t1.prdt_no,t1.bankid) as prdtname,
'人民币' as curr,
t1.bus_sum,
t1.bus_bal,
(select TITLE from ln_parm where bankid=t1.bankid and prdt_no=t1.CORE_PRDT_NO) as CORE_PRDT_NO,
decode(t1.float_type,'1','固定利率','2','浮动利率','未定义') as float_type,
nvl(t1.rate_float,0) as rate_float,
round((t1.arate/12*10),2) as Marate,
t1.arate as Yarate,
t1.begindate,t1.enddate,
decode(t1.guar_type,'10','抵押','20','质押','30','保证','50','信用','未定义') as guar_type,
decode(t4.oth_guar_type,'10','抵押','20','质押','30','保证','50','信用','未定义') as oth_guar_type,
nvl(t1.bus_bal,0)-nvl(t1.over_bal,0) as norm_bal,
case  when t1.ac_sts<>'3' and t1.tx_date>t1.enddate and t1.lo_date is not null
      then t1.over_bal else 0 end as over_bal,
case  when t1.ac_sts<>'3' and t1.tx_date<=t1.enddate and t1.lo_date is not null
      then t1.over_bal else 0 end as partover_bal,
case  when t1.ac_sts='3' then t1.bus_bal else 0 end as flow_bal,
(t1.IN_NORMAL_INTST+t1.IN_OVER_INTST) as in_debt_int,
(t1.OUT_NORMAL_INTST+t1.OUT_OVER_INTST+t1.DEBT_CMPD) as out_debt_int,
case 
    when t1.ac_sts='1' and t1.lo_date is null
    then '正常'
    when t1.ac_sts<>'3' and t1.tx_date>t1.enddate and t1.lo_date is not null
    then '逾期'
    when t1.ac_sts<>'3' and t1.tx_date<=t1.enddate and t1.lo_date is not null
    then '部分逾期'
    when t1.ac_sts='3'
    then '非应计'
    when t1.ac_sts='4'
    then '呆滞'
    when t1.ac_sts='5'
    then '呆账'
    when t1.ac_sts='6'
    then '核销'
    when t1.ac_sts='9'
    then '撤销'
    when t1.ac_sts='*'
    then '销户'
    end as ac_sts,
case when lo_date is null then 0 else to_date((select sys_date from com_sys_parm ),'YYYYMMDD')-to_date(lo_date,'YYYYMMDD') end  as overdue_day,
t1.INDUSTRYTYPE,
nvl((select decode(count(itemname),0,'',itemname||',') from dw_is_indsort where highlvl='root' start with itemno = (case when t1.INDUSTRYTYPE is not null then t1.INDUSTRYTYPE end ) connect by nocycle prior highlvl = sortno group by itemname)||(SELECT itemname FROM dw_is_indsort where itemno= t1.industrytype),'无记录') as industrytypename,
decode(t2.IS_PEASANT,'0','否','1','是','未定义') as IS_PEASANT,--是否农户
decode(t4.IS_FARM,'0','否','1','是','未定义') as IS_FARM,--是否农林牧渔
decode(t1.pay_type,'1','自主支付','2','受托支付','未定义') as pay_type,
decode(t1.repay_type,'1','等额本息','2','等额本金','3','利随本清','4','按期还息到期还本','5','净息还款','6','自定义还款计划','7','弹性还款','8','气球贷','未定义') as repay_type,
decode(t4.loan_purpose,'101','购进原材料','102','购进设备','103','加盟','104','开店','105','流动资金','106','营运周转','107','扩大经营','108','更换设备','109','进货','110','还款','111'
,'支付租金','112','装修','113','囤货备货','114','项目投资','115','垫付资金','116','兑店','117','开厂','118','开公司','119','新增代理','201','购房','202','购车','203','房屋装修'
,'204','助学贷款','205','旅游','206','耐用品消费','999','其它','未定义') as purpose,
decode(t1.rpt_five,'0102','正常','0202','关注','0302','次级','0402','可疑','0502','损失','') as rpt_five,
decode(t1.lst_rpt_five,'0102','正常','0202','关注','0302','次级','0402','可疑','0502','损失','') as lst_rpt_five,
case 
    WHEN t1.certtype ='110'
    THEN '身份证'
    WHEN t1.certtype ='111'
    THEN '临时身份证'
    WHEN t1.certtype ='112'
    THEN '学生证'
    WHEN t1.certtype ='121'
    THEN '解放军士兵证'
    WHEN t1.certtype ='122'
    THEN '军官证'
    WHEN t1.certtype ='123'
    THEN '解放军文职干部证'
    WHEN t1.certtype ='131'
    THEN '武警士兵证'
    WHEN t1.certtype ='132'
    THEN '警官证'
    WHEN t1.certtype ='133'
    THEN '武警文职干部证'
    WHEN t1.certtype ='140'
    THEN '户口簿'
    WHEN t1.certtype ='150'
    THEN '(外国)护照'
	WHEN t1.certtype ='151'
    THEN '外国人永久居留证'
	WHEN t1.certtype ='157'
    THEN '(中国)护照'
	WHEN t1.certtype ='159'
    THEN '居住证'
	WHEN t1.certtype ='160'
    THEN '港澳居民来往内地通行证'
	WHEN t1.certtype ='162'
    THEN '台湾居民来往大陆通行证'
	WHEN t1.certtype ='199'
    THEN '对私其他证件'
	WHEN t1.certtype ='210'
    THEN '营业执照'
	WHEN t1.certtype ='211'
    THEN '工会法人资格证书'
	WHEN t1.certtype ='212'
    THEN '社会团体法人登记证书'
	WHEN t1.certtype ='213'
    THEN '贷款卡号'
	WHEN t1.certtype ='214'
    THEN '境外企业证书'
	WHEN t1.certtype ='215'
    THEN '其他批文或证明'
	WHEN t1.certtype ='235'
    THEN '统一社会信息代码'
	WHEN t1.certtype ='230'
    THEN '组织机构代码证'
	WHEN t1.certtype ='231'
    THEN '预先核准通知书'
	WHEN t1.certtype ='232'
    THEN '政府批文'
	WHEN t1.certtype ='233'
    THEN '民办非企业登记证书'
	WHEN t1.certtype ='236'
    THEN '事业单位法人证书'
	WHEN t1.certtype ='245'
    THEN '机构信用代码证'
	WHEN t1.certtype ='999'
    THEN '其它'
  END as certtype,
t1.certno,
decode(nvl(t2.relation,'10'),'30','是','否') as relation,
decode(nvl(t2.is_self,'0'),'1','是','否') as isself,
t2.famaddr,t2.mtel,
decode(t1.occurtype,'1','新增','2','借新还旧','3','续贷','4','无还本续贷','5','并行','未定义') as occurtype,
TF_GET_USERNAME(t1.manage_operid,t1.bankid) as manage_operid,
TF_GET_INSTNAME(t1.manage_instcode,t1.bankid) as manage_instcode,
TF_GET_INSTNAME(t1.bal_instcode,t1.bankid) as bal_instcode,
decode(t5.cust_type,'1','个体工商户','2','小微企业主','3','个人其它经营','未定义') as cust_type, --核心客户类型
t2.REGIONNAME,
(select ITEMNAME from dw_nlmy where ITEMNO=t4.NLMY) as FARM_TYPE, --涉农贷款类型
t1.tx_date,'人民币贷款' as ywpz,
t1.IRATE， --基准利率
decode(t1.BASE_RATE_TYPE,'1','人行基准利率','2','LPR','未定义') as BASE_RATE_TYPE,--基准利率类型
t1.RATE_POINT --利率基点
from dp_vch_hst t1 
left join ind_base t2 on t1.cifid=t2.cifid and t1.bankid = t2.bankid
left join ac_businesscont t4 on t1.contno=t4.contno and t1.bankid=t4.bankid
left join app_main_info t5 on t4.applyno=t5.applyno and t4.bankid=t5.bankid
where   
t1.bankid = ?  and 
t1.begindate >= nvl(nullif(?,'@'),'********') and 
t1.begindate <= nvl(nullif(?,'@'),'********') and 
t1.enddate >= nvl(nullif(?,'@'),'********') and 
t1.enddate <= nvl(nullif(?,'@'),'********') and 
t1.loanac_no  like '%'||nvl(nullif(?,'@'),'')||'%' and
t1.cliname  like '%'||nvl(nullif(?,'@'),'')||'%' and
t1.manage_operid  like '%'||nvl(nullif(?,'@'),'')||'%' and
(t1.manage_instcode in (select instcode from cn_inst_level start with instcode=? connect by prior instcode=up_instcode  and bankid=t1.bankid)
or t1.bal_instcode in (select instcode from cn_inst_level start with instcode=? connect by prior instcode=up_instcode  and bankid=t1.bankid))
and t1.vch_sts like '%'||nvl(nullif(?,'@'),'')||'%'
and t1.BASE_RATE_TYPE like '%'||nvl(nullif(?,'@'),'')||'%'
and t1.tx_date=?
order by t1.begindate desc]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集1" id="162">
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">bankid</property>
                    <property name="dataType">string</property>
                    <property name="position">1</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">instcode</property>
                    <property name="dataType">string</property>
                    <property name="position">2</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">INSTCODE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">INSTCODE</property>
                    <property name="nativeName">INSTCODE</property>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[SELECT ('['
  ||t1.instcode
  ||']'
  ||t1.instname) instcode
FROM cn_inst_level t1
WHERE t1.bankid=?
AND t1.instcode=?]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集2" id="164">
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">begindate</property>
                    <property name="dataType">string</property>
                    <property name="position">1</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">begindate</property>
                    <property name="dataType">string</property>
                    <property name="position">2</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_3</property>
                    <property name="paramName">begindate</property>
                    <property name="dataType">string</property>
                    <property name="position">3</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_4</property>
                    <property name="paramName">beginmaxdate</property>
                    <property name="dataType">string</property>
                    <property name="position">4</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_5</property>
                    <property name="paramName">beginmaxdate</property>
                    <property name="dataType">string</property>
                    <property name="position">5</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_6</property>
                    <property name="paramName">beginmaxdate</property>
                    <property name="dataType">string</property>
                    <property name="position">6</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">BDATE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">BDATE</property>
                    <property name="nativeName">BDATE</property>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[SELECT (SUBSTR(?,0,4)
  ||'年'
  ||SUBSTR(?,5,2)
  ||'月'
  ||SUBSTR(?,7,2)
  ||'日')
  ||'--'
  ||(SUBSTR(?,0,4)
  ||'年'
  ||SUBSTR(?,5,2)
  ||'月'
  ||SUBSTR(?,7,2)
  ||'日') bdate
FROM dual]]></xml-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="4">
            <property name="fontFamily">sans-serif</property>
            <property name="fontSize">10pt</property>
        </style>
        <style name="crosstab-cell" id="5">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
        <style name="crosstab" id="6">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <page-footer>
                <text id="3">
                    <property name="contentType">html</property>
                    <text-property name="content"><![CDATA[<value-of>new Date()</value-of>]]></text-property>
                </text>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <table id="7">
            <property name="fontFamily">"宋体"</property>
            <property name="marginTop">2pt</property>
            <property name="marginLeft">2pt</property>
            <property name="marginBottom">2pt</property>
            <property name="marginRight">2pt</property>
            <property name="dataSet">数据集</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">CLINAME</property>
                    <text-property name="displayName">CLINAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CLINAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CERTNO</property>
                    <text-property name="displayName">CERTNO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CERTNO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CONTNO</property>
                    <text-property name="displayName">CONTNO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CONTNO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">VCHNO</property>
                    <text-property name="displayName">VCHNO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["VCHNO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BEGINDATE</property>
                    <text-property name="displayName">BEGINDATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BEGINDATE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">ENDDATE</property>
                    <text-property name="displayName">ENDDATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ENDDATE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BUS_SUM</property>
                    <text-property name="displayName">BUS_SUM</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BUS_SUM"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">REPAY_TYPE</property>
                    <text-property name="displayName">REPAY_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["REPAY_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">MANAGE_OPERID</property>
                    <text-property name="displayName">MANAGE_OPERID</text-property>
                    <expression name="expression" type="javascript">dataSetRow["MANAGE_OPERID"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">MANAGE_INSTCODE</property>
                    <text-property name="displayName">MANAGE_INSTCODE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["MANAGE_INSTCODE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">列绑定</property>
                    <expression name="expression" type="javascript">row[0]+1</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["BUS_SUM"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">PRDTNAME</property>
                    <text-property name="displayName">PRDTNAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PRDTNAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CURR</property>
                    <text-property name="displayName">CURR</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CURR"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BUS_BAL</property>
                    <text-property name="displayName">BUS_BAL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BUS_BAL"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">FLOAT_TYPE</property>
                    <text-property name="displayName">FLOAT_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FLOAT_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">RATE_FLOAT</property>
                    <text-property name="displayName">RATE_FLOAT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["RATE_FLOAT"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">YARATE</property>
                    <text-property name="displayName">YARATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["YARATE"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">MARATE</property>
                    <text-property name="displayName">MARATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["MARATE"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">GUAR_TYPE</property>
                    <text-property name="displayName">GUAR_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["GUAR_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OTH_GUAR_TYPE</property>
                    <text-property name="displayName">OTH_GUAR_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OTH_GUAR_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">NORM_BAL</property>
                    <text-property name="displayName">NORM_BAL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["NORM_BAL"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">OVER_BAL</property>
                    <text-property name="displayName">OVER_BAL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OVER_BAL"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">FLOW_BAL</property>
                    <text-property name="displayName">FLOW_BAL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FLOW_BAL"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">IN_DEBT_INT</property>
                    <text-property name="displayName">IN_DEBT_INT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["IN_DEBT_INT"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">OUT_DEBT_INT</property>
                    <text-property name="displayName">OUT_DEBT_INT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OUT_DEBT_INT"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">AC_STS</property>
                    <text-property name="displayName">AC_STS</text-property>
                    <expression name="expression" type="javascript">dataSetRow["AC_STS"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OVERDUE_DAY</property>
                    <text-property name="displayName">OVERDUE_DAY</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OVERDUE_DAY"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">INDUSTRYTYPE</property>
                    <text-property name="displayName">INDUSTRYTYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["INDUSTRYTYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">PAY_TYPE</property>
                    <text-property name="displayName">PAY_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PAY_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">PURPOSE</property>
                    <text-property name="displayName">PURPOSE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PURPOSE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">RPT_FIVE</property>
                    <text-property name="displayName">RPT_FIVE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["RPT_FIVE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">LST_RPT_FIVE</property>
                    <text-property name="displayName">LST_RPT_FIVE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["LST_RPT_FIVE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CERTTYPE</property>
                    <text-property name="displayName">CERTTYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CERTTYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">FAMADDR</property>
                    <text-property name="displayName">FAMADDR</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FAMADDR"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">MTEL</property>
                    <text-property name="displayName">MTEL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["MTEL"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OCCURTYPE</property>
                    <text-property name="displayName">OCCURTYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OCCURTYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">RELATION</property>
                    <text-property name="displayName">RELATION</text-property>
                    <expression name="expression" type="javascript">dataSetRow["RELATION"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">ISSELF</property>
                    <text-property name="displayName">ISSELF</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ISSELF"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CORE_PRDT_NO</property>
                    <text-property name="displayName">CORE_PRDT_NO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CORE_PRDT_NO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">LOANAC_NO</property>
                    <text-property name="displayName">LOANAC_NO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["LOANAC_NO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BAL_INSTCODE</property>
                    <text-property name="displayName">BAL_INSTCODE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BAL_INSTCODE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">INDUSTRYTYPENAME</property>
                    <text-property name="displayName">INDUSTRYTYPENAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["INDUSTRYTYPENAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">IS_FARM</property>
                    <text-property name="displayName">IS_FARM</text-property>
                    <expression name="expression" type="javascript">dataSetRow["IS_FARM"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">IS_PEASANT</property>
                    <text-property name="displayName">IS_PEASANT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["IS_PEASANT"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CUST_TYPE</property>
                    <text-property name="displayName">CUST_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CUST_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">REGIONNAME</property>
                    <text-property name="displayName">REGIONNAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["REGIONNAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">FARM_TYPE</property>
                    <text-property name="displayName">FARM_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["FARM_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">TX_DATE</property>
                    <text-property name="displayName">TX_DATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["TX_DATE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">聚合_1</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["BUS_SUM"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_2</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["BUS_SUM"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_3</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["BUS_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_4</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["BUS_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_5</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["NORM_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_6</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["OVER_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_7</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["PARTOVER_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_8</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["FLOW_BAL"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_9</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["IN_DEBT_INT"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_10</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["OUT_DEBT_INT"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">YWPZ</property>
                    <text-property name="displayName">YWPZ</text-property>
                    <expression name="expression" type="javascript">dataSetRow["YWPZ"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">PARTOVER_BAL</property>
                    <text-property name="displayName">PARTOVER_BAL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["PARTOVER_BAL"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">IRATE</property>
                    <text-property name="displayName">IRATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["IRATE"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">BASE_RATE_TYPE</property>
                    <text-property name="displayName">BASE_RATE_TYPE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BASE_RATE_TYPE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">RATE_POINT</property>
                    <text-property name="displayName">RATE_POINT</text-property>
                    <expression name="expression" type="javascript">dataSetRow["RATE_POINT"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <column id="134"/>
            <column id="22">
                <property name="width">1in</property>
            </column>
            <column id="74">
                <property name="width">2in</property>
            </column>
            <column id="62">
                <property name="width">2in</property>
            </column>
            <column id="717">
                <property name="width">2.3in</property>
            </column>
            <column id="201">
                <property name="width">1in</property>
            </column>
            <column id="208">
                <property name="width">1in</property>
            </column>
            <column id="44">
                <property name="width">1in</property>
            </column>
            <column id="239">
                <property name="width">1.3in</property>
            </column>
            <column id="233">
                <property name="width">1in</property>
            </column>
            <column id="227">
                <property name="width">1.3in</property>
            </column>
            <column id="707">
                <property name="width">2in</property>
            </column>
            <column id="221">
                <property name="width">1in</property>
            </column>
            <column id="969">
                <property name="width">1in</property>
            </column>
            <column id="961">
                <property name="width">1in</property>
            </column>
            <column id="215">
                <property name="width">1in</property>
            </column>
            <column id="977">
                <property name="width">1in</property>
            </column>
            <column id="38">
                <property name="width">1in</property>
            </column>
            <column id="250">
                <property name="width">1in</property>
            </column>
            <column id="117">
                <property name="width">1in</property>
            </column>
            <column id="324">
                <property name="width">1in</property>
            </column>
            <column id="330">
                <property name="width">1in</property>
            </column>
            <column id="336">
                <property name="width">1in</property>
            </column>
            <column id="342">
                <property name="width">1in</property>
            </column>
            <column id="348">
                <property name="width">1in</property>
            </column>
            <column id="727">
                <property name="width">1in</property>
            </column>
            <column id="354">
                <property name="width">1in</property>
            </column>
            <column id="360">
                <property name="width">1in</property>
            </column>
            <column id="366">
                <property name="width">1in</property>
            </column>
            <column id="372">
                <property name="width">1in</property>
            </column>
            <column id="378">
                <property name="width">1in</property>
            </column>
            <column id="384">
                <property name="width">1in</property>
            </column>
            <column id="734">
                <property name="width">1in</property>
            </column>
            <column id="390">
                <property name="width">3in</property>
            </column>
            <column id="741">
                <property name="width">1in</property>
            </column>
            <column id="771">
                <property name="width">1in</property>
            </column>
            <column id="765">
                <property name="width">1in</property>
            </column>
            <column id="759">
                <property name="width">1in</property>
            </column>
            <column id="753">
                <property name="width">1in</property>
            </column>
            <column id="747">
                <property name="width">1in</property>
            </column>
            <column id="782">
                <property name="width">1in</property>
            </column>
            <column id="396">
                <property name="width">1in</property>
            </column>
            <column id="402">
                <property name="width">1.3in</property>
            </column>
            <column id="408">
                <property name="width">1in</property>
            </column>
            <column id="414">
                <property name="width">1in</property>
            </column>
            <column id="270">
                <property name="width">1in</property>
            </column>
            <column id="318">
                <property name="width">1in</property>
            </column>
            <column id="312">
                <property name="width">1.5in</property>
            </column>
            <column id="790">
                <property name="width">1in</property>
            </column>
            <column id="306">
                <property name="width">1in</property>
            </column>
            <column id="827">
                <property name="width">1in</property>
            </column>
            <column id="821">
                <property name="width">1in</property>
            </column>
            <column id="815">
                <property name="width">1in</property>
            </column>
            <column id="809">
                <property name="width">1in</property>
            </column>
            <column id="803">
                <property name="width">1in</property>
            </column>
            <column id="797">
                <property name="width">1in</property>
            </column>
            <column id="845">
                <property name="width">1in</property>
            </column>
            <column id="839">
                <property name="width">1.5in</property>
            </column>
            <column id="264">
                <property name="width">1in</property>
            </column>
            <column id="105">
                <property name="width">1in</property>
            </column>
            <column id="93">
                <property name="width">2in</property>
            </column>
            <column id="895">
                <property name="width">2in</property>
            </column>
            <column id="889">
                <property name="width">1.3in</property>
            </column>
            <column id="883">
                <property name="width">1in</property>
            </column>
            <column id="877">
                <property name="width">1.3in</property>
            </column>
            <column id="871">
                <property name="width">1in</property>
            </column>
            <column id="865">
                <property name="width">1in</property>
            </column>
            <column id="859">
                <property name="width">1in</property>
            </column>
            <column id="853">
                <property name="width">1in</property>
            </column>
            <column id="921">
                <property name="width">1in</property>
            </column>
            <column id="915">
                <property name="width">2in</property>
            </column>
            <column id="909">
                <property name="width">1in</property>
            </column>
            <header>
                <row id="8">
                    <property name="whiteSpace">nowrap</property>
                    <cell id="129">
                        <property name="colSpan">72</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="23">
                            <property name="fontSize">18pt</property>
                            <property name="fontWeight">bold</property>
                            <text-property name="text">历史贷款台账</text-property>
                        </label>
                    </cell>
                </row>
                <row id="24">
                    <property name="whiteSpace">nowrap</property>
                    <cell id="130">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="163">
                            <property name="dataSet">数据集1</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">INSTCODE</property>
                                    <expression name="expression" type="javascript">"查询机构："+dataSetRow["INSTCODE"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">INSTCODE</property>
                        </data>
                    </cell>
                    <cell id="64">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="165">
                            <property name="dataSet">数据集2</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">BDATE</property>
                                    <expression name="expression" type="javascript">"查询日期范围："+dataSetRow["BDATE"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">BDATE</property>
                        </data>
                    </cell>
                    <cell id="713">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="197">
                        <property name="colSpan">54</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="101">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="926">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="891">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="885">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="879">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="873">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="867">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="861">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="855">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="849">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="917">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="911">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="925">
                            <text-property name="text">单位：元/‰</text-property>
                        </label>
                    </cell>
                </row>
                <row id="28">
                    <property name="backgroundColor">#EFF0F0</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="131">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="135">
                            <text-property name="text">序号</text-property>
                        </label>
                    </cell>
                    <cell id="31">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="76">
                            <text-property name="text">客户名称</text-property>
                        </label>
                    </cell>
                    <cell id="71">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="78">
                            <text-property name="text">合同编号</text-property>
                        </label>
                    </cell>
                    <cell id="59">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="79">
                            <text-property name="text">借据编号</text-property>
                        </label>
                    </cell>
                    <cell id="714">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="718">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款账号]]></text-property>
                        </text>
                    </cell>
                    <cell id="198">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="202">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[业务品种]]></text-property>
                        </text>
                    </cell>
                    <cell id="205">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="209">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[币种]]></text-property>
                        </text>
                    </cell>
                    <cell id="41">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="118">
                            <text-property name="text">借据金额</text-property>
                        </label>
                    </cell>
                    <cell id="236">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="240">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[借据金额(折人民币)]]></text-property>
                        </text>
                    </cell>
                    <cell id="230">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="241">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[借据余额]]></text-property>
                        </text>
                    </cell>
                    <cell id="224">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="242">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[借据余额(折人民币)]]></text-property>
                        </text>
                    </cell>
                    <cell id="704">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="708">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[核算产品类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="218">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="243">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[利率调整方式]]></text-property>
                        </text>
                    </cell>
                    <cell id="966">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="970">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[基准利率类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="958">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="962">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[基准利率]]></text-property>
                        </text>
                    </cell>
                    <cell id="212">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="244">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[利率浮动值]]></text-property>
                        </text>
                    </cell>
                    <cell id="974">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="978">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[利率基点]]></text-property>
                        </text>
                    </cell>
                    <cell id="35">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="119">
                            <text-property name="text">执行月利率</text-property>
                        </label>
                    </cell>
                    <cell id="247">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="251">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[执行年利率]]></text-property>
                        </text>
                    </cell>
                    <cell id="114">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="120">
                            <text-property name="text">放款日期</text-property>
                        </label>
                    </cell>
                    <cell id="321">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="415">
                            <text-property name="text">到期日期</text-property>
                        </label>
                    </cell>
                    <cell id="327">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="416">
                            <text-property name="text">主要担保方式</text-property>
                        </label>
                    </cell>
                    <cell id="333">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="417">
                            <text-property name="text">其他担保方式</text-property>
                        </label>
                    </cell>
                    <cell id="339">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="418">
                            <text-property name="text">正常余额</text-property>
                        </label>
                    </cell>
                    <cell id="345">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="419">
                            <text-property name="text">逾期余额</text-property>
                        </label>
                    </cell>
                    <cell id="724">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="728">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[部分逾期余额]]></text-property>
                        </text>
                    </cell>
                    <cell id="351">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="420">
                            <text-property name="text">非应计余额</text-property>
                        </label>
                    </cell>
                    <cell id="357">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="421">
                            <text-property name="text">表内欠息余额</text-property>
                        </label>
                    </cell>
                    <cell id="363">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="422">
                            <text-property name="text">表外欠息余额</text-property>
                        </label>
                    </cell>
                    <cell id="369">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="423">
                            <text-property name="text">账户状态</text-property>
                        </label>
                    </cell>
                    <cell id="375">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="424">
                            <text-property name="text">本金逾期天数</text-property>
                        </label>
                    </cell>
                    <cell id="381">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="425">
                            <text-property name="text">利息逾期天数</text-property>
                        </label>
                    </cell>
                    <cell id="731">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="735">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款投向码值]]></text-property>
                        </text>
                    </cell>
                    <cell id="387">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="426">
                            <text-property name="text">贷款投向</text-property>
                        </label>
                    </cell>
                    <cell id="738">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="772">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款行业分类（门类）]]></text-property>
                        </text>
                    </cell>
                    <cell id="768">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="773">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款行业分类（大类）]]></text-property>
                        </text>
                    </cell>
                    <cell id="762">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="774">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款行业分类（中类）]]></text-property>
                        </text>
                    </cell>
                    <cell id="756">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="775">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款行业分类（小类）]]></text-property>
                        </text>
                    </cell>
                    <cell id="750">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="776">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[贷款行业分类（小类）]]></text-property>
                        </text>
                    </cell>
                    <cell id="744">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="783">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[是否农户]]></text-property>
                        </text>
                    </cell>
                    <cell id="779">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="784">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[是否农林牧渔]]></text-property>
                        </text>
                    </cell>
                    <cell id="393">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="427">
                            <text-property name="text">支付方式</text-property>
                        </label>
                    </cell>
                    <cell id="399">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="428">
                            <text-property name="text">还款方式</text-property>
                        </label>
                    </cell>
                    <cell id="405">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="429">
                            <text-property name="text">用途</text-property>
                        </label>
                    </cell>
                    <cell id="411">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="430">
                            <text-property name="text">五级分类</text-property>
                        </label>
                    </cell>
                    <cell id="267">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="431">
                            <text-property name="text">上月五级分类</text-property>
                        </label>
                    </cell>
                    <cell id="315">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="432">
                            <text-property name="text">证件类型</text-property>
                        </label>
                    </cell>
                    <cell id="309">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="433">
                            <text-property name="text">证件号码</text-property>
                        </label>
                    </cell>
                    <cell id="787">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="791">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[中征码]]></text-property>
                        </text>
                    </cell>
                    <cell id="303">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="434">
                            <text-property name="text">是否本行股东</text-property>
                        </label>
                    </cell>
                    <cell id="824">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="828">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[客户评级结果]]></text-property>
                        </text>
                    </cell>
                    <cell id="818">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="829">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[国标客户规模]]></text-property>
                        </text>
                    </cell>
                    <cell id="812">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="830">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[本行客户规模]]></text-property>
                        </text>
                    </cell>
                    <cell id="806">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="831">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[企业法人注册地]]></text-property>
                        </text>
                    </cell>
                    <cell id="800">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="832">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[企业类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="794">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="833">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[经济控股类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="842">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="846">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[核心客户类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="836">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="847">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[行政区域]]></text-property>
                        </text>
                    </cell>
                    <cell id="261">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="439">
                            <text-property name="text">发生类型</text-property>
                        </label>
                    </cell>
                    <cell id="102">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="123">
                            <text-property name="text">经办人</text-property>
                        </label>
                    </cell>
                    <cell id="90">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="125">
                            <text-property name="text">经办机构</text-property>
                        </label>
                    </cell>
                    <cell id="892">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="896">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[记账机构]]></text-property>
                        </text>
                    </cell>
                    <cell id="886">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="897">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[数据日期【按日】]]></text-property>
                        </text>
                    </cell>
                    <cell id="880">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="898">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[业务品种分类]]></text-property>
                        </text>
                    </cell>
                    <cell id="874">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="899">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[涉农贷款类型]]></text-property>
                        </text>
                    </cell>
                    <cell id="868">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="900">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[国标行业分类码值]]></text-property>
                        </text>
                    </cell>
                    <cell id="862">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="901">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[国标行业分类]]></text-property>
                        </text>
                    </cell>
                    <cell id="856">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="902">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[文化产业分类码值]]></text-property>
                        </text>
                    </cell>
                    <cell id="850">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="903">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[文化产业分类名称]]></text-property>
                        </text>
                    </cell>
                    <cell id="918">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="922">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[是否本行员工]]></text-property>
                        </text>
                    </cell>
                    <cell id="912">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="923">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[居住地址]]></text-property>
                        </text>
                    </cell>
                    <cell id="906">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="924">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[手机号码]]></text-property>
                        </text>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="12">
                    <property name="textAlign">left</property>
                    <property name="whiteSpace">nowrap</property>
                    <list-property name="highlightRules">
                        <structure>
                            <property name="operator">eq</property>
                            <property name="backgroundColor">#DFE8F6</property>
                            <expression name="testExpr" type="javascript">row[0]%2</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">1</value>
                            </simple-property-list>
                        </structure>
                    </list-property>
                    <cell id="132">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="160">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">列绑定</property>
                        </data>
                    </cell>
                    <cell id="15">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="147">
                            <property name="textAlign">left</property>
                            <property name="resultSetColumn">CLINAME</property>
                        </data>
                    </cell>
                    <cell id="72">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="149">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">CONTNO</property>
                        </data>
                    </cell>
                    <cell id="60">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="150">
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">VCHNO</property>
                        </data>
                    </cell>
                    <cell id="715">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="719">
                            <property name="resultSetColumn">LOANAC_NO</property>
                        </data>
                    </cell>
                    <cell id="199">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="440">
                            <property name="resultSetColumn">PRDTNAME</property>
                        </data>
                    </cell>
                    <cell id="206">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="441">
                            <property name="resultSetColumn">CURR</property>
                        </data>
                    </cell>
                    <cell id="42">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="153">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">#,##0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">BUS_SUM</property>
                        </data>
                    </cell>
                    <cell id="237">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="720">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">BUS_SUM</property>
                        </data>
                    </cell>
                    <cell id="231">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="443">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">BUS_BAL</property>
                        </data>
                    </cell>
                    <cell id="225">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="721">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">BUS_BAL</property>
                        </data>
                    </cell>
                    <cell id="705">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="709">
                            <property name="resultSetColumn">CORE_PRDT_NO</property>
                        </data>
                    </cell>
                    <cell id="219">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="445">
                            <property name="resultSetColumn">FLOAT_TYPE</property>
                        </data>
                    </cell>
                    <cell id="967">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="971">
                            <property name="resultSetColumn">BASE_RATE_TYPE</property>
                        </data>
                    </cell>
                    <cell id="959">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="963">
                            <property name="resultSetColumn">IRATE</property>
                        </data>
                    </cell>
                    <cell id="213">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="446">
                            <property name="resultSetColumn">RATE_FLOAT</property>
                        </data>
                    </cell>
                    <cell id="975">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="979">
                            <property name="resultSetColumn">RATE_POINT</property>
                        </data>
                    </cell>
                    <cell id="36">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="448">
                            <property name="resultSetColumn">MARATE</property>
                        </data>
                    </cell>
                    <cell id="248">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="449">
                            <property name="resultSetColumn">YARATE</property>
                        </data>
                    </cell>
                    <cell id="115">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="450">
                            <property name="resultSetColumn">BEGINDATE</property>
                        </data>
                    </cell>
                    <cell id="322">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="451">
                            <property name="resultSetColumn">ENDDATE</property>
                        </data>
                    </cell>
                    <cell id="328">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="452">
                            <property name="resultSetColumn">GUAR_TYPE</property>
                        </data>
                    </cell>
                    <cell id="334">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="453">
                            <property name="resultSetColumn">OTH_GUAR_TYPE</property>
                        </data>
                    </cell>
                    <cell id="340">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="454">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">NORM_BAL</property>
                        </data>
                    </cell>
                    <cell id="346">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="455">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">OVER_BAL</property>
                        </data>
                    </cell>
                    <cell id="725">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="955">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">PARTOVER_BAL</property>
                        </data>
                    </cell>
                    <cell id="352">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="456">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">FLOW_BAL</property>
                        </data>
                    </cell>
                    <cell id="358">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="457">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">IN_DEBT_INT</property>
                        </data>
                    </cell>
                    <cell id="364">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="458">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">OUT_DEBT_INT</property>
                        </data>
                    </cell>
                    <cell id="370">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="459">
                            <property name="resultSetColumn">AC_STS</property>
                        </data>
                    </cell>
                    <cell id="376">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="460">
                            <property name="resultSetColumn">OVERDUE_DAY</property>
                        </data>
                    </cell>
                    <cell id="382">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="462">
                            <property name="resultSetColumn">OVERDUE_DAY</property>
                        </data>
                    </cell>
                    <cell id="732">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="933">
                            <property name="resultSetColumn">INDUSTRYTYPE</property>
                        </data>
                    </cell>
                    <cell id="388">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="934">
                            <property name="resultSetColumn">INDUSTRYTYPENAME</property>
                        </data>
                    </cell>
                    <cell id="739">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="769">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="763">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="757">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="751">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="745">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="936">
                            <property name="resultSetColumn">IS_PEASANT</property>
                        </data>
                    </cell>
                    <cell id="780">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="937">
                            <property name="resultSetColumn">IS_FARM</property>
                        </data>
                    </cell>
                    <cell id="394">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="463">
                            <property name="resultSetColumn">PAY_TYPE</property>
                        </data>
                    </cell>
                    <cell id="400">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="464">
                            <property name="resultSetColumn">REPAY_TYPE</property>
                        </data>
                    </cell>
                    <cell id="406">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="465">
                            <property name="resultSetColumn">PURPOSE</property>
                        </data>
                    </cell>
                    <cell id="412">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="466">
                            <property name="resultSetColumn">RPT_FIVE</property>
                        </data>
                    </cell>
                    <cell id="268">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="467">
                            <property name="resultSetColumn">LST_RPT_FIVE</property>
                        </data>
                    </cell>
                    <cell id="316">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="468">
                            <property name="resultSetColumn">CERTTYPE</property>
                        </data>
                    </cell>
                    <cell id="310">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="469">
                            <property name="resultSetColumn">CERTNO</property>
                        </data>
                    </cell>
                    <cell id="788">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="304">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="476">
                            <property name="resultSetColumn">RELATION</property>
                        </data>
                    </cell>
                    <cell id="825">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="819">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="813">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="807">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="801">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="795">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="843">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="938">
                            <property name="resultSetColumn">CUST_TYPE</property>
                        </data>
                    </cell>
                    <cell id="837">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="939">
                            <property name="resultSetColumn">REGIONNAME</property>
                        </data>
                    </cell>
                    <cell id="262">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="473">
                            <property name="resultSetColumn">OCCURTYPE</property>
                        </data>
                    </cell>
                    <cell id="103">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="474">
                            <property name="resultSetColumn">MANAGE_OPERID</property>
                        </data>
                    </cell>
                    <cell id="91">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="475">
                            <property name="resultSetColumn">MANAGE_INSTCODE</property>
                        </data>
                    </cell>
                    <cell id="893">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="932">
                            <property name="resultSetColumn">BAL_INSTCODE</property>
                        </data>
                    </cell>
                    <cell id="887">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="942">
                            <property name="resultSetColumn">TX_DATE</property>
                        </data>
                    </cell>
                    <cell id="881">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="954">
                            <property name="resultSetColumn">YWPZ</property>
                        </data>
                    </cell>
                    <cell id="875">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="940">
                            <property name="resultSetColumn">FARM_TYPE</property>
                        </data>
                    </cell>
                    <cell id="869">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="863">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="857">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="851">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="919">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="929">
                            <property name="resultSetColumn">ISSELF</property>
                        </data>
                    </cell>
                    <cell id="913">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="928">
                            <property name="resultSetColumn">FAMADDR</property>
                        </data>
                    </cell>
                    <cell id="907">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="927">
                            <property name="resultSetColumn">MTEL</property>
                        </data>
                    </cell>
                </row>
            </detail>
            <footer>
                <row id="16">
                    <property name="backgroundColor">#C0C0C0</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="133">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="19">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="73">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="61">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="716">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="200">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="207">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="43">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="944">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_1</property>
                        </data>
                    </cell>
                    <cell id="238">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="945">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_2</property>
                        </data>
                    </cell>
                    <cell id="232">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="946">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_3</property>
                        </data>
                    </cell>
                    <cell id="226">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="947">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_4</property>
                        </data>
                    </cell>
                    <cell id="706">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="220">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="968">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="960">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="214">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="976">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="37">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="249">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="116">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="323">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="329">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="335">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="341">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="948">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_5</property>
                        </data>
                    </cell>
                    <cell id="347">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="949">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_6</property>
                        </data>
                    </cell>
                    <cell id="726">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="950">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_7</property>
                        </data>
                    </cell>
                    <cell id="353">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="951">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_8</property>
                        </data>
                    </cell>
                    <cell id="359">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="952">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_9</property>
                        </data>
                    </cell>
                    <cell id="365">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="953">
                            <property name="textAlign">right</property>
                            <property name="resultSetColumn">聚合_10</property>
                        </data>
                    </cell>
                    <cell id="371">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="377">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="383">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="733">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="389">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="740">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="770">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="764">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="758">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="752">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="746">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="781">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="395">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="401">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="407">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="413">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="269">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="317">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="311">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="789">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="305">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="826">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="820">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="814">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="808">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="802">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="796">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="844">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="838">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="263">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="104">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="92">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="894">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="888">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="882">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="876">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="870">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="864">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="858">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="852">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="920">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="914">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="908">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                </row>
            </footer>
        </table>
    </body>
</report>

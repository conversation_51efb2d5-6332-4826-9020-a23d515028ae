<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.2.2.v201303011740 Build &lt;4.2.2.v20130301-1759></property>
    <list-property name="propertyBindings">
        <structure>
            <property name="name">queryText</property>
            <property name="id">442</property>
        </structure>
        <structure>
            <property name="name">queryTimeOut</property>
            <property name="id">442</property>
        </structure>
        <structure>
            <property name="name">rowFetchSize</property>
            <property name="id">442</property>
        </structure>
    </list-property>
    <property name="units">in</property>
    <method name="initialize"><![CDATA[var cliname = params["opername"].value;
params["opername"].value=decodeURI(cliname);]]></method>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="layoutPreference">auto layout</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="bankid" id="148">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">100000</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="instcode" id="160">
            <property name="valueType">static</property>
            <property name="isRequired">true</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">88016</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="opername" id="698">
            <property name="valueType">static</property>
            <property name="isRequired">true</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="concealValue">false</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="year" id="1243">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">2017</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="txdate" id="1306">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="数据源" id="147">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledContentBidiFormatStr</name>
                </ex-property>
            </list-property>
            <property name="odaDriverClass">oracle.jdbc.driver.OracleDriver</property>
            <property name="odaURL">****************************************</property>
            <property name="odaUser">xtnew</property>
            <encrypted-property name="odaPassword" encryptionID="base64">eHRuZXc=</encrypted-property>
            <property name="odaJndiName">java:comp/env/jdbc/bankreport</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集1" id="185">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints"/>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">bankid</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">1</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">instcode</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">2</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">INSTCODE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">INSTCODE</property>
                    <property name="nativeName">INSTCODE</property>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[SELECT ('['
  ||t1.instcode
  ||']'
  ||t1.instname) instcode
FROM cn_inst_level t1
WHERE t1.bankid=?
AND t1.instcode=?]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集" id="442">
            <list-property name="columnHints"/>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">1</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">2</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_3</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">3</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_4</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">4</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_5</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">5</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_6</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">6</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_7</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">7</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_8</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">8</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_9</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">9</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_10</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">10</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_11</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">11</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_12</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">12</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_13</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">13</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_14</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">14</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_15</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">15</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_16</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">16</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_17</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">17</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_18</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">18</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_19</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">19</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_20</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">20</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_21</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">21</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_22</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">22</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_23</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">23</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_24</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">24</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_25</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">25</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_26</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">26</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_27</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">27</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_28</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">28</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_29</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">29</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_30</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">30</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_31</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">31</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_32</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">32</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_33</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">33</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_34</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">34</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_35</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">35</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_36</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">36</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_37</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">37</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_38</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">38</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_39</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">39</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_40</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">40</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_41</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">41</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_42</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">42</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_43</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">43</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_44</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">44</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_45</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">45</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_46</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">46</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_47</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">47</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_48</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">48</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_49</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">49</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_50</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">50</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_51</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">51</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_52</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">52</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_53</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">53</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_54</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">54</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_55</property>
                    <property name="paramName">year</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">55</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_56</property>
                    <property name="paramName">bankid</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">56</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_57</property>
                    <property name="paramName">opername</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">57</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">INSTNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">XDJL</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">WANTED_BATCH</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">SUM(A.JBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">SUM(A.JJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">SUM(A.JBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">SUM(A.JJE)-----金额--2月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">SUM(A.FBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">SUM(A.FJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">SUM(A.FBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">SUM(A.FJE)-----金额--3月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">SUM(A.MBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">SUM(A.MJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">SUM(A.MBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">SUM(A.MJE)-----金额--4月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">SUM(A.ABSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">SUM(A.AJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">SUM(A.AABS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">SUM(A.AJE)-----金额--5月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">SUM(A.MMBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">SUM(A.MMJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">SUM(A.MMBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">SUM(A.MMJE)-----金额--6月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">SUM(A.JNBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">SUM(A.JNJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">SUM(A.JNBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">SUM(A.JNJE)-----金额--7月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">28</property>
                        <property name="name">SUM(A.JLBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">29</property>
                        <property name="name">SUM(A.JLJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">30</property>
                        <property name="name">SUM(A.JLBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">31</property>
                        <property name="name">SUM(A.JLJE)-----金额,--8月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">32</property>
                        <property name="name">SUM(A.AGBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">33</property>
                        <property name="name">SUM(A.AGJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">34</property>
                        <property name="name">SUM(A.AGBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">35</property>
                        <property name="name">SUM(A.AGJE)-----金额--9月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">36</property>
                        <property name="name">SUM(A.SBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">37</property>
                        <property name="name">SUM(A.SJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">38</property>
                        <property name="name">SUM(A.SBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">39</property>
                        <property name="name">SUM(A.SJE)-----金额--10月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">40</property>
                        <property name="name">SUM(A.OBSNEW)-----笔数,</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">41</property>
                        <property name="name">SUM(A.OJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">42</property>
                        <property name="name">SUM(A.OOBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">43</property>
                        <property name="name">SUM(A.OJE)-----金额--11月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">44</property>
                        <property name="name">SUM(A.NBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">45</property>
                        <property name="name">SUM(A.NJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">46</property>
                        <property name="name">SUM(A.NBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">47</property>
                        <property name="name">SUM(A.NJE)-----金额--12月---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">48</property>
                        <property name="name">SUM(A.DBSNEW)-----笔数,</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">49</property>
                        <property name="name">SUM(A.DJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">50</property>
                        <property name="name">SUM(A.DBS)-----笔数,</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">51</property>
                        <property name="name">SUM(A.DJE)-----金额--合计---首次贷款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">52</property>
                        <property name="name">SUM(A.ALLBSNEW)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">53</property>
                        <property name="name">SUM(A.ALLJENEW)-----金额---放款</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">54</property>
                        <property name="name">SUM(A.ALLBS)-----笔数</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">55</property>
                        <property name="name">SUM(A.ALLJE)-----金额--合计---占比</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">56</property>
                        <property name="name">ZBBS</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">57</property>
                        <property name="name">ZBJE</property>
                        <property name="dataType">decimal</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">INSTNAME</property>
                    <property name="nativeName">INSTNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">XDJL</property>
                    <property name="nativeName">XDJL</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">WANTED_BATCH</property>
                    <property name="nativeName">WANTED_BATCH</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">SUM(A.JBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.JBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">SUM(A.JJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.JJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">SUM(A.JBS)-----笔数</property>
                    <property name="nativeName">SUM(A.JBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">SUM(A.JJE)-----金额--2月---首次贷款</property>
                    <property name="nativeName">SUM(A.JJE)-----金额--2月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">SUM(A.FBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.FBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">SUM(A.FJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.FJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">SUM(A.FBS)-----笔数</property>
                    <property name="nativeName">SUM(A.FBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">SUM(A.FJE)-----金额--3月---首次贷款</property>
                    <property name="nativeName">SUM(A.FJE)-----金额--3月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">SUM(A.MBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.MBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">SUM(A.MJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.MJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">SUM(A.MBS)-----笔数</property>
                    <property name="nativeName">SUM(A.MBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">SUM(A.MJE)-----金额--4月---首次贷款</property>
                    <property name="nativeName">SUM(A.MJE)-----金额--4月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">SUM(A.ABSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.ABSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">SUM(A.AJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.AJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">SUM(A.AABS)-----笔数</property>
                    <property name="nativeName">SUM(A.AABS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">SUM(A.AJE)-----金额--5月---首次贷款</property>
                    <property name="nativeName">SUM(A.AJE)-----金额--5月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">SUM(A.MMBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.MMBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">SUM(A.MMJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.MMJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">SUM(A.MMBS)-----笔数</property>
                    <property name="nativeName">SUM(A.MMBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">SUM(A.MMJE)-----金额--6月---首次贷款</property>
                    <property name="nativeName">SUM(A.MMJE)-----金额--6月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">SUM(A.JNBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.JNBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">SUM(A.JNJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.JNJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">SUM(A.JNBS)-----笔数</property>
                    <property name="nativeName">SUM(A.JNBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">SUM(A.JNJE)-----金额--7月---首次贷款</property>
                    <property name="nativeName">SUM(A.JNJE)-----金额--7月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">28</property>
                    <property name="name">SUM(A.JLBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.JLBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">29</property>
                    <property name="name">SUM(A.JLJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.JLJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">30</property>
                    <property name="name">SUM(A.JLBS)-----笔数</property>
                    <property name="nativeName">SUM(A.JLBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">31</property>
                    <property name="name">SUM(A.JLJE)-----金额,--8月---首次贷款</property>
                    <property name="nativeName">SUM(A.JLJE)-----金额,--8月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">32</property>
                    <property name="name">SUM(A.AGBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.AGBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">33</property>
                    <property name="name">SUM(A.AGJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.AGJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">34</property>
                    <property name="name">SUM(A.AGBS)-----笔数</property>
                    <property name="nativeName">SUM(A.AGBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">35</property>
                    <property name="name">SUM(A.AGJE)-----金额--9月---首次贷款</property>
                    <property name="nativeName">SUM(A.AGJE)-----金额--9月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">36</property>
                    <property name="name">SUM(A.SBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.SBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">37</property>
                    <property name="name">SUM(A.SJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.SJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">38</property>
                    <property name="name">SUM(A.SBS)-----笔数</property>
                    <property name="nativeName">SUM(A.SBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">39</property>
                    <property name="name">SUM(A.SJE)-----金额--10月---首次贷款</property>
                    <property name="nativeName">SUM(A.SJE)-----金额--10月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">40</property>
                    <property name="name">SUM(A.OBSNEW)-----笔数,</property>
                    <property name="nativeName">SUM(A.OBSNEW)-----笔数,</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">41</property>
                    <property name="name">SUM(A.OJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.OJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">42</property>
                    <property name="name">SUM(A.OOBS)-----笔数</property>
                    <property name="nativeName">SUM(A.OOBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">43</property>
                    <property name="name">SUM(A.OJE)-----金额--11月---首次贷款</property>
                    <property name="nativeName">SUM(A.OJE)-----金额--11月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">44</property>
                    <property name="name">SUM(A.NBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.NBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">45</property>
                    <property name="name">SUM(A.NJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.NJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">46</property>
                    <property name="name">SUM(A.NBS)-----笔数</property>
                    <property name="nativeName">SUM(A.NBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">47</property>
                    <property name="name">SUM(A.NJE)-----金额--12月---首次贷款</property>
                    <property name="nativeName">SUM(A.NJE)-----金额--12月---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">48</property>
                    <property name="name">SUM(A.DBSNEW)-----笔数,</property>
                    <property name="nativeName">SUM(A.DBSNEW)-----笔数,</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">49</property>
                    <property name="name">SUM(A.DJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.DJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">50</property>
                    <property name="name">SUM(A.DBS)-----笔数,</property>
                    <property name="nativeName">SUM(A.DBS)-----笔数,</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">51</property>
                    <property name="name">SUM(A.DJE)-----金额--合计---首次贷款</property>
                    <property name="nativeName">SUM(A.DJE)-----金额--合计---首次贷款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">52</property>
                    <property name="name">SUM(A.ALLBSNEW)-----笔数</property>
                    <property name="nativeName">SUM(A.ALLBSNEW)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">53</property>
                    <property name="name">SUM(A.ALLJENEW)-----金额---放款</property>
                    <property name="nativeName">SUM(A.ALLJENEW)-----金额---放款</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">54</property>
                    <property name="name">SUM(A.ALLBS)-----笔数</property>
                    <property name="nativeName">SUM(A.ALLBS)-----笔数</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">55</property>
                    <property name="name">SUM(A.ALLJE)-----金额--合计---占比</property>
                    <property name="nativeName">SUM(A.ALLJE)-----金额--合计---占比</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">56</property>
                    <property name="name">ZBBS</property>
                    <property name="nativeName">ZBBS</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">57</property>
                    <property name="name">ZBJE</property>
                    <property name="nativeName">ZBJE</property>
                    <property name="dataType">decimal</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[--信贷经理每月放款统计表
select 
a.instname --支中心
,a.xdjl --信贷经理
,a.wanted_batch --批次
--1月
---首次贷款
, sum(a.jbsnew)-----笔数
, sum(a.jjenew)-----金额
---放款
, sum(a.jbs)-----笔数
, sum(a.jje)-----金额
--2月
---首次贷款
, sum(a.fbsnew)-----笔数
, sum(a.fjenew)-----金额
---放款
, sum(a.fbs)-----笔数
, sum(a.fje)-----金额
--3月
---首次贷款
,sum(a.mbsnew)-----笔数
,sum(a.mjenew)-----金额
---放款
, sum(a.mbs)-----笔数
, sum(a.mje)-----金额
--4月
---首次贷款
, sum(a.absnew)-----笔数
, sum(a.ajenew)-----金额
---放款
, sum(a.aabs)-----笔数
, sum(a.aje)-----金额
--5月
---首次贷款
, sum(a.mmbsnew)-----笔数
, sum(a.mmjenew)-----金额
---放款
, sum(a.mmbs)-----笔数
, sum(a.mmje)-----金额
--6月
---首次贷款
, sum(a.jnbsnew)-----笔数
, sum(a.jnjenew)-----金额
---放款
, sum(a.jnbs)-----笔数
, sum(a.jnje)-----金额
--7月
---首次贷款
, sum(a.jlbsnew)-----笔数
, sum(a.jljenew)-----金额
---放款
, sum(a.jlbs)-----笔数
, sum(a.jlje)-----金额,
--8月
---首次贷款
, sum(a.agbsnew)-----笔数
, sum(a.agjenew)-----金额
---放款
, sum(a.agbs)-----笔数
, sum(a.agje)-----金额
--9月
---首次贷款
, sum(a.sbsnew)-----笔数
, sum(a.sjenew)-----金额
---放款
, sum(a.sbs)-----笔数
, sum(a.sje)-----金额
--10月
---首次贷款
, sum(a.obsnew)-----笔数,
, sum(a.ojenew)-----金额
---放款
, sum(a.oobs)-----笔数
, sum(a.oje)-----金额
--11月
---首次贷款
, sum(a.nbsnew)-----笔数
, sum(a.njenew)-----金额
---放款
, sum(a.nbs)-----笔数
, sum(a.nje)-----金额
--12月
---首次贷款
, sum(a.dbsnew)-----笔数,
, sum(a.djenew)-----金额
---放款
, sum(a.dbs)-----笔数,
, sum(a.dje)-----金额
--合计
---首次贷款
, sum(a.allbsnew)-----笔数
, sum(a.alljenew)-----金额
---放款
, sum(a.allbs)-----笔数
, sum(a.allje)-----金额
--合计
---占比
,round(sum(a.sumbs)*100,4) as zbbs-----笔数
,round(sum(a.sumje)*100,4) as zbje-----金额
 from 
(
select 
--rownum--序号
b.instname--所属支中心
--,substr(a.begindate,0,6)
--,a.OCCURTYPE
,(case when d.cliname is not null then d.cliname||'['||a.bal_operid||']' else '未知'||'['||a.bal_operid||']' end ) as xdjl--信贷经理
,d.wanted_batch--批次
--1月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'01%' then count(*) else 0 end ) as jbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'01%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'01%' then count(*) else 0 end ) as jbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'01%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jje-----金额
--2月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'02%' then count(*) else 0 end ) as fbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'02%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as fjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'02%' then count(*) else 0 end ) as fbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'02%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as fje-----金额
--3月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'03%' then count(*) else 0 end ) as mbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'03%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as mjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'03%' then count(*) else 0 end ) as mbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'03%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as mje-----金额
--4月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'04%' then count(*) else 0 end ) as absnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'04%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as ajenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'04%'  then count(*) else 0 end ) as aabs-----笔数
,(case when substr(a.begindate,0,6) like ?||'04%'  then nvl(sum(a.BUS_SUM),0) else 0 end ) as aje-----金额
--5月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'05%'  then count(*) else 0 end ) as mmbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'05%'then nvl(sum(a.BUS_SUM),0) else 0 end ) as mmjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'05%' then count(*) else 0 end ) as mmbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'05%'then nvl(sum(a.BUS_SUM),0) else 0 end ) as mmje-----金额
--6月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'06%' then count(*) else 0 end ) as jnbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'06%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jnjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'06%' then count(*) else 0 end ) as jnbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'06%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jnje-----金额
--7月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'07%' then count(*) else 0 end ) as jlbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'07%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jljenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'07%' then count(*) else 0 end ) as jlbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'07%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as jlje-----金额
--8月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'08%' then count(*) else 0 end ) as agbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'08%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as agjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'08%' then count(*) else 0 end ) as agbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'08%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as agje-----金额
--9月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'09%'  then count(*) else 0 end ) as sbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'09%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as sjenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'09%' then count(*) else 0 end ) as sbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'09%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as sje-----金额
--10月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'10%' then count(*) else 0 end ) as obsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'10%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as ojenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'10%' then count(*) else 0 end ) as oobs-----笔数
,(case when substr(a.begindate,0,6) like ?||'10%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as oje-----金额
--11月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'11%' then count(*) else 0 end ) as nbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'11%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as njenew-----金额
---放款
,(case when substr(a.begindate,0,6)  like ?||'11%' then count(*) else 0 end ) as nbs-----笔数
,(case when substr(a.begindate,0,6)  like ?||'11%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as nje-----金额
--12月
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6)  like ?||'12%' then count(*) else 0 end ) as dbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'12%'  then nvl(sum(a.BUS_SUM),0) else 0 end ) as djenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'12%'  then count(*) else 0 end ) as dbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'12%'  then nvl(sum(a.BUS_SUM),0) else 0 end ) as dje-----金额
--合计
---首次贷款
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'%'  then count(*) else 0 end ) as allbsnew-----笔数
,(case when a.OCCURTYPE = 1 and substr(a.begindate,0,6) like ?||'%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as alljenew-----金额
---放款
,(case when substr(a.begindate,0,6) like ?||'%' then count(*) else 0 end ) as allbs-----笔数
,(case when substr(a.begindate,0,6) like ?||'%' then nvl(sum(a.BUS_SUM),0) else 0 end ) as allje-----金额
--合计
---占比
,count(*)/(select count(*) from ac_businessvch where substr(begindate,0,6) like ?|| '%' and a.bankid = bankid) as sumbs-----笔数
,sum(a.BUS_SUM)/(select sum(BUS_SUM) from ac_businessvch where substr(begindate,0,6) like ?|| '%' and a.bankid = bankid) as sumje-----金额
 
 from ac_businessvch a
 left join cn_basicinfo b on a.bankid = b.bankid and a.bal_instcode = b.instcode
-- left join cn_user c on a.bankid = c.bankid and a.bal_operid = c.operid
 left join manpower_info d on a.bankid = d.bankid and a.bal_operid = d.JOB_NO
 where 
 substr(a.begindate,0,6) like ?||'%' and
 a.bankid = ?
 group by b.instname,d.cliname,d.wanted_batch,a.OCCURTYPE,a.bal_operid,substr(a.begindate,0,6),a.bankid order by  substr(a.begindate,0,6)
 ) a 
 where 
 a.xdjl like '%'||nvl(nullif(?,'@'),'')||'%'
 group by instname,xdjl,wanted_batch
]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>OPERNAME</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>OPERNAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>OPERNAME</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>CLINAME</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>64</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>CLINAME</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>CLINAME</design:label>
            <design:formattingHints>
              <design:displaySize>64</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>UPANDDOWN_STREAM</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>200</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>UPANDDOWN_STREAM</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>UPANDDOWN_STREAM</design:label>
            <design:formattingHints>
              <design:displaySize>200</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>MTEL</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>32</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>Nullable</design:nullability>
            <design:uiHints>
              <design:displayName>MTEL</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>MTEL</design:label>
            <design:formattingHints>
              <design:displaySize>32</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集2" id="219">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">SYS_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SYS_DATE</text-property>
                    <text-property name="heading">SYS_DATE</text-property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">SYS_DATE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">SYS_DATE</property>
                    <property name="nativeName">SYS_DATE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select sys_date from com_sys_parm]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SYS_DATE</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>8</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>NotNullable</design:nullability>
            <design:uiHints>
              <design:displayName>SYS_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>SYS_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>8</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="4">
            <property name="fontFamily">sans-serif</property>
            <property name="fontSize">10pt</property>
        </style>
        <style name="crosstab-cell" id="5">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
        <style name="crosstab" id="6">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <page-footer>
                <text id="3">
                    <property name="contentType">html</property>
                    <text-property name="content"><![CDATA[<value-of>new Date()</value-of>]]></text-property>
                </text>
            </page-footer>
        </simple-master-page>
    </page-setup>
    <body>
        <table id="7">
            <property name="marginTop">2pt</property>
            <property name="marginLeft">2pt</property>
            <property name="marginBottom">2pt</property>
            <property name="marginRight">2pt</property>
            <property name="width">58in</property>
            <property name="dataSet">数据集</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">INSTNAME</property>
                    <text-property name="displayName">INSTNAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["INSTNAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">XDJL</property>
                    <text-property name="displayName">XDJL</text-property>
                    <expression name="expression" type="javascript">dataSetRow["XDJL"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">WANTED_BATCH</property>
                    <text-property name="displayName">WANTED_BATCH</text-property>
                    <expression name="expression" type="javascript">dataSetRow["WANTED_BATCH"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.JBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.JJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.JBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JJE)-----金额--2月---首次贷款</property>
                    <text-property name="displayName">SUM(A.JJE)-----金额--2月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JJE)-----金额--2月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.FBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.FBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.FBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.FJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.FJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.FJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.FBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.FBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.FBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.FJE)-----金额--3月---首次贷款</property>
                    <text-property name="displayName">SUM(A.FJE)-----金额--3月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.FJE)-----金额--3月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.MBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.MJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.MBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MJE)-----金额--4月---首次贷款</property>
                    <text-property name="displayName">SUM(A.MJE)-----金额--4月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MJE)-----金额--4月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.ABSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.ABSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.ABSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.AJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AABS)-----笔数</property>
                    <text-property name="displayName">SUM(A.AABS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AABS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AJE)-----金额--5月---首次贷款</property>
                    <text-property name="displayName">SUM(A.AJE)-----金额--5月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AJE)-----金额--5月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MMBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.MMBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MMBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MMJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.MMJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MMJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MMBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.MMBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MMBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.MMJE)-----金额--6月---首次贷款</property>
                    <text-property name="displayName">SUM(A.MMJE)-----金额--6月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.MMJE)-----金额--6月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JNBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.JNBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JNBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JNJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.JNJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JNJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JNBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.JNBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JNBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JNJE)-----金额--7月---首次贷款</property>
                    <text-property name="displayName">SUM(A.JNJE)-----金额--7月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JNJE)-----金额--7月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JLBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.JLBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JLBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JLJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.JLJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JLJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JLBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.JLBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JLBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.JLJE)-----金额,--8月---首次贷款</property>
                    <text-property name="displayName">SUM(A.JLJE)-----金额,--8月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.JLJE)-----金额,--8月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AGBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.AGBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AGBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AGJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.AGJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AGJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AGBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.AGBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AGBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.AGJE)-----金额--9月---首次贷款</property>
                    <text-property name="displayName">SUM(A.AGJE)-----金额--9月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.AGJE)-----金额--9月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.SBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.SBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.SBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.SJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.SJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.SJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.SBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.SBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.SBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.SJE)-----金额--10月---首次贷款</property>
                    <text-property name="displayName">SUM(A.SJE)-----金额--10月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.SJE)-----金额--10月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.OBSNEW)-----笔数,</property>
                    <text-property name="displayName">SUM(A.OBSNEW)-----笔数,</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.OBSNEW)-----笔数,"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.OJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.OJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.OJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.OOBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.OOBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.OOBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.OJE)-----金额--11月---首次贷款</property>
                    <text-property name="displayName">SUM(A.OJE)-----金额--11月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.OJE)-----金额--11月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.NBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.NBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.NBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.NJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.NJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.NJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.NBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.NBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.NBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.NJE)-----金额--12月---首次贷款</property>
                    <text-property name="displayName">SUM(A.NJE)-----金额--12月---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.NJE)-----金额--12月---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.DBSNEW)-----笔数,</property>
                    <text-property name="displayName">SUM(A.DBSNEW)-----笔数,</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.DBSNEW)-----笔数,"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.DJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.DJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.DJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.DBS)-----笔数,</property>
                    <text-property name="displayName">SUM(A.DBS)-----笔数,</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.DBS)-----笔数,"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.DJE)-----金额--合计---首次贷款</property>
                    <text-property name="displayName">SUM(A.DJE)-----金额--合计---首次贷款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.DJE)-----金额--合计---首次贷款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.ALLBSNEW)-----笔数</property>
                    <text-property name="displayName">SUM(A.ALLBSNEW)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.ALLBSNEW)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.ALLJENEW)-----金额---放款</property>
                    <text-property name="displayName">SUM(A.ALLJENEW)-----金额---放款</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.ALLJENEW)-----金额---放款"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.ALLBS)-----笔数</property>
                    <text-property name="displayName">SUM(A.ALLBS)-----笔数</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.ALLBS)-----笔数"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">SUM(A.ALLJE)-----金额--合计---占比</property>
                    <text-property name="displayName">SUM(A.ALLJE)-----金额--合计---占比</text-property>
                    <expression name="expression" type="javascript">dataSetRow["SUM(A.ALLJE)-----金额--合计---占比"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">ZBBS</property>
                    <text-property name="displayName">ZBBS</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ZBBS"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">ZBJE</property>
                    <text-property name="displayName">ZBJE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["ZBJE"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">TX</property>
                    <expression name="expression" type="javascript">row[0]+1</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["ZBBS"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">聚合_1</property>
                    <property name="dataType">float</property>
                    <property name="aggregateFunction">SUM</property>
                    <list-property name="arguments">
                        <structure>
                            <property name="name">Expression</property>
                            <expression name="value" type="javascript">row["SUM(A.ALLBS)-----笔数"]</expression>
                        </structure>
                    </list-property>
                    <property name="allowExport">true</property>
                </structure>
            </list-property>
            <list-property name="sort">
                <structure>
                    <expression name="key" type="javascript">row["INSTNAME"]</expression>
                    <property name="direction">asc</property>
                    <property name="strength">-1</property>
                </structure>
                <structure>
                    <expression name="key" type="javascript">row["OPERNAME"]</expression>
                    <property name="direction">asc</property>
                    <property name="strength">-1</property>
                </structure>
                <structure>
                    <expression name="key" type="javascript">row["VCHNO"]</expression>
                    <property name="direction">asc</property>
                    <property name="strength">-1</property>
                </structure>
            </list-property>
            <column id="313">
                <property name="width">1in</property>
            </column>
            <column id="709">
                <property name="width">1in</property>
            </column>
            <column id="361">
                <property name="width">1.2in</property>
            </column>
            <column id="419">
                <property name="width">1.2in</property>
            </column>
            <column id="353">
                <property name="width">1.2in</property>
            </column>
            <column id="740">
                <property name="width">1.2in</property>
            </column>
            <column id="748">
                <property name="width">1.2in</property>
            </column>
            <column id="732">
                <property name="width">1.2in</property>
            </column>
            <column id="762">
                <property name="width">1.2in</property>
            </column>
            <column id="770">
                <property name="width">1.2in</property>
            </column>
            <column id="778">
                <property name="width">1.2in</property>
            </column>
            <column id="786">
                <property name="width">1.2in</property>
            </column>
            <column id="801">
                <property name="width">1.2in</property>
            </column>
            <column id="809">
                <property name="width">1.2in</property>
            </column>
            <column id="817">
                <property name="width">1.2in</property>
            </column>
            <column id="825">
                <property name="width">1.2in</property>
            </column>
            <column id="836">
                <property name="width">1.2in</property>
            </column>
            <column id="844">
                <property name="width">1.2in</property>
            </column>
            <column id="852">
                <property name="width">1.2in</property>
            </column>
            <column id="860">
                <property name="width">1.2in</property>
            </column>
            <column id="871">
                <property name="width">1.2in</property>
            </column>
            <column id="879">
                <property name="width">1.2in</property>
            </column>
            <column id="887">
                <property name="width">1.2in</property>
            </column>
            <column id="895">
                <property name="width">1.2in</property>
            </column>
            <column id="904">
                <property name="width">1.2in</property>
            </column>
            <column id="912">
                <property name="width">1.2in</property>
            </column>
            <column id="920">
                <property name="width">1.2in</property>
            </column>
            <column id="928">
                <property name="width">1.2in</property>
            </column>
            <column id="937">
                <property name="width">1.2in</property>
            </column>
            <column id="945">
                <property name="width">1.2in</property>
            </column>
            <column id="953">
                <property name="width">1.2in</property>
            </column>
            <column id="961">
                <property name="width">1.2in</property>
            </column>
            <column id="970">
                <property name="width">1.2in</property>
            </column>
            <column id="978">
                <property name="width">1.2in</property>
            </column>
            <column id="986">
                <property name="width">1.2in</property>
            </column>
            <column id="994">
                <property name="width">1.2in</property>
            </column>
            <column id="1003">
                <property name="width">1.2in</property>
            </column>
            <column id="1011">
                <property name="width">1.2in</property>
            </column>
            <column id="1019">
                <property name="width">1.2in</property>
            </column>
            <column id="1027">
                <property name="width">1.2in</property>
            </column>
            <column id="1036">
                <property name="width">1.2in</property>
            </column>
            <column id="1044">
                <property name="width">1.2in</property>
            </column>
            <column id="1052">
                <property name="width">1.2in</property>
            </column>
            <column id="1060">
                <property name="width">1.2in</property>
            </column>
            <column id="1069">
                <property name="width">1.2in</property>
            </column>
            <column id="1077">
                <property name="width">1.2in</property>
            </column>
            <column id="1085">
                <property name="width">1.2in</property>
            </column>
            <column id="1093">
                <property name="width">1.2in</property>
            </column>
            <column id="1110">
                <property name="width">1.2in</property>
            </column>
            <column id="1118">
                <property name="width">1.2in</property>
            </column>
            <column id="1126">
                <property name="width">1.2in</property>
            </column>
            <column id="1134">
                <property name="width">1.2in</property>
            </column>
            <column id="1143">
                <property name="width">1.2in</property>
            </column>
            <column id="1151">
                <property name="width">1.2in</property>
            </column>
            <column id="1159">
                <property name="width">1.2in</property>
            </column>
            <column id="1167">
                <property name="width">1.2in</property>
            </column>
            <column id="1176">
                <property name="width">1.2in</property>
            </column>
            <column id="439">
                <property name="width">1in</property>
            </column>
            <header>
                <row id="8">
                    <cell id="9">
                        <property name="colSpan">58</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="127">
                            <property name="fontSize">18pt</property>
                            <property name="fontWeight">bold</property>
                            <text-property name="text">客户类型 按信贷经理放款统计（月）</text-property>
                        </label>
                    </cell>
                </row>
                <row id="109">
                    <cell id="110">
                        <property name="colSpan">3</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="186">
                            <property name="textAlign">center</property>
                            <property name="dataSet">数据集1</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">INSTCODE</property>
                                    <expression name="expression" type="javascript">"查询机构："+dataSetRow["INSTCODE"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">INSTCODE</property>
                        </data>
                    </cell>
                    <cell id="426">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="221">
                            <property name="dataSet">数据集2</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">SYS_DATE</property>
                                    <expression name="expression" type="javascript">"当前日期:"+dataSetRow["SYS_DATE"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">SYS_DATE</property>
                        </data>
                    </cell>
                    <cell id="734">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="742">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="726">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="756">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="764">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="772">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="780">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="795">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="803">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="811">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="819">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="830">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="838">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="846">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="854">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="865">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="873">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="881">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="889">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="898">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="906">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="914">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="922">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="931">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="939">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="947">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="955">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="964">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="972">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="980">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="988">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="997">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1005">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1013">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1021">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1030">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1038">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1046">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1054">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1063">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1071">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1079">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1087">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1104">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1112">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1120">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1128">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1137">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1145">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1153">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1161">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1170">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="429">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                </row>
                <row id="718">
                    <property name="backgroundColor">#EFF0F0</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="719">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">3</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <property name="verticalAlign">middle</property>
                        <label id="362">
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">序号</text-property>
                        </label>
                    </cell>
                    <cell id="720">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">3</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <property name="verticalAlign">middle</property>
                        <text id="710">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[所属支中心]]></text-property>
                        </text>
                    </cell>
                    <cell id="721">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">3</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <property name="verticalAlign">middle</property>
                        <label id="364">
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">信贷经理</text-property>
                        </label>
                    </cell>
                    <cell id="722">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">3</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <property name="verticalAlign">middle</property>
                        <label id="420">
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">批次</text-property>
                        </label>
                    </cell>
                    <cell id="723">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="749">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[1月]]></text-property>
                        </text>
                    </cell>
                    <cell id="757">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="787">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[2月]]></text-property>
                        </text>
                    </cell>
                    <cell id="796">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="826">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[3月]]></text-property>
                        </text>
                    </cell>
                    <cell id="831">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="861">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[4月]]></text-property>
                        </text>
                    </cell>
                    <cell id="866">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="896">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[5月]]></text-property>
                        </text>
                    </cell>
                    <cell id="899">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="929">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[6月]]></text-property>
                        </text>
                    </cell>
                    <cell id="932">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="962">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[7月]]></text-property>
                        </text>
                    </cell>
                    <cell id="965">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="995">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[8月]]></text-property>
                        </text>
                    </cell>
                    <cell id="998">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1028">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[9月]]></text-property>
                        </text>
                    </cell>
                    <cell id="1031">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1061">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[10月]]></text-property>
                        </text>
                    </cell>
                    <cell id="1064">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1094">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[11月]]></text-property>
                        </text>
                    </cell>
                    <cell id="1105">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1135">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[12月]]></text-property>
                        </text>
                    </cell>
                    <cell id="1138">
                        <property name="colSpan">4</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1302">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[合计]]></text-property>
                        </text>
                    </cell>
                    <cell id="1171">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1177">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[合计]]></text-property>
                        </text>
                    </cell>
                </row>
                <row id="711">
                    <property name="backgroundColor">#EFF0F0</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="716">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="750">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="744">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="751">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="758">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="788">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="774">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="789">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="797">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="827">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="813">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="828">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="832">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="862">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="848">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="863">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="867">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1179">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="883">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1188">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="900">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1180">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="916">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1189">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="933">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1181">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="949">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1190">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="966">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1182">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="982">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1191">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="999">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1183">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1015">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1192">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1032">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1184">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1048">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1193">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1065">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1185">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1081">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1194">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1106">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1186">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1122">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1195">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1139">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1187">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[首次贷款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1155">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1196">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[放款]]></text-property>
                        </text>
                    </cell>
                    <cell id="1172">
                        <property name="colSpan">2</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1178">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[占比]]></text-property>
                        </text>
                    </cell>
                </row>
                <row id="75">
                    <property name="backgroundColor">#EFF0F0</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="349">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="365">
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">笔数</text-property>
                        </label>
                    </cell>
                    <cell id="737">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="752">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="745">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="753">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="729">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="754">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="759">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="790">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="767">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="792">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="775">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="791">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="783">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="793">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="798">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1197">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="806">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1220">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="814">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1198">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="822">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1221">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="833">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1199">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="841">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1222">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="849">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1200">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="857">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1223">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="868">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1201">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="876">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1224">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="884">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1202">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="892">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1225">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="901">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1203">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="909">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1226">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="917">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1204">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="925">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1227">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="934">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1205">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="942">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1228">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="950">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1206">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="958">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1229">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="967">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1207">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="975">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1230">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="983">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1208">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="991">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1231">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1000">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1209">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1008">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1232">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1016">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1210">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1024">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1233">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1033">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1211">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1041">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1234">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1049">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1212">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1057">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1235">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1066">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1213">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1074">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1236">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1082">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1214">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1090">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1237">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1107">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1215">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1115">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1238">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1123">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1216">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1131">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1239">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1140">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1217">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1148">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1240">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1156">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1218">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="1164">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1241">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                    <cell id="1173">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1219">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[笔数]]></text-property>
                        </text>
                    </cell>
                    <cell id="430">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1242">
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[金额]]></text-property>
                        </text>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="12">
                    <property name="textAlign">center</property>
                    <property name="whiteSpace">nowrap</property>
                    <list-property name="highlightRules">
                        <structure>
                            <property name="operator">eq</property>
                            <property name="backgroundColor">#DFE8F6</property>
                            <expression name="testExpr" type="javascript">row[0]%2</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">1</value>
                            </simple-property-list>
                        </structure>
                        <structure>
                            <property name="operator">eq</property>
                            <property name="color">#FF0080</property>
                            <expression name="testExpr" type="javascript">row["OVER_BAL"]</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">0</value>
                            </simple-property-list>
                        </structure>
                    </list-property>
                    <cell id="310">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="154">
                            <property name="fontSize">10pt</property>
                            <property name="marginRight">0pt</property>
                            <property name="textAlign">center</property>
                            <property name="resultSetColumn">TX</property>
                        </data>
                    </cell>
                    <cell id="707">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1244">
                            <property name="resultSetColumn">INSTNAME</property>
                        </data>
                    </cell>
                    <cell id="358">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1245">
                            <property name="resultSetColumn">XDJL</property>
                        </data>
                    </cell>
                    <cell id="417">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1246">
                            <property name="resultSetColumn">WANTED_BATCH</property>
                        </data>
                    </cell>
                    <cell id="350">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1247">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="738">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1248">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="746">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1249">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="730">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1250">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JJE)-----金额--2月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="760">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1251">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.FBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="768">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1252">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.FJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="776">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1253">
                            <structure name="numberFormat">
                                <property name="category">Unformatted</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.FBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="784">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1254">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.FJE)-----金额--3月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="799">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1255">
                            <property name="resultSetColumn">SUM(A.MBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="807">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1256">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.MJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="815">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1257">
                            <property name="resultSetColumn">SUM(A.MBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="823">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1258">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.MJE)-----金额--4月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="834">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1259">
                            <property name="resultSetColumn">SUM(A.ABSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="842">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1260">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.AJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="850">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1261">
                            <property name="resultSetColumn">SUM(A.AABS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="858">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1262">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.AJE)-----金额--5月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="869">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1263">
                            <property name="resultSetColumn">SUM(A.MMBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="877">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1264">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.MMJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="885">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1265">
                            <property name="resultSetColumn">SUM(A.MMBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="893">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1266">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.MMJE)-----金额--6月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="902">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1267">
                            <property name="resultSetColumn">SUM(A.JNBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="910">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1268">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JNJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="918">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1269">
                            <property name="resultSetColumn">SUM(A.JNBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="926">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1270">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JNJE)-----金额--7月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="935">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1271">
                            <property name="resultSetColumn">SUM(A.JLBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="943">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1272">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JLJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="951">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1273">
                            <property name="resultSetColumn">SUM(A.JLBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="959">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1274">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.JLJE)-----金额,--8月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="968">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1276">
                            <property name="resultSetColumn">SUM(A.AGBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="976">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1275">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.AGJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="984">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1277">
                            <property name="resultSetColumn">SUM(A.AGBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="992">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1278">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.AGJE)-----金额--9月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="1001">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1279">
                            <property name="resultSetColumn">SUM(A.SBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1009">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1280">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.SJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="1017">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1281">
                            <property name="resultSetColumn">SUM(A.SBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1025">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1282">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.SJE)-----金额--10月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="1034">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1283">
                            <property name="resultSetColumn">SUM(A.OBSNEW)-----笔数,</property>
                        </data>
                    </cell>
                    <cell id="1042">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1284">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.OJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="1050">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1285">
                            <property name="resultSetColumn">SUM(A.OOBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1058">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1286">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.OJE)-----金额--11月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="1067">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1287">
                            <property name="resultSetColumn">SUM(A.NBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1075">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1288">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.NJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="1083">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1289">
                            <property name="resultSetColumn">SUM(A.NBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1091">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1290">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.NJE)-----金额--12月---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="1108">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1291">
                            <property name="resultSetColumn">SUM(A.DBSNEW)-----笔数,</property>
                        </data>
                    </cell>
                    <cell id="1116">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1292">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.DJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="1124">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1293">
                            <property name="resultSetColumn">SUM(A.DBS)-----笔数,</property>
                        </data>
                    </cell>
                    <cell id="1132">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1294">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.DJE)-----金额--合计---首次贷款</property>
                        </data>
                    </cell>
                    <cell id="1141">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1295">
                            <property name="resultSetColumn">SUM(A.ALLBSNEW)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1149">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1296">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.ALLJENEW)-----金额---放款</property>
                        </data>
                    </cell>
                    <cell id="1157">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1297">
                            <property name="resultSetColumn">SUM(A.ALLBS)-----笔数</property>
                        </data>
                    </cell>
                    <cell id="1165">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1298">
                            <structure name="numberFormat">
                                <property name="category">Currency</property>
                                <property name="pattern">###0.00{RoundingMode=HALF_UP}</property>
                            </structure>
                            <property name="resultSetColumn">SUM(A.ALLJE)-----金额--合计---占比</property>
                        </data>
                    </cell>
                    <cell id="1174">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1300">
                            <property name="resultSetColumn">ZBBS</property>
                        </data>
                    </cell>
                    <cell id="431">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1301">
                            <property name="resultSetColumn">ZBJE</property>
                        </data>
                    </cell>
                </row>
            </detail>
            <footer>
                <row id="16">
                    <property name="backgroundColor">#808080</property>
                    <property name="textAlign">center</property>
                    <property name="whiteSpace">nowrap</property>
                    <cell id="312">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <text id="1303">
                            <property name="textAlign">center</property>
                            <property name="contentType">auto</property>
                            <text-property name="content"><![CDATA[总计]]></text-property>
                        </text>
                    </cell>
                    <cell id="708">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="360">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="418">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="352">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="739">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="747">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="731">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="761">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="769">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="777">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="785">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="800">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="808">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="816">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="824">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="835">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="843">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="851">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="859">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="870">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="878">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="886">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="894">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="903">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="911">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="919">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="927">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="936">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="944">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="952">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="960">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="969">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="977">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="985">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="993">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1002">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1010">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1018">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1026">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1035">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1043">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1051">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1059">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1068">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1076">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1084">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1092">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1109">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1117">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1125">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1133">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1142">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1150">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1158">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="1305">
                            <property name="resultSetColumn">聚合_1</property>
                        </data>
                    </cell>
                    <cell id="1166">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="1175">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="438">
                        <property name="borderBottomColor">#C0C0C0</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C0C0C0</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C0C0C0</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C0C0C0</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                </row>
            </footer>
        </table>
    </body>
</report>

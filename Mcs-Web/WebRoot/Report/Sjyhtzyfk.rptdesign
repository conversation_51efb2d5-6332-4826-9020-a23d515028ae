<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.2.2.v201303011740 Build &lt;4.2.2.v20130301-1759></property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="layoutPreference">auto layout</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="bankid" id="316">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="mindate" id="317">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="maxdate" id="383">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="manageinstcode" id="384">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="数据源" id="147">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledContentBidiFormatStr</name>
                </ex-property>
            </list-property>
            <property name="odaDriverClass">oracle.jdbc.driver.OracleDriver</property>
            <property name="odaURL">jdbc:oracle:thin:@************:1521/orcl</property>
            <property name="odaUser">xtmcs</property>
            <encrypted-property name="odaPassword" encryptionID="base64">eHRtY3M=</encrypted-property>
            <property name="odaJndiName">java:comp/env/jdbc/bankreport</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集2" id="219">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">SYS_DATE</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">SYS_DATE</text-property>
                    <text-property name="heading">SYS_DATE</text-property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">SYS_DATE</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">SYS_DATE</property>
                    <property name="nativeName">SYS_DATE</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select sys_date from com_sys_parm]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>SYS_DATE</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>8</design:precision>
            <design:scale>0</design:scale>
            <design:nullability>NotNullable</design:nullability>
            <design:uiHints>
              <design:displayName>SYS_DATE</design:displayName>
            </design:uiHints>
          </design:attributes>
          <design:usageHints>
            <design:label>SYS_DATE</design:label>
            <design:formattingHints>
              <design:displaySize>8</design:displaySize>
            </design:formattingHints>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="数据集" id="315">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints"/>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">bankid</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">1</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">mindate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">2</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_3</property>
                    <property name="paramName">maxdate</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">3</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_4</property>
                    <property name="paramName">manageinstcode</property>
                    <property name="nativeName"></property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">0</property>
                    <property name="position">4</property>
                    <property name="isOptional">false</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">INSTNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">OPERNAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">CLINAME</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">CERTNO</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">OCCURDATE</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">BUS_SUM</property>
                        <property name="dataType">decimal</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">LOANAC_NO</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">数据源</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">INSTNAME</property>
                    <property name="nativeName">INSTNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">OPERNAME</property>
                    <property name="nativeName">OPERNAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">CLINAME</property>
                    <property name="nativeName">CLINAME</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">CERTNO</property>
                    <property name="nativeName">CERTNO</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">OCCURDATE</property>
                    <property name="nativeName">OCCURDATE</property>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">BUS_SUM</property>
                    <property name="nativeName">BUS_SUM</property>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">LOANAC_NO</property>
                    <property name="nativeName">LOANAC_NO</property>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[select
(select instname from cn_inst_level where instcode = a.manage_instcode ) as instname,
(select opername from cn_user where operid = a.manage_operid) as opername,
a.cliname,a.certno,d.occurdate,a.bus_sum,a.loanac_no from 
ac_businessvch a,ac_businesscont b,app_phone_info c,app_main_info d
where a.bankid = b.bankid and a.contno = b.contno
and b.applyno = c.applyno and b.bankid = c.bankid
and b.applyno = d.applyno and b.bankid = d.bankid
and a.vch_sts != '20'
and a.account_type = '1'
and a.bankid=?
and d.occurdate >= nvl(nullif(?,'@'),'********') 
and d.occurdate <= nvl(nullif(?,'@'),'********') 
and a.manage_instcode in (select instcode from cn_inst_level start with instcode=? connect by prior instcode=up_instcode  and bankid=a.bankid)
]]></xml-property>
        </oda-data-set>
    </data-sets>
    <styles>
        <style name="report" id="4">
            <property name="fontFamily">sans-serif</property>
            <property name="fontSize">10pt</property>
        </style>
        <style name="crosstab-cell" id="5">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
        <style name="crosstab" id="6">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2"/>
    </page-setup>
    <body>
        <table id="7">
            <property name="whiteSpace">nowrap</property>
            <property name="width">15in</property>
            <property name="dataSet">数据集</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">INSTNAME</property>
                    <text-property name="displayName">INSTNAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["INSTNAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OPERNAME</property>
                    <text-property name="displayName">OPERNAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OPERNAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CLINAME</property>
                    <text-property name="displayName">CLINAME</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CLINAME"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">CERTNO</property>
                    <text-property name="displayName">CERTNO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["CERTNO"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">OCCURDATE</property>
                    <text-property name="displayName">OCCURDATE</text-property>
                    <expression name="expression" type="javascript">dataSetRow["OCCURDATE"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">BUS_SUM</property>
                    <text-property name="displayName">BUS_SUM</text-property>
                    <expression name="expression" type="javascript">dataSetRow["BUS_SUM"]</expression>
                    <property name="dataType">decimal</property>
                </structure>
                <structure>
                    <property name="name">LOANAC_NO</property>
                    <text-property name="displayName">LOANAC_NO</text-property>
                    <expression name="expression" type="javascript">dataSetRow["LOANAC_NO"]</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <column id="45">
                <property name="width">1in</property>
            </column>
            <column id="46">
                <property name="width">1.2in</property>
            </column>
            <column id="356">
                <property name="width">1.2in</property>
            </column>
            <column id="362">
                <property name="width">1.65in</property>
            </column>
            <column id="47">
                <property name="width">2.2in</property>
            </column>
            <column id="48">
                <property name="width">2.808333333333333in</property>
            </column>
            <column id="52">
                <property name="width">4.133333333333334in</property>
            </column>
            <header>
                <row id="8">
                    <cell id="9">
                        <property name="colSpan">7</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="127">
                            <property name="fontSize">18pt</property>
                            <property name="fontWeight">bold</property>
                            <text-property name="text">手机银行台账</text-property>
                        </label>
                    </cell>
                </row>
                <row id="323">
                    <cell id="324">
                        <property name="colSpan">6</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="375">
                            <property name="dataSet">数据集2</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">SYS_DATE</property>
                                    <text-property name="displayName">SYS_DATE</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["SYS_DATE"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">SYS_DATE</property>
                        </data>
                    </cell>
                    <cell id="331">
                        <property name="colSpan">1</property>
                        <property name="rowSpan">1</property>
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="145">
                            <text-property name="text"></text-property>
                        </label>
                    </cell>
                </row>
                <row id="55">
                    <property name="backgroundColor">#EFF0F0</property>
                    <cell id="57">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="129">
                            <text-property name="text">机构名称</text-property>
                        </label>
                    </cell>
                    <cell id="58">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="130">
                            <text-property name="text">客户名称</text-property>
                        </label>
                    </cell>
                    <cell id="353">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="363">
                            <text-property name="text">证件号</text-property>
                        </label>
                    </cell>
                    <cell id="359">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="364">
                            <text-property name="text">申请日期</text-property>
                        </label>
                    </cell>
                    <cell id="59">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="131">
                            <text-property name="text">负责信贷经理</text-property>
                        </label>
                    </cell>
                    <cell id="60">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="132">
                            <text-property name="text">放款金额</text-property>
                        </label>
                    </cell>
                    <cell id="64">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="136">
                            <text-property name="text">贷款账号</text-property>
                        </label>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="20">
                    <list-property name="highlightRules">
                        <structure>
                            <property name="operator">eq</property>
                            <property name="backgroundColor">#DFE8F6</property>
                            <expression name="testExpr" type="javascript">row.__rownum%2</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">1</value>
                            </simple-property-list>
                        </structure>
                    </list-property>
                    <cell id="22">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="376">
                            <property name="resultSetColumn">INSTNAME</property>
                        </data>
                    </cell>
                    <cell id="23">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="377">
                            <property name="resultSetColumn">CLINAME</property>
                        </data>
                    </cell>
                    <cell id="354">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="378">
                            <property name="resultSetColumn">CERTNO</property>
                        </data>
                    </cell>
                    <cell id="360">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="385">
                            <property name="resultSetColumn">OCCURDATE</property>
                        </data>
                    </cell>
                    <cell id="24">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="380">
                            <property name="resultSetColumn">OPERNAME</property>
                        </data>
                    </cell>
                    <cell id="25">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="381">
                            <property name="resultSetColumn">BUS_SUM</property>
                        </data>
                    </cell>
                    <cell id="29">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="382">
                            <property name="resultSetColumn">LOANAC_NO</property>
                        </data>
                    </cell>
                </row>
            </detail>
            <footer>
                <row id="32">
                    <property name="backgroundColor">#808080</property>
                    <cell id="34">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="35">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="355">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="361">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="36">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="37">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                    <cell id="41">
                        <property name="borderBottomColor">#C6C6C6</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftColor">#C6C6C6</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightColor">#C6C6C6</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopColor">#C6C6C6</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                    </cell>
                </row>
            </footer>
        </table>
    </body>
</report>

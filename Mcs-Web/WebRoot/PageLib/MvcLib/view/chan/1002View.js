

    Ext.define('mvc.view.chan.1002View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"selType":"checkboxmodel","allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"channelName","text":"渠道业务名称"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"applyno","text":"申请号"},
                                { xtype:'gridcolumn',"width":110,"dataIndex":"iptBrno","text":"登记机构"},
                                { xtype:'tfoptcolumn',"width":100,"dataIndex":"channelType","optId":"7013_200807","text":"渠道业务类型"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"masterName","text":"负责人"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"phasename","text":"当前阶段"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"opername","text":"当前阶段经办人"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"iptUsr","text":"登记人"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"iptDate","text":"登记日期"},
                                { xtype:'tfoptcolumn',"width":100,"dataIndex":"flag","optId":"7013_new1073","text":"负责人为本公司客户"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"overDate"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,            
            items:[
                                { xtype:'textfield',id:'tf_applyno',"width":200,"labelWidth":60,"maxLength":32,"name":"search_applyno","fieldLabel":"申请编号"},
                                { xtype:'textfield',id:'tf_channel_name',"width":200,"labelWidth":60,"maxLength":32,"name":"search_channel_name","fieldLabel":"业务名称"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_sarch',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_res',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,            
            items:[
                                { xtype:'label',"cls":"label_title","text":"待处理业务"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_cat_detail',"iconCls":"icon_document--arrow","text":"业务详情"},
                                { xtype:'button',id:'tf_submit',"iconCls":"icon_hand-point-090","disabled":true,"text":"签署提交意见"}
            ]

}
        ]

},
                { xtype:'panel',"layout":"fit","height":"40%","split":true,"collapsible":true,"collapsed":false,"region":"south",        
        items:[
                        { xtype:'grid',dataSource:'flow_grid_store',id:'flow_grid',"border":0,"forceFit":true,"autoLoad":false,"allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'gridcolumn',"width":170,"minWidth":170,"maxWidth":170,"hidden":true,"dataIndex":"phaseno","text":"流程编号"},
                                { xtype:'gridcolumn',"width":170,"minWidth":170,"maxWidth":170,"dataIndex":"phasename","text":"流程阶段"},
                                { xtype:'gridcolumn',"width":170,"minWidth":170,"maxWidth":170,"hidden":true,"dataIndex":"phasechoice","text":"阶段动作"},
                                { xtype:'templatecolumn',"width":170,"text":"阶段时间","tpl":"<tpl if=\"enddate!=(null)\"><div></font><font style='color:green'>{begindate}--{enddate}</font></div><tpl else><div></font><font style='color:green'>{begindate}--</font></div></tpl>"},
                                { xtype:'templatecolumn',"text":"审批信息","tpl":"<tpl if=\"enddate!=(null)\"><div><font style='color:#42426F;'>阶段经办人：</font><font style='color:#42426F;font-weight:bold;'>{opername}</font></div><tpl else><div style='color:blue;'>阶段中</div></tpl>"},
                                { xtype:'gridcolumn',"width":170,"minWidth":170,"maxWidth":170,"dataIndex":"phaseoptions","text":"审批意见"},
                                { xtype:'gridcolumn',"width":80,"hideable":true,"hidden":true,"dataIndex":"opername","text":"阶段经办人"},
                                { xtype:'gridcolumn',"width":160,"hideable":true,"hidden":true,"dataIndex":"begindate","text":"阶段开始时间"},
                                { xtype:'gridcolumn',"width":160,"hideable":true,"hidden":true,"dataIndex":"enddate","text":"阶段结束时间"},
                                { xtype:'gridcolumn',"width":160,"hideable":true,"hidden":true,"dataIndex":"phaseoptions","text":"阶段意见"},
                                { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"phaseaction","text":"下一阶段经办人编号"},
                                { xtype:'gridcolumn',"width":100,"minWidth":100,"maxWidth":100,"dataIndex":"operidName","text":"下一阶段经办人"},
                                { xtype:'templatecolumn',"width":100,"minWidth":100,"maxWidth":100,"text":"阶段类型","tpl":"<tpl if=\"phasechoice=='nextphase'\"><div style='color:green;'>正常提交</div ></tpl><tpl if=\"phasechoice=='rollback'\"><div style='color:blue;'>退回补充材料</div ></tpl><tpl if=\"phasechoice=='deny'\"><div style='color:#FF3300;'>否决</div ></tpl><tpl if=\"phasechoice=='agree'\"><div style='color:#FFCC00;'>放款</div ></tpl>"},
                                { xtype:'tfoptcolumn',"width":80,"minWidth":80,"maxWidth":80,"hideable":true,"hidden":true,"dataIndex":"isrtn","optId":"7013_290001_#","text":"退回标志"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.chan.1002View.win_detail',{ extend:'Ext.window.Window', alias:'win_detail'
    ,id:'win_detail',"layout":"fit","width":"80%","height":"80%","modal":true,"constrain":true,"resizable":false,"title":"业务详情",    
    items:[
                { xtype:'panel',"layout":"fit",        
        items:[
                        { xtype:'form',id:'form_detail_info',"layout":"column","border":0,"bodyStyle":"background-color:#DFE8F6;","autoScroll":true,            
            items:[
                                { xtype:'fieldset',"margin":"30px 5px 5px 90px","columnWidth":0.9,"bodyStyle":"background-color:#DFE8F6;","title":"<渠道部分>",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,"bodyStyle":"background-color:#DFE8F6;","autoScroll":false,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_detail_channelType',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"channelType","optId":"7013_200807","fieldLabel":"类型"},
                                                { xtype:'textfield',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"channelName","fieldLabel":"名称"},
                                                { xtype:'textfield',id:'tf_tax_id',"columnWidth":0.4,"labelWidth":100,"maxLength":32,"hidden":true,"readOnly":true,"name":"taxId","fieldLabel":"税号"},
                                                { xtype:'textfield',id:'tf_business_license_name',"columnWidth":0.8,"labelWidth":100,"maxLength":32,"hidden":true,"readOnly":true,"name":"businessLicenseName","fieldLabel":"营业执照全称"},
                                                { xtype:'textfield',id:'tf_detail_addr',"columnWidth":0.8,"labelWidth":100,"hidden":true,"readOnly":true,"name":"addr","fieldLabel":"详细地址"},
                                                { xtype:'textareafield',id:'tf_risk',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"riskDetail","fieldLabel":"设立原因，潜在风险及风控措施"},
                                                { xtype:'textareafield',id:'tf_cen',"columnWidth":0.8,"labelWidth":100,"hidden":true,"readOnly":true,"name":"otherCooperateExplain","fieldLabel":"说明"},
                                                { xtype:'tfcombobox',id:'tf_ocpf',"columnWidth":0.4,"labelWidth":100,"hidden":true,"readOnly":true,"name":"otherCooperateFlag","optId":"7013_new1073","fieldLabel":"是否与其他信贷机构合作"},
                                                { xtype:'textareafield',id:'tf_es',"columnWidth":0.8,"labelWidth":100,"hidden":true,"readOnly":true,"name":"establishReason","fieldLabel":"设立原因"}
                    ]

}
                ]

},
                                { xtype:'fieldset',id:'tf_channel_master_1',"margin":"30px 5px 5px 90px","columnWidth":0.9,"bodyStyle":"background-color:#DFE8F6;","title":"<负责人部分>",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,"bodyStyle":"background-color:#DFE8F6;","autoScroll":false,                    
                    items:[
                                                { xtype:'textfield',id:'tf_detail_masterName',"columnWidth":0.8,"labelWidth":100,"allowBlank":false,"readOnly":true,"name":"masterName","fieldLabel":"姓名"},
                                                { xtype:'textfield',id:'tf_detail_mtel',"columnWidth":0.8,"labelWidth":100,"allowBlank":false,"readOnly":true,"name":"mtel","fieldLabel":"电话"},
                                                { xtype:'textfield',id:'tf_detail_idcard',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"masterIdcard","fieldLabel":"身份证号"},
                                                { xtype:'tfcombobox',id:'tf_xx_flag',"columnWidth":0.4,"labelWidth":100,"allowBlank":false,"readOnly":true,"name":"flag","optId":"7013_new1073","fieldLabel":"是否为美兴客户"},
                                                { xtype:'tfcombobox',id:'tf_add_level',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"level","optId":"7013_new1072","fieldLabel":"内部评级"}
                    ]

}
                ]

},
                                { xtype:'fieldset',id:'tf_detail_channel_3',"margin":"30px 5px 5px 90px","columnWidth":0.9,"bodyStyle":"background-color:#DFE8F6;","title":"<补充录入信息部分>",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,"bodyStyle":"background-color:#DFE8F6;","autoScroll":false,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_detail_signFlag',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"signFlag","optId":"7013_new1073","fieldLabel":"居间协议是否签署"},
                                                { xtype:'datefield',id:'tf_detail_beginDate',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"beginDate","fieldLabel":"协议有效期开始","format":"Ymd"},
                                                { xtype:'datefield',id:'tf_detail_overDate',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"overDate","fieldLabel":"协议有效期截止","format":"Ymd"},
                                                { xtype:'textfield',id:'tf_detail_masterAccountNo',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"masterAccountNo","fieldLabel":"渠道负责人银行卡号"},
                                                { xtype:'textfield',id:'tf_detail_bankDeposit',"columnWidth":0.8,"labelWidth":100,"readOnly":true,"name":"bankDeposit","fieldLabel":"开户银行"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,"bodyStyle":"background-color:#DFE8F6;","autoScroll":false,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_detail_isGuar',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"isGuar","optId":"7013_new1073","fieldLabel":"是否担保"},
                                                { xtype:'textfield',id:'tf_detail_maxGuar',"columnWidth":0.4,"labelWidth":100,"readOnly":true,"name":"maxGuar","fieldLabel":"最高担保金额"}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });

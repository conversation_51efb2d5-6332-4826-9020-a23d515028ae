

    Ext.define('mvc.view.mcpt.6004View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'form',id:'tf_form',"border":0,"autoScroll":false,            
            items:[
                                { xtype:'fieldset',"margin":"5px","minHeight":200,"collapsible":true,"title":"账户信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfselectfield',id:'win_tf_cifid',"columnWidth":0.5,"allowBlank":false,"name":"cifid","fieldLabel":"客户编号"},
                                                { xtype:'textfield',id:'win_tf_cliname',"columnWidth":0.5,"allowBlank":false,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfselectfield',id:'tfc_vchno_add',"columnWidth":0.5,"allowBlank":false,"name":"vchno","fieldLabel":"借据编号"},
                                                { xtype:'textfield',id:'tf_sort',"columnWidth":0.5,"readOnly":true,"name":"assigninst","fieldLabel":"存款户开户机构"},
                                                { xtype:'tfselectfield',id:'tf_sortss',"columnWidth":0.5,"hideable":true,"hidden":true,"name":"assigninst","fieldLabel":"存款户开户机构"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfselectfield',id:'tf_mxbkacno',"columnWidth":0.5,"allowBlank":false,"name":"mxbkacno","fieldLabel":"资金账号"},
                                                { xtype:'tfcombobox',id:'tf_rechnl',"columnWidth":0.5,"fieldStyle":"background:#CCCCCC","allowBlank":false,"readOnly":true,"name":"rechnl","optId":"7013_new1062_#","fieldLabel":"支付渠道"},
                                                { xtype:'tfcombobox',id:'tf_curr',"columnWidth":0.5,"allowBlank":false,"readOnly":true,"name":"curr","optId":"7013_200014","value":"CNY","fieldLabel":"币种"},
                                                { xtype:'tfnumberfield',id:'tf_amt',"columnWidth":0.5,"allowBlank":false,"maxLength":16,"allowDecimals":true,"decimalPrecision":2,"name":"amt","fieldLabel":"充值金额","unit":1,"postfix":"元","tiptype":"money"},
                                                { xtype:'textfield',id:'hd_sortno',"padding":"5 0 5 0px","columnWidth":0.5,"hidden":true,"readOnly":true,"name":"assigninstcode","fieldLabel":"机构编号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_refuseBrf',"columnWidth":0.5,"hidden":true,"name":"refuseBrf","optId":"7013_new1133_#","fieldLabel":"退款原因"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_checkCode',"hidden":true,"readOnly":true,"fieldLabel":"检查是否重复充值暂存框"}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'button',id:'tf_check',"width":90,"iconCls":"icon_coins","text":"账户充值"},
                                { xtype:'button',id:'tf_submit',"width":90,"iconCls":"icon_coins","hidden":true,"text":"账户充值-真实按钮"},
                                { xtype:'label',"cls":"label_title"},
                                { xtype:'tbfill'}
            ]

}
        ]

}
    ]

    });

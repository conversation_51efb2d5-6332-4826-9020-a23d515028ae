

    Ext.define('mvc.view.wben.5000View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'cp_main',"layout":"fit","border":1,"style":"background-color:#FFF;","bodyStyle":"background:url(/ThemeUi/WebDesk/wallpapers/rptbg.jpeg) no-repeat;background-position:center;","collapsible":false,"collapsed":false,"region":"center",        
        items:[
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'datefield',id:'tj_date',"width":150,"labelWidth":60,"labelAlign":"right","fieldLabel":"查询日期","format":"Ymd"},
                                { xtype:'label',"padding":"0 0 0 5px","text":"逾期"},
                                { xtype:'tfnumberfield',id:'tf_var1',"width":40,"labelWidth":5,"allowBlank":false,"vtype":"vInt","allowDecimals":false,"unit":1,"tiptype":"normal","minValue":0},
                                { xtype:'label',"text":"-"},
                                { xtype:'tfnumberfield',id:'tf_var2',"width":40,"labelWidth":44,"allowBlank":false,"vtype":"vInt","allowDecimals":false,"unit":1,"tiptype":"normal","minValue":0},
                                { xtype:'label',"text":"天贷款"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_sel',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_res',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.guar.0002View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"autoScroll":true,"region":"center",        
        items:[
                        { xtype:'form',id:'form_guarbaseinfo',"autoScroll":true,            
            items:[
                                { xtype:'fieldset',"margin":"5px","title":"公用信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.2,"fieldCls":"field_cls_2","name":"colTypeName","fieldLabel":"分类归属"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.3,"fieldCls":"field_cls_2","name":"guarNo","fieldLabel":"押品编号"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"guarName","fieldLabel":"押品名称"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.2,"fieldCls":"field_cls_2","name":"colOneName","fieldLabel":"一级分类"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.3,"fieldCls":"field_cls_2","name":"colTwoName","fieldLabel":"二级分类"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"colNa","fieldLabel":"押品类型"}
                    ]

}
                ]

},
                                { xtype:'fieldset',"margin":"5px","title":"权属人信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfselectfield',id:'tbs_cliname',"columnWidth":0.5,"allowBlank":false,"maxLength":128,"name":"cliname","fieldLabel":"客户名称"},
                                                { xtype:'textfield',id:'tf_cifid',"columnWidth":0.5,"allowBlank":true,"maxLength":32,"readOnly":true,"name":"cifid","fieldLabel":"客户编号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_certtype',"columnWidth":0.5,"readOnly":true,"name":"certtype","optId":"7013_200079","fieldLabel":"证件类型"},
                                                { xtype:'textfield',id:'tf_certno',"columnWidth":0.5,"readOnly":true,"name":"certno","fieldLabel":"证件号码"}
                    ]

}
                ]

},
                                { xtype:'fieldset',"margin":"5px","title":"基本信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_receiptNo',"columnWidth":0.33,"allowBlank":false,"maxLength":32,"name":"receiptNo","fieldLabel":"存单号"},
                                                { xtype:'tfcombobox',id:'tf_commCurr',"columnWidth":0.33,"allowBlank":false,"name":"comCurr","optId":"7013_200014","fieldLabel":"存单币种"},
                                                { xtype:'tfnumberfield',id:'tf_comSum',"columnWidth":0.33,"allowBlank":false,"maxLength":17,"allowDecimals":true,"decimalPrecision":2,"name":"comSum","fieldLabel":"存单金额","postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfnumberfield',"columnWidth":0.33,"allowBlank":false,"maxLength":10,"allowDecimals":true,"decimalPrecision":6,"name":"deptInt","fieldLabel":"存款利率","postfix":"%-年","tiptype":"normal","minValue":0,"maxValue":100},
                                                { xtype:'datefield',id:'tf_comEnddate',"columnWidth":0.33,"allowBlank":false,"name":"comEnddate","fieldLabel":"存单到期日"},
                                                { xtype:'textfield',id:'tf_deptBrna',"columnWidth":0.33,"allowBlank":false,"maxLength":40,"name":"deptBrna","fieldLabel":"存款机构"},
                                                { xtype:'tfcombobox',id:'tf_deptIsEle',"columnWidth":0.33,"allowBlank":false,"hidden":true,"disabled":true,"name":"deptIsEle","optId":"7013_290001","fieldLabel":"是否电子存单"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"maxLength":32,"vtype":"vInt","hidden":true,"disabled":true,"name":"deptAcno","fieldLabel":"存款账号"},
                                                { xtype:'textfield',id:'tf_deptFenno',"columnWidth":0.33,"allowBlank":false,"maxLength":32,"vtype":"vInt","hidden":true,"disabled":true,"name":"deptFenno","fieldLabel":"分账号"},
                                                { xtype:'textfield',id:'tf_deptNo',"columnWidth":0.33,"allowBlank":true,"maxLength":32,"hidden":true,"disabled":true,"name":"deptNo","fieldLabel":"存单凭证号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',id:'tf_isAutotrans',"columnWidth":0.33,"allowBlank":false,"name":"isAutotrans","optId":"7013_290050","fieldLabel":"是否自动转存"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":true,"hidden":true,"name":"deptIsLoacl","optId":"7013_290001","fieldLabel":"是否本行存单"},
                                                { xtype:'textfield',"columnWidth":0.5,"maxLength":40,"hidden":true,"name":"deptCliname","fieldLabel":"存单户名"},
                                                { xtype:'tfcombobox',id:'tf_frozsts',"columnWidth":0.33,"readOnly":true,"disabled":true,"optId":"7013_250501","fieldLabel":"冻结状态"},
                                                { xtype:'textfield',id:'tf_corename',"columnWidth":0.33,"maxLength":40,"readOnly":true,"disabled":true,"fieldLabel":"核心客户名称"},
                                                { xtype:'tfcombobox',id:'tf_transts',"columnWidth":0.33,"readOnly":true,"disabled":true,"optId":"7013_290001","fieldLabel":"是否圈存"}
                    ]

}
                ]

},
                                { xtype:'fieldset',"margin":"5px","title":"价值信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'datefield',id:'tf_fstDate',"columnWidth":0.33,"allowBlank":false,"disabled":false,"name":"fstDate","fieldLabel":"押品价值认定时间"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"readOnly":true,"disabled":false,"name":"fstCurr","optId":"7013_200014","value":"CNY","fieldLabel":"押品价值认定币种"},
                                                { xtype:'tfnumberfield',id:'tf_fstValue',"columnWidth":0.33,"allowBlank":false,"maxLength":17,"disabled":false,"allowDecimals":true,"decimalPrecision":2,"name":"fstValue","fieldLabel":"押品认定价值(评估值)","postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"disabled":false,"name":"fstMode","optId":"7013_210001","fieldLabel":"押品价值认定方式"},
                                                { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"maxLength":40,"name":"fstBrno","fieldLabel":"押品评估机构"},
                                                { xtype:'datefield',"columnWidth":0.33,"hidden":true,"disabled":false,"name":"finDate","fieldLabel":"我行认定时间"},
                                                { xtype:'tfnumberfield',id:'tf_finValue',"columnWidth":0.33,"allowBlank":false,"maxLength":17,"disabled":false,"allowDecimals":true,"decimalPrecision":2,"name":"finValue","fieldLabel":"行内认定价值","postfix":"元","tiptype":"money","minValue":1,"maxValue":99999999999999.99}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"hidden":true,"readOnly":true,"disabled":false,"name":"finCurr","optId":"7013_200014","value":"CNY","fieldLabel":"我行认定币种"},
                                                { xtype:'tfnumberfield',id:'tf_cheapLtv',"columnWidth":0.33,"hidden":true,"readOnly":true,"fieldLabel":"抵质押率","postfix":"%","tiptype":"normal"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfnumberfield',id:'tf_guarSumMax',"columnWidth":0.33,"allowBlank":false,"maxLength":17,"allowDecimals":true,"decimalPrecision":2,"name":"guarSumMax","fieldLabel":"最高可抵押金额","postfix":"元","tiptype":"money","minValue":1,"maxValue":99999999999999.99},
                                                { xtype:'tfnumberfield',id:'tf_guarLtvMax',"columnWidth":0.33,"allowBlank":false,"maxLength":10,"allowDecimals":true,"decimalPrecision":2,"name":"guarLtvMax","fieldLabel":"最高可抵押率","postfix":"%","minValue":1,"maxValue":999.999999}
                    ]

}
                ]

},
                                { xtype:'fieldset',"margin":"5px","title":"关联信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"name":"comLawvad","optId":"7013_210021","fieldLabel":"法律有效性"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"name":"comChbty","optId":"7013_210002","fieldLabel":"变现能力"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"name":"comFunvalof","optId":"7013_210024","fieldLabel":"价值波动性"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,"hidden":true,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"hidden":true,"name":"comCldown","optId":"7013_210023","fieldLabel":"查封便利性"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"hidden":true,"name":"comInterfix","optId":"7013_210022","fieldLabel":"押品借款人相关性"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,"frame":false,"autoScroll":true,                    
                    items:[
                                                { xtype:'textareafield',"height":80,"columnWidth":0.99,"maxLength":150,"name":"brf","fieldLabel":"押品描述"}
                    ]

}
                ]

},
                                { xtype:'fieldset',"margin":"5px","title":"登记信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'iptUsrName',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"iptUsrName","fieldLabel":"登记人"},
                                                { xtype:'tfdisplayfield',id:'updUsrName',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"updUsrName","fieldLabel":"更新人"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","hidden":true,"name":"iptUsr","fieldLabel":"登记人"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","hidden":true,"name":"updUsr","fieldLabel":"更新人"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'iptBrnoName',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"iptBrnoName","fieldLabel":"登记机构"},
                                                { xtype:'tfdisplayfield',id:'updBrnoName',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"updBrnoName","fieldLabel":"更新机构"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","hidden":true,"name":"iptBrno","fieldLabel":"登记机构"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","hidden":true,"name":"updBrno","fieldLabel":"更新机构"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"iptDate","fieldLabel":"登记日期"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.5,"fieldCls":"field_cls_2","name":"updDate","fieldLabel":"更新日期"}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',            
            items:[
                                { xtype:'label',"cls":"label_title","text":"押品详情"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_guarbase_tmpsave',"iconCls":"icon_disk--arrow","text":"暂存"},
                                { xtype:'button',id:'btn_guarbase_save',"iconCls":"icon_disk-black","text":"保存"}
            ]

}
        ]

}
    ]

    });

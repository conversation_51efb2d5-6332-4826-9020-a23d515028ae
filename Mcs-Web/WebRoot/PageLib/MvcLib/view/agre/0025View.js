

    Ext.define('mvc.view.agre.0025View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"fit","border":0,            
            items:[
                                { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"allowPage":true,"pageSize":20,                
                columns:[
                                        { xtype:'gridcolumn',"width":130,"dataIndex":"applyno","text":"申请编号"},
                                        { xtype:'gridcolumn',"width":135,"dataIndex":"contno","text":"合同编号"},
                                        { xtype:'tfoptcolumn',"width":70,"dataIndex":"occurtype","optId":"7013_250027_#","text":"发生类型"},
                                        { xtype:'gridcolumn',"width":160,"dataIndex":"prdtNa","text":"贷款产品"},
                                        { xtype:'gridcolumn',"width":130,"dataIndex":"cifid","text":"组长编号"},
                                        { xtype:'gridcolumn',"dataIndex":"cliname","text":"组长名称"},
                                        { xtype:'gridcolumn',"width":80,"dataIndex":"occurdate","text":"申请日期"},
                                        { xtype:'numbercolumn',"width":110,"dataIndex":"busSum","text":"合同金额"},
                                        { xtype:'gridcolumn',"width":90,"dataIndex":"appTerm","text":"期限值"},
                                        { xtype:'tfoptcolumn',"width":100,"dataIndex":"appTermType","optId":"7013_250026","text":"期限表达类型"},
                                        { xtype:'gridcolumn',"width":80,"dataIndex":"begindate","text":"起始日期"},
                                        { xtype:'gridcolumn',"width":80,"dataIndex":"enddate","text":"到期日期"},
                                        { xtype:'tfoptcolumn',"width":90,"dataIndex":"guarType","optId":"7013_220007","text":"主要担保方式"},
                                        { xtype:'gridcolumn',"width":60,"dataIndex":"cnt","text":"协议人数"},
                                        { xtype:'gridcolumn',"width":110,"hideable":true,"hidden":true,"dataIndex":"flowno","text":"流程号"},
                                        { xtype:'gridcolumn',"width":110,"hideable":true,"hidden":true,"dataIndex":"phaseno","text":"当前阶段编号"},
                                        { xtype:'gridcolumn',"width":120,"hideable":true,"hidden":true,"dataIndex":"phasename","text":"当前阶段"},
                                        { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"opername","text":"阶段经办人"},
                                        { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"recphaseno","text":"阶段编号"}
                ]

}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'tbseparator'},
                                        { xtype:'textfield',id:'tf_applyno',"width":190,"labelWidth":60,"fieldLabel":"申请编号"},
                                        { xtype:'textfield',id:'tf_cifid',"width":210,"labelWidth":60,"fieldLabel":"组长编号"},
                                        { xtype:'textfield',id:'tf_cliname',"width":200,"labelWidth":60,"fieldLabel":"组长名称"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_sel',"iconCls":"icon_view","text":"查询"},
                                        { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"}
                ]

},
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'label',"cls":"label_title","text":"已完成申请"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_view',"iconCls":"icon_script_edit","disabled":true,"text":"申请详情"},
                                        { xtype:'button',id:'btn_contno',"iconCls":"icon_document--arrow","disabled":true,"text":"合同详情"}
                ]

}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.task.0005View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'form',id:'form_detail',"border":0,            
            items:[
                                { xtype:'fieldset',"margin":"5px","title":"任务信息",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',"columnWidth":0.5,"readOnly":true,"fieldLabel":"任务名称"},
                                                { xtype:'textfield',"columnWidth":0.5,"readOnly":true,"fieldLabel":"任务范围"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textareafield',"readOnly":true,"fieldLabel":"任务说明"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'datefield',"columnWidth":0.5,"readOnly":true,"fieldLabel":"任务截止日期","format":"Ymd"},
                                                { xtype:'tfuploadpanel',"columnWidth":0.5,"border":0,"readOnly":true,"files":1,"text":"相关文档"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textareafield',"columnWidth":1,"allowBlank":false,"fieldLabel":"任务结论"}
                    ]

}
                ]

},
                                { xtype:'fieldset',"layout":"fit","margin":"5px",                
                items:[
                                        { xtype:'panel',"layout":"fit","border":1,                    
                    items:[
                                                { xtype:'grid',"height":150,"border":0,                        
                        columns:[
                                                        { xtype:'gridcolumn'},
                                                        { xtype:'gridcolumn'},
                                                        { xtype:'gridcolumn'}
                        ]

}
                    ]
,                    
                    dockedItems:[
                                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,                        
                        items:[
                                                        { xtype:'button',"iconCls":"icon_collapse","text":"上传报告"},
                                                        { xtype:'button',"iconCls":"icon_delete","text":"删除"}
                        ]

}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'tbseparator'},
                                { xtype:'button',"iconCls":"icon_disk-black","text":"保存"}
            ]

}
        ]

}
    ]

    });

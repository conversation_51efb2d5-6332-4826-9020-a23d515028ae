

    Ext.define('mvc.view.hmdm.0004View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"multiSelect":false,"selType":"checkboxmodel","allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"blacklistProcessId","text":"申请号"},
                                { xtype:'tfoptcolumn',"dataIndex":"applyType","optId":"7013_250036","text":"流程类型"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"customerCode","text":"客户编号"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"customerName","text":"客户名称"},
                                { xtype:'gridcolumn',"dataIndex":"idType","text":"证件类型"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"idNumber","text":"证件号码"},
                                { xtype:'gridcolumn',"dataIndex":"customerManager","text":"客户经理"},
                                { xtype:'gridcolumn',"dataIndex":"belongOrgan","text":"归属机构"},
                                { xtype:'tfoptcolumn',"dataIndex":"approveStatus","optId":"7013_0003","text":"流程状态"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"createTime","text":"创建时间"},
                                { xtype:'gridcolumn',"dataIndex":"createName","text":"创建人"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'textfield',id:'customerCode',"width":160,"padding":"0 0 0 0px","labelWidth":60,"fieldLabel":"客户编号"},
                                { xtype:'textfield',id:'customerName',"width":160,"padding":"0 0 0 0px","labelWidth":60,"fieldLabel":"客户名称"},
                                { xtype:'textfield',id:'idNumber',"width":180,"padding":"0 0 0 0px","labelWidth":80,"fieldLabel":"证件号码"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'button',id:'btn_view',"iconCls":"icon_document-search-result","text":"详情"},
                                { xtype:'button',id:'btn_del',"iconCls":"icon_delete","disabled":true,"text":"取消申请"},
                                { xtype:'button',id:'btn_check',"iconCls":"icon_inbox-download","disabled":true,"text":"认领"},
                                { xtype:'button',id:'btn_flow',"iconCls":"icon_hand-point-090","disabled":true,"text":"签署意见并提交"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.hmdm.0004View.win_add_del',{ extend:'Ext.window.Window', alias:'win_add_del'
    ,id:'win_add_del',"layout":"fit","width":450,"height":320,"modal":true,"constrain":true,"resizable":false,"title":"取消申请",    
    items:[
                { xtype:'form',id:'del_form',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0px","border":0,            
            items:[
                                { xtype:'textfield',id:'win_applyno',"columnWidth":0.7,"allowBlank":false,"readOnly":true,"name":"applyno","fieldLabel":"申请编号"},
                                { xtype:'textfield',id:'win_customerName',"columnWidth":0.7,"allowBlank":false,"readOnly":true,"name":"customerName","fieldLabel":"客户名称"},
                                { xtype:'textareafield',id:'tfd_refusereason',"columnWidth":1,"name":"refusereason","fieldLabel":"详细说明"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'tfbutton',"tfAction":"win_close"},
                        { xtype:'tbseparator'},
                        { xtype:'button',id:'btn_cancel',"iconCls":"icon_tick","text":"确认"}
        ]

}
    ]

    });

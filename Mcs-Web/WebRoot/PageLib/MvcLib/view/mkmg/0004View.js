

    Ext.define('mvc.view.mkmg.0004View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'tf_center',"layout":"fit","minHeight":100,"border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"selType":"checkboxmodel","forceFit":false,"autoLoad":false,"allowPage":true,"pageSize":15,            
            columns:[
                                { xtype:'gridcolumn',"dataIndex":"serialsno","text":"序号"},
                                { xtype:'datecolumn',"dataIndex":"planDate","text":"计划日期","format":"Ymd"},
                                { xtype:'gridcolumn',"dataIndex":"planTime","text":"计划时间"},
                                { xtype:'tfoptcolumn',"dataIndex":"marketMode","optId":"7013_200097","text":"营销方式"},
                                { xtype:'gridcolumn',"dataIndex":"marketAddr","text":"营销地点"},
                                { xtype:'gridcolumn',"dataIndex":"advanceWarnDays","text":"提前提醒天数"},
                                { xtype:'gridcolumn',"width":200,"dataIndex":"warnContent","text":"提醒内容"},
                                { xtype:'tfoptcolumn',"dataIndex":"finishFlag","optId":"7013_220055","text":"完成标识"}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'label',"cls":"label_title","text":"营销计划/任务"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_add_plan',"iconCls":"icon_add","disabled":true,"text":"新增"},
                                        { xtype:'button',id:'btn_edit_plan',"iconCls":"icon_script_edit","disabled":true,"text":"编辑"},
                                        { xtype:'button',id:'btn_delete_plan',"iconCls":"icon_cancel","disabled":true,"text":"删除"},
                                        { xtype:'button',id:'btn_detail_plan',"iconCls":"icon_grid","disabled":true,"text":"详情"}
                ]

}
            ]

}
        ]

},
                { xtype:'panel',"layout":"fit","height":200,"split":true,"collapsible":false,"collapsed":false,"region":"north",        
        items:[
                        { xtype:'grid',dataSource:'grid_list_store',id:'grid_list',"border":0,"selType":"checkboxmodel","forceFit":false,"allowPage":true,"pageSize":15,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',id:'tf_cifid',"width":120,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"telephone","text":"联系电话"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"addr","text":"客户地址"},
                                { xtype:'tfoptcolumn',"width":120,"dataIndex":"cstype","optId":"7013_260001","text":"客户类型"},
                                { xtype:'gridcolumn',"width":120,"hidden":true,"dataIndex":"iptManage","text":"管户经理"},
                                { xtype:'gridcolumn',"dataIndex":"iptManagename","text":"负责经理"},
                                { xtype:'tfoptcolumn',"width":120,"dataIndex":"busStatus","optId":"7013_260002","text":"商机状态"}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"vbox","dock":"top",                
                items:[
                                        { xtype:'toolbar',"layout":"hbox","width":"100%","border":0,"cls":"toolbar_cls_1",                    
                    items:[
                                                { xtype:'textfield',id:'tb_cliname',"width":200,"padding":"0 0 0 10px","labelWidth":60,"fieldLabel":"客户名称"},
                                                { xtype:'textfield',id:'tb_cifid',"width":200,"labelWidth":60,"fieldLabel":"客户编号"},
                                                { xtype:'tbseparator'},
                                                { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                                { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"},
                                                { xtype:'tbfill'}
                    ]

}
                ]

},
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'button',id:'btn_detail',"iconCls":"icon_script_edit","disabled":true,"text":"客户详情"},
                                        { xtype:'button',id:'btn_indBase_detail',"iconCls":"icon_script_edit","hidden":true,"disabled":true,"text":"客户详情"}
                ]

}
            ]

}
        ]

},
                { xtype:'panel',"layout":"fit","height":200,"split":true,"collapsible":false,"collapsed":false,"region":"south",        
        items:[
                        { xtype:'grid',dataSource:'grid_record_store',id:'grid_record',"border":0,"selType":"checkboxmodel","forceFit":false,"autoLoad":false,"allowPage":true,"pageSize":15,            
            columns:[
                                { xtype:'gridcolumn',"width":100,"dataIndex":"serid","text":"营销序号"},
                                { xtype:'gridcolumn',"width":100,"hidden":true,"dataIndex":"marketMan","text":"营销人"},
                                { xtype:'gridcolumn',"dataIndex":"marketManname","text":"营销人"},
                                { xtype:'datecolumn',"width":100,"dataIndex":"marketDate","text":"营销执行日期","format":"Ymd"},
                                { xtype:'tfoptcolumn',"width":100,"dataIndex":"impType","optId":"7013_260005","text":"执行方式"},
                                { xtype:'tfoptcolumn',"width":100,"dataIndex":"marketEffect","optId":"7013_260006","text":"营销效果"},
                                { xtype:'gridcolumn',"width":100,"dataIndex":"marketConclusion","text":"营销结论"},
                                { xtype:'gridcolumn',"width":100,"dataIndex":"conclusionReason","text":"结论原因"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"marketTaskno","text":"营销任务编号"}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'label',"cls":"label_title","text":"营销记录"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_add_record',"iconCls":"icon_add","disabled":true,"text":"新增"},
                                        { xtype:'button',id:'btn_edit_record',"iconCls":"icon_script_edit","disabled":true,"text":"编辑"},
                                        { xtype:'button',id:'btn_delete_record',"iconCls":"icon_cancel","disabled":true,"text":"删除"},
                                        { xtype:'button',id:'btn_detail_record',"iconCls":"icon_grid","disabled":true,"text":"详情"}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_plan_add',{ extend:'Ext.window.Window', alias:'win_plan_add'
    ,id:'win_plan_add',"layout":"fit","width":400,"height":320,"frame":false,"modal":true,"constrain":true,"resizable":false,"title":"新增",    
    items:[
                { xtype:'form',id:'plan_add_form',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0px","border":0,            
            items:[
                                { xtype:'datefield',"columnWidth":1,"allowBlank":false,"name":"planDate","fieldLabel":"计划日期","format":"Ymd"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'timefield',"columnWidth":1,"name":"planTime","fieldLabel":"计划时间","format":"g:i:A","increment":30}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":1,"name":"marketMode","optId":"7013_200097_#","fieldLabel":"营销方式"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"maxLength":128,"name":"marketAddr","fieldLabel":"营销地点"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"allowBlank":false,"maxLength":10,"vtype":"vInt","name":"advanceWarnDays","fieldLabel":"提前提醒天数"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"maxLength":300,"name":"warnContent","fieldLabel":"提醒内容"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tfd_finishFlag',"columnWidth":1,"allowBlank":false,"name":"finishFlag","optId":"7013_220055_#","fieldLabel":"完成标识"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_serialsno',"columnWidth":1,"allowBlank":true,"hidden":true,"readOnly":true,"disabled":true,"name":"serialsno","fieldLabel":"序号"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptUser","fieldLabel":"操作人员"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptDate","fieldLabel":"操作日期"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptBrno","fieldLabel":"操作机构"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'tfbutton',"tfAction":"win_close"},
                        { xtype:'tbseparator'},
                        { xtype:'button',id:'btn_submit',"iconCls":"icon_tick","text":"确认"}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_plan_edit',{ extend:'Ext.window.Window', alias:'win_plan_edit'
    ,id:'win_plan_edit',"layout":"fit","width":400,"height":320,"modal":true,"constrain":true,"resizable":false,"title":"编辑",    
    items:[
                { xtype:'form',id:'plan_edit_form',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0px","border":0,            
            items:[
                                { xtype:'datefield',"columnWidth":1,"allowBlank":false,"name":"planDate","fieldLabel":"计划日期","format":"Ymd"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'timefield',"columnWidth":1,"name":"planTime","fieldLabel":"计划时间","format":"g:i:A","increment":30}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":1,"name":"marketMode","optId":"7013_200097_#","fieldLabel":"营销方式"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"maxLength":128,"name":"marketAddr","fieldLabel":"营销地点"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"allowBlank":false,"maxLength":10,"name":"advanceWarnDays","fieldLabel":"提前提醒天数"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"maxLength":300,"name":"warnContent","fieldLabel":"提醒内容"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":1,"allowBlank":false,"name":"finishFlag","optId":"7013_220055_#","fieldLabel":"完成标识"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"readOnly":true,"name":"serialsno","fieldLabel":"序号"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptUser","fieldLabel":"操作人员"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptDate","fieldLabel":"操作日期"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptBrno","fieldLabel":"操作机构"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'tfbutton',"tfAction":"win_close"},
                        { xtype:'tbseparator'},
                        { xtype:'button',id:'btn_submit_plan',"iconCls":"icon_tick","text":"确认"}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_record_add',{ extend:'Ext.window.Window', alias:'win_record_add'
    ,id:'win_record_add',"layout":"column","width":900,"height":430,"modal":true,"constrain":true,"resizable":false,"title":"新增",    
    items:[
                { xtype:'form',id:'form_market_record',"layout":"fit","border":0,        
        items:[
                        { xtype:'fieldset',"margin":"5px","collapsible":false,"collapsed":false,"title":"<营销记录新增>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'datefield',id:'tf_exedate',"width":300,"columnWidth":0.33,"allowBlank":false,"vtype":"vExp","vtypeText":"营销执行日期不能晚于系统日期","vtypeExp":"Ext.getCmp('tf_exedate').rawValue<=tf.sysdb.workDate","name":"marketDate","fieldLabel":"营销执行日期","format":"Ymd"},
                                        { xtype:'textfield',id:'tf_r_cifid',"columnWidth":0.33,"inputType":"text","readOnly":true,"name":"cifid","fieldLabel":"客户编号","tiptype":"money"},
                                        { xtype:'textfield',id:'tf_cliname',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',id:'tf_sex',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"sex","optId":"7013_200080_#","fieldLabel":"客户性别"},
                                        { xtype:'textfield',id:'tf_tel',"columnWidth":0.33,"allowBlank":false,"name":"tel","fieldLabel":"联系电话"},
                                        { xtype:'textfield',id:'tf_addr',"columnWidth":0.33,"allowBlank":false,"name":"addr","fieldLabel":"客户地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"manageType","optId":"7013_260004_#","fieldLabel":"经营类型"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"industry","optId":"7013_200064_#","fieldLabel":"所属行业"},
                                        { xtype:'tfselectfield',id:'tfs_industry',"columnWidth":0.333,"hidden":true,"disabled":true,"fieldLabel":"所属行业"},
                                        { xtype:'hiddenfield',id:'hd_industry',"hidden":true,"disabled":true,"name":"industry"},
                                        { xtype:'textfield',"columnWidth":0.33,"name":"mainRange","fieldLabel":"主营范围"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.33,"name":"busEntityName","fieldLabel":"营销实体名称"},
                                        { xtype:'textfield',"columnWidth":0.66,"name":"busAddr","fieldLabel":"经营地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"impType","optId":"7013_260005_#","fieldLabel":"执行方式"},
                                        { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"name":"impTime","fieldLabel":"执行时长","postfix":"小时"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"marketEffect","optId":"7013_260006_#","fieldLabel":"营销效果"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"width":400,"columnWidth":0.99,"name":"custDemandBack","fieldLabel":"客户需求反馈"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.99,"name":"custSitustionDesc","fieldLabel":"客户情况描述"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.66,"name":"othBusRequire","emptyText":"POS机、信用卡、理财业务、网上银行、银行结算等行内其他服务","fieldLabel":"其他业务需求"},
                                        { xtype:'tfcombobox',id:'tfr_busStatus',"columnWidth":0.33,"allowBlank":false,"name":"busStatus","optId":"7013_260002_#","fieldLabel":"商机状态"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"custAccessChannel","optId":"7013_200123_#","fieldLabel":"客户获取渠道"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"marketConclusion","optId":"7013_260007_#","fieldLabel":"营销结论"},
                                        { xtype:'tfcombobox',id:'tfr_custType',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"custType","optId":"7013_260001_#","fieldLabel":"客户类型"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textareafield',"columnWidth":0.99,"name":"conclusionReason","fieldLabel":"结论原因"}
                ]

}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'tfbutton',"tfAction":"win_close"},
                        { xtype:'tbseparator'},
                        { xtype:'button',id:'btn_submit_record',"iconCls":"icon_tick","hidden":true,"disabled":true,"text":"确认"},
                        { xtype:'button',id:'btn_submit_record_1',"iconCls":"icon_tick","text":"确认"}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_record_edit',{ extend:'Ext.window.Window', alias:'win_record_edit'
    ,id:'win_record_edit',"layout":"fit","width":900,"height":430,"modal":true,"constrain":true,"resizable":false,"title":"编辑",    
    items:[
                { xtype:'form',id:'form_edit_record',"layout":"fit","border":0,        
        items:[
                        { xtype:'fieldset',"margin":"5px","collapsible":false,"collapsed":false,"title":"<营销记录新增>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'datefield',id:'tf_exedate1',"width":300,"columnWidth":0.33,"allowBlank":false,"vtype":"vExp","vtypeExp":"Ext.getCmp('tf_exedate1').rawValue<=tf.sysdb.workDate","name":"marketDate","fieldLabel":"营销执行日期","format":"Ymd"},
                                        { xtype:'textfield',id:'tf_r_cifid_1',"columnWidth":0.33,"inputType":"text","readOnly":true,"name":"cifid","fieldLabel":"客户编号","tiptype":"money"},
                                        { xtype:'textfield',id:'tf_cliname_1',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"},
                                        { xtype:'textfield',id:'tf_marketMan',"hidden":true,"name":"marketMan","fieldLabel":"营销人"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',id:'tf_sex_1',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"sex","optId":"7013_200080_#","fieldLabel":"客户性别"},
                                        { xtype:'textfield',id:'tf_tel_1',"columnWidth":0.33,"allowBlank":false,"name":"tel","fieldLabel":"联系电话"},
                                        { xtype:'textfield',id:'tf_addr_1',"columnWidth":0.33,"allowBlank":false,"name":"addr","fieldLabel":"客户地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"manageType","optId":"7013_260004_#","fieldLabel":"经营类型"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"industry","optId":"7013_200064_#","fieldLabel":"所属行业"},
                                        { xtype:'tfselectfield',id:'tfs_industry_1',"columnWidth":0.333,"hidden":true,"disabled":true,"fieldLabel":"所属行业"},
                                        { xtype:'hiddenfield',id:'hd_industry_1',"hidden":true,"disabled":true,"name":"industry"},
                                        { xtype:'textfield',"columnWidth":0.33,"name":"mainRange","fieldLabel":"主营范围"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.33,"name":"busEntityName","fieldLabel":"营销实体名称"},
                                        { xtype:'textfield',"columnWidth":0.66,"name":"busAddr","fieldLabel":"经营地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"impType","optId":"7013_260005_#","fieldLabel":"执行方式"},
                                        { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"name":"impTime","fieldLabel":"执行时长","postfix":"小时"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"marketEffect","optId":"7013_260006_#","fieldLabel":"营销效果"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"width":400,"columnWidth":0.99,"allowBlank":true,"name":"custDemandBack","fieldLabel":"客户需求反馈"},
                                        { xtype:'hiddenfield',id:'tf_marketTaskno',"hidden":true,"name":"marketTaskno"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.99,"name":"custSitustionDesc","fieldLabel":"客户情况描述"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.66,"name":"othBusRequire","emptyText":"POS机、信用卡、理财业务、网上银行、银行结算等行内其他服务","fieldLabel":"其他业务需求"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"name":"busStatus","optId":"7013_260002_#","fieldLabel":"商机状态"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"multiSelect":false,"name":"custAccessChannel","optId":"7013_200123_#","fieldLabel":"客户获取渠道"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"marketConclusion","optId":"7013_260007_#","fieldLabel":"营销结论"},
                                        { xtype:'tfcombobox',id:'tf_custType',"columnWidth":0.33,"allowBlank":false,"multiSelect":false,"name":"custType","optId":"7013_260001_#","fieldLabel":"客户类型"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textareafield',"columnWidth":0.99,"name":"conclusionReason","fieldLabel":"结论原因"}
                ]

}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'tfbutton',"tfAction":"win_close"},
                        { xtype:'tbseparator'},
                        { xtype:'button',id:'btn_submit_edit',"iconCls":"icon_tick","text":"确认"}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_plan_detail',{ extend:'Ext.window.Window', alias:'win_plan_detail'
    ,id:'win_plan_detail',"layout":"fit","width":400,"height":320,"modal":true,"constrain":true,"resizable":false,"title":"详情",    
    items:[
                { xtype:'form',id:'plan_detail_form',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0px","border":0,            
            items:[
                                { xtype:'datefield',"columnWidth":1,"allowBlank":false,"readOnly":true,"name":"planDate","fieldLabel":"计划日期","format":"Ymd"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'timefield',"columnWidth":1,"readOnly":true,"name":"planTime","fieldLabel":"计划时间","format":"g:i:s","increment":30}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":1,"readOnly":true,"name":"marketMode","optId":"7013_200097_#","fieldLabel":"营销方式"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"readOnly":true,"name":"marketAddr","fieldLabel":"营销地点"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"allowBlank":false,"readOnly":true,"name":"advanceWarnDays","fieldLabel":"提前提醒天数"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"readOnly":true,"name":"warnContent","fieldLabel":"提醒内容"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":1,"allowBlank":false,"readOnly":true,"name":"finishFlag","optId":"7013_220055_#","fieldLabel":"完成标识"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"readOnly":true,"name":"serialsno","fieldLabel":"序号"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptUser","fieldLabel":"操作人员"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptDate","fieldLabel":"操作日期"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"iptBrno","fieldLabel":"操作机构"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mkmg.0004View.win_record_detail',{ extend:'Ext.window.Window', alias:'win_record_detail'
    ,id:'win_record_detail',"layout":"fit","width":900,"height":430,"modal":true,"constrain":true,"resizable":false,"title":"详情",    
    items:[
                { xtype:'form',id:'form_detail_record',"layout":"fit","border":0,        
        items:[
                        { xtype:'fieldset',"margin":"5px","collapsible":false,"collapsed":false,"title":"<营销记录新增>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'datefield',"width":300,"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"marketDate","fieldLabel":"营销执行日期","format":"Ymd"},
                                        { xtype:'textfield',"columnWidth":0.33,"inputType":"text","readOnly":true,"name":"cifid","fieldLabel":"客户编号","tiptype":"money"},
                                        { xtype:'textfield',id:'tfd_cliname',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"},
                                        { xtype:'textfield',id:'tfd_marketMan',"hidden":true,"readOnly":true,"name":"marketMan","fieldLabel":"营销人"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',id:'tfd_sex',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"sex","optId":"7013_200080_#","fieldLabel":"客户性别"},
                                        { xtype:'textfield',id:'tfd_tel',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"tel","fieldLabel":"联系电话"},
                                        { xtype:'textfield',id:'tfd_addr',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"addr","fieldLabel":"客户地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"readOnly":true,"multiSelect":false,"name":"manageType","optId":"7013_260004_#","fieldLabel":"经营类型"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"readOnly":true,"multiSelect":false,"name":"industry","optId":"7013_200064_#","fieldLabel":"所属行业"},
                                        { xtype:'tfselectfield',id:'tfd_industry',"columnWidth":0.333,"hidden":true,"readOnly":true,"disabled":true,"fieldLabel":"所属行业"},
                                        { xtype:'hiddenfield',id:'hdd_industry',"hidden":true,"disabled":true,"name":"industry"},
                                        { xtype:'textfield',"columnWidth":0.33,"readOnly":true,"name":"mainRange","fieldLabel":"主营范围"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.33,"readOnly":true,"name":"busEntityName","fieldLabel":"营销实体名称"},
                                        { xtype:'textfield',"columnWidth":0.66,"readOnly":true,"name":"busAddr","fieldLabel":"经营地址"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"impType","optId":"7013_260005_#","fieldLabel":"执行方式"},
                                        { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"impTime","fieldLabel":"执行时长","postfix":"小时"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"marketEffect","optId":"7013_260006_#","fieldLabel":"营销效果"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"width":400,"columnWidth":0.99,"readOnly":true,"name":"custDemandBack","fieldLabel":"客户需求反馈"},
                                        { xtype:'hiddenfield',id:'tf_marketTaskno_1',"hidden":true,"name":"marketTaskno"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.99,"readOnly":true,"name":"custSitustionDesc","fieldLabel":"客户情况描述"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.66,"readOnly":true,"name":"othBusRequire","emptyText":"POS机、信用卡、理财业务、网上银行、银行结算等行内其他服务","fieldLabel":"其他业务需求"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"busStatus","optId":"7013_260002_#","fieldLabel":"商机状态"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.33,"readOnly":true,"multiSelect":false,"name":"custAccessChannel","optId":"7013_200123_#","fieldLabel":"客户获取渠道"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"marketConclusion","optId":"7013_260007_#","fieldLabel":"营销结论"},
                                        { xtype:'tfcombobox',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"multiSelect":false,"name":"custType","optId":"7013_260001_#","fieldLabel":"客户类型"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textareafield',"columnWidth":0.99,"readOnly":true,"name":"conclusionReason","fieldLabel":"结论原因"}
                ]

}
            ]

}
        ]

}
    ]

    });

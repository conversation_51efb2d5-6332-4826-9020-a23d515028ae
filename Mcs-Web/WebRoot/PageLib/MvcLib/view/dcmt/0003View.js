

    Ext.define('mvc.view.dcmt.0003View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"serid","text":"流水号"},
                                { xtype:'tfoptcolumn',"width":80,"dataIndex":"signsts","optId":"7013_290001","text":"是否签署"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"relserid","text":"关联流水号"},
                                { xtype:'tfoptcolumn',"width":150,"dataIndex":"fileType","optId":"7013_20131","text":"文件类型"},
                                { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"fileurl","text":"文件地址"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"filename","text":"文件名称"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"filedescription","text":"文件描述"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"eccontno","text":"电子合同编号"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"operName","text":"登记人"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"instName","text":"登记机构"},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"exdate","text":"登记日期"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,"cls":"toolbar_cls_1",            
            items:[
                                { xtype:'tbseparator'},
                                { xtype:'textfield',id:'tf_cifid',"width":190,"labelWidth":60,"labelAlign":"right","fieldLabel":"客户编号"},
                                { xtype:'textfield',id:'tf_cliname',"width":190,"labelWidth":60,"labelAlign":"right","fieldLabel":"客户名称"},
                                { xtype:'textfield',id:'tf_certno',"width":220,"labelWidth":60,"labelAlign":"right","hidden":true,"fieldLabel":"证件号码"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_sel',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_res',"text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,            
            items:[
                                { xtype:'label',"cls":"label_title","text":"凭证下载"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_detail',"iconCls":"icon_book-open-text","disabled":true,"text":"下载"}
            ]

}
        ]

}
    ]

    });

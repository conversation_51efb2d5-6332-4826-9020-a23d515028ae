

    Ext.define('mvc.view.hfjk.0001View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"autoScroll":true,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'form',id:'hf_rs',"border":0,"autoScroll":true,            
            items:[
                                { xtype:'fieldset',                
                items:[
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"外部数据查询人员"},
                                                { xtype:'button',id:'tf_view_1',"icon":"/ThemeUi/Shared/icons/fam/attach.png","disabled":true,"text":"查看报告详情"}
                    ]

},
                                        { xtype:'grid',dataSource:'men_store',id:'men',"border":0,"pageSize":5,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cliname","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"time","text":"报告查询时间"},
                                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"cifid","text":"客户编号"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"汇法查询记录"}
                    ]

},
                                        { xtype:'grid',dataSource:'record_store',id:'record',"border":0,"pageSize":5,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cliname","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"querystatus","text":"查询状态"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"fxmsgnum","text":"命中风险信息条数"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"公告事项"}
                    ]

},
                                        { xtype:'grid',dataSource:'shenpan_store',id:'shenpan',"border":0,"pageSize":5,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"受理机构"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"日期"},
                                                { xtype:'gridcolumn',"dataIndex":"pctype","text":"原/被告"},
                                                { xtype:'gridcolumn',"dataIndex":"perstate","text":"状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"casetopic","text":"详情-案由"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"plaintiff","text":"详情-原告"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"defendant","text":"详情-被告"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-公告内容"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"诉讼信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'caipan_store',id:'caipan',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"受理机构"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"日期"},
                                                { xtype:'gridcolumn',"dataIndex":"pctype","text":"原/被告"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"涉案金额"},
                                                { xtype:'gridcolumn',"dataIndex":"mstype","text":"案件类型"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"casetopic","text":"详情-案由"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"plaintiff","text":"详情-原告"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"defendant","text":"详情-被告"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"otherparty","text":"详情-其他当事人"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-审理结果"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"vprogram","text":"详情-审理程序"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"执行信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'zhixing_store',id:'zhixing',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'gridcolumn',"dataIndex":"apply","text":"申请执行人"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"执行标的金额"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"执行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"sx_wh","text":"详情-执行依据文号"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-执行内容"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"wlmoney","text":"详情-未履行金额"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"state","text":"详情-收录时案件状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"zblong","text":"详情-终本日期"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"终本信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'zhongben_store',id:'zhongben',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'gridcolumn',"dataIndex":"apply","text":"申请执行人"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"执行标的金额"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"执行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"sx_wh","text":"详情-执行依据文号"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-执行内容"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"wlmoney","text":"详情-未履行金额"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"state","text":"详情-收录时案件状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"zblong","text":"详情-终本日期"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"失信被执行信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'shixin_store',id:'shixin',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'gridcolumn',"dataIndex":"apply","text":"申请执行人"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"涉案金额"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"执行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"sx_wh","text":"详情-执行依据文号"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-执行内容"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"wlmoney","text":"详情-未履行金额"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"state","text":"详情-收录时案件状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"zblong","text":"详情-终本日期"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"sx_jt","text":"详情-具体情形"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"两限信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'xiangao_store',id:'xiangao',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"执行标的金额"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"履行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-执行内容"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"state","text":"详情-案件状态"}
                    ]

},
                                        { xtype:'grid',dataSource:'xianchu_store',id:'xianchu',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"执行标的金额"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"履行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-执行内容"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"state","text":"详情-案件状态"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"行政违法信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'weifa_store',id:'weifa',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"执行案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"执行法院"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"立案时间"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"金额"},
                                                { xtype:'gridcolumn',"dataIndex":"perstate","text":"履行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"casetopic","text":"详情-事由"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-结果"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"涉税信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'qianshui_store',id:'qianshui',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"税务机关"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"欠税时间"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"金额"},
                                                { xtype:'gridcolumn',"dataIndex":"perstate","text":"履行状态"},
                                                { xtype:'gridcolumn',"dataIndex":"taxtype","text":"所欠税种"}
                    ]

},
                                        { xtype:'grid',dataSource:'feizheng_store',id:'feizheng',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"税务机关"},
                                                { xtype:'gridcolumn',"dataIndex":"state","text":"纳税状态"},
                                                { xtype:'gridcolumn',"dataIndex":"content","text":"内容"}
                    ]

},
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"text":"犯罪信息"}
                    ]

},
                                        { xtype:'grid',dataSource:'zuifan_store',id:'zuifan',"border":0,"pageSize":1,                    
                    columns:[
                                                { xtype:'rownumberer',"width":35,"text":"No."},
                                                { xtype:'gridcolumn',"dataIndex":"cifitype","text":"自然人类型"},
                                                { xtype:'gridcolumn',"dataIndex":"inputdate","text":"查询日期"},
                                                { xtype:'gridcolumn',"dataIndex":"title","text":"标题"},
                                                { xtype:'gridcolumn',"dataIndex":"name","text":"姓名"},
                                                { xtype:'gridcolumn',"dataIndex":"cardid","text":"证件号码"},
                                                { xtype:'gridcolumn',"dataIndex":"accuracy","text":"匹配度"},
                                                { xtype:'gridcolumn',"dataIndex":"casenum","text":"文书案号"},
                                                { xtype:'gridcolumn',"dataIndex":"court","text":"审理机关"},
                                                { xtype:'gridcolumn',"dataIndex":"sslong","text":"审理日期"},
                                                { xtype:'gridcolumn',"dataIndex":"casetopic","text":"事由"},
                                                { xtype:'numbercolumn',"dataIndex":"money","text":"涉案金额"},
                                                { xtype:'gridcolumn',"dataIndex":"perstate","text":"履行状态"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"content","text":"详情-判决结果"},
                                                { xtype:'gridcolumn',"width":120,"dataIndex":"xingqi","text":"详情-刑期"}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,"icon":"toolbar_cls_1","hidden":true,            
            items:[
                                { xtype:'button',id:'tf_view',"icon":"icon_view","text":"网页查看"}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.aply.8012View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'main_panel',"layout":"fit","border":1,"autoScroll":false,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"fit","width":"100%","border":0,"bodyCls":"panel_cls_1",            
            items:[
                                { xtype:'panel',"layout":"border","border":0,                
                items:[
                                        { xtype:'panel',"layout":"fit","border":0,"region":"center",                    
                    items:[
                                                { xtype:'panel',"layout":"border","border":0,                        
                        items:[
                                                        { xtype:'panel',id:'img_panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",                            
                            items:[
                                                                { xtype:'panel',id:'imglist_panel',"layout":"column","border":0,"autoScroll":true},
                                                                { xtype:'panel',id:'scan_panel',"layout":"fit","autoScroll":true}
                            ]
,                            
                            dockedItems:[
                                                                { xtype:'toolbar',"layout":"hbox","border":1,                                
                                items:[
                                                                        { xtype:'label',"cls":"label_title","text":"图片列表"},
                                                                        { xtype:'button',id:'btn_download',"iconCls":"icon_expand","text":"下载"},
                                                                        { xtype:'button',id:'btn_upload_1',"iconCls":"icon_collapse","hidden":true,"disabled":false,"text":"上传文件"},
                                                                        { xtype:'button',id:'btn_upload',"iconCls":"icon_collapse","hidden":true,"disabled":false,"text":"上传照片"},
                                                                        { xtype:'button',id:'btn_upload_2',"iconCls":"icon_collapse","hidden":true,"disabled":false,"text":"上传文件"},
                                                                        { xtype:'button',id:'btn_upload_3',"iconCls":"icon_collapse","hidden":true,"disabled":false,"text":"上传征信报告"},
                                                                        { xtype:'button',id:'btn_remove',"iconCls":"icon_delete","hidden":true,"text":"删除"},
                                                                        { xtype:'button',id:'btn_download_1',"iconCls":"icon_expand","text":"下载当页附件"}
                                ]

}
                            ]

},
                                                        { xtype:'panel',"layout":"fit","height":25,"border":0,"split":false,"collapsible":false,"collapsed":false,"region":"south",                            
                            items:[
                                                                { xtype:'grid',dataSource:'grid_file_store',id:'grid_file',"border":0,"cls":"border_r","multiSelect":true,"selType":"checkboxmodel","forceFit":false,"allowPage":true,"pageSize":20,"groupField":"filetype",                                
                                columns:[
                                                                        { xtype:'rownumberer',"width":30,"text":"No."},
                                                                        { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"serid","text":"流水号"},
                                                                        { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"relserid","text":"关联流水号"},
                                                                        { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"fileurl","text":"文件地址"},
                                                                        { xtype:'gridcolumn',"width":160,"dataIndex":"filename","text":"文件名称"},
                                                                        { xtype:'gridcolumn',"width":160,"dataIndex":"filedescribe","text":"文件描述"},
                                                                        { xtype:'tfoptcolumn',"width":80,"hidden":true,"dataIndex":"filetype","optId":"7013_allFileType","text":"文件类型"},
                                                                        { xtype:'gridcolumn',id:'iptUsrName',"width":100,"dataIndex":"iptUsrName","text":"上传人"},
                                                                        { xtype:'gridcolumn',id:'iptBrnoName',"width":140,"dataIndex":"iptBrnoName","text":"上传机构"},
                                                                        { xtype:'gridcolumn',"dataIndex":"iptDate","text":"上传日期"},
                                                                        { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"fileprefix","text":"文件后缀"},
                                                                        { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"mediaurl","text":"影像平台地址"}
                                ]

}
                            ]

}
                        ]

}
                    ]

},
                                        { xtype:'panel',"layout":"fit","width":360,"border":0,"region":"east",                    
                    items:[
                                                { xtype:'panel',"layout":"card","border":0,                        
                        items:[
                                                        { xtype:'panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",                            
                            items:[
                                                                { xtype:'form',id:'form_detail',"border":0,"bodyCls":"panel_cls_1","hidden":false,                                
                                items:[
                                                                        { xtype:'fieldset',"margin":"5px","border":1,"bodyStyle":"background-color:#000000;","title":"<文件信息>",                                    
                                    items:[
                                                                                { xtype:'panel',"layout":"column","border":0,"bodyCls":"panel_cls_1",                                        
                                        items:[
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"filename","fieldLabel":"文件名称"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","hidden":true,"name":"filetype","optId":"7013_201001","fieldLabel":"文件类型"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","hidden":true,"name":"filetypedec","optId":"7013_200112","fieldLabel":"类型描述"},
                                                                                        { xtype:'textareafield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"filedescribe","fieldLabel":"文件描述"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptUsrName","fieldLabel":"上传人"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptBrnoName","fieldLabel":"上传机构"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptDate","fieldLabel":"上传时间"}
                                        ]

}
                                    ]

}
                                ]

}
                            ]

}
                        ]

}
                    ]

}
                ]

}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","border":1,                
                items:[
                                        { xtype:'label',"cls":"label_title","text":"审贷详情附件列表","value":"变更大事记"}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.aply.8012View.win_upload',{ extend:'Ext.window.Window', alias:'win_upload'
    ,id:'win_upload',"layout":"fit","width":700,"height":330,"modal":true,"constrain":true,"resizable":false,"title":"上传文件",    
    items:[
    ]

    });



    Ext.define('mvc.view.aply.1508View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'tf_center',"layout":"fit","border":1,"autoScroll":false,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'tabpanel',id:'main_tab',"layout":"fit","border":0,"hidden":false,            
            items:[
                                { xtype:'panel',id:'panel_1',"layout":"fit","border":0,"autoScroll":false,"title":"客户经理决策建议",                
                items:[
                                        { xtype:'form',id:'form_result',"border":0,"autoScroll":true,                    
                    items:[
                                                { xtype:'fieldset',id:'fds_1',"margin":"5px","collapsible":true,"title":"<客户基本信息>",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'textfield',id:'applyno',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"applyno","fieldLabel":"申请编号"},
                                                                { xtype:'textfield',id:'cifid',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"cifid","fieldLabel":"客户编号"},
                                                                { xtype:'textfield',id:'cliname',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"cliname","fieldLabel":"客户名称"},
                                                                { xtype:'textfield',"hidden":true,"name":"serid","fieldLabel":"唯一标识"}
                            ]

}
                        ]

},
                                                { xtype:'fieldset',id:'fds_2',"margin":"5px","collapsible":true,"title":"<客户经理建议>",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfnumberfield',id:'tn_adviceamount',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"vtype":"vExp","vcompare":"pre_montn_pay_money","allowDecimals":true,"decimalPrecision":2,"name":"adviceamount","fieldLabel":"建议贷款金额","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                                { xtype:'tfnumberfield',id:'tf_advicelimit',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"vtype":"vExp","allowDecimals":false,"name":"advicelimit","fieldLabel":"建议贷款期限","unit":1,"tiptype":"normal"},
                                                                { xtype:'tfcombobox',id:'tf_advisereturnstyle',"columnWidth":0.333,"allowBlank":false,"name":"advisereturnstyle","fieldLabel":"还款方式"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfcombobox',id:'tf_adviseguartype',"columnWidth":0.333,"allowBlank":false,"name":"adviseguartype","optId":"7013_new1059_#","fieldLabel":"担保方式"},
                                                                { xtype:'tfnumberfield',id:'pre_month_pay_money',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"allowDecimals":true,"decimalPrecision":2,"name":"monthpaysum","fieldLabel":"每月还款金额","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                                { xtype:'tfnumberfield',id:'tn_busdebttrate',"columnWidth":0.333,"allowBlank":false,"maxLength":10,"vtype":"vExp","readOnly":true,"allowDecimals":true,"decimalPrecision":6,"name":"busdebttrate","fieldLabel":"企业资产负债率","unit":1,"postfix":"%","tiptype":"normal","minValue":0.000001,"maxValue":999.999999}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfnumberfield',id:'tn_debttrate',"columnWidth":0.333,"allowBlank":false,"maxLength":10,"vtype":"vExp","readOnly":true,"allowDecimals":true,"decimalPrecision":6,"name":"debttrate","fieldLabel":"调整后企业资产负债率(60%)","unit":1,"postfix":"%","tiptype":"normal","minValue":0.000001,"maxValue":999.999999},
                                                                { xtype:'tfnumberfield',id:'tn_repabilityrate',"columnWidth":0.333,"allowBlank":false,"maxLength":10,"vtype":"vExp","readOnly":true,"allowDecimals":true,"decimalPrecision":6,"name":"repabilityrate","fieldLabel":"还款能力比率(70%)","unit":1,"postfix":"%","tiptype":"normal","minValue":0,"maxValue":999.999999},
                                                                { xtype:'tfnumberfield',id:'tn_alldebttrate',"columnWidth":0.333,"allowBlank":false,"maxLength":10,"vtype":"vExp","readOnly":true,"allowDecimals":true,"decimalPrecision":6,"name":"alldebttrate","fieldLabel":"调整后总资产负债率(50%)","unit":1,"postfix":"%","tiptype":"normal","minValue":0.000001,"maxValue":999.999999}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'textareafield',id:'tf_guardesc',"height":40,"columnWidth":1,"name":"guardesc","fieldLabel":"抵押物描述（如有）"},
                                                                { xtype:'textareafield',id:'tf_overreason',"height":40,"columnWidth":1,"name":"overreason","fieldLabel":"超标发放的原因"}
                            ]

}
                        ]

}
                    ]

}
                ]
,                
                dockedItems:[
                                        { xtype:'toolbar',"height":27,"minHeight":27,"maxHeight":27,"cls":"toolbar_cls_1",                    
                    items:[
                                                { xtype:'label',"cls":"label_title"},
                                                { xtype:'tbseparator'},
                                                { xtype:'button',id:'btn_save',"icon":"/ThemeUi/Shared/icons/fugue/disk-black.png","text":"保存"}
                    ]

}
                ]

},
                                { xtype:'panel',id:'panel_2',"layout":"fit","border":0,"autoScroll":true,"hidden":true,"title":"一级信贷委员会审批意见",                
                items:[
                                        { xtype:'form',id:'form_result2',"border":0,"autoScroll":true,                    
                    items:[
                                                { xtype:'fieldset',id:'fds_3',"margin":"5px","collapsible":true,"title":"<客户基本信息>",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'textfield',id:'tf_applynos',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"applynos","fieldLabel":"申请编号"},
                                                                { xtype:'textfield',id:'tf_cifids',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"cifids","fieldLabel":"客户编号"},
                                                                { xtype:'textfield',id:'tf_clinames',"columnWidth":0.333,"fieldCls":"field_cls_2","readOnly":true,"name":"clinames","fieldLabel":"客户名称"},
                                                                { xtype:'textfield',"hidden":true,"name":"serid","fieldLabel":"唯一标识"}
                            ]

}
                        ]

},
                                                { xtype:'fieldset',id:'fds_4',"margin":"5px","collapsible":true,"title":"<决策建议>",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'datefield',id:'tf_approvaldate',"columnWidth":0.333,"allowBlank":false,"name":"approvaldate","fieldLabel":"审批日期","format":"Ymd"},
                                                                { xtype:'tfcombobox',id:'tf_approvaladvise',"columnWidth":0.333,"allowBlank":false,"name":"approvaladvise","optId":"7013_new1060_#","fieldLabel":"审批建议"}
                            ]

},
                                                        { xtype:'panel',id:'pal_1',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfnumberfield',id:'tf_loansum',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"allowDecimals":true,"decimalPrecision":2,"name":"loansum","fieldLabel":"金额","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                                { xtype:'tfnumberfield',id:'tf_loanterm',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"vtype":"vExp","allowDecimals":false,"name":"loanterm","fieldLabel":"期限","unit":1,"tiptype":"normal"},
                                                                { xtype:'tfnumberfield',id:'tf_loanrate',"columnWidth":0.333,"allowBlank":false,"maxLength":9,"allowDecimals":true,"decimalPrecision":4,"name":"loanrate","fieldLabel":"利率","unit":1,"postfix":"%","tiptype":"normal","minValue":0,"maxValue":99999999999999.99},
                                                                { xtype:'tfnumberfield',id:'tf_loanfee',"columnWidth":0.333,"allowBlank":false,"maxLength":17,"allowDecimals":true,"decimalPrecision":2,"name":"loanfee","fieldLabel":"手续费","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                                { xtype:'tfcombobox',id:'tf_isoveruse',"columnWidth":0.333,"allowBlank":false,"name":"isoveruse","optId":"7013_290001_#","fieldLabel":"是否多用一个月"},
                                                                { xtype:'tfcombobox',id:'tf_guartype',"columnWidth":0.333,"allowBlank":false,"name":"guartype","optId":"7013_new1059_#","fieldLabel":"担保方式"}
                            ]

},
                                                        { xtype:'panel',id:'pal_2',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfcombobox',id:'tf_isguarrate',"columnWidth":0.333,"name":"isguarrate","optId":"7013_290001_#","fieldLabel":"抵押率是否足值"},
                                                                { xtype:'tfcombobox',id:'tf_repaytype',"columnWidth":0.333,"allowBlank":false,"name":"repaytype","fieldLabel":"还款方式"},
                                                                { xtype:'textfield',id:'tf_loanuser',"columnWidth":0.333,"allowBlank":false,"name":"loanuser","fieldLabel":"贷款签字人"}
                            ]

},
                                                        { xtype:'panel',id:'pal_3',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'textareafield',id:'tf_guarinfodesc',"height":40,"columnWidth":1,"name":"guarinfodesc","fieldLabel":"抵押物描述"},
                                                                { xtype:'textareafield',id:'tf_othrequire',"height":40,"columnWidth":1,"name":" othrequire","fieldLabel":"其他要求"},
                                                                { xtype:'textareafield',id:'tf_rejreason',"height":40,"columnWidth":1,"hidden":true,"name":" rejreason","fieldLabel":"拒绝原因"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                            ]

}
                        ]

}
                    ]

}
                ]
,                
                dockedItems:[
                                        { xtype:'toolbar',"height":27,"minHeight":27,"maxHeight":27,"cls":"toolbar_cls_1",                    
                    items:[
                                                { xtype:'label',"cls":"label_title"},
                                                { xtype:'tbseparator'},
                                                { xtype:'button',id:'btn_save2',"icon":"/ThemeUi/Shared/icons/fugue/disk-black.png","text":"保存"}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });

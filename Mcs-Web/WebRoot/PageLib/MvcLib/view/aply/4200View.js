

    Ext.define('mvc.view.aply.4200View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"region":"center","collapsed":false,"collapsible":false,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","border":1,"dock":"top",            
            items:[
                                { xtype:'label',"text":"贷后征信延期待审批申请列表","cls":"label_title"},
                                { xtype:'tbseparator'}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","border":1,"dock":"top","cls":"toolbar_cls_1",            
            items:[
                                { xtype:'textfield',id:'tf_contno',"width":220,"padding":"0 0 0 10px","maxLength":32,"fieldLabel":"合同编号","labelWidth":70},
                                { xtype:'textfield',id:'tf_cliname',"width":150,"padding":"0 0 0 10px","maxLength":64,"fieldLabel":"客户名称","labelWidth":70},
                                { xtype:'textfield',id:'tf_instname',"width":150,"padding":"0 0 0 10px","maxLength":64,"fieldLabel":"机构名称","labelWidth":70},
                                { xtype:'textfield',id:'tf_mngname',"width":150,"padding":"0 0 0 10px","maxLength":64,"fieldLabel":"客户经理名称","labelWidth":70},
                                { xtype:'textfield',id:'tf_overday',"width":150,"padding":"0 0 0 10px","maxLength":64,"fieldLabel":"逾期天数","labelWidth":70},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_sel',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_res',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","border":0,"dock":"top",            
            items:[
                                { xtype:'button',id:'tf_eff',"iconCls":"icon_add","text":"提交生效","disabled":true},
                                { xtype:'button',id:'btn_flow',"iconCls":"icon_hand-point-090","text":"签署意见并提交","disabled":true},
                                { xtype:'button',id:'tf_info',"iconCls":"icon_add","text":"查看详情","disabled":true},
                                { xtype:'button',id:'tf_receive',"iconCls":"icon_add","text":"领取征信池任务","disabled":true}
            ]

}
        ]
,        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":1,"selType":"checkboxmodel","allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'rownumberer',"width":30,"minWidth":30,"text":"No."},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"applyno","text":"申请号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"contno","text":"合同编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"vchno","text":"借据编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cliname","text":"客户姓名"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"instname","text":"所属机构"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"mngname","text":"所属机构客户经理"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"prdtName","text":"产品"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"begindate","text":"放款时间"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"busSum","text":"放款金额"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"currcnt","text":"还款期次"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"rependdate","text":"当月还款日"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"changerReason","text":"申请原因","hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"extstartdate","text":"调整前宽限期"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"extenddate","text":"调整后宽限期"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"applynum","text":"延期次数","hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"appSts","text":"申请状态","hidden":true},
                                { xtype:'gridcolumn',"dataIndex":"recphaseno","text":"阶段编号","hideable":true,"hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"term","text":"期限","hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"repayType","text":"还款方式","hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"leaveTerm","text":"剩余期限","hidden":true},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"busBal","text":"余额","hidden":true},
                                { xtype:'gridcolumn',"dataIndex":"currOperid","text":"当前节点审批人编号","hideable":true,"hidden":true},
                                { xtype:'gridcolumn',"dataIndex":"flowno","text":"流程号","hideable":true,"hidden":true},
                                { xtype:'gridcolumn',"dataIndex":"serid","text":"流水号","hideable":true,"hidden":true}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.aply.4200View.win_1',{ extend:'Ext.window.Window', alias:'win_1'
    ,id:'win_1',"width":"80%","height":"60%","layout":"fit","modal":true,"constrain":true,"resizable":false,    
    items:[
                { xtype:'form',id:'form_info',"layout":"form","border":0,"bodyPadding":"0px 0px 10px 0px","autoScroll":true,        
        items:[
                        { xtype:'fieldset',"layout":"fit","margin":"0px","maxWidth":800,"columnWidth":0.5,            
            items:[
                                { xtype:'panel',"layout":"column","border":0,"bodyPadding":"5px  20px 0px 0px",                
                items:[
                                        { xtype:'textfield',id:'tf_1',"columnWidth":0.333,"value":"vchno","fieldLabel":"借据编号","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_2',"columnWidth":0.333,"value":"cifid","fieldLabel":"客户编号","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_3',"columnWidth":0.333,"value":"cliname","fieldLabel":"客户姓名","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_4',"columnWidth":0.333,"value":"prdtname","fieldLabel":"产品类型","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_5',"columnWidth":0.333,"value":"busSum","fieldLabel":"放款金额","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_6',"columnWidth":0.333,"value":"term","fieldLabel":"期限","readOnly":true,"labelWidth":70},
                                        { xtype:'tfcombobox',id:'tf_7',"optId":"7013_250029","name":"repayType","columnWidth":0.33,"fieldLabel":"还款方式","readOnly":true,"multiSelect":false,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_8',"columnWidth":0.333,"value":"rependdate","fieldLabel":"当月还款日","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_9',"columnWidth":0.333,"value":"busBal","fieldLabel":"在贷余额","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_10',"columnWidth":0.333,"value":"surplusTerm","fieldLabel":"剩余期限","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_11',"columnWidth":0.333,"value":"applynum","fieldLabel":"延期次数","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_12',"columnWidth":0.333,"value":"appSts","fieldLabel":"申请状态","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_13',"columnWidth":0.333,"value":"extdate","fieldLabel":"调整前宽限期","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_14',"columnWidth":0.333,"value":"newExtdate","fieldLabel":"调整后宽限期","readOnly":true,"labelWidth":70},
                                        { xtype:'textfield',id:'tf_applyno',"columnWidth":0.333,"value":"applyno","fieldLabel":"申请编号","hidden":true,"readOnly":true,"labelWidth":70}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,"bodyPadding":"5px  20px 0px 0px",                
                items:[
                                        { xtype:'textareafield',id:'tf_reason',"name":"reason","columnWidth":0.8,"allowBlank":false,"fieldLabel":"申请原因","readOnly":true}
                ]

},
                                { xtype:'panel',"layout":"fit","border":0,"bodyPadding":"5px  20px 0px 0px",                
                items:[
                                        { xtype:'grid',dataSource:'tf_record_store',id:'tf_record',"border":1,"selType":"checkboxmodel","allowPage":true,"pageSize":20,                    
                    columns:[
                                                { xtype:'gridcolumn',"dataIndex":"phasename","text":"阶段"},
                                                { xtype:'gridcolumn',"dataIndex":"phasechoice","text":"意见选择"},
                                                { xtype:'gridcolumn',"dataIndex":"phaseoptions","text":"意见描述"}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","border":0,"dock":"top",            
            items:[
                                { xtype:'label',"text":"申请说明","cls":"label_title"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","border":0,"dock":"bottom",            
            items:[
                                { xtype:'button',id:'tf_win1_cancel',"width":80,"icon":"/ThemeUi/Shared/icons/fam/cancel.png","text":"取消"},
                                { xtype:'button',id:'tf_win1_eff',"width":80,"iconCls":"icon_add","text":"提交生效","hidden":true}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.aply.6009View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'cp_main',"layout":"fit","border":1,"autoScroll":false,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',id:'panel_1',"layout":"fit","border":0,"autoScroll":false,            
            items:[
                                { xtype:'form',id:'form_main',"border":0,"autoScroll":true,                
                items:[
                                        { xtype:'fieldset',id:'fds_1',"margin":"5px","collapsible":true,"hidden":true,"disabled":true,"title":"<总利润表(元/月)>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_incometotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"incometotal","value":"0","fieldLabel":"销售收入","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_costtotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"costtotal","value":"0","fieldLabel":"销售成本","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_profittotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"profittotal","value":"0","fieldLabel":"毛利润","unit":1,"postfix":"元","tiptype":"money"}
                        ]

},
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_feetotal',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"feetotal","fieldLabel":"费用(含贷款支出)","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_othopersum',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"othopersum","fieldLabel":"其他收入","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_netprofit',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"netprofit","fieldLabel":"可支配净利润","unit":1,"postfix":"元","tiptype":"money","maxValue":99999999999999.99}
                        ]

},
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_famIncome',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"famIncome","fieldLabel":"家庭收入","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_fampay',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"fampay","fieldLabel":"家庭支出","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_famnetincome',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"famnetincome","fieldLabel":"家庭净收入","unit":1,"postfix":"元","tiptype":"money","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_netincome',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"netincome","fieldLabel":"可支配净收入合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_2',"margin":"5px","collapsible":true,"hidden":true,"disabled":true,"title":"<企业资产>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_cashdeposittotal',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"cashdeposittotal","fieldLabel":"现金存款合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_receivetotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"receivetotal","fieldLabel":"应收合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_advpaytotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"advpaytotal","fieldLabel":"预付合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_stocktotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"stocktotal","fieldLabel":"存货合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_othtotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"othtotal","fieldLabel":"租金/押金/保证金","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_flowassetstotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"flowassetstotal","fieldLabel":"企业流动资产","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

},
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_fixedtotal',"columnWidth":0.333,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"fixedtotal","value":"0","fieldLabel":"固定资产合计","unit":1,"postfix":"元","tiptype":"money"},
                                                        { xtype:'tfnumberfield',id:'tf_busassetstotal',"columnWidth":0.333,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"busassetstotal","value":"0","fieldLabel":"企业总资产","unit":1,"postfix":"元","tiptype":"money"}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_9',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<家庭资产>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_famassetstotal',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"name":"famassetstotal","fieldLabel":"家庭总资产","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_3',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<总资产>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_assetstotal',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"name":"assetstotal","fieldLabel":"总资产","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_4',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<企业负债>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_sdebttotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"sdebttotal","fieldLabel":"企业短期负债合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_ldebttotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"ldebttotal","fieldLabel":"企业长期负债合计","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_busdebttotal',"columnWidth":0.333,"maxLength":17,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"busdebttotal","value":"0","fieldLabel":"企业总负债","unit":1,"postfix":"元","tiptype":"money"}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_5',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<家庭负债>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_famdebttotal',"columnWidth":0.333,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"famdebttotal","value":"0","fieldLabel":"家庭负债","unit":1,"postfix":"元","tiptype":"money"}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_6',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<总负债>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_debttotal',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"name":"debttotal","fieldLabel":"总负债","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_8',"layout":"fit","margin":"5px","hidden":true,"disabled":true,"title":"<净资产>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_busnetassets',"columnWidth":0.333,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"busnetassets","value":"0","fieldLabel":"企业净资产","unit":1,"postfix":"元","tiptype":"money"},
                                                        { xtype:'tfnumberfield',id:'tf_famnetassets',"columnWidth":0.333,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"famnetassets","value":"0","fieldLabel":"家庭净资产","unit":1,"postfix":"元","tiptype":"money"},
                                                        { xtype:'tfnumberfield',id:'tf_allnetassets',"columnWidth":0.333,"allowBlank":true,"maxLength":17,"readOnly":true,"name":"allnetassets","fieldLabel":"总净资产","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99}
                        ]

}
                    ]

},
                                        { xtype:'fieldset',id:'fds_7',"layout":"fit","margin":"5px","title":"<财务指标>",                    
                    items:[
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_tfreturnrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"returnrate","fieldLabel":"月资产回报率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_netprofitrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"netprofitrate","fieldLabel":"净利润率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_flowrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"flowrate","fieldLabel":"流动比率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_fastactrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"fastactrate","fieldLabel":"速动比例","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_stockday',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":false,"name":"stockday","fieldLabel":"存货周转天数","unit":1,"tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_receiveday',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":false,"name":"receiveday","fieldLabel":"应收款周转天数","unit":1,"tiptype":"normal","maxValue":99999999999999.99}
                        ]

},
                                                { xtype:'panel',"layout":"column","border":0,                        
                        items:[
                                                        { xtype:'tfnumberfield',id:'tf_probusdebtrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"probusdebtrate","fieldLabel":"调整前企业负债率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_aftbusdebtrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"aftbusdebtrate","fieldLabel":"调整后企业负债率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_aftalldebtrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"aftalldebtrate","fieldLabel":"调整后的总负债率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_laonmrepay',"columnWidth":0.333,"allowBlank":true,"maxLength":16,"readOnly":true,"allowDecimals":true,"decimalPrecision":2,"name":"laonmrepay","fieldLabel":"本次贷款月还","unit":1,"postfix":"元","tiptype":"money","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_repayabilityrate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"repayabilityrate","fieldLabel":"还款能力比率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99},
                                                        { xtype:'tfnumberfield',id:'tf_repincomerate',"columnWidth":0.333,"allowBlank":true,"maxLength":9,"readOnly":true,"allowDecimals":true,"decimalPrecision":4,"name":"repincomerate","fieldLabel":"每月还款额与日营业收入比率","unit":1,"postfix":"%","tiptype":"normal","maxValue":99999999999999.99}
                        ]

}
                    ]

}
                ]

}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"height":27,"minHeight":27,"maxHeight":27,"cls":"toolbar_cls_1",                
                items:[
                                        { xtype:'label',"cls":"label_title"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_edit',"iconCls":"icon_disk-black","text":"保存"}
                ]

}
            ]

}
        ]

}
    ]

    });

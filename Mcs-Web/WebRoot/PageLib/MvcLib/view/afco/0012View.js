

    Ext.define('mvc.view.afco.0012View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"region":"center",        
        items:[
                        { xtype:'form',id:'form_task',"border":0,"autoScroll":true,            
            items:[
                                { xtype:'fieldset',"margin":"5px","border":0,                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',"columnWidth":0.35,"readOnly":true,"name":"misnNo","fieldLabel":"任务编号"},
                                                { xtype:'textfield',"columnWidth":0.325,"readOnly":true,"name":"cifid","fieldLabel":"客户编号"},
                                                { xtype:'textfield',"columnWidth":0.325,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_launchUsrName',"columnWidth":0.35,"readOnly":true,"name":"launchUsrName","fieldLabel":"发起人"},
                                                { xtype:'datefield',"columnWidth":0.325,"readOnly":true,"name":"launchDate","fieldLabel":"发起日期"},
                                                { xtype:'textfield',id:'tf_launchBrnoName',"columnWidth":0.325,"readOnly":true,"name":"launchBrnoName","fieldLabel":"发起机构"},
                                                { xtype:'textfield',"columnWidth":0.33,"hidden":true,"readOnly":true,"name":"launchUsr","fieldLabel":"发起人"},
                                                { xtype:'textfield',"columnWidth":0.33,"hidden":true,"readOnly":true,"name":"launchBrno","fieldLabel":"发起机构"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textareafield',"columnWidth":0.99,"maxLength":512,"name":"taskBrf","fieldLabel":"催收任务说明"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfselectfield',id:'tf_iptUsrNa',"columnWidth":0.33,"allowBlank":false,"name":"iptUsrNa","fieldLabel":"选择执行人"},
                                                { xtype:'textfield',id:'tf_iptUsr',"allowBlank":true,"hidden":true,"name":"iptUsr","fieldLabel":"执行人编号"},
                                                { xtype:'textfield',id:'tf_iptBrna',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"iptBrna","fieldLabel":"执行机构"},
                                                { xtype:'textfield',id:'tf_iptBrno',"hidden":true,"name":"iptBrno","fieldLabel":"执行机构编号"}
                    ]

}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,            
            items:[
                                { xtype:'label',"cls":"label_title","text":"任务详情"},
                                { xtype:'button',id:'btn_save',"iconCls":"icon_disk-black","text":"保存"}
            ]

}
        ]

},
                { xtype:'panel',"layout":"fit","height":200,"border":1,"autoScroll":false,"split":true,"collapsible":false,"collapsed":false,"region":"south",        
        items:[
                        { xtype:'grid',dataSource:'grid_task_store',id:'grid_task',"border":0,"forceFit":false,"allowPage":true,"pageSize":10,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":135,"dataIndex":"contno","text":"合同号"},
                                { xtype:'gridcolumn',"minWidth":80,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"prdtNa","text":"业务品种"},
                                { xtype:'gridcolumn',"minWidth":80,"hidden":true,"dataIndex":"prdtNo","text":"业务品种"},
                                { xtype:'numbercolumn',"minWidth":80,"dataIndex":"busSum","text":"放款金额"},
                                { xtype:'gridcolumn',"minWidth":80,"dataIndex":"begindate","text":"放款时间"},
                                { xtype:'numbercolumn',"minWidth":80,"dataIndex":"busBal","text":"贷款余额"},
                                { xtype:'numbercolumn',"minWidth":80,"dataIndex":"overBal","text":"逾期金额"},
                                { xtype:'tfoptcolumn',"minWidth":80,"dataIndex":"clsfive","optId":"7013_220094","text":"五级分类结果"},
                                { xtype:'gridcolumn',"minWidth":80,"dataIndex":"manageInstName","text":"经办机构"},
                                { xtype:'gridcolumn',"minWidth":80,"dataIndex":"manageOperName","text":"信贷经理"},
                                { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"afbcTreeId"},
                                { xtype:'gridcolumn',"minWidth":80,"hidden":true,"dataIndex":"manageOperid","text":"信贷经理"},
                                { xtype:'gridcolumn',"minWidth":80,"hidden":true,"dataIndex":"manageInstcode","text":"经办机构"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"dock":"top","border":1,            
            items:[
                                { xtype:'button',id:'btn_contno',"iconCls":"icon_document--arrow","hidden":true,"text":"合同详情"}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.zxks.0000View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'main_panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"fit","border":0,            
            items:[
                                { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"multiSelect":false,"selType":"checkboxmodel","allowPage":true,"pageSize":20,                
                columns:[
                                        { xtype:'rownumberer',"width":40,"text":"No."},
                                        { xtype:'gridcolumn',"hidden":true,"dataIndex":"serid","text":"试题编号"},
                                        { xtype:'gridcolumn',"hidden":true,"dataIndex":"clsno","text":"试题分类"},
                                        { xtype:'gridcolumn',"dataIndex":"clsname","text":"试题分类"},
                                        { xtype:'tfoptcolumn',"dataIndex":"type","optId":"7013_TEST01","text":"试题类型"},
                                        { xtype:'gridcolumn',"width":400,"dataIndex":"content","text":"题干"},
                                        { xtype:'gridcolumn',"dataIndex":"optiona","text":"选项A"},
                                        { xtype:'gridcolumn',"dataIndex":"optionb","text":"选项B"},
                                        { xtype:'gridcolumn',"dataIndex":"optionc","text":"选项C"},
                                        { xtype:'gridcolumn',"dataIndex":"optiond","text":"选项D"},
                                        { xtype:'gridcolumn',"dataIndex":"optione","text":"选项E"},
                                        { xtype:'gridcolumn',"dataIndex":"optionf","text":"选项F"},
                                        { xtype:'gridcolumn',"width":50,"dataIndex":"score","text":"分值"},
                                        { xtype:'gridcolumn',"hidden":true,"dataIndex":"operid","text":"操作人"},
                                        { xtype:'gridcolumn',"dataIndex":"opername","text":"操作人"},
                                        { xtype:'gridcolumn',"dataIndex":"workdate","text":"操作时间"},
                                        { xtype:'gridcolumn',"hidden":true,"dataIndex":"brno","text":"操作机构"},
                                        { xtype:'gridcolumn',"dataIndex":"brnoname","text":"操作机构"}
                ]

}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'tbseparator'},
                                        { xtype:'tfcombobox',id:'tf_clsname',"width":200,"labelWidth":60,"fieldLabel":"试题分类"},
                                        { xtype:'textfield',id:'tf_clsno',"hidden":true},
                                        { xtype:'tfcombobox',id:'tf_type',"width":180,"labelWidth":60,"optId":"7013_TEST01_#","fieldLabel":"试题类型"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                        { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"}
                ]

},
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'button',id:'btn_add',"iconCls":"icon_add","text":"新增试题"},
                                        { xtype:'button',id:'btn_detail',"iconCls":"icon_script_edit","text":"试题详情"},
                                        { xtype:'button',id:'btn_delete',"iconCls":"icon_delete","text":"删除试题"}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.zxks.0000View.win_add',{ extend:'Ext.window.Window', alias:'win_add'
    ,id:'win_add',"layout":"fit","width":650,"height":520,"modal":true,"constrain":true,"resizable":false,"title":"新增试题",    
    items:[
                { xtype:'form',id:'form_add',"layout":"form","border":0,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfselectfield',id:'tfs_clsname',"columnWidth":0.45,"allowBlank":false,"selName":"clsname","fieldLabel":"试题分类"},
                                { xtype:'textfield',id:'hidden_clsno',"columnWidth":0.4,"hidden":true,"name":"clsno","fieldLabel":"试题分类"},
                                { xtype:'tfcombobox',id:'tb_type',"columnWidth":0.45,"allowBlank":false,"dataMode":"write","name":"type","optId":"7013_TEST01_#","fieldLabel":"试题类型"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textareafield',"height":150,"columnWidth":0.9,"allowBlank":false,"name":"content","fieldLabel":"试题内容"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optiona',"columnWidth":0.8,"allowBlank":false,"name":"optiona","fieldLabel":"选项A"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optionb',"columnWidth":0.8,"allowBlank":false,"name":"optionb","fieldLabel":"选项B"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optionc',"columnWidth":0.8,"allowBlank":false,"name":"optionc","fieldLabel":"选项C"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optiond',"columnWidth":0.8,"allowBlank":false,"name":"optiond","fieldLabel":"选项D"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optione',"columnWidth":0.8,"allowBlank":false,"name":"optione","fieldLabel":"选项E"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_optionf',"columnWidth":0.8,"allowBlank":false,"name":"optionf","fieldLabel":"选项F"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tf_choice',"columnWidth":0.4,"allowBlank":false,"optId":"7013_500001_#","fieldLabel":"正确答案"},
                                { xtype:'tfcheckboxgroup',id:'tf_mchoice',"columnWidth":0.8,"allowBlank":false,"optId":"7013_500002","fieldLabel":"正确答案"},
                                { xtype:'textfield',id:'tf_filling',"columnWidth":0.9,"allowBlank":false,"fieldLabel":"正确答案","postfix":"（注：填空题有多个填空，请用\";\"或者\"；\"分开）"},
                                { xtype:'textfield',id:'tf_keyword',"columnWidth":0.9,"allowBlank":false,"name":"keyword","fieldLabel":"关键字","postfix":"（注：填空题有多个填空，请用\";\"或者\"；\"分开）"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfnumberfield',"columnWidth":0.3,"allowBlank":false,"name":"score","fieldLabel":"分值","unit":1,"postfix":"分","tiptype":"normal"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_save',"iconCls":"icon_disk-black","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.zxks.0000View.win_detail',{ extend:'Ext.window.Window', alias:'win_detail'
    ,id:'win_detail',"layout":"fit","width":650,"height":540,"modal":true,"constrain":true,"resizable":false,"title":"试题详情",    
    items:[
                { xtype:'form',id:'form_detail',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"5px 0px 0px 0px","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.45,"hidden":true,"readOnly":true,"name":"serid","fieldLabel":"试题编号"},
                                { xtype:'tfselectfield',id:'tfd_clsname',"columnWidth":0.45,"allowBlank":false,"selName":"clsname","fieldLabel":"试题分类"},
                                { xtype:'textfield',id:'tfd_clsno',"columnWidth":0.4,"hidden":true,"name":"clsno","fieldLabel":"试题分类"},
                                { xtype:'tfcombobox',id:'tfd_type',"columnWidth":0.45,"allowBlank":false,"dataMode":"write","name":"type","optId":"7013_TEST01_#","fieldLabel":"试题类型"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textareafield',"height":150,"columnWidth":0.9,"allowBlank":false,"name":"content","fieldLabel":"试题内容"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optiona',"columnWidth":0.8,"allowBlank":false,"name":"optiona","fieldLabel":"选项A"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optionb',"columnWidth":0.8,"allowBlank":false,"name":"optionb","fieldLabel":"选项B"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optionc',"columnWidth":0.8,"allowBlank":false,"name":"optionc","fieldLabel":"选项C"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optiond',"columnWidth":0.8,"allowBlank":false,"name":"optiond","fieldLabel":"选项D"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optione',"columnWidth":0.8,"allowBlank":false,"name":"optione","fieldLabel":"选项E"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tfd_optionf',"columnWidth":0.8,"allowBlank":false,"name":"optionf","fieldLabel":"选项F"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tfd_choice',"columnWidth":0.4,"allowBlank":false,"optId":"7013_500001_#","fieldLabel":"正确答案"},
                                { xtype:'tfcheckboxgroup',id:'tfd_mchoice',"columnWidth":0.8,"allowBlank":false,"optId":"7013_500002","fieldLabel":"正确答案"},
                                { xtype:'textfield',id:'tfd_filling',"columnWidth":0.9,"allowBlank":false,"fieldLabel":"正确答案","postfix":"（注：填空题有多个填空，请用\";\"或者\"；\"分开）"},
                                { xtype:'textfield',id:'tfd_keyword',"columnWidth":0.9,"allowBlank":false,"name":"keyword","fieldLabel":"关键字","postfix":"（注：填空题有多个填空，请用\";\"或者\"；\"分开）"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfnumberfield',"columnWidth":0.3,"allowBlank":false,"name":"score","fieldLabel":"分值","unit":1,"postfix":"分","tiptype":"normal"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfdisplayfield',"columnWidth":0.3,"hidden":true,"name":"operid","fieldLabel":"操作人"},
                                { xtype:'tfdisplayfield',id:'tfd_operid',"columnWidth":0.33,"fieldCls":"field_cls_2","fieldLabel":"操作人"},
                                { xtype:'tfdisplayfield',"columnWidth":0.3,"fieldCls":"field_cls_2","name":"workdate","fieldLabel":"操作时间"},
                                { xtype:'tfdisplayfield',"columnWidth":0.33,"hidden":true,"name":"brno","fieldLabel":"操作机构"},
                                { xtype:'tfdisplayfield',id:'tfd_brno',"columnWidth":0.36,"fieldCls":"field_cls_2","fieldLabel":"操作机构"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_update',"iconCls":"icon_disk-black","text":"保存"}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.afck.0016View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","autoScroll":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'main_grid_store',id:'main_grid',"border":0,"forceFit":false,"allowPage":true,"pageSize":10,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":140,"dataIndex":"misnNo","text":"任务编号"},
                                { xtype:'gridcolumn',"width":140,"minWidth":60,"dataIndex":"guarNo","text":"抵质押物编号"},
                                { xtype:'gridcolumn',"width":150,"minWidth":60,"dataIndex":"guarName","text":"押品名称"},
                                { xtype:'gridcolumn',"minWidth":80,"dataIndex":"evalnum","text":"评估次数"},
                                { xtype:'gridcolumn',"width":140,"minWidth":80,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"dataIndex":"iptUsrName","text":"登记人员"},
                                { xtype:'gridcolumn',"dataIndex":"iptBrnoName","text":"登记机构"},
                                { xtype:'gridcolumn',"dataIndex":"iptDate","text":"登记日期"},
                                { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"colNo","text":"抵押类型编号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"iptUsr","text":"登记人员"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"iptBrno","text":"登记机构"}
            ]

}
        ]

},
                { xtype:'panel',id:'panel_detail',"minHeight":300,"maxHeight":300,"autoScroll":true,"split":true,"collapsible":true,"collapsed":true,"title":"详细信息","region":"south",        
        items:[
                        { xtype:'form',id:'form_detail',"border":0,"autoScroll":true,            
            items:[
                                { xtype:'fieldset',id:'fieldset_detail',"border":0,"collapsible":false,"collapsed":false,                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',"columnWidth":0.33,"style":"display:none","allowBlank":false,"readOnly":true,"name":"cifid","fieldLabel":"客户编号"},
                                                { xtype:'textfield',id:'tf_evalnum',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"evalnum","fieldLabel":"评估次数"},
                                                { xtype:'textfield',id:'tf_guarNo',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"guarNo","fieldLabel":"抵质押物编号"},
                                                { xtype:'textfield',id:'tf_guarName',"columnWidth":0.33,"allowBlank":false,"readOnly":true,"name":"guarName","fieldLabel":"担保物名称"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'datefield',"columnWidth":0.33,"allowBlank":false,"readOnly":false,"name":"evaluateDate","fieldLabel":"评估日期"},
                                                { xtype:'textfield',"columnWidth":0.33,"allowBlank":false,"hidden":false,"readOnly":false,"name":"evaluateBrno","fieldLabel":"评估机构"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":false,"readOnly":false,"name":"isGuarqualify","optId":"7013_290002","fieldLabel":"保证人有无保证资格"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":false,"readOnly":true,"name":"isEachguan","optId":"7013_290001","fieldLabel":"客户之间相互保证"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":true,"name":"isOvertime","optId":"7013_290001","fieldLabel":"保证责任超过时效"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":true,"name":"isLackofability","optId":"7013_290001","fieldLabel":"保证人保证能力不足"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isUnwilling","optId":"7013_290001","fieldLabel":"保证人不愿履行保证责任"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":false,"readOnly":false,"name":"isIllegal","optId":"7013_290001","fieldLabel":"抵押物不符合法律规定"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":false,"readOnly":false,"name":"isMortnotreg","optId":"7013_290001","fieldLabel":"应办未办抵押登记"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isDiffdeal","optId":"7013_290001","fieldLabel":"抵押物难以处置、变现"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":true,"disabled":false,"name":"isDisputed","optId":"7013_290001","fieldLabel":"属有争议"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isEchomort","optId":"7013_290001","fieldLabel":"重复抵押"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isDamaged","optId":"7013_290001","fieldLabel":"抵押物保管不当、发生毁损"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isSellof","optId":"7013_290001","fieldLabel":"抵押物被转移、变卖"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isWorthless","optId":"7013_290001","fieldLabel":"抵押物价值不足"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isImperfect","optId":"7013_290001","fieldLabel":"抵押合同不完善"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isNoinsure","optId":"7013_290001","fieldLabel":"未办保险手续"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isNolist","optId":"7013_290001","fieldLabel":"没有抵押物清单"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isRightillegal","optId":"7013_290001","fieldLabel":"质押不符合法律"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPlenotreg","optId":"7013_290001","fieldLabel":"应办未办质押登记"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isRightdiffdeal","optId":"7013_290001","fieldLabel":"质押权利难以处置、变现"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isRightdisputed","optId":"7013_290001","fieldLabel":"权属有争议"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isEchople","optId":"7013_290001","fieldLabel":"重复质押"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPledamaged","optId":"7013_290001","fieldLabel":"质物保管不当、毁损"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPlesellof","optId":"7013_290001","fieldLabel":"质物被转移、变卖"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPleworthless","optId":"7013_290001","fieldLabel":"质物价值不足"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPleimperfect","optId":"7013_290001","fieldLabel":"质押合同不完善"},
                                                { xtype:'tfcombobox',"columnWidth":0.33,"labelWidth":200,"allowBlank":true,"readOnly":false,"name":"isPlenoinsure","optId":"7013_290001","fieldLabel":"未办保险手续"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textareafield',"name":"brf","fieldLabel":"存在问题说明"}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });

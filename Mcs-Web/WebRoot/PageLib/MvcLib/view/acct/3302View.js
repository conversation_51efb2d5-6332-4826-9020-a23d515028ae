

    Ext.define('mvc.view.acct.3302View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","autoScroll":false,"region":"center",        
        dockedItems:[
                        { xtype:'toolbar',            
            items:[
                                { xtype:'label',"cls":"label_title","text":"首次检查执行已处理"},
                                { xtype:'tbseparator'},
                                { xtype:'textfield',id:'tf_vchno',"width":190,"labelWidth":60,"name":"vchno","fieldLabel":"借据编号"},
                                { xtype:'textfield',id:'tf_cliname',"width":220,"padding":"0 0 0 10px","labelWidth":60,"name":"cliname","fieldLabel":"客户名称"},
                                { xtype:'tbseparator'},
                                { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox",            
            items:[
                                { xtype:'button',id:'btn_detail',"iconCls":"icon_book-open-text","disabled":true,"text":"业务详情"},
                                { xtype:'button',id:'btn_assign',"icon":"/ThemeUi/Shared/icons/fugue/hand-point-090.png","hidden":true,"disabled":true,"text":"分配主管"}
            ]

}
        ]
,        
        items:[
                        { xtype:'grid',dataSource:'grid_list_store',id:'grid_list',"border":0,"selType":"checkboxmodel","allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'gridcolumn',"width":120,"dataIndex":"misnNo","text":"任务编号"},
                                { xtype:'gridcolumn',"dataIndex":"launchUsrname","text":"分配人员"},
                                { xtype:'gridcolumn',"dataIndex":"launchInstname","text":"处理机构"},
                                { xtype:'gridcolumn',"width":150,"hidden":true,"dataIndex":"applyno","text":"申请号"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"vchno","text":"借据编号"},
                                { xtype:'tfoptcolumn',"hidden":true,"dataIndex":"occurtype","optId":"7013_250055","text":"发生类型"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"prdtNa","text":"贷款产品"},
                                { xtype:'gridcolumn',"width":135,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'gridcolumn',"dataIndex":"famaddr","text":"住址"},
                                { xtype:'gridcolumn',"dataIndex":"mtel","text":"联系电话"},
                                { xtype:'gridcolumn',"width":80,"hideable":true,"hidden":true,"dataIndex":"occurdate","text":"发生日期"},
                                { xtype:'gridcolumn',"width":80,"dataIndex":"begindate","text":"起始日期"},
                                { xtype:'gridcolumn',"width":80,"dataIndex":"enddate","text":"到期日期"},
                                { xtype:'tfoptcolumn',"dataIndex":"curr","optId":"7013_200014","text":"币种"},
                                { xtype:'numbercolumn',"width":135,"dataIndex":"busSum","text":"合同金额"},
                                { xtype:'numbercolumn',"width":135,"hidden":true,"dataIndex":"busBal","text":"合同余额"},
                                { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"prdtNo","text":"产品编号"},
                                { xtype:'gridcolumn',"hideable":true,"hidden":true,"dataIndex":"afbcTreeId"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"creditapplyno","text":"授信申请号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"contno","text":"合同号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"wechatid","text":"微信授权ID"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"opername","text":"检查人"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"instname","text":"检查机构"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"conapplyno","text":"合同申请号"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.acct.3302View.win_assign',{ extend:'Ext.window.Window', alias:'win_assign'
    ,id:'win_assign',"layout":"fit","width":300,"height":200,"modal":true,"constrain":true,"resizable":false,"title":"分配主管",    
    items:[
                { xtype:'form',id:'form_assign',"border":0,        
        items:[
                        { xtype:'panel',"layout":"form","margin":"10px 30px 0 0","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_applyno',"columnWidth":0.5,"flex":0,"labelWidth":100,"readOnly":true,"name":"applyno","fieldLabel":"任务编号"},
                                { xtype:'tfselectfield',id:'tf_operid',"padding":"5 0 5 0px","columnWidth":0.5,"labelWidth":100,"allowBlank":false,"fieldLabel":"主管名称"},
                                { xtype:'textfield',id:'tf_operid_use',"columnWidth":0.7,"labelAlign":"left","maxLength":10,"hidden":true,"name":"operid","fieldLabel":"用户编号"},
                                { xtype:'textfield',id:'tf_instcode',"maxLength":10,"hidden":true,"name":"instcode","fieldLabel":"机构编号"},
                                { xtype:'textfield',id:'tf_instname',"columnWidth":0.5,"flex":0,"labelWidth":100,"readOnly":true,"fieldLabel":"机构名称"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'button',id:'btn_sure',"iconCls":"icon_tick","text":"确定"},
                        { xtype:'button',id:'btn_close',"iconCls":"icon_cancel","text":"取消"}
        ]

}
    ]

    });

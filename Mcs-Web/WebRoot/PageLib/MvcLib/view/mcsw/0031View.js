

    Ext.define('mvc.view.mcsw.0031View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",            
            items:[
                                { xtype:'grid',dataSource:'grid_list_store',id:'grid_list',"border":0,"multiSelect":true,"selType":"checkboxmodel","forceFit":true,"allowPage":false,"pageSize":15,                
                columns:[
                                        { xtype:'rownumberer',"text":"No."},
                                        { xtype:'gridcolumn',"width":135,"dataIndex":"cifid","text":"客户编号"},
                                        { xtype:'gridcolumn',"width":160,"dataIndex":"cliname","text":"客户名称"},
                                        { xtype:'tfoptcolumn',"dataIndex":"certtype","optId":"7013_200079","text":"证件类型"},
                                        { xtype:'gridcolumn',"width":150,"dataIndex":"certno","text":"证件号码"}
                ]
,                
                dockedItems:[
                                        { xtype:'toolbar',"layout":"vbox","dock":"top",                    
                    items:[
                                                { xtype:'toolbar',"layout":"hbox","width":"100%","border":0,"cls":"toolbar_cls_1",                        
                        items:[
                                                        { xtype:'tbseparator'},
                                                        { xtype:'textfield',id:'tb_cifid',"width":190,"labelWidth":60,"fieldLabel":"客户编号"},
                                                        { xtype:'textfield',id:'tb_cliname',"width":190,"padding":"0 0 0 10px","labelWidth":60,"fieldLabel":"客户名称"},
                                                        { xtype:'tbseparator'},
                                                        { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                                        { xtype:'button',id:'btn_reset',"text":"重置"},
                                                        { xtype:'tbfill'},
                                                        { xtype:'button',id:'btn_selected',"iconCls":"icon_tick","text":"确定"}
                        ]

}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.mcsw.0047View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'grid_list_store',id:'grid_list',"border":0,"selType":"checkboxmodel","forceFit":false,"allowPage":true,"pageSize":15,            
            columns:[
                                { xtype:'rownumberer',"width":30,"text":"No."},
                                { xtype:'gridcolumn',"width":135,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'tfoptcolumn',"dataIndex":"cstype","optId":"7013_200001","text":"客户类型"},
                                { xtype:'tfoptcolumn',"dataIndex":"certtype","optId":"7013_200079","text":"证件类型"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"certno","text":"证件号码"}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"vbox","dock":"top",                
                items:[
                                        { xtype:'toolbar',"layout":"hbox","width":"100%","border":0,"cls":"toolbar_cls_1",                    
                    items:[
                                                { xtype:'tbseparator'},
                                                { xtype:'textfield',id:'tb_cifid',"width":190,"labelWidth":60,"fieldLabel":"客户编号"},
                                                { xtype:'textfield',id:'tb_cliname',"width":190,"padding":"0 0 0 10px","labelWidth":60,"fieldLabel":"客户名称"},
                                                { xtype:'tbseparator'},
                                                { xtype:'button',id:'btn_search',"iconCls":"icon_view","text":"查询"},
                                                { xtype:'button',id:'btn_reset',"iconCls":"icon_arrow_undo","text":"重置"},
                                                { xtype:'tbfill'}
                    ]

}
                ]

},
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0},
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'button',id:'btn_ind_add',"iconCls":"icon_add","text":"新增个人客户"},
                                        { xtype:'button',id:'btn_ent_add',"icon":"/ThemeUi/Shared/icons/fam/add.gif","hidden":true,"text":"新增公司客户"},
                                        { xtype:'button',id:'btn_selected_1',"iconCls":"icon_tick","hidden":true,"text":"确定"},
                                        { xtype:'button',id:'btn_selected',"iconCls":"icon_tick","disabled":true,"text":"确定"}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_indBase_add',{ extend:'Ext.window.Window', alias:'win_indBase_add'
    ,id:'win_indBase_add',"layout":"fit","width":650,"height":435,"iconCls":"icon_add","modal":true,"constrain":true,"resizable":false,"title":"个人客户登记",    
    items:[
                { xtype:'form',id:'form_indBase',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_outcifid_ind',"columnWidth":0.98,"allowBlank":false,"readOnly":true,"name":"outcifid","fieldLabel":"ECIF客户号"},
                                { xtype:'tfcombobox',id:'tf_orgnature_ind',"columnWidth":0.95,"hidden":true,"readOnly":true,"disabled":false,"name":"orgnature","optId":"7013_200002","value":"10","fieldLabel":"客户类别"},
                                { xtype:'tfcombobox',id:'tfc_certtype_ind',"columnWidth":0.49,"allowBlank":false,"readOnly":true,"name":"certtype","optId":"7013_220093_#","value":"110","fieldLabel":"证件类型"},
                                { xtype:'hiddenfield',id:'tf_cstype_ind',"columnWidth":0.95,"hidden":true,"name":"cstype","value":"C01","fieldLabel":"客户类型"},
                                { xtype:'textfield',id:'certno_ind',"columnWidth":0.49,"allowBlank":false,"readOnly":true,"name":"certno","fieldLabel":"证件号码"},
                                { xtype:'textfield',"columnWidth":0.98,"hidden":true,"readOnly":true,"name":"checkResult","fieldLabel":"联网核查结果"},
                                { xtype:'textfield',"columnWidth":0.98,"hidden":true,"readOnly":true,"name":"certId","fieldLabel":"证件号唯一标识"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.95,"vtype":"repetition","vcompare":"certno","hidden":true,"allowPaste":false,"name":"certno_1","fieldLabel":"再次确认"},
                                { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"certteddate","fieldLabel":"证件到期日","format":"Ymd"},
                                { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"readOnly":true,"name":"cliname","fieldLabel":"客户名称"},
                                { xtype:'datefield',id:'tf_birthday_ind',"columnWidth":0.49,"allowBlank":false,"name":"birthday","fieldLabel":"出生日期","format":"Ymd"},
                                { xtype:'tfcombobox',id:'tf_sex_ind',"columnWidth":0.49,"allowBlank":false,"name":"sex","optId":"7013_200017_#","fieldLabel":"性别"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tf_nation_ind',"columnWidth":0.49,"allowBlank":false,"name":"nation","optId":"7013_200043","value":"01","fieldLabel":"民族"},
                                { xtype:'tfselectfield',id:'tbs_nationality_ind',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"nationality","fieldLabel":"国籍"},
                                { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":256,"readOnly":false,"name":"workcorp","fieldLabel":"现就职单位"},
                                { xtype:'tfselectfield',id:'tbs_industryname_ind',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"jobTypename","fieldLabel":"单位所属行业"},
                                { xtype:'textfield',id:'tf_industrytype_ind',"columnWidth":0.95,"allowBlank":false,"hidden":true,"name":"jobType","fieldLabel":"单位所属行业"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":0.49,"allowBlank":false,"name":"occupation","optId":"7013_200035_#","fieldLabel":"职业"},
                                { xtype:'tfnumberfield',"columnWidth":0.49,"allowBlank":false,"maxLength":17,"name":"personIncome","fieldLabel":"个人月收入","unit":1,"postfix":"元","tiptype":"money","minValue":0,"maxValue":99999999999999.99},
                                { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":11,"minLength":11,"name":"mtel","fieldLabel":"手机号码"},
                                { xtype:'tfselectfield',id:'tf_regisName',"columnWidth":0.49,"allowBlank":false,"name":"regisName","selName":"regisName","fieldLabel":"户籍行政区划"},
                                { xtype:'textfield',id:'tf_regisCode',"columnWidth":0.95,"allowBlank":false,"maxLength":64,"hidden":true,"name":"regisCode","fieldLabel":"户籍行政区划代码"},
                                { xtype:'textareafield',"columnWidth":0.98,"allowBlank":false,"maxLength":128,"name":"house","fieldLabel":"户籍地址"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'label',id:'tf_not_exists_ind',"padding":"5px","columnWidth":0.95,"text":"<ECIF不存在此客户,录入信息后点击\"保存\"按钮将客户新增至ECIF及微贷系统>"},
                                { xtype:'label',id:'tf_exists_ind',"padding":"5px","columnWidth":0.95,"hidden":true,"text":"<ECIF存在此客户,点击\"保存\"按钮将客户新增至微贷系统>"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"hidden":true,"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_indBase_add_submit',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_entBase_add',{ extend:'Ext.window.Window', alias:'win_entBase_add'
    ,id:'win_entBase_add',"layout":"fit","width":650,"height":500,"iconCls":"icon_add","modal":true,"constrain":true,"resizable":false,"title":"对公客户登记",    
    items:[
                { xtype:'form',id:'form_entBase',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'fieldset',"title":"<客户信息>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',id:'tf_outcifid_ent',"columnWidth":0.98,"allowBlank":false,"readOnly":true,"name":"outcifid","fieldLabel":"ECIF客户号"},
                                        { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"name":"cliname","fieldLabel":"客户名称"},
                                        { xtype:'tfcombobox',"columnWidth":0.49,"hidden":false,"readOnly":true,"name":"certtype","optId":"7013_200004","value":"230","fieldLabel":"证件类型"},
                                        { xtype:'hiddenfield',id:'tf_cstype_ent',"columnWidth":1,"hidden":true,"name":"cstype","value":"A01","fieldLabel":"客户类型"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"readOnly":true,"name":"certno","fieldLabel":"证件号码"},
                                        { xtype:'textfield',"columnWidth":1,"maxLength":32,"vtype":"repetition","vcompare":"certno","hidden":true,"allowPaste":false,"name":"certno_1","fieldLabel":"证件号码确认"},
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"busliceddate","fieldLabel":"证件到期日"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'tfcombobox',"columnWidth":0.49,"allowBlank":false,"name":"ecotistype","optId":"7013_200012","fieldLabel":"单位性质"},
                                        { xtype:'tfselectfield',id:'tbs_industryname_ent',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"industryname","fieldLabel":"国标行业分类"},
                                        { xtype:'textfield',id:'tf_industrytype_ent',"maxLength":16,"hidden":true,"name":"industrytype","fieldLabel":"国标行业分类"},
                                        { xtype:'tfselectfield',id:'tbs_countryname_ent',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"countryname","fieldLabel":"国别"},
                                        { xtype:'textfield',id:'tf_countrycode_ent',"maxLength":16,"hidden":true,"name":"countrycode","fieldLabel":"国别"},
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"certdate","fieldLabel":"企业注册成立日"},
                                        { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"name":"offTel","fieldLabel":"联系电话","tiptype":"normal"},
                                        { xtype:'tfselectfield',id:'tbs_regionname',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"regionname","fieldLabel":"行政区划代码"},
                                        { xtype:'textfield',id:'tf_regioncode',"maxLength":16,"hidden":true,"name":"regioncode","fieldLabel":"行政区划代码"},
                                        { xtype:'textfield',"columnWidth":0.98,"inputType":"text","allowBlank":false,"maxLength":512,"name":"regAddr","fieldLabel":"注册地址"}
                ]

}
            ]

},
                        { xtype:'fieldset',"title":"<法人信息>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.49,"inputType":"text","allowBlank":false,"maxLength":128,"name":"legalperson","fieldLabel":"法人代表"},
                                        { xtype:'tfcombobox',id:'tf_certtype',"columnWidth":0.49,"allowBlank":false,"name":"legalcerttype","optId":"7013_200007_#","value":"110","fieldLabel":"证件类型"},
                                        { xtype:'textfield',id:'tf_legalcertno',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"name":"legalcertno","fieldLabel":"法人证件号"},
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"legalcertedate","fieldLabel":"证件到期日","format":"Ymd"},
                                        { xtype:'tfcombobox',id:'tf_sex',"columnWidth":0.49,"allowBlank":false,"multiSelect":false,"name":"legalsex","optId":"7013_200017_#","fieldLabel":"性别"},
                                        { xtype:'datefield',id:'tf_birthday',"columnWidth":0.49,"allowBlank":false,"name":"legalbirth","fieldLabel":"出生日期","format":"Ymd"},
                                        { xtype:'tfselectfield',id:'tbs_legalnat',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"name":"legalnat","selName":"legalnat","fieldLabel":"国籍"},
                                        { xtype:'textfield',id:'tf_legaltel',"columnWidth":0.49,"allowBlank":false,"maxLength":11,"minLength":11,"name":"legaltel","fieldLabel":"手机号码"}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'label',id:'tf_not_exists_ent',"columnWidth":1,"text":"<ECIF不存在此客户,录入信息后点击\"保存\"按钮将客户新增至ECIF及微贷系统>"},
                                { xtype:'label',id:'tf_exists_ent',"columnWidth":1,"hidden":true,"text":"<ECIF存在此客户,点击\"保存\"按钮将客户新增至微贷系统>"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"hidden":true,"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_entBase_add_submit',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_ecif_check_ind',{ extend:'Ext.window.Window', alias:'win_ecif_check_ind'
    ,id:'win_ecif_check_ind',"layout":"fit","width":500,"height":190,"modal":true,"constrain":true,"resizable":false,"title":"个人证件检测",    
    items:[
                { xtype:'form',id:'form_indBase_check',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":0.98,"allowBlank":false,"readOnly":true,"name":"certtype","optId":"7013_220093_#","value":"110","fieldLabel":"证件类型"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.98,"allowBlank":false,"name":"certno","fieldLabel":"证件号码"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.98,"allowBlank":false,"maxLength":128,"name":"cliname","fieldLabel":"客户名称"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","height":5,"dock":"top","border":0},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"hidden":true,"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_indBase_add_check',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_ecif_check_ent',{ extend:'Ext.window.Window', alias:'win_ecif_check_ent'
    ,id:'win_ecif_check_ent',"layout":"fit","width":500,"height":190,"modal":true,"constrain":true,"resizable":false,"title":"对公证件检测",    
    items:[
                { xtype:'form',id:'form_entBase_check',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',"columnWidth":0.98,"allowBlank":false,"hidden":false,"name":"certtype","optId":"7013_200004","value":"230","fieldLabel":"证件类型"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.98,"allowBlank":false,"name":"certno","fieldLabel":"证件号码"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":0.98,"allowBlank":false,"maxLength":128,"name":"cliname","fieldLabel":"客户名称"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","height":5,"dock":"top","border":0},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"hidden":true,"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_entBase_add_check',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_indBase_sync',{ extend:'Ext.window.Window', alias:'win_indBase_sync'
    ,id:'win_indBase_sync',"layout":"fit","width":550,"height":360,"modal":true,"constrain":true,"resizable":false,"title":"个人客户登记",    
    items:[
                { xtype:'form',id:'form_indBase_sync',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tfc_certtype_sync',"columnWidth":1,"allowBlank":false,"name":"certtype","optId":"7013_200005_#","value":"110","fieldLabel":"证件类型"},
                                { xtype:'textfield',id:'certno_sync',"columnWidth":1,"allowBlank":false,"hidden":false,"name":"certno","fieldLabel":"证件号码"},
                                { xtype:'textfield',id:'certno_sync_sec',"columnWidth":1,"allowBlank":false,"vtype":"repetition","vcompare":"certno_sync","hidden":false,"name":"certnosec","fieldLabel":"证件号码确认"},
                                { xtype:'textfield',id:'tf_cliname_sync',"columnWidth":1,"allowBlank":false,"maxLength":128,"hidden":false,"name":"cliname","fieldLabel":"客户名称"},
                                { xtype:'tfcombobox',id:'tf_sex_sync',"columnWidth":1,"allowBlank":false,"name":"sex","optId":"7013_200017_#","fieldLabel":"性别"},
                                { xtype:'datefield',id:'tf_birthday_sync',"columnWidth":1,"allowBlank":false,"name":"birthday","fieldLabel":"出生日期","format":"Ymd"},
                                { xtype:'textfield',id:'tf_mtel_sync',"columnWidth":1,"allowBlank":false,"maxLength":11,"minLength":11,"hidden":false,"name":"mtel","fieldLabel":"手机号码"},
                                { xtype:'textareafield',id:'tf_house_sync',"columnWidth":1,"allowBlank":false,"maxLength":128,"name":"house","fieldLabel":"户籍地址"},
                                { xtype:'hiddenfield',id:'tf_cstype_sync',"hidden":true,"name":"cstype","value":"C01","fieldLabel":"客户类型"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_indBase_sync_submit',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.mcsw.0047View.win_entBase_add_new',{ extend:'Ext.window.Window', alias:'win_entBase_add_new'
    ,id:'win_entBase_add_new',"layout":"fit","width":650,"height":498,"modal":true,"constrain":true,"resizable":false,"title":"对公客户登记",    
    items:[
                { xtype:'form',id:'form_entBase_new',"layout":"form","bodyPadding":"10px","border":0,"autoScroll":true,"trackResetOnLoad":true,        
        items:[
                        { xtype:'fieldset',"title":"<客户信息>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"name":"cliname","fieldLabel":"客户名称"},
                                        { xtype:'tfcombobox',"columnWidth":0.49,"hidden":false,"readOnly":false,"name":"certtype","optId":"7013_200004","value":"230","fieldLabel":"证件类型"},
                                        { xtype:'hiddenfield',id:'tf_cstype_ent_new',"columnWidth":1,"hidden":true,"name":"cstype","value":"A01","fieldLabel":"客户类型"},
                                        { xtype:'textfield',"columnWidth":0.98,"hidden":true,"readOnly":true,"name":"certId","fieldLabel":"证件号唯一标识"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',id:'certno_new',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"name":"certno","fieldLabel":"证件号码"},
                                        { xtype:'textfield',id:'certno_sec_new',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"vtype":"repetition","vcompare":"certno_new","fieldLabel":"证件号码确认"}
                ]

},
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"busliceddate","fieldLabel":"证件到期日"},
                                        { xtype:'tfcombobox',"columnWidth":0.49,"allowBlank":false,"name":"ecotistype","optId":"7013_200012","fieldLabel":"单位性质"},
                                        { xtype:'tfselectfield',id:'tbs_industryname_ent_new',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"industryname","fieldLabel":"国标行业分类"},
                                        { xtype:'textfield',id:'tf_industrytype_ent_new',"maxLength":16,"hidden":true,"name":"industrytype","fieldLabel":"国标行业分类"},
                                        { xtype:'tfselectfield',id:'tbs_countryname_ent_new',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"countryname","fieldLabel":"国别"},
                                        { xtype:'textfield',id:'tf_countrycode_ent_new',"maxLength":16,"hidden":true,"name":"countrycode","fieldLabel":"国别"},
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"certdate","fieldLabel":"企业注册成立日"},
                                        { xtype:'textfield',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"name":"offTel","fieldLabel":"联系电话","tiptype":"normal"},
                                        { xtype:'tfselectfield',id:'tbs_regionname_ent_new',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"selName":"regionname","fieldLabel":"行政区划代码"},
                                        { xtype:'textfield',id:'tf_regioncode_ent_new',"maxLength":16,"hidden":true,"name":"regioncode","fieldLabel":"行政区划代码"},
                                        { xtype:'tfcombobox',"columnWidth":0.49,"allowBlank":false,"name":"scope","optId":"7013_200006","fieldLabel":"企业规模"},
                                        { xtype:'textfield',"columnWidth":0.98,"inputType":"text","allowBlank":false,"maxLength":512,"name":"regAddr","fieldLabel":"注册地址"}
                ]

}
            ]

},
                        { xtype:'fieldset',"title":"<法人信息>",            
            items:[
                                { xtype:'panel',"layout":"column","border":0,                
                items:[
                                        { xtype:'textfield',"columnWidth":0.49,"inputType":"text","allowBlank":false,"maxLength":128,"name":"legalperson","fieldLabel":"法人代表"},
                                        { xtype:'tfcombobox',id:'tf_certtype_new',"columnWidth":0.49,"allowBlank":false,"name":"legalcerttype","optId":"7013_200005_#","value":"0","fieldLabel":"证件类型"},
                                        { xtype:'textfield',id:'tf_legalcertno_new',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"name":"legalcertno","fieldLabel":"法人证件号"},
                                        { xtype:'textfield',id:'legalcertnosec_1',"columnWidth":0.49,"allowBlank":false,"maxLength":32,"vtype":"repetition","vcompare":"tf_legalcertno_new","fieldLabel":"证件号确认"},
                                        { xtype:'datefield',"columnWidth":0.49,"allowBlank":false,"name":"legalcertedate","fieldLabel":"证件到期日","format":"Ymd"},
                                        { xtype:'tfcombobox',id:'tf_sex_new',"columnWidth":0.49,"allowBlank":false,"multiSelect":false,"name":"legalsex","optId":"7013_200017_#","fieldLabel":"性别"},
                                        { xtype:'datefield',id:'tf_birthday_new',"columnWidth":0.49,"allowBlank":false,"name":"legalbirth","fieldLabel":"出生日期","format":"Ymd"},
                                        { xtype:'tfselectfield',id:'tbs_legalnat_new',"columnWidth":0.49,"allowBlank":false,"maxLength":128,"name":"legalnat","selName":"legalnat","fieldLabel":"国籍"},
                                        { xtype:'textfield',id:'tf_legaltel_new',"columnWidth":0.49,"allowBlank":false,"maxLength":11,"minLength":11,"name":"legaltel","fieldLabel":"手机号码"}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'tfbutton',"tfAction":"form_reset"},
                                { xtype:'button',id:'btn_entBase_add_submit_new',"iconCls":"icon_disk-share","text":"保存"}
            ]

}
        ]

}
    ]

    });

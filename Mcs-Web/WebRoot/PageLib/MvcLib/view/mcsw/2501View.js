

    Ext.define('mvc.view.mcsw.2501View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'center_panel_search',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'grid_main_store',id:'grid_main',"border":0,            
            columns:[
                                { xtype:'gridcolumn',"width":160,"dataIndex":"relserid","text":"业务关联编号"},
                                { xtype:'tfoptcolumn',"width":100,"dataIndex":"reltype","optId":"7013_200200","text":"客户身份"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cifid","text":"客户编号"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"cliname","text":"客户名称"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"certno","text":"证件号"},
                                { xtype:'tfoptcolumn',"width":80,"dataIndex":"sts","optId":"7013_200201","text":"完成状态"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"vbox","dock":"top",            
            items:[
                                { xtype:'toolbar',"layout":"hbox","width":"100%","border":0,                
                items:[
                                        { xtype:'button',id:'btn_del',"iconCls":"icon_delete","disabled":true,"text":"删除"}
                ]

}
            ]

}
        ]

}
    ]

    });

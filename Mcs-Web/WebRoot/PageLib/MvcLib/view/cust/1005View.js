

    Ext.define('mvc.view.cust.1005View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'main_panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
        ]

},
                { xtype:'panel',"layout":"fit","width":"50%","split":true,"collapsible":false,"collapsed":false,"region":"east",        
        items:[
                        { xtype:'panel',"layout":"border","border":0,            
            items:[
                                { xtype:'panel',id:'sub_panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",                
                items:[
                ]
,                
                dockedItems:[
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"cls":"label_title","text":"应加项"},
                                                { xtype:'button',id:'btn_add',"iconCls":"icon_add","text":"新增"},
                                                { xtype:'button',id:'btn_edit',"iconCls":"icon_script_edit","disabled":true,"text":"编辑"},
                                                { xtype:'button',id:'btn_del',"iconCls":"icon_delete","disabled":true,"text":"删除"},
                                                { xtype:'tbfill'},
                                                { xtype:'button',id:'btn_save',"iconCls":"icon_tick","text":"提交修改"}
                    ]

}
                ]

},
                                { xtype:'panel',id:'sub_panel_sec',"layout":"fit","height":"50%","border":0,"split":true,"collapsible":false,"collapsed":false,"region":"south",                
                dockedItems:[
                                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                    
                    items:[
                                                { xtype:'label',"cls":"label_title","text":"应减项"},
                                                { xtype:'button',id:'btn_add_sec',"iconCls":"icon_add","text":"新增"},
                                                { xtype:'button',id:'btn_edit_sec',"iconCls":"icon_script_edit","disabled":true,"text":"编辑"},
                                                { xtype:'button',id:'btn_del_sec',"iconCls":"icon_delete","disabled":true,"text":"删除"},
                                                { xtype:'tbfill'},
                                                { xtype:'button',id:'btn_save_sec',"iconCls":"icon_tick","text":"提交修改"}
                    ]

}
                ]
,                
                items:[
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.cust.1005View.winDetail',{ extend:'Ext.window.Window', alias:'winDetail'
    ,id:'winDetail',"layout":"fit","width":300,"height":150,"modal":true,"constrain":true,"resizable":false,    
    items:[
                { xtype:'form',id:'form_detail',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"allowBlank":true,"hidden":true,"readOnly":true,"name":"itemno","fieldLabel":"指标代码"},
                                { xtype:'textfield',"columnWidth":1,"allowBlank":false,"name":"itemna","fieldLabel":"指标项"},
                                { xtype:'tfnumberfield',"columnWidth":1,"allowDecimals":true,"decimalPrecision":2,"name":"itemvalue","fieldLabel":"指标值","unit":1,"tiptype":"money"},
                                { xtype:'textfield',"columnWidth":1,"hidden":true,"name":"itemtype","fieldLabel":"类型"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'button',id:'btn_cancel',"iconCls":"icon_cancel","text":"取消"},
                                { xtype:'button',id:'btn_submit',"iconCls":"icon_tick","text":"提交"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.cust.1005View.winDetailSec',{ extend:'Ext.window.Window', alias:'winDetailSec'
    ,id:'winDetailSec',"layout":"fit","width":300,"height":150,"modal":true,"constrain":true,"resizable":false,    
    items:[
                { xtype:'form',id:'form_detail_sec',"border":0,        
        items:[
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',"columnWidth":1,"allowBlank":true,"hidden":true,"readOnly":true,"name":"itemno","fieldLabel":"指标代码"},
                                { xtype:'textfield',"columnWidth":1,"allowBlank":false,"name":"itemna","fieldLabel":"指标项"},
                                { xtype:'tfnumberfield',"columnWidth":1,"allowDecimals":true,"decimalPrecision":2,"name":"itemvalue","fieldLabel":"指标值","unit":1,"tiptype":"money"},
                                { xtype:'textfield',"columnWidth":1,"hidden":true,"name":"itemtype","fieldLabel":"类型"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'button',id:'btn_cancel_1',"iconCls":"icon_cancel","text":"取消"},
                                { xtype:'button',id:'btn_submit_1',"iconCls":"icon_tick","text":"提交"}
            ]

}
        ]

}
    ]

    });

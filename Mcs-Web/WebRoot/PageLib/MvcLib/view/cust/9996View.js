

    Ext.define('mvc.view.cust.9996View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'main_panel',"layout":"fit","border":1,"autoScroll":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"fit","width":"100%","border":0,"bodyCls":"panel_cls_1",            
            items:[
                                { xtype:'panel',"layout":"border","border":0,                
                items:[
                                        { xtype:'panel',"layout":"fit","border":0,"region":"center",                    
                    items:[
                                                { xtype:'panel',"layout":"border","border":0,                        
                        items:[
                                                        { xtype:'panel',id:'img_panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",                            
                            items:[
                            ]
,                            
                            dockedItems:[
                                                                { xtype:'toolbar',"layout":"hbox","border":1,                                
                                items:[
                                                                        { xtype:'label',"cls":"label_title","text":"图片列表"},
                                                                        { xtype:'button',id:'btn_upload',"iconCls":"icon_collapse","disabled":false,"text":"上传图片"},
                                                                        { xtype:'button',id:'btn_remove',"iconCls":"icon_delete","disabled":true,"text":"删除"},
                                                                        { xtype:'button',id:'btn_download',"iconCls":"icon_expand","disabled":true,"text":"下载"}
                                ]

}
                            ]

},
                                                        { xtype:'panel',"layout":"fit","height":300,"border":0,"split":true,"collapsible":false,"collapsed":false,"region":"north",                            
                            items:[
                                                                { xtype:'grid',dataSource:'grid_file_store',id:'grid_file',"border":0,"cls":"border_r","selType":"checkboxmodel","forceFit":false,"allowPage":true,"pageSize":20,"groupField":"filetype",                                
                                columns:[
                                                                        { xtype:'rownumberer',"text":"No."},
                                                                        { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"serid","text":"流水号"},
                                                                        { xtype:'gridcolumn',"width":150,"hideable":true,"hidden":true,"dataIndex":"relserid","text":"关联流水号"},
                                                                        { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"fileurl","text":"文件地址"},
                                                                        { xtype:'gridcolumn',"width":160,"dataIndex":"filename","text":"文件名称"},
                                                                        { xtype:'gridcolumn',"width":160,"dataIndex":"filedescribe","text":"文件描述"},
                                                                        { xtype:'tfoptcolumn',"width":80,"dataIndex":"filetype","optId":"7013_200112","text":"文件类型"},
                                                                        { xtype:'gridcolumn',id:'iptUsrName',"width":100,"dataIndex":"iptUsrName","text":"上传人"},
                                                                        { xtype:'gridcolumn',id:'iptBrnoName',"width":140,"dataIndex":"iptBrnoName","text":"上传机构"},
                                                                        { xtype:'gridcolumn',"dataIndex":"iptDate","text":"上传日期"}
                                ]

}
                            ]

}
                        ]

}
                    ]

},
                                        { xtype:'panel',"layout":"fit","width":360,"border":0,"region":"east",                    
                    items:[
                                                { xtype:'panel',"layout":"border","border":0,                        
                        items:[
                                                        { xtype:'panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",                            
                            items:[
                                                                { xtype:'form',id:'form_detail',"border":0,"bodyCls":"panel_cls_1","hidden":false,                                
                                items:[
                                                                        { xtype:'fieldset',"margin":"5px","border":1,"bodyStyle":"background-color:#000000;","title":"文件信息",                                    
                                    items:[
                                                                                { xtype:'panel',"layout":"column","border":0,"bodyCls":"panel_cls_1",                                        
                                        items:[
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"filename","fieldLabel":"文件名称"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","hidden":true,"name":"filetype","fieldLabel":"文件类型"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","hidden":true,"name":"filetypedec","optId":"7013_200112","fieldLabel":"类型描述"},
                                                                                        { xtype:'textareafield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"filedescribe","fieldLabel":"文件描述"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptUsrName","fieldLabel":"上传人"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptBrnoName","fieldLabel":"上传机构"},
                                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"labelWidth":60,"labelAlign":"left","fieldCls":"field_cls_2","name":"iptDate","fieldLabel":"上传时间"}
                                        ]

}
                                    ]

}
                                ]

}
                            ]

},
                                                        { xtype:'panel',"layout":"fit","height":263,"margin":"5px","border":0,"split":false,"collapsible":false,"collapsed":false,"region":"south",                            
                            items:[
                                                                { xtype:'image',id:'tf_image',"width":350,"height":263,"style":"background-color: #DFE9F6"}
                            ]

}
                        ]

}
                    ]

}
                ]

}
            ]
,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","border":1,                
                items:[
                                        { xtype:'label',"cls":"label_title","text":"影像资料","value":"变更大事记"}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.cust.9996View.win_upload',{ extend:'Ext.window.Window', alias:'win_upload'
    ,id:'win_upload',"layout":"fit","width":400,"height":200,"modal":true,"constrain":true,"resizable":true,"title":"上传图片",    
    items:[
                { xtype:'form',id:'form_filedetail',"layout":"column","border":0,        
        items:[
                        { xtype:'fieldset',"layout":"column","margin":"5px","border":0,            
            items:[
                                { xtype:'tfuploadpanel',id:'tf_uppanel',"columnWidth":1,"border":0,"readOnly":false,"fileTypes":"*","files":5,"folder":"/cust"},
                                { xtype:'textfield',"columnWidth":0.7,"allowBlank":false,"name":"filedescribe","fieldLabel":"文件描述"},
                                { xtype:'tfcombobox',"columnWidth":0.7,"allowBlank":false,"name":"filetype","optId":"7013_200112","fieldLabel":"文件类型"},
                                { xtype:'textfield',"columnWidth":0.7,"allowBlank":false,"hidden":true,"readOnly":true,"name":"fieldname","value":"押品编号_权证序号","fieldLabel":"关联字段名称"},
                                { xtype:'textfield',"columnWidth":0.8,"hidden":true,"readOnly":true,"name":"phasename","value":"押品登记","fieldLabel":"阶段名称"},
                                { xtype:'textfield',"columnWidth":0.8,"hidden":true,"readOnly":true,"name":"phaseno","value":"guarreg","fieldLabel":"阶段编号"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'button',id:'btn_cancel',"iconCls":"icon_cancel","text":"取消"},
                        { xtype:'button',id:'btn_submit',"iconCls":"icon_tick","text":"提交"}
        ]

}
    ]

    });

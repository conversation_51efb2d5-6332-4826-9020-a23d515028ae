

    Ext.define('mvc.view.afmg.2007View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',"layout":"fit","border":0,"split":true,"collapsible":false,"collapsed":false,"region":"north",        
        items:[
                        { xtype:'form',id:'tf_afterapply',"border":0,"frame":false,"hidden":false,            
            dockedItems:[
                                { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,                
                items:[
                                        { xtype:'button',id:'btn_cal',"hidden":true},
                                        { xtype:'label',"cls":"label_title","text":"欠款特殊还款落实"},
                                        { xtype:'tbseparator'},
                                        { xtype:'button',id:'btn_auth',"iconCls":"icon_book-open-text","text":"落实变更"},
                                        { xtype:'button',id:'tf_submit',"iconCls":"icon_tick","hidden":true,"text":"落实变更_授权后"},
                                        { xtype:'button',id:'btn_media',"iconCls":"icon_system-monitor--arrow","text":"影像上传"},
                                        { xtype:'tbseparator'},
                                        { xtype:'tbfill'},
                                        { xtype:'button',id:'btn_debtrepay',"iconCls":"icon_expand","hidden":true,"text":"欠款还款凭证打印"},
                                        { xtype:'button',id:'btn_debtrepay_unused',"iconCls":"icon_expand","hidden":true,"text":"欠款还款凭证打印"},
                                        { xtype:'hiddenfield',id:'tf_auth',"hidden":true,"name":"tfAuth","fieldLabel":"授权人号"}
                ]

}
            ]
,            
            items:[
                                { xtype:'fieldset',id:'tf_applyInfo',"margin":"5px","columnWidth":0.99,"collapsible":false,"title":"<申请信息>",                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'tf_applyno',"columnWidth":0.33,"fieldCls":"field_cls_2","readOnly":true,"name":"applyno","fieldLabel":"申请编号"},
                                                { xtype:'tfdisplayfield',id:'tf_changerType',"columnWidth":0.33,"fieldCls":"field_cls_2","readOnly":true,"name":"changerType","optId":"7013_230024","fieldLabel":"业务变更类型"},
                                                { xtype:'tfdisplayfield',id:'tf_loTerm',"columnWidth":0.33,"fieldCls":"field_cls_2","hidden":true,"readOnly":true,"name":"loTerm","fieldLabel":"还款期次"},
                                                { xtype:'tfdisplayfield',id:'ta_vchno',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"vchno","fieldLabel":"借据编号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'tf_changerReason',"columnWidth":0.99,"fieldCls":"field_cls_2","name":"changerReason","fieldLabel":"变更原因"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_cliname',"columnWidth":0.4,"fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"客户名称"},
                                                { xtype:'tfcombobox',id:'tf_flag',"columnWidth":0.3,"labelWidth":60,"hidden":true,"multiSelect":false,"optId":"7013_300003_#","value":"1","fieldLabel":"还款方式"},
                                                { xtype:'tfcombobox',id:'tx_type',"columnWidth":0.3,"labelWidth":70,"hidden":true,"multiSelect":false,"optId":"7013_300004_#","fieldLabel":"账户类型"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_acno',"columnWidth":0.5,"fieldCls":"field_cls_2","allowBlank":false,"fieldLabel":"账号"},
                                                { xtype:'button',id:'tf_selLedgerbal',"columnWidth":0.15,"style":"top:2px;","text":"查询余额"},
                                                { xtype:'button',id:'btn_read',"columnWidth":0.13,"style":"top:2px;left:2px;","hidden":true,"text":"读卡"},
                                                { xtype:'button',id:'btn_read_1',"columnWidth":0.13,"style":"top:2px;left:4px;","hidden":true,"text":"读折"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'textfield',id:'tf_password',"columnWidth":0.33,"inputType":"password","fieldCls":"field_cls_2","hidden":true,"fieldLabel":"密码"},
                                                { xtype:'textfield',id:'tf_acnoBal',"columnWidth":0.33,"inputType":"text","fieldCls":"field_cls_2","hidden":true,"readOnly":true,"fieldLabel":"账户余额","postfix":"元"},
                                                { xtype:'textfield',id:'tf_acnoBal_1',"columnWidth":0.33,"inputType":"text","fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"账户余额","postfix":"元"},
                                                { xtype:'textfield',id:'tx_name',"columnWidth":0.33,"fieldCls":"field_cls_2","readOnly":true,"name":"txname","fieldLabel":"还款户名"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'tf_applyDate',"columnWidth":0.33,"fieldCls":"field_cls_2","name":"applyDate","fieldLabel":"申请日期","format":"Ymd"},
                                                { xtype:'tfdisplayfield',id:'tf_iptUser',"fieldCls":"field_cls_2","hidden":true,"readOnly":true,"name":"iptUser","fieldLabel":"操作员"},
                                                { xtype:'tfdisplayfield',id:'tf_iptBrno',"fieldCls":"field_cls_2","hidden":true,"readOnly":true,"name":"iptBrno","fieldLabel":"操作机构"},
                                                { xtype:'tfdisplayfield',id:'tf_iptUsrName',"columnWidth":0.33,"fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"操作员"},
                                                { xtype:'tfdisplayfield',id:'tf_iptBrnoName',"columnWidth":0.33,"fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"操作机构"}
                    ]

}
                ]

}
            ]

}
        ]

},
                { xtype:'panel',"layout":"fit","border":0,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'panel',"layout":"column","border":0,"autoScroll":true,            
            items:[
                                { xtype:'form',id:'form_sequence',"layout":"fit","border":1,"autoScroll":true,                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'form',id:'form_lo',"margin":"2px;","columnWidth":0.33,"border":0,"style":"background-color:#FFF;","bodyStyle":"background-color:#FFF;","frame":true,"title":"欠款金额",                        
                        items:[
                                                        { xtype:'fieldset',"border":0,                            
                            items:[
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loSum',"columnWidth":1,"fieldCls":"field_cls_2","fieldLabel":"欠款总额","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loFee',"columnWidth":1,"fieldCls":"field_cls_2","name":"loFee","fieldLabel":"欠款费用","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loCmpdIntst',"columnWidth":1,"fieldCls":"field_cls_2","name":"loCmpdIntst","fieldLabel":"欠款复利","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loOverIntst',"columnWidth":1,"fieldCls":"field_cls_2","name":"loOverIntst","fieldLabel":"欠款逾期利息","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loAgreIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"loAgreIntst","fieldLabel":"欠款约定利息","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loDivIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"loDivIntst","fieldLabel":"欠款挪用利息","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loMisapIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"name":"loMisapIntst","fieldLabel":"欠款挤占利息","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loIntst',"columnWidth":1,"fieldCls":"field_cls_2","name":"loIntst","fieldLabel":"欠款利息","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tfd_loAmt',"columnWidth":1,"fieldCls":"field_cls_2","name":"loAmt","fieldLabel":"欠款本金","postfix":"元"}
                                ]

}
                            ]

}
                        ]

},
                                                { xtype:'form',id:'form_repay',"margin":"2px 2px 2px 0;","columnWidth":0.33,"border":0,"style":"background-color:#FFF;","bodyStyle":"background-color:#FFF;","frame":true,"title":"还款金额",                        
                        items:[
                                                        { xtype:'fieldset',id:'fs_repay',"border":0,"disabled":true,                            
                            items:[
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_repaysum',"fieldCls":"field_cls_2","fieldLabel":"还款总金额","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loFee',"vtype":"vExp","name":"payFeeAmt","value":"0","fieldLabel":"还欠款费用","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loCmpdIntst',"vtype":"vExp","name":"payCmpdIntst","value":"0","fieldLabel":"还欠款复利","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loOverIntst',"vtype":"vExp","name":"payOverIntst","value":"0","fieldLabel":"还逾期利息","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loAgreIntst',"vtype":"vExp","hidden":true,"name":"payAgreIntst","value":"0","fieldLabel":"还欠款约定利息","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loDivIntst',"vtype":"vExp","hidden":true,"name":"payDivIntst","value":"0","fieldLabel":"还欠款挪用利息","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loMisapIntst',"vtype":"vExp","hidden":true,"name":"payMisapIntst","value":"0","fieldLabel":"还欠款挤占利息","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loIntst',"vtype":"vExp","name":"payLoIntst","value":"0","fieldLabel":"还欠款利息","unit":1,"postfix":"元","tiptype":"money"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfnumberfield',id:'tf_loAmt',"vtype":"vExp","name":"payLoAmt","value":"0","fieldLabel":"还欠款本金","unit":1,"postfix":"元","tiptype":"money"}
                                ]

}
                            ]

}
                        ]

},
                                                { xtype:'form',id:'form_unpay',"margin":"2px 2px 2px 0;","columnWidth":0.33,"border":0,"style":"background-color:#FFF;","bodyStyle":"background-color:#FFF;","frame":true,"title":"剩余金额",                        
                        items:[
                                                        { xtype:'fieldset',"border":0,                            
                            items:[
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpaySum',"columnWidth":1,"fieldCls":"field_cls_2","fieldLabel":"剩余总金额","postfix":"元"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoFee',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款费用","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoCmpdIntst',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款复利","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoOverIntst',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余逾期利息","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoAgreIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"value":"0","fieldLabel":"剩余欠款约定利息","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoDivIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"value":"0","fieldLabel":"剩余欠款挪用利息","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoMisapIntst',"columnWidth":1,"fieldCls":"field_cls_2","hidden":true,"value":"0","fieldLabel":"剩余欠款挤占利息","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoIntst',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款利息","postfix":"元","dataFormat":"moneyFormat"}
                                ]

},
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_unpayLoAmt',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款本金","postfix":"元","dataFormat":"moneyFormat"}
                                ]

}
                            ]

}
                        ]

},
                                                { xtype:'form',id:'form_pay',"margin":"2px 2px 2px 0;","columnWidth":0.25,"border":0,"style":"background-color:#FFF;","bodyStyle":"background-color:#FFF;","frame":true,"hidden":true,"title":"已还金额",                        
                        items:[
                                                        { xtype:'fieldset',"border":0,                            
                            items:[
                                                                { xtype:'panel',"layout":"column","border":0,                                
                                items:[
                                                                        { xtype:'tfdisplayfield',id:'tf_paySum',"columnWidth":1,"fieldCls":"field_cls_2","fieldLabel":"已还总额","postfix":"元","dataFormat":"moneyFormat"},
                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","name":"payFeeAmt","fieldLabel":"已还费用","postfix":"元","dataFormat":"moneyFormat"},
                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","name":"payCmpdIntst","fieldLabel":"已还复利","postfix":"元","dataFormat":"moneyFormat"},
                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","name":"payOverIntst","fieldLabel":"已还逾期利息","postfix":"元","dataFormat":"moneyFormat"},
                                                                        { xtype:'tfdisplayfield',id:'tf_payLnIntst',"columnWidth":1,"fieldCls":"field_cls_2","name":"payLoIntst","fieldLabel":"已还利息","postfix":"元","dataFormat":"moneyFormat"},
                                                                        { xtype:'tfdisplayfield',"columnWidth":1,"fieldCls":"field_cls_2","name":"payLoAmt","fieldLabel":"已还本金","postfix":"元","dataFormat":"moneyFormat"}
                                ]

}
                            ]

}
                        ]

}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.afmg.2007View.win_view',{ extend:'Ext.window.Window', alias:'win_view'
    ,id:'win_view',"layout":"fit","width":"80%","height":"80%","modal":true,"constrain":true,"resizable":false,    
    items:[
                { xtype:'panel',"layout":"border","border":1,        
        items:[
                        { xtype:'panel',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",            
            items:[
                                { xtype:'form',id:'form_info_2',"border":0,"autoScroll":true,                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"vchno","fieldLabel":"借据编号"},
                                                { xtype:'tfdisplayfield',id:'sub_tfs_contno_2',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"contno","fieldLabel":"合同编号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'sub_tfs_cliname_2',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"cliname","fieldLabel":"客户名称"},
                                                { xtype:'tfdisplayfield',id:'sub_tfs_cifid_1',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"cifid","fieldLabel":"客户编号"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"curr","optId":"7013_200014","fieldLabel":"币种"},
                                                { xtype:'tfdisplayfield',id:'tf_loTerm_2',"columnWidth":0.45,"fieldCls":"field_cls_2","fieldLabel":"还款期次"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"busSum","fieldLabel":"借据金额"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"busBal","fieldLabel":"借据余额"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',id:'tf_intstSts_2',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"intstSts","optId":"7013_250070_#","fieldLabel":"利息状态"},
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"loDate","fieldLabel":"当前逾期起始日","dataFormat":"dateFormat"}
                    ]

},
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'tfdisplayfield',"columnWidth":0.45,"fieldCls":"field_cls_2","name":"manageInstcode","fieldLabel":"机构编号"},
                                                { xtype:'tfdisplayfield',id:'tf_instcodename_2',"columnWidth":0.45,"fieldCls":"field_cls_2","fieldLabel":"机构名称"}
                    ]

}
                ]

}
            ]

},
                        { xtype:'panel',"layout":"fit","height":"60%","border":0,"split":true,"collapsible":false,"collapsed":false,"region":"south",            
            items:[
                                { xtype:'form',id:'form_sequence_2',"layout":"fit","border":0,"autoScroll":true,                
                dockedItems:[
                                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,                    
                    items:[
                                                { xtype:'tbfill'},
                                                { xtype:'button',id:'btn_cfm_comm',"iconCls":"icon_tick","text":"确认"}
                    ]

}
                ]
,                
                items:[
                                        { xtype:'panel',"layout":"column","border":0,                    
                    items:[
                                                { xtype:'form',id:'form_lo_2',"columnWidth":0.334,"border":1,"title":"欠款金额",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loSum_2',"columnWidth":1,"fieldCls":"field_cls_2","fieldLabel":"欠款总额"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loFee_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loFee","fieldLabel":"欠款费用"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loCmpdIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loCmpdIntst","fieldLabel":"欠款复利"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loOverIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loOverIntst","fieldLabel":"欠款逾期利息"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loAgreIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loAgreIntst","fieldLabel":"欠款约定利息"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loDivIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loDivIntst","fieldLabel":"欠款挪用利息"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loMisapIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loMisapIntst","fieldLabel":"欠款挤占利息"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loIntst","fieldLabel":"欠款正常利息"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tfd_loAmt_2',"columnWidth":1,"fieldCls":"field_cls_2","name":"loAmt","fieldLabel":"欠款本金"}
                            ]

}
                        ]

},
                                                { xtype:'form',id:'form_repay_2',"columnWidth":0.334,"border":1,"title":"还款金额",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_repaysum_2',"fieldCls":"field_cls_2","value":"0","fieldLabel":"还款金额","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loFee_2',"fieldCls":"field_cls_2","name":"payFeeAmt","fieldLabel":"还欠款费用","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loCmpdIntst_2',"fieldCls":"field_cls_2","name":"payCmpdIntst","fieldLabel":"还欠款复利","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loOverIntst_2',"fieldCls":"field_cls_2","name":"payOverIntst","fieldLabel":"还欠款逾期利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loAgreIntst_2',"fieldCls":"field_cls_2","name":"payAgreIntst","fieldLabel":"还欠款约定利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loDivIntst_2',"fieldCls":"field_cls_2","name":"payDivIntst","fieldLabel":"还欠款挪用利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loMisapIntst_2',"fieldCls":"field_cls_2","name":"payMisapIntst","fieldLabel":"还欠款挤占利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loIntst_2',"fieldCls":"field_cls_2","name":"payLoIntst","fieldLabel":"还欠款正常利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_loAmt_2',"fieldCls":"field_cls_2","name":"payLoAmt","fieldLabel":"还欠款本金","dataFormat":"moneyFormat"}
                            ]

}
                        ]

},
                                                { xtype:'form',id:'form_unpay_2',"columnWidth":0.333,"border":1,"title":"剩余金额",                        
                        items:[
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpaySum_2',"columnWidth":1,"fieldCls":"field_cls_2","fieldLabel":"剩余总金额"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoFee_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款费用","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoCmpdIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款复利","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoOverIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款逾期利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoAgreIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款约定利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoDivIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款挪用利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoMisapIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款挤占利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoIntst_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款正常利息","dataFormat":"moneyFormat"}
                            ]

},
                                                        { xtype:'panel',"layout":"column","border":0,                            
                            items:[
                                                                { xtype:'tfdisplayfield',id:'tf_unpayLoAmt_2',"columnWidth":1,"fieldCls":"field_cls_2","value":"0","fieldLabel":"剩余欠款本金","dataFormat":"moneyFormat"}
                            ]

}
                        ]

}
                    ]

}
                ]

}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.afmg.2007View.win_auth',{ extend:'Ext.window.Window', alias:'win_auth'
    ,id:'win_auth',"layout":"fit","width":400,"height":300,"modal":true,"constrain":true,"resizable":false,"title":"授权执行",    
    items:[
                { xtype:'form',id:'form_auth',"layout":"form","bodyPadding":"0px 0px 10px 0px","border":0,        
        items:[
                        { xtype:'fieldset',id:'fds_auth',"width":360,"height":200,"margin":"10px","border":1,"collapsible":true,"title":"<授权信息>",            
            items:[
                                { xtype:'panel',"layout":"column","margin":"20px 60px 10px 0px","border":0,                
                items:[
                                        { xtype:'tfcombobox',id:'auth_operid',"width":300,"padding":"0px 0px 15px 0px","allowBlank":false,"name":"operid","fieldLabel":"选择授权人"},
                                        { xtype:'tfcombobox',id:'auth_authType',"width":300,"padding":"0px 0px 15px 0px","allowBlank":false,"readOnly":true,"name":"authType","optId":"7013_400023","fieldLabel":"授权方式"},
                                        { xtype:'textfield',id:'auth_psd',"width":300,"padding":"0px 0px 15px 0px","inputType":"password","allowBlank":false,"fieldLabel":"授权人密码"}
                ]

}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0},
                        { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,            
            items:[
                                { xtype:'tbfill'},
                                { xtype:'tfbutton',"tfAction":"win_close"},
                                { xtype:'button',id:'btn_pass',"iconCls":"icon_tick","text":"确定"},
                                { xtype:'button',id:'btn_finger',"iconCls":"icon_hand-point-090","hidden":true,"text":"确定"}
            ]

}
        ]

}
    ]

    });



    Ext.define('mvc.view.rygl.0011View.Main', {extend : 'Ext.Viewport',alias : 'widget.main'
    ,"layout":"border",    
    items:[
                { xtype:'panel',id:'tf_main',"layout":"fit","border":1,"collapsible":false,"collapsed":false,"region":"center",        
        items:[
                        { xtype:'grid',dataSource:'grid_main_store',id:'grid_main',"border":0,"autoScroll":true,"allowPage":true,"pageSize":20,            
            columns:[
                                { xtype:'gridcolumn',"width":60,"dataIndex":"jobNo","text":"工号"},
                                { xtype:'gridcolumn',"width":100,"dataIndex":"cliname","text":"人员名称"},
                                { xtype:'gridcolumn',"width":120,"dataIndex":"moveReason","text":"调动原因"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"movebfInstcode","text":"调动前机构"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"moveafInstcode","text":"调动后机构"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"movebfStation","text":"调动前职务"},
                                { xtype:'gridcolumn',"width":150,"dataIndex":"moveafStation","text":"调动后职务"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"bfpostname","text":"调动前岗位"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"afpostname","text":"调动后岗位"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"bfRoleName","text":"调动前角色"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"afRoleName","text":"调动后角色"},
                                { xtype:'gridcolumn',"width":100,"dataIndex":"moveDate","text":"调动日期"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"insideNo","text":"员工内部标识"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"serid","text":"调动流水号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"bfInstcode","text":"调动前机构号"},
                                { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"movebfPostid","text":"调动前岗位"},
                                { xtype:'gridcolumn',"width":130,"hidden":true,"dataIndex":"moveafPostid","text":"调动后岗位"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"bfRoleId","text":"调动前角色ID"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"afInstcode","text":"调动后机构号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"afRoleId","text":"调动后角色ID"},
                                { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"iptUsr","text":"登记人员"},
                                { xtype:'gridcolumn',"dataIndex":"opername","text":"登记人员"},
                                { xtype:'gridcolumn',"width":160,"hidden":true,"dataIndex":"iptInstcode","text":"登记机构"},
                                { xtype:'gridcolumn',"width":130,"dataIndex":"instname","text":"登记机构"},
                                { xtype:'gridcolumn',"width":100,"dataIndex":"iptTime","text":"登记时间"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,"cls":"toolbar_cls_1",            
            items:[
                                { xtype:'textfield',id:'tf_cliname',"width":200,"labelWidth":60,"fieldLabel":"人员名称"},
                                { xtype:'button',id:'tf_select_1',"iconCls":"icon_view","text":"查询"},
                                { xtype:'button',id:'tf_reset',"iconCls":"icon_arrow_undo","text":"重置"}
            ]

},
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":1,            
            items:[
                                { xtype:'button',id:'btn_move',"iconCls":"icon_add","text":"调动登记"},
                                { xtype:'button',id:'btn_detail',"iconCls":"icon_document-search-result","disabled":true,"text":"调动详情"},
                                { xtype:'button',id:'btn_del',"iconCls":"icon_delete","hidden":true,"disabled":true,"text":"删除"}
            ]

}
        ]

},
                { xtype:'panel',id:'tf_panel_south',"layout":"fit","height":200,"split":true,"collapsible":true,"collapsed":true,"title":"附件列表","region":"south",        
        items:[
                        { xtype:'grid',dataSource:'grid_file_store',id:'grid_file',"border":0,"forceFit":true,"autoLoad":false,            
            columns:[
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"relserid","text":"上传编号"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"fileurl","text":"文件路径"},
                                { xtype:'gridcolumn',"width":160,"dataIndex":"filename","text":"文件名称"},
                                { xtype:'gridcolumn',"dataIndex":"filedescribe","text":"文件描述"},
                                { xtype:'tfoptcolumn',"dataIndex":"filetype","optId":"7013_251105","text":"文件类型"},
                                { xtype:'gridcolumn',"dataIndex":"iptUsrName","text":"上传人"},
                                { xtype:'gridcolumn',"dataIndex":"iptDate","text":"上传时间"},
                                { xtype:'gridcolumn',"hidden":true,"dataIndex":"serid","text":"附件流水号"}
            ]

}
        ]
,        
        dockedItems:[
                        { xtype:'toolbar',"layout":"hbox","dock":"top","border":0,            
            items:[
                                { xtype:'button',id:'btn_upload',"iconCls":"icon_collapse","disabled":true,"text":"上传附件"},
                                { xtype:'button',id:'btn_delete',"iconCls":"icon_cancel","disabled":true,"text":"删除"},
                                { xtype:'button',id:'btn_download',"iconCls":"icon_expand","disabled":true,"text":"下载"}
            ]

}
        ]

}
    ]

    });


    Ext.define('mvc.view.rygl.0011View.win_move',{ extend:'Ext.window.Window', alias:'win_move'
    ,id:'win_move',"layout":"fit","width":600,"height":400,"modal":true,"constrain":true,"resizable":false,"title":"调动登记",    
    items:[
                { xtype:'form',id:'form_move',"layout":"column","width":600,"height":250,"border":0,"collapsible":false,"collapsed":false,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0培","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_insideNo',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"name":"jobNo","fieldLabel":"人员编号"},
                                { xtype:'tfselectfield',id:'tf_name',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"cliname","fieldLabel":"人员名称"},
                                { xtype:'datefield',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveDate","fieldLabel":"调动日期","format":"Ymd"},
                                { xtype:'textfield',id:'tf_bfinstcode',"hidden":true,"name":"bfInstcode","fieldLabel":"调动前机构号"},
                                { xtype:'textfield',id:'tf_afinstcode',"hidden":true,"name":"afInstcode","fieldLabel":"调动后机构号"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfselectfield',id:'win_movebfInstcode',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"name":"movebfInstcode","fieldLabel":"调动前机构"},
                                { xtype:'tfselectfield',id:'win_moveafInstcode',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveafInstcode","fieldLabel":"调动后机构"},
                                { xtype:'button',id:'btn_bef',"hidden":true,"text":"调动前"},
                                { xtype:'button',id:'btn_aft',"hidden":true,"text":"调动后"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tf_movebfStation',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"name":"movebfStation","fieldLabel":"调动前职务"},
                                { xtype:'tfcombobox',id:'tf_moveafStation',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveafStation","fieldLabel":"调动后职务"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'win_tf_movebfPostid',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"name":"movebfPostid","fieldLabel":"调动前权限编号"},
                                { xtype:'tfselectfield',id:'tf_movebfPostid',"padding":"5 0 5 0px","columnWidth":0.5,"labelWidth":70,"allowBlank":false,"readOnly":true,"fieldLabel":"调动前岗位"},
                                { xtype:'textfield',id:'win_tf_moveafPostid',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"name":"moveafPostid","fieldLabel":"调动后权限编号"},
                                { xtype:'tfselectfield',id:'tf_moveafPostid',"padding":"5 0 5 0px","columnWidth":0.5,"labelWidth":70,"allowBlank":false,"fieldLabel":"调动后岗位"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfselectfield',id:'tf_role',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"readOnly":true,"fieldLabel":"调动前角色"},
                                { xtype:'textfield',id:'win_roleId',"hidden":true,"name":"bfRoleId","fieldLabel":"调动前角色ID"},
                                { xtype:'textfield',id:'win_roleName',"hidden":true,"name":"bfRoleName","fieldLabel":"调动前角色名称"},
                                { xtype:'tfselectfield',id:'tf_role_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"fieldLabel":"调动后角色"},
                                { xtype:'textfield',id:'win_roleId_1',"hidden":true,"name":"afRoleId","fieldLabel":"调动后角色ID"},
                                { xtype:'textfield',id:'win_roleName_1',"hidden":true,"name":"afRoleName","fieldLabel":"调动后角色名称"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textareafield',id:'tf_movereason',"labelWidth":70,"allowBlank":false,"name":"moveReason","fieldLabel":"调用原因"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_iptusr',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","hidden":true,"readOnly":true,"name":"iptUsr","fieldLabel":"登记人员"},
                                { xtype:'textfield',id:'tf_det_iptinstname',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"登记机构"},
                                { xtype:'textfield',id:'tf_iptusrname',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"fieldLabel":"登记人员"},
                                { xtype:'textfield',id:'tf_ipttime',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"name":"iptTime","fieldLabel":"登记时间"},
                                { xtype:'textfield',id:'tf_det_iptinstcode',"columnWidth":0.5,"labelWidth":70,"hidden":true,"readOnly":true,"name":"iptInstcode","fieldLabel":"登记机构"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'button',id:'btn_cancel_1_1',"iconCls":"icon_cancel","text":"取消"},
                        { xtype:'button',id:'btn_submit',"columnWidth":70,"iconCls":"icon_tick","text":"确认提交"}
        ]

}
    ]

    });


    Ext.define('mvc.view.rygl.0011View.win_detail',{ extend:'Ext.window.Window', alias:'win_detail'
    ,id:'win_detail',"layout":"fit","width":600,"height":400,"modal":true,"constrain":true,"resizable":false,"title":"调动详情",    
    items:[
                { xtype:'form',id:'form_move_1',"layout":"column","width":600,"height":250,"border":0,"collapsible":false,"collapsed":false,        
        items:[
                        { xtype:'panel',"layout":"column","margin":"20px 0px 0px 0培","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_insideNo_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"name":"jobNo","fieldLabel":"人员编号"},
                                { xtype:'tfselectfield',id:'tf_name_1',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"name":"cliname","fieldLabel":"人员名称"},
                                { xtype:'datefield',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveDate","fieldLabel":"调动日期","format":"Ymd"},
                                { xtype:'textfield',id:'tf_bfinstcode_1',"hidden":true,"name":"bfInstcode","fieldLabel":"调动前机构号"},
                                { xtype:'textfield',id:'tf_afinstcode_1',"hidden":true,"name":"afInstcode","fieldLabel":"调动后机构号"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfselectfield',id:'win_movebfInstcode_1',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"name":"movebfInstcode","fieldLabel":"调动前机构"},
                                { xtype:'tfselectfield',id:'win_moveafInstcode_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveafInstcode","fieldLabel":"调动后机构"},
                                { xtype:'button',id:'btn_bf',"hidden":true,"text":"调动前"},
                                { xtype:'button',id:'btn_af',"hidden":true,"text":"调动后"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfcombobox',id:'tf_movebfStation_1',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"name":"movebfStation","fieldLabel":"调动前职务"},
                                { xtype:'tfcombobox',id:'tf_moveafStation_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"name":"moveafStation","fieldLabel":"调动后职务"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'win_tf_movebfPostid_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"name":"movebfPostid","fieldLabel":"调动前权限编号"},
                                { xtype:'tfselectfield',id:'tf_movebfPostid_1',"padding":"5 0 5 0px","columnWidth":0.5,"labelWidth":70,"readOnly":true,"fieldLabel":"调动前岗位"},
                                { xtype:'textfield',id:'win_tf_moveafPostid_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"hidden":true,"readOnly":true,"name":"moveafPostid","fieldLabel":"调动后权限编号"},
                                { xtype:'tfselectfield',id:'tf_moveafPostid_1',"padding":"5 0 5 0px","columnWidth":0.5,"labelWidth":70,"allowBlank":false,"fieldLabel":"调动后岗位"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'tfselectfield',id:'tf_role_d',"columnWidth":0.5,"labelWidth":70,"readOnly":true,"fieldLabel":"调动前角色"},
                                { xtype:'textfield',id:'win_roleId_d',"hidden":true,"name":"bfRoleId","fieldLabel":"调动前角色ID"},
                                { xtype:'textfield',id:'win_roleName_d',"hidden":true,"name":"bfRoleName","fieldLabel":"调动前角色名称"},
                                { xtype:'tfselectfield',id:'tf_role_d_1',"columnWidth":0.5,"labelWidth":70,"allowBlank":false,"readOnly":false,"fieldLabel":"调动后角色"},
                                { xtype:'textfield',id:'win_roleId_d_1',"hidden":true,"name":"afRoleId","fieldLabel":"调动后角色ID"},
                                { xtype:'textfield',id:'win_roleName_d_1',"hidden":true,"name":"afRoleName","fieldLabel":"调动后角色名称"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textareafield',id:'tf_movereason_1',"labelWidth":70,"allowBlank":false,"name":"moveReason","fieldLabel":"调用原因"}
            ]

},
                        { xtype:'panel',"layout":"column","border":0,            
            items:[
                                { xtype:'textfield',id:'tf_iptInstcode_1',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"name":"instname","fieldLabel":"登记机构"},
                                { xtype:'textfield',id:'tf_iptusr_1',"columnWidth":0.5,"labelWidth":70,"hidden":true,"readOnly":true,"name":"iptUsr","fieldLabel":"登记人员"},
                                { xtype:'textfield',id:'tf_iptusr_1_1',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"name":"opername","fieldLabel":"登记人员"},
                                { xtype:'textfield',id:'tf_iptInstcode',"columnWidth":0.5,"labelWidth":70,"hidden":true,"readOnly":true,"name":"iptInstcode","fieldLabel":"登记机构"},
                                { xtype:'textfield',id:'tf_ipttime_1',"columnWidth":0.5,"labelWidth":70,"fieldCls":"field_cls_2","readOnly":true,"name":"iptTime","fieldLabel":"登记时间"},
                                { xtype:'textfield',"hidden":true,"name":"serid","fieldLabel":"调动流水号"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'button',id:'btn_cancel_1',"iconCls":"icon_cancel","text":"取消"},
                        { xtype:'button',id:'btn_update',"columnWidth":70,"iconCls":"icon_tick","text":"确认提交"}
        ]

}
    ]

    });


    Ext.define('mvc.view.rygl.0011View.win_upload',{ extend:'Ext.window.Window', alias:'win_upload'
    ,id:'win_upload',"layout":"fit","width":400,"height":200,"modal":true,"constrain":true,"resizable":false,"title":"上传附件",    
    items:[
                { xtype:'form',id:'form_filedetail',"layout":"column","border":0,        
        items:[
                        { xtype:'fieldset',"layout":"column","margin":"5px","border":0,            
            items:[
                                { xtype:'tfuploadpanel',id:'tf_uppanel',"columnWidth":1,"border":0,"readOnly":false,"fileTypes":"*","files":5,"fileSize":102400,"folder":"/cust/"},
                                { xtype:'textfield',"columnWidth":0.7,"allowBlank":false,"name":"filedescribe","fieldLabel":"文件描述"},
                                { xtype:'tfcombobox',"columnWidth":0.7,"allowBlank":false,"name":"filetype","optId":"7013_251105","fieldLabel":"文件类型"},
                                { xtype:'textfield',"columnWidth":0.7,"allowBlank":false,"hidden":true,"readOnly":true,"name":"fieldname","value":"客户编号","fieldLabel":"关联字段名称"},
                                { xtype:'textfield',"columnWidth":0.8,"hidden":true,"readOnly":true,"name":"phasename","value":"客户登记","fieldLabel":"阶段名称"},
                                { xtype:'textfield',"columnWidth":0.8,"hidden":true,"readOnly":true,"name":"phaseno","value":"custreg","fieldLabel":"阶段编号"},
                                { xtype:'textfield',id:'tf_up_iptusr',"hidden":true,"name":"iptUsr","fieldLabel":"上传人"},
                                { xtype:'textfield',id:'tf_up_ipttime',"hidden":true,"name":"iptDate","fieldLabel":"上传时间"}
            ]

}
        ]

}
    ]
,    
    dockedItems:[
                { xtype:'toolbar',"layout":"hbox","dock":"bottom","border":0,        
        items:[
                        { xtype:'tbfill'},
                        { xtype:'button',id:'btn_cancel',"iconCls":"icon_cancel","text":"取消"},
                        { xtype:'button',id:'btn_submit_1_1',"iconCls":"icon_tick","text":"提交"}
        ]

}
    ]

    });

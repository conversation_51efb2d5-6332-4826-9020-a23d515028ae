Ext.define("mvc.controller.sysm.0066Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_200001_#|7013_200014_#|7013_250066_#|7013_250026_#|7013_400024_#|7013_290001_#");
		//-------全局变量定义-------开始-------
		var instcode = TF.ExtHelper.request('instcode');
		var prdtNo = TF.ExtHelper.request('prdtNo');
		//-------全局变量定义-------结束-------
		//grids' stores
		
		//trees' stores

		this.control({
			'#btn_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var cnPrdtApplyinstcode=new Object();
cnPrdtApplyinstcode=Ext.getCmp('form_detail').getForm().getExtValues();
sendData.cnPrdtApplyinstcode=cnPrdtApplyinstcode;
					//前处理 结束
					sendData.flowId='0013_SYSM0066_btn_add_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码

Ext.Msg.show({title:'提示',msg:'保存基本信息成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_detail').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#form_detail':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var  cnPrdtApplyinstcode=new Object();
cnPrdtApplyinstcode.prdtNo=prdtNo;
cnPrdtApplyinstcode.instcode=instcode;
sendData.cnPrdtApplyinstcode=cnPrdtApplyinstcode;
					//前处理 结束
					sendData.flowId='0013_SYSM0066_form_detail_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('form_detail').getForm().setValues(obj.refObj.cnProduct);
Ext.getCmp('form_detail').getForm().setValues(obj.refObj.cnPrdtApplyinstcode);
var _cnProduct = obj.refObj.cnProduct;
//最低金额验证
var minBal=_cnProduct.minBal;
var maxBal=_cnProduct.maxBal;
var exp = 'Ext.getCmp(\'tf_minBal\').value>='+minBal+' && Ext.getCmp(\'tf_minBal\').value<='+maxBal;
Ext.getCmp('tf_minBal').vtypeExp=exp;
Ext.getCmp('tf_minBal').vtypeText='输入金额不在【'+minBal+','+ maxBal+'】';
//最高金额验证
var exp1 = 'Ext.getCmp(\'tf_maxBal\').value>='+minBal+' && Ext.getCmp(\'tf_maxBal\').value<='+maxBal;
Ext.getCmp('tf_maxBal').vtypeExp=exp1;
Ext.getCmp('tf_maxBal').vtypeText='输入金额不在【'+minBal+','+ maxBal+'】';
//最小期限验证
var minTerm=_cnProduct.minTerm;
var maxTerm=_cnProduct.maxTerm;
var exp2 = 'Ext.getCmp(\'tf_minTerm\').value>='+minTerm+' && Ext.getCmp(\'tf_minTerm\').value<='+maxTerm;
Ext.getCmp('tf_minTerm').vtypeExp=exp2;
Ext.getCmp('tf_minTerm').vtypeText='输入期限不在【'+minTerm+','+ maxTerm+'】';
//最大期限验证
var exp3 = 'Ext.getCmp(\'tf_maxTerm\').value>='+minTerm+' && Ext.getCmp(\'tf_maxTerm\').value<='+maxTerm;
Ext.getCmp('tf_maxTerm').vtypeExp=exp3;
Ext.getCmp('tf_maxTerm').vtypeText='输入期限不在【'+minTerm+','+ maxTerm+'】';
//最低利率验证
var prdtRate=_cnProduct.prdtRate ;
var maxPrdtRate=_cnProduct.maxPrdtRate;
var exp4 = 'Ext.getCmp(\'tf_prdtRate\').value>='+prdtRate+' && Ext.getCmp(\'tf_prdtRate\').value<='+maxPrdtRate;
Ext.getCmp('tf_prdtRate').vtypeExp=exp4;
Ext.getCmp('tf_prdtRate').vtypeText='利率不在【'+prdtRate+','+ maxPrdtRate+'】';
//最高利率验证
var exp4 = 'Ext.getCmp(\'tf_maxPrdtRate\').value>='+prdtRate+' && Ext.getCmp(\'tf_maxPrdtRate\').value<='+maxPrdtRate;
Ext.getCmp('tf_maxPrdtRate').vtypeExp=exp4;
Ext.getCmp('tf_maxPrdtRate').vtypeText='利率不在【'+prdtRate+','+ maxPrdtRate+'】';
//最低抵质押率验证
var minGuarRate=_cnProduct.minGuarRate ;
var maxGuarRate=_cnProduct.maxGuarRate;
var exp4 = 'Ext.getCmp(\'tf_minGuarRate\').value>='+minGuarRate+' && Ext.getCmp(\'tf_minGuarRate\').value<='+maxGuarRate;
Ext.getCmp('tf_minGuarRate').vtypeExp=exp4;
Ext.getCmp('tf_minGuarRate').vtypeText='抵质押率不在【'+minGuarRate+','+ maxGuarRate+'】';
//最高抵质押率验证
var exp4 = 'Ext.getCmp(\'tf_maxGuarRate\').value>='+minGuarRate+' && Ext.getCmp(\'tf_maxGuarRate\').value<='+maxGuarRate;
Ext.getCmp('tf_maxGuarRate').vtypeExp=exp4;
Ext.getCmp('tf_maxGuarRate').vtypeText='抵质押率不在【'+minGuarRate+','+ maxGuarRate+'】';
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'sysm.0066View' ]
});
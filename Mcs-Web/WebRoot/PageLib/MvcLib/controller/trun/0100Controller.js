Ext.define("mvc.controller.trun.0100Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_flow_store',
			fields: [
				{name:'flowno'},				
				{name:'flowname'},				
				{name:'flowVersion'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.flowno=Ext.getCmp('tf_qFlowno').value;
sendData.flowname=Ext.getCmp('tf_qFlowname').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_TRUN0100_grid_flow_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_flow_pagingtoolbar')){
						Ext.getCmp('grid_flow_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_flow_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_cancel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码

Ext.getCmp('win_update').close();
					//前处理 结束
				}			
			},			
			'#btn_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.flowno=Ext.getCmp('tf_flowno').value;
sendData.flowname=Ext.getCmp('tf_flowname').value;
					//前处理 结束
					sendData.flowId='0013_TRUN0100_btn_submit_click';
					sendData.flowDesc='event';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({title:'提示',msg:'修改成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.data.StoreManager.lookup('grid_flow_store').loadPage(1);
Ext.getCmp('win_update').close();
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#win_update':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tf_flowname').setValue(_flowData.flowname);
Ext.getCmp('tf_flowno').setValue(_flowData.flowno);
					//前处理 结束
				}			
			},			
			'#grid_flow':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_flowData=record.data;
Ext.getCmp('btn_apply').setDisabled(false);
Ext.getCmp('btn_deal').setDisabled(false);
Ext.getCmp('btn_detail').setDisabled(false);
Ext.getCmp('btn_permission').setDisabled(false);
Ext.getCmp('btn_update').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_deal':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('',_flowData.flowname,'toptask','/PageLib/MvcBase.html?pageId=TRUN0105&_flowno='+_flowData.flowno,1310,640,true,true);
					//前处理 结束
				}			
			},			
			'#btn_permission':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('',_flowData.flowname,'toptask','/PageLib/MvcBase.html?pageId=TRUN0120&_flowno='+_flowData.flowno,1310,640,true,true);
					//前处理 结束
				}			
			},			
			'#btn_update':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_update').show();
					//前处理 结束
				}			
			},			
			'#btn_detail':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('',_flowData.flowname,'toptask','/PageLib/MvcBase.html?pageId=TRUN0105&_flowno='+_flowData.flowno,1310,640,true,true);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'trun.0100View' ]
});


		//-------全局变量定义-------开始-------
		var _flowData = null;
		//-------全局变量定义-------结束-------


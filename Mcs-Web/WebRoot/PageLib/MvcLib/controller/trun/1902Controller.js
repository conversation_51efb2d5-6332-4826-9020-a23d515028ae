Ext.define("mvc.controller.trun.1902Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_main_store',
			fields: [
				{name:'wzdno'},				
				{name:'stepno'},				
				{name:'stepna'},				
				{name:'pageno'},				
				{name:'seqno'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.funname=Ext.getCmp('tb_funname').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_TRUN1902_grid_main_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_main_pagingtoolbar')){
						Ext.getCmp('grid_main_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_cancle':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tb_funname').reset();

Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#grid_main':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_mcsdata=record.data;
Ext.getCmp('btn_submit').setDisabled(false);
					//前处理 结束
				},				itemdblclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_mcsdata){
	//console.log('mcsdata:'+_mcsdata);
	var win=window.parent.Ext.getCmp('sel_win');
	if(win != undefined && win !=null){
		win.selData = _mcsdata;
		win.close();
		}
	
}else{
		Ext.Msg.alert('提示','请选择一条记录');
}
					//前处理 结束
				}			
			},			
			'#btn_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代

var win = window.parent.Ext.getCmp('sel_win');
if (win != undefined && win != null) {
win.selData = _mcsdata;
win.close();
}
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'trun.1902View' ]
});


		//-------全局变量定义-------开始-------
		var _mcsdata = null;
		//-------全局变量定义-------结束-------


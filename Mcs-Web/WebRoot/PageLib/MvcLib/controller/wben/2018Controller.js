Ext.define("mvc.controller.wben.2018Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
				//-------全局变量定义-------开始-------
		//-------全局变量定义-------结束-------
		//grids' stores
		
		//trees' stores

		this.control({
			'#cp_main':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
					//前处理 结束
					sendData.flowId='0013_WBEN2018_cp_main_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
TF.ExtHelper.setOptStore('tf_instcode',obj.refObj.VO1.list,{'optionValue':'key','optionLabel':'value'},true);
Ext.getCmp('tf_instcode').setValue(tf.sysdb.branchId);
var sysdate=new Date(Ext.Date.format(Ext.Date.parse(tf.sysdb.workDate,'Ymd'),'m/d/Y'));
var yestday=Ext.Date.format(Ext.Date.add(sysdate, Ext.Date.DAY, -1),'Ymd');
Ext.getCmp('tj_date').setValue(yestday);                              
TF.ExtHelper.setIframe('cp_main','/frameset?__report=view/over_loanstate.rptdesign'+'&rptdate='+Ext.getCmp('tj_date').rawValue+'&brno='+Ext.getCmp('tf_instcode').value,'iframe_birt');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tf_instcode').reset();
Ext.getCmp('tj_date').reset();
//Ext.getCmp('over_bdate').reset();
//Ext.getCmp('over_edate').reset();
					//前处理 结束
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(!Ext.isEmpty(Ext.getCmp('tf_instcode').value)&&!Ext.isEmpty(Ext.getCmp('tj_date').value)){
	TF.ExtHelper.setIframe('cp_main','/frameset?__report=view/over_loanstate.rptdesign'+'&rptdate='+Ext.getCmp('tj_date').rawValue+'&brno='+Ext.getCmp('tf_instcode').value,'iframe_birt');
}else{
	Ext.Msg.show({
		title:'温馨提示',
		msg:'请补全查询条件！',
		icon:Ext.Msg.WARNING,
		buttons: Ext.Msg.OK
	});
}
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'wben.2018View' ]
});
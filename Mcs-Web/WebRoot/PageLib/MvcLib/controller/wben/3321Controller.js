Ext.define("mvc.controller.wben.3321Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		
		//trees' stores

		this.control({
			'#tf_instcode':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var instcode = Ext.getCmp('tf_instcode').value;
TF.ExtHelper.selWindow('查询机构选择','/PageLib/MvcBase.html?pageId=MCSW2020',800,400,function(data){
   me.setValue('['+data.instcode+']'+data.instname);
   me.setSelValue(data.instcode);
   //_instcode=data.instcode;
   //Ext.getCmp('tfs_instcode').setValue(data.instcode);
});
					//前处理 结束
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tj_date').reset();
Ext.getCmp('tf_cliname').reset();
Ext.getCmp('tf_acid').reset();
Ext.getCmp('tj_enddate').reset();
if(_flag!=0){
   Ext.getCmp('tf_sele').setReadOnly(true);
}else{
   Ext.getCmp('tf_sele').reset();
   Ext.getCmp('tf_sele').setSelValue('');
   Ext.getCmp('tf_sele').setReadOnly(false);
}
Ext.getCmp('tj_endmaxdate').reset();
Ext.getCmp('tj_maxdate').reset();
					//前处理 结束
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(!Ext.isEmpty(Ext.getCmp('tf_instcode').value)){ //&&!Ext.isEmpty(Ext.getCmp('tf_sele').value)
    var bankid = tf.sysdb.bankid;
    var operid = tf.sysdb.optel;
    var instcode =Ext.getCmp('tf_instcode').getSelValue();
    var tjoperid=Ext.getCmp('tf_sele').getSelValue();if(Ext.isEmpty(tjoperid)) tjoperid='@';
    var cliname = Ext.getCmp('tf_cliname').value;if(Ext.isEmpty(cliname)) cliname='@';
    var begindate = Ext.getCmp('tj_date').rawValue;if(Ext.isEmpty(begindate)) begindate='@';
    var beginmaxdate = Ext.getCmp('tj_maxdate').rawValue;if(Ext.isEmpty(beginmaxdate)) beginmaxdate='@';
    var endmindate = Ext.getCmp('tj_endmaxdate').rawValue;if(Ext.isEmpty(endmindate)) endmindate='@';
    var enddate=Ext.getCmp('tj_enddate').rawValue;if(Ext.isEmpty(enddate)) enddate='@';
    var acno = Ext.getCmp('tf_acid').value;if(Ext.isEmpty(acno)) acno='@';
TF.ExtHelper.setIframe('cp_main','/frameset?__report=dkfhz.rptdesign'+'&bankid='+bankid+'&operid='+operid+'&instcode='+instcode+'&tjoperid='+tjoperid+'&cliname='+cliname+'&begindate='+begindate+'&beginmaxdate='+beginmaxdate+'&endmindate='+endmindate+'&enddate='+enddate+'&loanacNo='+acno,'iframe_birt');
}else{
	Ext.Msg.show({
		title:'温馨提示',
		msg:'查询机构条件不能为空！',
		icon:Ext.Msg.WARNING,
		buttons: Ext.Msg.OK
	});
}
					//前处理 结束
				}			
			},			
			'#tf_sele':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var instcode = Ext.getCmp('tf_instcode').getSelValue();
TF.ExtHelper.selWindow('管户经理选择','/PageLib/MvcBase.html?pageId=WBEN9991&instcode='+instcode+'&flag='+'1',800,400,function(data){
//TF.ExtHelper.selWindow('管户经理选择','/PageLib/MvcBase.html?pageId=WBEN9991',800,400,function(data){
   me.setValue('['+data.operid+']'+data.opername);
   me.setSelValue(data.operid);
   if(data.num=='1'){
   	 Ext.getCmp('tf_sele').setReadOnly(true);
   }else{
     Ext.getCmp('tf_sele').setReadOnly(false);
   }
   //_instcode=data.instcode;
   //Ext.getCmp('tfs_instcode').setValue(data.instcode);
});
					//前处理 结束
				}			
			},			
			'#tj_endmaxdate':{
				blur:function(me,The,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var bmindate = Ext.getCmp('tj_enddate').rawValue;
var bmaxdate = Ext.getCmp('tj_endmaxdate').rawValue;
if(bmindate > bmaxdate){
    Ext.Msg.show({title:'警告',msg:'初始到期日不能大于最终到期日!',icon:[Ext.Msg.WARNING],buttons: Ext.Msg.OK});
    Ext.getCmp('btn_sel').setDisabled(true);
}else{
    Ext.getCmp('btn_sel').setDisabled(false);
}
					//前处理 结束
				}			
			},			
			'#tj_maxdate':{
				blur:function(me,The,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var bmindate = Ext.getCmp('tj_date').rawValue;
var bmaxdate = Ext.getCmp('tj_maxdate').rawValue;
if(bmindate > bmaxdate){
    Ext.Msg.show({title:'警告',msg:'初始起始日不能大于最终起始日!',icon:[Ext.Msg.WARNING],buttons: Ext.Msg.OK});
    Ext.getCmp('btn_sel').setDisabled(true);
}else{
    Ext.getCmp('btn_sel').setDisabled(false);
}
					//前处理 结束
				}			
			},			
			'#cp_main':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
					//前处理 结束
					sendData.flowId='0013_WBEN3321_cp_main_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码

var num = obj.refObj.VO1.list.length;
var data = obj.refObj.VO1.list[num-1].flag;
_flag=data;
for (var i = 0; i < num; i++) {
     if (obj.refObj.VO1.list[i].key =='null'||obj.refObj.VO1.list[i].key==null) {
           obj.refObj.VO1.list.splice(i, 1);
     }
}
//TF.ExtHelper.setOptStore('tf_instcode',obj.refObj.VO1.list,{'optionValue':'key','optionLabel':'value'},true);
if(data=='1'){
	Ext.getCmp('tf_sele').setReadOnly(true);
	Ext.getCmp('tf_sele').setValue('['+tf.sysdb.optel+']'+tf.sysdb.suName);
	Ext.getCmp('tf_sele').setSelValue(tf.sysdb.optel);
}else{
    Ext.getCmp('tf_sele').setReadOnly(false);
}
Ext.getCmp('tf_instcode').setValue('['+tf.sysdb.branchId+']'+tf.sysdb.branchName);
Ext.getCmp('tf_instcode').setSelValue(tf.sysdb.branchId);
var sysdate=new Date(Ext.Date.format(Ext.Date.parse(tf.sysdb.workDate,'Ymd'),'m/d/Y'));
var yestday=Ext.Date.format(Ext.Date.add(sysdate, Ext.Date.DAY, -1),'Ymd');
Ext.getCmp('tj_maxdate').setValue(yestday);
Ext.getCmp('tj_maxdate').setMaxValue(yestday);
Ext.getCmp('tj_date').setMaxValue(yestday);


var bankid = tf.sysdb.bankid;
var operid = tf.sysdb.optel;
var instcode =Ext.getCmp('tf_instcode').getSelValue();
var tjoperid=Ext.getCmp('tf_sele').getSelValue();if(Ext.isEmpty(tjoperid)) tjoperid='@';
var cliname = Ext.getCmp('tf_cliname').value;if(Ext.isEmpty(cliname)) cliname='@';

var begindate = Ext.getCmp('tj_date').rawValue;if(Ext.isEmpty(begindate)) begindate='@';
var beginmaxdate = Ext.getCmp('tj_maxdate').rawValue;if(Ext.isEmpty(beginmaxdate)) beginmaxdate='@';
var endmindate = Ext.getCmp('tj_endmaxdate').rawValue;if(Ext.isEmpty(endmindate)) endmindate='@';
var enddate=Ext.getCmp('tj_enddate').rawValue;if(Ext.isEmpty(enddate)) enddate='@';

var acno = Ext.getCmp('tf_acid').value;if(Ext.isEmpty(acno)) acno='@';
TF.ExtHelper.setIframe('cp_main','/frameset?__report=dkfhz.rptdesign'+'&bankid='+bankid+'&operid='+operid+'&instcode='+instcode+'&tjoperid='+tjoperid+'&cliname='+cliname+'&begindate='+begindate+'&beginmaxdate='+beginmaxdate+'&endmindate='+endmindate+'&enddate='+enddate+'&loanacNo='+acno,'iframe_birt');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'wben.3321View' ]
});


		//-------全局变量定义-------开始-------
		var _flag = null;
		//-------全局变量定义-------结束-------


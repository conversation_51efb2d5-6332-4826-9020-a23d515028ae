Ext.define("mvc.controller.cust.0104Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_250010|7013_290001|7013_250010_#");
		//-------全局变量定义-------开始-------
		var _doeManagerInfo = null;
		var cifid = TF.ExtHelper.request('cifid');
		var readonly = TF.ExtHelper.request('readonly');
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_main_store',
			fields: [
				{name:'mngCifid'},				
				{name:'cliname'},				
				{name:'postname'},				
				{name:'isPar'},				
				{name:'entryDate'},				
				{name:'importLvl'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var doeManagerInfo=new Object();
//doeManagerInfo.cifid=cifid;
doeManagerInfo.cifid=cifid;
sendData.doeManagerInfo=doeManagerInfo;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_CUST0104_grid_main_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_main_pagingtoolbar')){
						Ext.getCmp('grid_main_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_del':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(!Ext.isEmpty(_doeManagerInfo)){
    Ext.Msg.confirm('提示', '确认删除记录吗?', function (button) {
      if (button == 'yes') {
     sendData.doeManagerInfo=_doeManagerInfo;
       sendData.doeManagerInfo.cifid=cifid;

       
					//前处理 结束
					sendData.flowId='0013_CUST0104_btn_del_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
Ext.Msg.show({title:'提示',msg:'删除信息成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.getCmp('btn_cancel').fireEvent('click');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
    }
 });
}
				}			
			},			
			'#btn_cancel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('form_detail').clearFields();
Ext.getCmp('btn_edit').setDisabled(true);
Ext.getCmp('btn_del').setDisabled(true);


Ext.getCmp('fieldset_form').setDisabled(false);
Ext.getCmp('fieldset_form').setTitle('新增管理者信息');
//Ext.getCmp('tf_mngCifid').setReadOnly(false);
Ext.getCmp('btn_subadd').setVisible(true);
Ext.getCmp('btn_subedit').setVisible(false);
Ext.getCmp('tf_cliname').setReadOnly(false);
					//前处理 结束
				}			
			},			
			'#btn_subadd':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.doeManagerInfo=Ext.getCmp('form_detail').getForm().getExtValues();
//sendData.doeOwnerInfo.cifid=cifid;
sendData.doeManagerInfo.cifid=cifid;
					//前处理 结束
					sendData.flowId='0013_CUST0104_btn_subadd_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('form_detail').clearFields();
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
Ext.Msg.show({title:'提示',msg:'保存信息成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.getCmp('btn_edit').setDisabled(true);
Ext.getCmp('btn_del').setDisabled(true);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_detail').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_subedit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.doeManagerInfo=Ext.getCmp('form_detail').getForm().getExtValues();
sendData.doeManagerInfo.cifid=cifid;
//sendData.doeManagerInfo.mngOutcifid=_doeManagerInfo.mngOutcifid;
					//前处理 结束
					sendData.flowId='0013_CUST0104_btn_subedit_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
Ext.Msg.show({title:'提示',msg:'保存信息成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.getCmp('btn_edit').setDisabled(true);
Ext.getCmp('btn_del').setDisabled(true);
Ext.getCmp('btn_cancel').fireEvent('click');
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_detail').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_edit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('fieldset_form').setTitle('编辑管理者情况信息');
Ext.getCmp('fieldset_form').setDisabled(false);
Ext.getCmp('btn_subadd').setVisible(false);
Ext.getCmp('btn_subedit').setVisible(true);
//Ext.getCmp('tf_mngCifid').setReadOnly(true);
Ext.getCmp('tf_cliname').setReadOnly(true);
					//前处理 结束
				}			
			},			
			'#main_panel':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(readonly=='1'){
Ext.getCmp('btn_edit').setVisible(false);
Ext.getCmp('btn_del').setVisible(false);
Ext.getCmp('btn_cancel').setVisible(false);
Ext.getCmp('btn_subadd').setVisible(false);
Ext.getCmp('btn_subedit').setVisible(false);
}else{
Ext.getCmp('btn_edit').setDisabled(true);
Ext.getCmp('btn_del').setDisabled(true);
}
					//前处理 结束
				}			
			},			
			'#tf_mngCifid':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择客户', '/PageLib/MvcBase.html?pageId=MCSW0006', 600, 400, function(data){
	me.setValue(data.cifid);
	me.setSelValue(data.cifid);
	Ext.getCmp('tf_cliname').setValue(data.cliname);
	Ext.getCmp('tf_identno').setValue(data.certno);
});
					//前处理 结束
				}			
			},			
			'#grid_main':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_doeManagerInfo=record.data;
var doeManagerInfo=new Object();
doeManagerInfo.mngCifid=_doeManagerInfo.mngCifid;
doeManagerInfo.cifid=cifid;
sendData.doeManagerInfo=doeManagerInfo;
					//前处理 结束
					sendData.flowId='0013_CUST0104_grid_main_itemclick';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象ob
Ext.getCmp('form_detail').getForm().setValues(obj.refObj.doeManagerInfo);
TF.ExtHelper.setValues('fieldset_reg',obj.refObj.VO1);
Ext.getCmp('btn_subadd').setVisible(false);
Ext.getCmp('btn_subedit').setVisible(false);
Ext.getCmp('btn_edit').setDisabled(false);
Ext.getCmp('btn_del').setDisabled(false);
Ext.getCmp('fieldset_form').setTitle('管理者信息');
Ext.getCmp('fieldset_form').setDisabled(true);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'cust.0104View' ]
});
Ext.define("mvc.controller.cust.0611Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_290001_#");

		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'applyno'},				
				{name:'serid'},				
				{name:'doeCifid1'},				
				{name:'doeCliname1'},				
				{name:'cliname'},				
				{name:'cifid'},				
				{name:'changeReason'},				
				{name:'operid'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=Ext.getCmp('tb_applyno').value;
sendData.cifid=Ext.getCmp('tb_cifid').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_CUST0611_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'flow_grid_store',
			fields: [
				{name:'phaseno'},				
				{name:'phasename'},				
				{name:'phasechoice'},				
				{name:'opername'},				
				{name:'begindate'},				
				{name:'enddate'},				
				{name:'phaseoptions'},				
				{name:'phaseaction'},				
				{name:'operidName'},				
				{name:'isrtn'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_appMainInfo!=null){
	sendData.applyno=_appMainInfo.applyno;
}
					//---------发送数据装配---结束------------
					sendData.flowId='0013_CUST0611_flow_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('flow_grid_pagingtoolbar')){
						Ext.getCmp('flow_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#flow_grid':{
			
			},			
			'#btn_search_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tb_applyno').reset();
Ext.getCmp('tb_cifid').reset();
Ext.getCmp('tb_cliname').reset();
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_search_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_black_edit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('winFlow', '实体变更详情','this','/PageLib/MvcBase.html?pageId=CUST0616&applyno='+_appMainInfo.applyno+'&cifid='+_appMainInfo.cifid,650,380,true,true);
					//前处理 结束
				}			
			},			
			'#btn_ok':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码

sendData.applyBlackinfo=_applyBlackinfo;
					//前处理 结束
					sendData.flowId='0013_CUST0611_btn_ok_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.alert('完成', '黑名单客户完成');
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('btn_black_edit').setDisabled(false);
_appMainInfo=record.data;
Ext.data.StoreManager.lookup('flow_grid_store').loadPage(1);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'cust.0611View' ]
});


		//-------全局变量定义-------开始-------
		var _appMainInfo = TF.ExtHelper.request('');
		var applyno = null;
		//-------全局变量定义-------结束-------


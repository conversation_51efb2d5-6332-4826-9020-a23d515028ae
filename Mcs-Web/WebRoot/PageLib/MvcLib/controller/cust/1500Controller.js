Ext.define("mvc.controller.cust.1500Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		
		//trees' stores

		this.control({
			'#panel_crosscheck':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;
//TF.ExtHelper.setIframe('panel_crosscheck','/PageLib/MvcBase.html?pageId=CUST1501&cifid='+cifid+'&readonly='+readonly+'&survey='+survey+'&preRepaysum='+preRepaysum+'&_selData='+_selData);
TF.ExtHelper.setIframe('panel_crosscheck','/PageLib/MvcBase.html?pageId=CUST1501&applyno='+applyno+'&survey='+survey);
					//前处理 结束
				}			
			},			
			'#panel_scordresult':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;
TF.ExtHelper.setIframe('panel_scordresult','/PageLib/MvcBase.html?pageId=CUST1502&applyno='+applyno);
					//前处理 结束
				}			
			},			
			'#panel_result':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.cifid=cifid;
					//前处理 结束
					sendData.flowId='0013_CUST1500_panel_result_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;

if(obj.refObj.VO1.cstype == 'A01'){
	TF.ExtHelper.setIframe('panel_result','/PageLib/MvcBase.html?pageId=APLY0021&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey+'&checked='+checked+'&preRepaysum='+preRepaysum+'&_selData='+_selData);
}else{
	TF.ExtHelper.setIframe('panel_result','/PageLib/MvcBase.html?pageId=APLY1107&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey+'&checked='+checked+'&preRepaysum='+preRepaysum+'&_selData='+_selData+'&yflag='+yflag);
}
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#panel_cust':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;
TF.ExtHelper.setIframe('panel_cust','/PageLib/MvcBase.html?pageId=APLY1118&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey);
					//前处理 结束
				}			
			},			
			'#panel_evaluate':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.cifid=cifid;
//readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;
//TF.ExtHelper.setIframe('panel_evaluate','/PageLib/MvcBase.html?pageId=APLY1502&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey);
					//前处理 结束
					sendData.flowId='0013_CUST1500_panel_evaluate_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
readonly=(Ext.isEmpty(readonly)||readonly=='null')?0:readonly;

if(obj.refObj.VO1.cstype == 'A01'){
	TF.ExtHelper.setIframe('panel_evaluate','/PageLib/MvcBase.html?pageId=APLY1501&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey);
}else{
	TF.ExtHelper.setIframe('panel_evaluate','/PageLib/MvcBase.html?pageId=APLY1502&cifid='+cifid+'&readonly='+readonly+'&applyno='+applyno+'&survey='+survey);
}
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'cust.1500View' ]
});


		//-------全局变量定义-------开始-------
		var _selData = _selData;
		var applyno = TF.ExtHelper.request('applyno');
		var checked = TF.ExtHelper.request('checked');
		var cifid = TF.ExtHelper.request('cifid');
		var preRepaysum = TF.ExtHelper.request('preRepaysum');
		var readonly = TF.ExtHelper.request('readonly');
		var survey = TF.ExtHelper.request('survey');
		var yflag = TF.ExtHelper.request('yflag');
		//-------全局变量定义-------结束-------


Ext.define("mvc.controller.cust.0101Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_200064|7013_290001|7013_250008_#|7013_200064_#|7013_200079_#");
		//-------全局变量定义-------开始-------
		var _doeBase = null;
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_main_store',
			autoLoad:true,
			fields: [
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'industrytype'},				
				{name:'operDate'},				
				{name:'doeaddr'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.cliname=Ext.getCmp('tb_entity_name').value;
sendData.operDate=Ext.getCmp('tb_begindate').value;
sendData.cifid=Ext.getCmp('tb_cifid').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_CUST0101_grid_main_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_entity_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var doeBase=Ext.getCmp('form_entity').getForm().getExtValues();

sendData.doeBase=doeBase;
					//前处理 结束
					sendData.flowId='0013_CUST0101_btn_entity_submit_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.alert('保存', '保存成功！');
Ext.getCmp('win_add').close();
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.alert('错误',obj.result);
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_entity').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_readFile':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(Ext.isEmpty(Ext.getCmp('tf_uploadFile').filesUrl)){
	Ext.Msg.alert('错误', '请先选择要读取的文件');
	return;
}
sendData.fileurl=Ext.getCmp('tf_uploadFile').filesUrl;
					//前处理 结束
					sendData.flowId='0013_CUST0101_btn_readFile_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('tf_cliname').setValue(obj.refObj.VO1.cliname);
Ext.getCmp('tf_doeaddr').setValue(obj.refObj.VO1.doeaddr);
Ext.getCmp('tf_doetype').setValue(obj.refObj.VO1.doetype);
Ext.getCmp('tf_industrytype').setValue(obj.refObj.VO1.industrytype);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_entity_search_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tb_begindate').reset();
Ext.getCmp('tb_entity_name').reset();

Ext.getCmp('tb_cifid').reset();
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_entity_imp':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					Ext.create('win_imp').show();
					//前处理 结束
				}			
			},			
			'#btn_entity_search_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_delete':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码

if(!Ext.isEmpty(_doeBase)){
    Ext.Msg.confirm('提示', '确认取消申请吗?', function (button) {
      if (button == 'yes') {
     sendData.doeBase=_doeBase;

       
					//前处理 结束
					sendData.flowId='0013_CUST0101_btn_delete_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
Ext.Msg.alert('成功', '删除实体成功');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
    }
 });
}
				}			
			},			
			'#tf_legal':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
 

  TF.ExtHelper.selWindow('选择客户', '/PageLib/MvcBase.html?pageId=MCSW0006', 600, 400, function(data){
    me.setValue(data.cliname);
	me.setSelValue(data.cliname);
	//Ext.getCmp('tf_cliname').setValue(data.cliname);
   
	
	Ext.getCmp('tf_legalcertno').setValue(data.certno);
});
					//前处理 结束
				}			
			},			
			'#btn_entity_detail':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var cifid=_doeBase.cifid;

TF.ExtHelper.showWindow('JRSQ', '实体详情','toptask','/PageLib/Navigation/VerticaMenu.html?menuId=sm_1010&cifid='+cifid,1310,640,true,true);
					//前处理 结束
				}			
			},			
			'#tf_legal_imp':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码

TF.ExtHelper.selWindow('选择客户', '/PageLib/MvcBase.html?pageId=MCSW0006', 600, 400, function(data){
	me.setValue(data.cliname);
	me.setSelValue(data.cliname);
	Ext.getCmp('tf_legalcertno_imp').setValue(data.certno);
});
					//前处理 结束
				}			
			},			
			'#btn_entity_imp_Submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.doeBase=Ext.getCmp('form_entity_imp').getForm().getExtValues();
					//前处理 结束
					sendData.flowId='0013_CUST0101_btn_entity_imp_Submit_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.alert('成功', '保存成功！');
Ext.getCmp('win_imp').close();
Ext.data.StoreManager.lookup('grid_main_store').loadPage(1);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.alert('错误',obj.result);
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_entity_imp').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_entity_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					Ext.create('win_add').show();
					//前处理 结束
				}			
			},			
			'#grid_main':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_doeBase=record.data;

Ext.getCmp('btn_entity_detail').setDisabled(false);
Ext.getCmp('btn_delete').setDisabled(false);
Ext.getCmp('form_entity_detail').clearFields();
Ext.getCmp('form_entity_detail').getForm().setValues(_doeBase);
Ext.getCmp('form_entity_detail').setTitle('经营实体概要');
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'cust.0101View' ]
});
Ext.define("mvc.controller.aply.4206Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_MKT0001_#|7013_MKT0001|7013_201221");

		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'latentId'},				
				{name:'oldoperid'},				
				{name:'opername'},				
				{name:'instname'},				
				{name:'createTime'},				
				{name:'creditNum'},				
				{name:'cliname'},				
				{name:'phone'},				
				{name:'address'},				
				{name:'busSts'},				
				{name:'project'},				
				{name:'property'},				
				{name:'firstCredit'},				
				{name:'contactDate'},				
				{name:'marketplanDate'},				
				{name:'recphaseno'},				
				{name:'operid'},				
				{name:'applyno'},				
				{name:'mcsSts'},				
				{name:'isMainOperid'},				
				{name:'isBlack'},				
				{name:'sevenMarket'},				
				{name:'reason'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var v1 = Ext.getCmp('tb_date1').value;
sendData.visitTime=v1;
var v2 = Ext.getCmp('tb_date2').value;
sendData.createTime=v2;
sendData.busSts=Ext.getCmp('tf_busSts').value;
sendData.firstCredit=Ext.getCmp('tf_firstCredit1').value;
sendData.firstjudgecourt=Ext.getCmp('tf_firstCredit2').value;
sendData.operid=Ext.getCmp('tf_mngname').value;
sendData.flag='4';
sendData.phone=Ext.getCmp('tf_phone').value;
sendData.cliname=Ext.getCmp('tf_cliname').value;


sendData.instname=Ext.getCmp('tf_instname').value;
var a=Ext.getCmp('tb_plan_date_1').getRawValue();
var a1 = Ext.util.Format.date(a, 'Y-m-d');//格式化日期控件值
sendData.appTime=a1;

	//var audTime=Ext.getCmp('tb_plan_date_2').value;
	var audTime=Ext.getCmp('tb_plan_date_2').getRawValue();
	var s = Ext.util.Format.date(audTime, 'Y-m-d');//格式化日期控件值
	console.log(s);
	sendData.audTime=s;
sendData.begintime=Ext.getCmp('tb_date1_1').value;
var applyDate=Ext.getCmp('tb_date2_1').value;
var b = Ext.util.Format.date(applyDate, 'Y-m-d');//格式化日期控件值
sendData.applyDate=b;

console.log(sendData.instname);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_APLY4206_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'tf_fileList_store',
			fields: [
				{name:'serid'},				
				{name:'iptDate'},				
				{name:'filename'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
console.log("查询文件列表参数--------------------");

sendData.latentId=_selData.latentId;

console.log(sendData);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_APLY4206_tf_fileList_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('tf_fileList_pagingtoolbar')){
						Ext.getCmp('tf_fileList_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'grid_file_detail_store',
			fields: [
				{name:'serid'},				
				{name:'iptDate'},				
				{name:'filename'},				
				{name:'fileurl'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno = applyno;
sendData.latentId = _selData.latentId;
sendData.filetype = '10000';
console.log("查询附件详情");
console.log(sendData);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_APLY4206_grid_file_detail_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_file_detail_pagingtoolbar')){
						Ext.getCmp('grid_file_detail_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'tf_record_store',
			fields: [
				{name:'visitTime'},				
				{name:'conditionRecord'},				
				{name:'opername'},				
				{name:'nextTime'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.latentId=_selData.latentId;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_APLY4206_tf_record_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('tf_record_pagingtoolbar')){
						Ext.getCmp('tf_record_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_file_pre':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//window.open(fileurl);
TF.ExtHelper.showWindow('','营销管理-附件信息','toptask','/PageLib/MvcImage.html?pageId=APLY4208&applyno='+_selData.latentId+'&app=W&readonly=1&tqType=cswj',1310,640,true,true);
//Ext.getCmp('btn_file_load').setDisabled(true);
//Ext.getCmp('btn_file_pre').setDisabled(true);
					//前处理 结束
				}			
			},			
			'#tf_trans':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_trans').show();
					//前处理 结束
				}			
			},			
			'#tf_eff':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(!Ext.isEmpty(_selData)){
    Ext.Msg.confirm('提示', '确认将删除吗?', function (button) {
        if (button == 'yes') {
           var sels=Ext.getCmp('main_grid').getSelectionModel().getSelection();
			var array=new Array();
			for(var i=0;i<sels.length;i++){
			    var item=sels[i].data;
			    //item.newoperid=Ext.getCmp('tf_newoperid_submit').getSelValue();
			    //item.brf=Ext.getCmp('tf_brf_submit').value;
			    array.push(item);
			}
			
			sendData.VO1=array;
            
					//前处理 结束
					sendData.flowId='0013_APLY4206_tf_eff_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({title:'提示',msg:'删除成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
        }
    });
}
				}			
			},			
			'#tf_fileList':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('btn_detail').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_update_file':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('btn_detail').setDisabled(true);
Ext.getCmp('btn_update_file').setDisabled(true);
var pageid = 'MCPT3013';
TF.ExtHelper.showWindow('', '法律文件上传','this','/PageLib/MvcBase.html?pageId='+pageid+'&applyno='+applyno+'&cifid='+cifid,1024,'86%',true,true);
					//前处理 结束
				}			
			},			
			'#tf_win1_cancel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('win_1').close();
					//前处理 结束
				}			
			},			
			'#btn_detail':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_file_detail').show();
Ext.getCmp('btn_detail').setDisabled(true);
Ext.getCmp('btn_update_file').setDisabled(true);
					//前处理 结束
				}			
			},			
			'#tf_record':{
			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_selData=record.data;
var phaseno = _selData.recphaseno;
cifid= _selData.latentId;
Ext.getCmp('tf_info').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#form_info':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
console.log(_selData);
Ext.getCmp('tf_1').setValue(_selData.latentId);
Ext.getCmp('tf_2').setValue(_selData.createTime);
Ext.getCmp('tf_3').setValue(_selData.creditNum);
Ext.getCmp('tf_4').setValue(_selData.cliname);
Ext.getCmp('tf_5').setValue(_selData.phone);
Ext.getCmp('tf_6').setValue(_selData.othContact);
Ext.getCmp('tf_isBlack').setValue(_selData.isBlack);
Ext.getCmp('tf_sevenMarket').setValue(_selData.sevenMarket);
Ext.getCmp('tf_mcsSts').setValue(_selData.mcsSts);
Ext.getCmp('tf_14').setValue(_selData.property);
if(_selData.isMainOperid=='1'){
	Ext.getCmp('tf_ismain_1').setValue('是');
	Ext.getCmp('tf_ismain_2').setValue('否');
}
if(_selData.isMainOperid=='2'){
	Ext.getCmp('tf_ismain_2').setValue('是');
	Ext.getCmp('tf_ismain_1').setValue('否');
}
if(_selData.isMainOperid=='0'){
	Ext.getCmp('tf_ismain_2').setValue('否');
	Ext.getCmp('tf_ismain_1').setValue('否');
}

sendData.latentId=_selData.latentId;
/**Ext.getCmp('tf_7').setValue(_selData.repayType);
Ext.getCmp('tf_8').setValue(_selData.rependdate);
Ext.getCmp('tf_9').setValue(_selData.busBal);
Ext.getCmp('tf_10').setValue(_selData.leaveTerm);
Ext.getCmp('tf_11').setValue(_selData.applynum);
Ext.getCmp('tf_12').setValue(_selData.appSts);
Ext.getCmp('tf_13').setValue(_selData.extstartdate);
Ext.getCmp('tf_14').setValue(_selData.extenddate);
Ext.getCmp('tf_reason').setValue(_selData.changerReason);**/
					//前处理 结束
					sendData.flowId='0013_APLY4206_form_info_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
console.log("返回值------------");
console.log(obj.refObj);
Ext.getCmp('form_info').getForm().setValues(obj.refObj.mktCustomer);
Ext.getCmp('tf_14').setValue(obj.refObj.mktCustomer.propertyLast+"元至"+obj.refObj.mktCustomer.propertyBigst+'元');
Ext.getCmp('tf_mcsSts').setValue(_selData.mcsSts);
Ext.getCmp('tf_6').setValue(obj.refObj.mktCustomer.othContact);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#tf_win1_cancel_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('win_1').close();
					//前处理 结束
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码

Ext.getCmp('tb_date1').reset();
Ext.getCmp('tb_date2').reset();
Ext.getCmp('tf_busSts').reset();
Ext.getCmp('tf_firstCredit1').reset();
Ext.getCmp('tf_firstCredit2').reset();
Ext.getCmp('tf_cliname').reset();
Ext.getCmp('tf_phone').reset();
Ext.getCmp('tf_mngname').reset();
Ext.getCmp('tb_date1_1').reset();
Ext.getCmp('tb_date2_1').reset();
Ext.getCmp('tf_instname').reset();
Ext.getCmp('tb_plan_date_1').reset();
Ext.getCmp('tb_plan_date_2').reset();


Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_file_load':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.DownMultiFiles(filename ,fileurl.replace(/\|/g,''),serid);
Ext.getCmp('btn_file_load').setDisabled(true);
Ext.getCmp('btn_file_pre').setDisabled(true);
					//前处理 结束
				}			
			},			
			'#tf_win1_eff_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var sels=Ext.getCmp('main_grid').getSelectionModel().getSelection();
var array=new Array();
for(var i=0;i<sels.length;i++){
    var item=sels[i].data;
    //item.newoperid=Ext.getCmp('tf_newoperid_submit').getSelValue();
    //item.brf=Ext.getCmp('tf_brf_submit').value;
    array.push(item);
}

sendData.VO1=array;
sendData.operid=Ext.getCmp('tf_oper').value;
//sendData.reason=Ext.getCmp('tf_reason').value;
sendData.reason='原客户经理离职';
console.log(sendData);

Ext.getCmp('win_trans').close();
					//前处理 结束
					sendData.flowId='0013_APLY4206_tf_win1_eff_1_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({title:'提示',msg:'移交成功',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_flow':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=_selData.applyno;
var bustype = _selData.bustype;
if(bustype=='40'){
	sendData.flowtype='BATCH3';
	}else{
	sendData.flowtype='INDSINAPL';
	}
//Ext.getCmp('btn_flow').setDisabled(true);
//Ext.getCmp('btn_detail').setDisabled(true);
					//前处理 结束
					sendData.flowId='0013_APLY4206_btn_flow_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var cifid=_selData.cifid;
var applyno=_selData.applyno;
var phaseno=_selData.recphaseno;
//var bustype = _selData.bustype;
var modno='';
/*if(bustype=='50'){
	modno='MCPT01';
}else **/
if(phaseno=='2100'){
	modno='SURVEY01';
}else if(phaseno=='2300'){
	modno='SURVEY01';
}else if(phaseno=='2200'){
	modno='CHECK01';
}else if(phaseno=='3100'){
	modno='AFRIM01';
}else if(phaseno=='1100'){
	modno='SURVEY06';
}

if(bustype=='40'){//授信业务贷审会阶段风险探测同单报单批
	if(phaseno=='3100'){
	modno='AFRIM02';
	}
}
TF.ExtHelper.showWindow('winFlow', '业务申请','this','/PageLib/MvcBase.html?pageId=FLOW0017&applyno='+applyno+'&modno='+modno+'&cifid='+cifid+'&btnid='+me.id,600,400,true,true);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#tf_info':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_1').show();
					//前处理 结束
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码

Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#tf_20':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var sels=Ext.getCmp('main_grid').getSelectionModel().getSelection();
var array=new Array();
for(var i=0;i<sels.length;i++){
    var item=sels[i].data;
    //item.newoperid=Ext.getCmp('tf_newoperid_submit').getSelValue();
    //item.brf=Ext.getCmp('tf_brf_submit').value;
    array.push(item);
}
sendData.VO2=array;
					//前处理 结束
					sendData.flowId='0013_APLY4206_tf_20_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
console.log(obj.refObj.VO1);
if(obj.refObj.VO1.list.length>0){
    TF.ExtHelper.setOptStore('tf_oper',obj.refObj.VO1.list,{'optionValue':'key','optionLabel':'value'},true);
}
Ext.getCmp('tf_date').setValue(tf.sysdb.workDate);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#grid_file_detail':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
fileurl = record.data.fileurl;
filename =  record.data.filename;
serid = record.data.serid;
_selData2=record.data;
Ext.getCmp('btn_file_pre').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#tf_win1_eff':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=Ext.getCmp('tf_applyno').value;
					//前处理 结束
					sendData.flowId='0013_APLY4206_tf_win1_eff_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('win_1').close();

Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'aply.4206View' ]
});


		//-------全局变量定义-------开始-------
		var _selData = null;
		var _selData2 = null;
		var applyno = TF.ExtHelper.request('applyno');
		var cifid = null;
		var type = null;
		//-------全局变量定义-------结束-------


Ext.define("mvc.controller.dkhx.0004Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
				//-------全局变量定义-------开始-------
		var _record = null;
		var applyno = TF.ExtHelper.request('applyno');
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'applyno'},				
				{name:'iptUsrName'},				
				{name:'prdtNo'},				
				{name:'contno'},				
				{name:'vchno'},				
				{name:'cliname'},				
				{name:'busSum'},				
				{name:'verifiAmt'},				
				{name:'overDay'},				
				{name:'overBal'},				
				{name:'verifiFlag'},				
				{name:'verifiIntst'},				
				{name:'verifiFee'},				
				{name:'applynoDate'},				
				{name:'iptUser'},				
				{name:'iptBrno'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno = applyno;
console.log(applyno);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_DKHX0004_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_record = record.data;
//console.log(_record);
Ext.getCmp('show_form').getForm().setValues(_record);
					//前处理 结束
				},				itemdblclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_record){
	var win = window.parent.Ext.getCmp('sel_win');
    if (win != undefined && win != null) {
    	win.selData = _record;
        win.close();
    }
}else{
	Ext.Msg.alert('提示','请选择一条记录');
}
					//前处理 结束
				}			
			},			
			'#show_form':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//Ext.getCmp('show_form').getForm().setValues(_record);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'dkhx.0004View' ]
});
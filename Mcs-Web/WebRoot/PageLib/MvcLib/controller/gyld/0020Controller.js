Ext.define("mvc.controller.gyld.0020Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_200079_#|7013_400001_#|7013_250026_#|7013_200014_#|7013_250029_#|7013_220025_#|7013_290001_#|7013_220007_#|7013_220008_#|7013_250027_#|7013_220019_#|7013_220022_#|7013_250058_#|7013_240002_#|7013_220024_#");
		//-------全局变量定义-------开始-------
		var applyno = TF.ExtHelper.request('applyno');
		var impl = TF.ExtHelper.request('impl');
		//-------全局变量定义-------结束-------
		//grids' stores
		
		//trees' stores

		this.control({
			'#btn_printCont_xt':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var contno=Ext.getCmp('tf_contno').value;
if(!Ext.isEmpty(contno)){
	sendData.contno=contno;
	
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_printCont_xt_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var file=obj.refObj.VO1.filePath;
window.location.href=downLoadServlet + '?mode=get&fileName='+file;
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
}else{
	Ext.Msg.show({title:'温馨提示',
	msg:'请先确认合同要素内容，再进行业务合同打印！',
	icon:Ext.Msg.WARNING,
	buttons:Ext.Msg.OK,
	closable:false,
	fn:function(btnId){
		switch(btnId){
			case "ok":
				break;
		}
	}
});
}
				}			
			},			
			'#btn_read_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var acno = TF.McsHelper.GetCardNo(); //账号
if(acno.length<1) {
	Ext.Msg.show({title:'提示',msg:'未录入卡号，请重新点击读卡',icon:Ext.Msg.WARNING,buttons: Ext.Msg.OK});
	return;
}
Ext.getCmp('tf_reppricaNo').setValue(acno);
Ext.getCmp('btn_coreCust_2').fireEvent('click');
					//前处理 结束
				}			
			},			
			'#btn_conffirm':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.acBusinesscont=Ext.getCmp('form_cont').getForm().getExtValues();

var termType=Ext.getCmp('tfc_termType').value;
var term=Ext.getCmp('tfn_term').value;
if(Ext.getCmp('df_begindate').getRawValue()<tf.sysdb.workDate){
	Ext.Msg.show({title:'错误',msg:'合同起始日期：'+Ext.getCmp('df_begindate').getRawValue()+'应在当前系统日期之后，当前系统日期：'+tf.sysdb.workDate+'！',icon:Ext.Msg.ERROR,buttons: Ext.Msg.OK});
	return;
}
/*if(termType=='M'){
	
  if(Ext.getCmp('df_enddate').getRawValue()<Ext.util.Format.date(Ext.Date.add(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH, (term-1)), Ext.Date.DAY,1),'Ymd')||
  Ext.getCmp('df_enddate').getRawValue()>Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd'))
	{
  		Ext.Msg.show({title:'错误',msg:'终止日期应在【'+Ext.util.Format.date(Ext.Date.add(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH, (term-1)), Ext.Date.DAY,1),'Ymd')+','+   Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd')+'】之间',icon:Ext.Msg.ERROR,buttons: Ext.Msg.OK});
		return;
	}
}else{
	if(Ext.getCmp('df_enddate').getRawValue()!=Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.DAY, term))
	{
		Ext.Msg.show({title:'错误',msg:'终止日期应为'+Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.DAY, term),icon:Ext.Msg.ERROR,buttons: Ext.Msg.OK});
		return;
	}
}*/
var guarType=Ext.getCmp('tfc_guartype').value;
var othGuarType=Ext.getCmp('tfc_othGuarType').value;
if(guarType==othGuarType){
	//Ext.Msg.alert('提示', '主要担保方式和其他担保方式不能相同！');
	Ext.Msg.show({title:'提示',msg:'主要担保方式和其他担保方式不能相同！',icon:[Ext.Msg.WARNING],buttons: Ext.Msg.OK});
	return;

}

//校验放款账号户名与贷款申请户名是否一致
/*var cliname = Ext.getCmp('tfd_depaccNa').value;//放款账号户名
var tfd_cliname = Ext.getCmp('tf_cliname').value;//贷款申请客户名称
console.log('AAAA');
if(cliname!=tfd_cliname){
		Ext.Msg.show({title:'提示',msg:'放款账号户名与贷款申请户名不一致！',icon:[Ext.Msg.WARNING],buttons: Ext.Msg.OK});
		return;
}*/
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_conffirm_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({
	title:'提示',
	msg:'确认合同信息成功！合同号['+obj.refObj.VO1.contno+']',
	icon:Ext.Msg.SUCCESS,
	buttons:Ext.Msg.OK,
	closable:false,
	fn:function(btnId){
		window.location.reload();
	}
});
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_cont').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_read_1_1_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var acno = TF.McsHelper.GetBookNo(); //账号
if(acno.length<1) {
	Ext.Msg.show({title:'提示',msg:'未录入卡号，请重新点击读卡',icon:Ext.Msg.WARNING,buttons: Ext.Msg.OK});
	return;
}
Ext.getCmp('tf_reppricaNo').setValue(acno);
Ext.getCmp('btn_coreCust_2').fireEvent('click');
					//前处理 结束
				}			
			},			
			'#cp_main':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(impl=='W'){
	
}else{
	Ext.getCmp('btn_conffirm').setVisible(false);
	Ext.getCmp('btn_printCont_xt').setVisible(false);
	Ext.getCmp('btn_contprint_xt').setVisible(false);
	Ext.getCmp('field_1').setDisabled(true);
	Ext.getCmp('field_2').setDisabled(true);
	Ext.getCmp('field_3').setDisabled(true);
	Ext.getCmp('field_4').setDisabled(true);
	Ext.getCmp('field_5').setDisabled(true);
	Ext.getCmp('field_6').setDisabled(true);
}
					//前处理 结束
				}			
			},			
			'#form_cont':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=applyno;
					//前处理 结束
					sendData.flowId='0013_GYLD0020_form_cont_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var require=obj.refObj.VO1.sts;
var appMainInfo=obj.refObj.appMainInfo;
var btnFlg = obj.refObj.VO1.btnFlg;

if(btnFlg=='1'){//最高额，不上会
	 if(require=='1'){//未签合同
    var afmResInfo=obj.refObj.afmResInfo;
    var appBusInfo=obj.refObj.appBusInfo;
    Ext.getCmp('form_cont').getForm().setValues(obj.refObj.afmResInfo);
    //借款信息从申请信息出
	Ext.getCmp('tfc_termType').setValue(appMainInfo.appTermType);//期限类型
	Ext.getCmp('tfn_term').setValue(appMainInfo.appTerm);//期限值
	Ext.getCmp('tfc_curr').setValue(appMainInfo.curr);//币种
	Ext.getCmp('tfn_busSum').setValue(appMainInfo.appSum);//合同金额
	Ext.getCmp('tfc_repayType').setValue(appMainInfo.repayType);//还款方式
	//Ext.getCmp('tfc_ibtype').setValue('5');//计息方式默认按月计息
	Ext.getCmp('tfc_isCic').setValue(obj.refObj.cnProduct.circlePutout);//是否可循环
	Ext.getCmp('hd_repayday').setValue(obj.refObj.appMainInfo.repayday);//指定还款日
    Ext.getCmp('tfc_guartype').setValue(obj.refObj.appMainInfo.guarType);//担保方式
	Ext.getCmp('tfc_othGuarType').setValue(obj.refObj.appMainInfo.othGuarType);//其他担保方式
    Ext.getCmp('hd_prdtNo').setValue(obj.refObj.appMainInfo.prdtNo);//产品号
	Ext.getCmp('hd_industrytype').setValue(appBusInfo.industrytype);//贷款投向码
    Ext.getCmp('tf_applyno').setValue(appMainInfo.applyno);
    Ext.getCmp('tf_cifid').setValue(appMainInfo.cifid);
    Ext.getCmp('tf_cliname').setValue(appMainInfo.cliname);
    Ext.getCmp('tfc_certtype').setValue(appMainInfo.certtype);
    Ext.getCmp('tf_certno').setValue(appMainInfo.certno);
    Ext.getCmp('tfc_paytype').readOnly=false;
    Ext.getCmp('tfc_paytype').fieldCls='field_cls_2';
    Ext.getCmp('tf_prdtNa').setValue('['+obj.refObj.appMainInfo.prdtNo+']'+obj.refObj.VO1.prdtNa);
    Ext.getCmp('tfs_industrytype').setValue('['+appBusInfo.industrytype+']'+obj.refObj.VO1.itemname);
    //计息方式(默认按月计息)
    Ext.getCmp('tfc_ibtype').setValue('5');
    //业务发生类型(默认申请主表类型)
    Ext.getCmp('tfc_occurtype').setValue(obj.refObj.appMainInfo.occurtype);
    //业务类型(默认申请主表类型)
    Ext.getCmp('tfc_bustype').setValue(obj.refObj.appMainInfo.bustype);
    //合同金额使用情况
    Ext.getCmp('tfn_unusedSum').setValue(obj.refObj.appMainInfo.appSum);
    Ext.getCmp('tfn_usedSum').setValue('0');
    Ext.getCmp('tfn_busBal').setValue('0');
    Ext.getCmp('hd_repayday').setValue(obj.refObj.appBusInfo.repayday);
    Ext.getCmp('tfd_surveyUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_checkUsr').setValue(obj.refObj.VO1.checkOperidName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.manageOperidName);
    Ext.getCmp('tfd_appinstcode').setValue(obj.refObj.VO1.manageInstcodeName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.balOperidName);
    Ext.getCmp('tfd_iptUsr').setValue(obj.refObj.VO1.operidName);
    Ext.getCmp('tfd_instcode').setValue(obj.refObj.VO1.instcodeName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('hd_survey').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_check').setValue(obj.refObj.appMainInfo.subUsr);
    Ext.getCmp('hd_manageOperid').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_balOperid').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_operid').setValue(tf.sysdb.optel);
    Ext.getCmp('hd_instcode').setValue(tf.sysdb.branchId);
    Ext.getCmp('hd_manageInstcode').setValue(obj.refObj.appMainInfo.surveyBrno);
    Ext.getCmp('hd_balInstcode').setValue(obj.refObj.appMainInfo.surveyBrno);
    Ext.getCmp('tfc_guartype').setValue(obj.refObj.VO1.guarType);
    Ext.getCmp('tfc_othGuarType').setValue(obj.refObj.VO1.othGuarType);
    Ext.getCmp('tfn_rateFloat').setValue(obj.refObj.VO1.rateFloat);
    //最高额用信支付方式默认为自主支付
    Ext.getCmp('tfc_paytype').setValue('1');
    Ext.getCmp('tf_repayLimit').setValue(appBusInfo.repayLimit);
    
    //获取产品是否循環標志
	if(obj.refObj.cnProduct.circlePutout=='1'){//如果是循环贷则打印合同按钮改为循环贷的打印合同按钮
		Ext.getCmp('btn_contprint_xt').setVisible(true);
		Ext.getCmp('btn_printCont_xt').setVisible(false);
	}else{
		Ext.getCmp('btn_contprint_xt').setVisible(false);
		Ext.getCmp('btn_printCont_xt').setVisible(true);
	}
    //涉农相关
	Ext.getCmp('tb_isFarm').setValue(obj.refObj.appBusInfo.isFarm);
    Ext.getCmp('tf_nlmy').setValue('['+obj.refObj.appBusInfo.nlmy+']'+obj.refObj.VO1.itemNlmy);
	Ext.getCmp('tf_nlmy').setSelValue(obj.refObj.appBusInfo.nlmy);
	//合同起始日期和结束日期
	var term=appMainInfo.appTerm;
	Ext.getCmp('df_begindate').setValue(tf.sysdb.workDate);
	Ext.getCmp('df_enddate').setValue(Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd'));
}
	else{
    Ext.getCmp('form_cont').getForm().setValues(obj.refObj.acBusinesscont);
    Ext.getCmp('tf_prdtNa').setValue('['+obj.refObj.acBusinesscont.prdtNo+']'+obj.refObj.VO1.prdtNa);
    Ext.getCmp('tfs_industrytype').setValue('['+obj.refObj.acBusinesscont.industrytype+']'+obj.refObj.VO1.itemname);  
    Ext.getCmp('tfd_surveyUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_checkUsr').setValue(obj.refObj.VO1.checkOperidName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.manageOperidName);
    Ext.getCmp('tfd_appinstcode').setValue(obj.refObj.VO1.manageInstcodeName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.balOperidName);
    Ext.getCmp('tfd_iptUsr').setValue(obj.refObj.VO1.operidName);
    Ext.getCmp('tfd_instcode').setValue(obj.refObj.VO1.instcodeName);
    Ext.getCmp('tf_loantype').setValue('2');
    //Ext.getCmp('tfn_rateFloat').setValue(obj.refObj.VO1.rateFloat);  
    //获取产品编号
	if(obj.refObj.cnProduct.circlePutout=='1'){//如果是循环贷则打印合同按钮改为循环贷的打印合同按钮obj.refObj.acBusinesscont.isCic=='1'
		Ext.getCmp('btn_contprint_xt').setVisible(true);
		Ext.getCmp('btn_printCont_xt').setVisible(false);
	}else{
		Ext.getCmp('btn_contprint_xt').setVisible(false);
		Ext.getCmp('btn_printCont_xt').setVisible(true);
	}
    Ext.getCmp('tf_nlmy').setValue('['+obj.refObj.acBusinesscont.nlmy+']'+obj.refObj.VO1.itemNlmy);
	Ext.getCmp('tf_nlmy').setSelValue(obj.refObj.acBusinesscont.nlmy);
}
}else{
	if(require=='1'){
    var afmResInfo=obj.refObj.afmResInfo;
    var appBusInfo=obj.refObj.appBusInfo;
    Ext.getCmp('form_cont').getForm().setValues(obj.refObj.afmResInfo);
    Ext.getCmp('tf_applyno').setValue(appMainInfo.applyno);
    Ext.getCmp('tf_cifid').setValue(appMainInfo.cifid);
    Ext.getCmp('tf_cliname').setValue(appMainInfo.cliname);
    Ext.getCmp('tfc_certtype').setValue(appMainInfo.certtype);
    Ext.getCmp('tf_certno').setValue(appMainInfo.certno);
    Ext.getCmp('tf_prdtNa').setValue('['+obj.refObj.appMainInfo.prdtNo+']'+obj.refObj.VO1.prdtNa);
    Ext.getCmp('tfs_industrytype').setValue('['+afmResInfo.industrytype+']'+obj.refObj.VO1.itemname);
    //电话号
    Ext.getCmp('tx_metl').setValue(obj.refObj.VO1.metel);
    //计息方式(默认按月计息)
    Ext.getCmp('tfc_ibtype').setValue('5');
    //业务发生类型(默认申请主表类型)
    Ext.getCmp('tfc_occurtype').setValue(obj.refObj.afmResInfo.occurtype);
    //业务类型(默认申请主表类型)
    Ext.getCmp('tfc_bustype').setValue(obj.refObj.appMainInfo.bustype);
    //合同金额使用情况
    //合同金额使用情况
    Ext.getCmp('tfn_unusedSum').setValue(obj.refObj.afmResInfo.busSum);
    Ext.getCmp('tfn_usedSum').setValue('0');
    Ext.getCmp('tfn_busBal').setValue('0');
    Ext.getCmp('hd_repayday').setValue(obj.refObj.afmResInfo.repayDate);
    Ext.getCmp('tfd_surveyUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_checkUsr').setValue(obj.refObj.VO1.checkOperidName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.manageOperidName);
    Ext.getCmp('tfd_appinstcode').setValue(obj.refObj.VO1.manageInstcodeName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.balOperidName);
    Ext.getCmp('tfd_iptUsr').setValue(obj.refObj.VO1.operidName);
    Ext.getCmp('tfd_instcode').setValue(obj.refObj.VO1.instcodeName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('hd_survey').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_check').setValue(obj.refObj.appMainInfo.subUsr);
    Ext.getCmp('hd_manageOperid').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_balOperid').setValue(obj.refObj.appMainInfo.surveyUsr);
    Ext.getCmp('hd_operid').setValue(tf.sysdb.optel);
    Ext.getCmp('hd_instcode').setValue(tf.sysdb.branchId);
    Ext.getCmp('hd_manageInstcode').setValue(obj.refObj.appMainInfo.surveyBrno);
    Ext.getCmp('hd_balInstcode').setValue(obj.refObj.appMainInfo.surveyBrno);
    Ext.getCmp('tfc_guartype').setValue(obj.refObj.VO1.guarType);
    Ext.getCmp('tfc_othGuarType').setValue(obj.refObj.VO1.othGuarType);
    Ext.getCmp('tfn_rateFloat').setValue(obj.refObj.VO1.rateFloat);
    Ext.getCmp('tf_repayLimit').setValue(obj.refObj.afmResInfo.repayLimit);
     //获取贷审会阶段是否循环标志
	if(obj.refObj.cnProduct.circlePutout=='1'){//如果是循环贷则打印合同按钮改为循环贷的打印合同按钮obj.refObj.afmResInfo.isCic=='1'
		Ext.getCmp('btn_contprint_xt').setVisible(true);
		Ext.getCmp('btn_printCont_xt').setVisible(false);
	}else{
		Ext.getCmp('btn_contprint_xt').setVisible(false);
		Ext.getCmp('btn_printCont_xt').setVisible(true);
	}
     //涉农相关
	Ext.getCmp('tb_isFarm').setValue(obj.refObj.afmResInfo.isFarm);
    Ext.getCmp('tf_nlmy').setValue('['+obj.refObj.afmResInfo.nlmy+']'+obj.refObj.VO1.itemNlmy);
	Ext.getCmp('tf_nlmy').setSelValue(obj.refObj.afmResInfo.nlmy);
	//合同起始日和到期日
	var term=afmResInfo.term;
	Ext.getCmp('df_begindate').setValue(tf.sysdb.workDate);
    Ext.getCmp('df_enddate').setValue(Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd'));
}else{
    Ext.getCmp('form_cont').getForm().setValues(obj.refObj.acBusinesscont);
    Ext.getCmp('tf_prdtNa').setValue('['+obj.refObj.acBusinesscont.prdtNo+']'+obj.refObj.VO1.prdtNa);
    Ext.getCmp('tfs_industrytype').setValue('['+obj.refObj.acBusinesscont.industrytype+']'+obj.refObj.VO1.itemname);
    Ext.getCmp('tfd_surveyUsr').setValue(obj.refObj.VO1.surveyOperidName);
    Ext.getCmp('tfd_checkUsr').setValue(obj.refObj.VO1.checkOperidName);
    Ext.getCmp('tfd_opnUsr').setValue(obj.refObj.VO1.manageOperidName);
    Ext.getCmp('tfd_appinstcode').setValue(obj.refObj.VO1.manageInstcodeName);
    Ext.getCmp('tfd_balUsr').setValue(obj.refObj.VO1.balOperidName);
    Ext.getCmp('tfd_iptUsr').setValue(obj.refObj.VO1.operidName);
    Ext.getCmp('tfd_instcode').setValue(obj.refObj.VO1.instcodeName);
    Ext.getCmp('tf_loantype').setValue('2'); 
    //Ext.getCmp('tfn_rateFloat').setValue(obj.refObj.VO1.rateFloat);
    //获取合同表内是否循环标志
	if(obj.refObj.cnProduct.circlePutout=='1'){//如果是循环贷则打印合同按钮改为循环贷的打印合同按钮obj.refObj.acBusinesscont.isCic=='1'
		Ext.getCmp('btn_contprint_xt').setVisible(true);
		Ext.getCmp('btn_printCont_xt').setVisible(false);
	}else{
		Ext.getCmp('btn_contprint_xt').setVisible(false);
		Ext.getCmp('btn_printCont_xt').setVisible(true);
	}
    Ext.getCmp('tf_nlmy').setValue('['+obj.refObj.acBusinesscont.nlmy+']'+obj.refObj.VO1.itemNlmy);
	Ext.getCmp('tf_nlmy').setSelValue(obj.refObj.acBusinesscont.nlmy);
}
}
var bdate= Ext.getCmp('df_begindate').value;
//console.log("111111"+bdate);
if(bdate==null||bdate=="null"||bdate==""){
Ext.getCmp('df_begindate').setValue(tf.sysdb.workDate);

}
if(impl!='W'){
	Ext.getCmp('btn_contprint_xt').setVisible(false);
	Ext.getCmp('btn_printCont_xt').setVisible(false);
}
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#df_begindate':{
				blur:function(me,The,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var term=Ext.getCmp('tfn_term').value;
var termType = Ext.getCmp('tfc_termType').value;
//Ext.getCmp('df_enddate').setValue(Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd'));
if(termType=="M"){
	Ext.getCmp('df_enddate').setValue(Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd'));
}else if(termType=="D"){
	Ext.getCmp('df_enddate').setValue(Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.DAY,term),'Ymd'));
}
					//前处理 结束
				}			
			},			
			'#field_1':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
/*
var termType=Ext.getCmp('tfc_termType').value;
var term=Ext.getCmp('tfn_term').value;
if(termType=='M'){
	Ext.getCmp('df_enddate').vtypeText='终止日期应在【'+Ext.util.Format.date(Ext.Date.add(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH, (term-1)), Ext.Date.DAY,1),'Ymd')+','+   Ext.util.Format.date(Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.MONTH,term),'Ymd')+'】之间';
else{
	Ext.getCmp('df_enddate').vtypeText='终止日期应为'+Ext.Date.add(Ext.getCmp('df_begindate').value, Ext.Date.DAY, term);
}
*/
					//前处理 结束
				}			
			},			
			'#tf_nlmy':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('农林牧渔选择', '/PageLib/MvcBase.html?pageId=MCSW0072', 600, 400, function(data){
	me.setValue('['+data.itemno+']'+data.itemname);
	me.setSelValue(data.itemno);
});
					//前处理 结束
				}			
			},			
			'#tb_isFarm':{
				change:function(me,newValue,oldValue,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var isFarm=Ext.getCmp('tb_isFarm').value;
if(isFarm=='1'){
	Ext.getCmp('tf_nlmy').setVisible(true);
	Ext.getCmp('tf_nlmy').allowBlank=false;
}else{
	Ext.getCmp('tf_nlmy').setVisible(false);
	Ext.getCmp('tf_nlmy').setValue('');
	Ext.getCmp('tf_nlmy').allowBlank=true;
}
					//前处理 结束
				}			
			},			
			'#btn_printCont':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var contno=Ext.getCmp('tf_contno').value;
if(!Ext.isEmpty(contno)){
	sendData.contno=contno;
	
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_printCont_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var file=obj.refObj.VO1.filePath;
window.location.href=downLoadServlet + '?mode=get&fileName='+file;
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
}else{
	Ext.Msg.show({title:'温馨提示',
	msg:'请先确认合同要素内容，再进行业务合同打印！',
	icon:Ext.Msg.WARNING,
	buttons:Ext.Msg.OK,
	closable:false,
	fn:function(btnId){
		switch(btnId){
			case "ok":
				break;
		}
	}
});
}
				}			
			},			
			'#btn_coreCust_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//放款账号
sendData.acno = Ext.getCmp('tf_depaccNo').value;
sendData.applyno=applyno;
sendData.manageInstcode = Ext.getCmp('hd_manageInstcode').value;
	//Ext.getCmp('manageInstcode').value;
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_coreCust_1_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
//放款账号户名赋值
Ext.getCmp('tfd_depaccNa').setValue(obj.refObj.VO1.cliname);
//开户行赋值
Ext.getCmp('tfd_depopnbrna').setValue(obj.refObj.VO1.opnbrname)

//Ext.getCmp('tf_reppriacNo').setValue(Ext.getCmp('tf_depaccNo').value);
//Ext.getCmp('tfd_reppriacNa').setValue(obj.refObj.VO1.cliname);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.getCmp('tf_depaccNo').reset();
Ext.getCmp('tf_reppriacNo').reset();
Ext.getCmp('tfd_depaccNa').reset();
Ext.getCmp('tfd_reppriacNa').reset();
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_coreCust':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//放款账号
sendData.acno = Ext.getCmp('tf_depaccNo').value;
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_coreCust_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
//放款账号户名赋值
Ext.getCmp('tfd_depaccNa').setValue(obj.refObj.VO1.cliname);
Ext.getCmp('tf_depaccNo_cifid').setValue(obj.refObj.VO1.loancifid);
Ext.getCmp('tf_reppriacNo').setValue(Ext.getCmp('tf_depaccNo').value);
Ext.getCmp('tf_repintacNo').setValue(Ext.getCmp('tf_depaccNo').value);
Ext.getCmp('tfd_reppriacNa').setValue(obj.refObj.VO1.cliname);
//var loancifid= obj.refObj.VO1.loancifid;

//console.log('==========='+loancifid);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_coreCust_2':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//放款账号
sendData.acno = Ext.getCmp('tf_reppriacNo').value;
sendData.applyno=applyno;
sendData.manageInstcode = Ext.getCmp('hd_manageInstcode').value;
	//Ext.getCmp('manageInstcode').value;
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_coreCust_2_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
//放款账号户名赋值
//Ext.getCmp('tfd_depaccNa').setValue(obj.refObj.VO1.cliname);
//Ext.getCmp('tf_reppriacNo').setValue(Ext.getCmp('tf_depaccNo').value);
Ext.getCmp('tfd_reppriacNa').setValue(obj.refObj.VO1.cliname);

//开户行赋值
Ext.getCmp('tfd_repopnbrna').setValue(obj.refObj.VO1.opnbrname)
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.getCmp('tf_depaccNo').reset();
Ext.getCmp('tf_reppriacNo').reset();
Ext.getCmp('tfd_depaccNa').reset();
Ext.getCmp('tfd_reppriacNa').reset();
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_read_1_1':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var acno = TF.McsHelper.GetBookNo(); //账号
if(acno.length<1) {
	Ext.Msg.show({title:'提示',msg:'未录入卡号，请重新点击读卡',icon:Ext.Msg.WARNING,buttons: Ext.Msg.OK});
	return;
}
Ext.getCmp('tf_depaccNo').setValue(acno);
Ext.getCmp('btn_coreCust_1').fireEvent('click');
					//前处理 结束
				}			
			},			
			'#btn_read':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var acno = TF.McsHelper.GetCardNo(); //账号
if(acno.length<1) {
	Ext.Msg.show({title:'提示',msg:'未录入卡号，请重新点击读卡',icon:Ext.Msg.WARNING,buttons: Ext.Msg.OK});
	return;
}
Ext.getCmp('tf_depaccNo').setValue(acno);
Ext.getCmp('btn_coreCust_1').fireEvent('click');
					//前处理 结束
				}			
			},			
			'#btn_contprint_xt':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var contno=Ext.getCmp('tf_contno').value;
if(!Ext.isEmpty(contno)){
	sendData.contno=contno;
	
					//前处理 结束
					sendData.flowId='0013_GYLD0020_btn_contprint_xt_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var file=obj.refObj.VO1.filePath;
window.location.href=downLoadServlet + '?mode=get&fileName='+file;
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
}else{
	Ext.Msg.show({title:'温馨提示',
	msg:'请先确认合同要素内容，再进行业务合同打印！',
	icon:Ext.Msg.WARNING,
	buttons:Ext.Msg.OK,
	closable:false,
	fn:function(btnId){
		switch(btnId){
			case "ok":
				break;
		}
	}
});
}
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'gyld.0020View' ]
});
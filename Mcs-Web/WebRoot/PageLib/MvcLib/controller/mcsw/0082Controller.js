Ext.define("mvc.controller.mcsw.0082Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_200079|7013_200001_#|7013_200003|7013_200004|7013_200012|7013_200007_#|7013_200017_#");
		//-------全局变量定义-------开始-------
		var _selData = null;
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_list_store',
			fields: [
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'certtype'},				
				{name:'certno'},				
				{name:'cstype'},				
				{name:'outcifid'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.cifid=Ext.getCmp('tb_cifid').value;
sendData.cliname=Ext.getCmp('tb_cliname').value;
Ext.getCmp('btn_selected').setDisabled(true);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_MCSW0082_grid_list_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_list_pagingtoolbar')){
						Ext.getCmp('grid_list_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#tf_legalcertno':{
				blur:function(me,The,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var certno=Ext.getCmp('tf_legalcertno').value;
if(Ext.isEmpty(certno)||certno.length!=18){
	return;
}
var birthday=certno.substr(6, 8);
console.log(birthday);
Ext.getCmp('tf_birthday').setValue(birthday);
var sex=certno.substr(16, 1);
sex = sex%2>0? '1' : '2';
Ext.getCmp('tf_sex').setValue(sex);
					//前处理 结束
				}			
			},			
			'#tbs_legalnat':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择国别', '/PageLib/MvcBase.html?pageId=MCSW0030', 600, 400, function(data){
		me.setSelValue(data.countrycode);
	me.setValue('['+data.countrycode+']'+data.countryname);
});
					//前处理 结束
				}			
			},			
			'#btn_entBase_add_check':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var custInfo= Ext.getCmp('form_entBase_check').getForm().getExtValues();
sendData.entBase= custInfo;
					//前处理 结束
					sendData.flowId='0013_MCSW0082_btn_entBase_add_check_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var ecifCustNo = obj.refObj.VO1.ecifCustNo;
var entBase = obj.refObj.entBase;

Ext.getCmp('win_ecif_check_ent').close();
Ext.create('win_entBase_add').show();
Ext.getCmp('form_entBase').getForm().setValues(entBase);
Ext.getCmp('tf_cstype_ent').setValue("A01");
if(Ext.isEmpty(ecifCustNo)){
	Ext.getCmp('tf_outcifid_ent').setDisabled(true);
	Ext.getCmp('tf_outcifid_ent').setVisible(false);
	Ext.getCmp('tf_exists_ent').setVisible(false);
	Ext.getCmp('tf_not_exists_ent').setVisible(true);
	Ext.getCmp('tbs_countryname_ent').setValue('[CHN]中华人民共和国');
	Ext.getCmp('tbs_countryname_ent').setSelValue('中华人民共和国');
	Ext.getCmp('tf_countrycode_ent').setValue('CHN');
	Ext.getCmp('tbs_legalnat').setValue('[CHN]中华人民共和国');
	Ext.getCmp('tbs_legalnat').setSelValue('CHN');
	Ext.getCmp('tf_certtype').setValue('110');
	Ext.getCmp('win_entBase_add').setHeight(465);
}else{
	Ext.getCmp('tf_outcifid_ent').setDisabled(false);
	Ext.getCmp('tf_outcifid_ent').setVisible(true);
	Ext.getCmp('tf_outcifid_ent').setValue(obj.refObj.VO1.ecifCustNo);
	Ext.getCmp('tf_exists_ent').setVisible(true);
	Ext.getCmp('tf_not_exists_ent').setVisible(false);
	if(!Ext.isEmpty(obj.refObj.entBase.countrycode)){
		Ext.getCmp('tbs_countryname_ent').setValue('['+obj.refObj.entBase.countrycode+']'+obj.refObj.VO1.countryname);
		Ext.getCmp('tbs_countryname_ent').setSelValue(obj.refObj.VO1.countryname);
	}
	if(!Ext.isEmpty(obj.refObj.entBase.industrytype)){
		Ext.getCmp('tbs_industryname_ent').setValue('['+obj.refObj.entBase.industrytype+']'+obj.refObj.VO1.industryname);
		Ext.getCmp('tbs_industryname_ent').setSelValue(obj.refObj.VO1.industryname);
	}
	if(!Ext.isEmpty(obj.refObj.entBase.legalnat)){
		Ext.getCmp('tbs_legalnat').setValue('['+obj.refObj.entBase.legalnat+']'+obj.refObj.VO1.relCountryname);
		Ext.getCmp('tbs_legalnat').setSelValue(obj.refObj.entBase.legalnat);
	}
	if(!Ext.isEmpty(obj.refObj.entBase.regioncode)){
		Ext.getCmp('tbs_regionname').setValue('['+obj.refObj.entBase.regioncode+']'+obj.refObj.entBase.regionname);
		Ext.getCmp('tbs_regionname').setSelValue(obj.refObj.entBase.regionname);
	}
	Ext.getCmp('win_entBase_add').setHeight(498);
}
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_entBase_check').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_entBase_add_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var custInfo= Ext.getCmp('form_entBase').getForm().getExtValues();
sendData.custInfo= custInfo;
sendData.entBase= custInfo;
					//前处理 结束
					sendData.flowId='0013_MCSW0082_btn_entBase_add_submit_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码intBase){f(e
Ext.getCmp('win_entBase_add').close();
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_entBase').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#tbs_industryname_ent':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
	TF.ExtHelper.selWindow('国标行业分类', '/PageLib/MvcBase.html?pageId=MCSW0002', 600, 400, function(data){
	me.setSelValue(data.itemname);
	me.setValue('['+data.itemno+']'+data.itemname);
	Ext.getCmp('tf_industrytype_ent').setValue(data.itemno);
});
					//前处理 结束
				}			
			},			
			'#certno':{
				blur:function(me,The,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var certno=Ext.getCmp('certno').value;
if(Ext.isEmpty(certno)||certno.length!=18){
	return;
}
var birthday=certno.substr(6, 8);
Ext.getCmp('tf_birthday').setValue(birthday);
var sex=certno.substr(16, 1);
sex = sex%2>0? '1' : '2';
Ext.getCmp('tf_sex').setValue(sex);
//Ext.getCmp('tf_sex').setRawValue(sex);
					//前处理 结束
				}			
			},			
			'#btn_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tb_cifid').reset();
Ext.getCmp('tb_cliname').reset();
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_indBase_add_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var custInfo= Ext.getCmp('form_endBase').getForm().getExtValues();
sendData.custInfo= custInfo;
sendData.entBase= custInfo;
					//前处理 结束
					sendData.flowId='0013_MCSW0082_btn_indBase_add_submit_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码intBase){f(e
	var win = window.parent.Ext.getCmp('sel_win');
    if (win != undefined && win != null) {
    	win.selData = obj.refObj.entBase;
        win.close();
    }
Ext.getCmp('win_add').close();
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_endBase').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#tbs_countryname_ent':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择国别', '/PageLib/MvcBase.html?pageId=MCSW0030', 600, 400, function(data){
	me.setSelValue(data.countryname);
	me.setValue('['+data.countrycode+']'+data.countryname);
	Ext.getCmp('tf_countrycode_ent').setValue(data.countrycode);
});
					//前处理 结束
				}			
			},			
			'#grid_list':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_selData = record.data;
Ext.getCmp('btn_selected').setDisabled(false);
					//前处理 结束
				},				itemdblclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
	if(_selData){
	var win = window.parent.Ext.getCmp('sel_win');
    if (win != undefined && win != null) {
    	win.selData = _selData;
        win.close();
    }
}else{
	Ext.Msg.alert('提示','请选择一条记录');
}
					//前处理 结束
				}			
			},			
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_selected':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_selData){
	var win = window.parent.Ext.getCmp('sel_win');
    if (win != undefined && win != null) {
    	win.selData = _selData;
        win.close();
    }
}else{
	Ext.Msg.alert('提示','请选择一条记录');
}
					//前处理 结束
				}			
			},			
			'#btn_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_ecif_check_ent').show();
					//前处理 结束
				}			
			},			
			'#tbs_regionname':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('行政区划代码选择', '/PageLib/MvcBase.html?pageId=MCSW0007', 600, 400, function(data){
	me.setSelValue(data.areaname);
	me.setValue('['+data.areacode+']'+data.areaname);
	Ext.getCmp('tf_regioncode').setValue(data.areacode);
	});
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'mcsw.0082View' ]
});
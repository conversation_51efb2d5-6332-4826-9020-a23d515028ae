Ext.define("mvc.controller.sjzf.0002Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_290001_#|7013_300051_#|7013_300052_#|7013_300053_#|7013_300054_#|7013_300055_#|7013_300056_#|7013_300057_#|7013_300058_#");

		//grids' stores
		
		//trees' stores

		this.control({
			'#tf_visitForm':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(Ext.isEmpty(visitId)){
	Ext.getCmp('tf_contno').hide();
	Ext.getCmp('tf_contno').setDisabled(true);
	Ext.getCmp('tf_contnoSelect').show();
}else{
	Ext.getCmp('tf_contno').show();
	Ext.getCmp('tf_contnoSelect').setDisabled(true);
	Ext.getCmp('tf_contnoSelect').hide();
}
sendData.contno=contno;
sendData.visitId=visitId;
sendData.operateKey='query_detail';
					//前处理 结束
					sendData.flowId='0013_SJZF0002_tf_visitForm_afterrender';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
var dataType = obj.refObj.refObj.dataType;
if(dataType=='1'){
	Ext.getCmp('tf_panel_sj').setDisabled(false);
	Ext.getCmp('tf_panel_yy').setDisabled(true);
	Ext.getCmp('tf_panel_sj').show();
	Ext.getCmp('tf_panel_yy').hide();
}else{
	Ext.getCmp('tf_panel_sj').setDisabled(true);
	Ext.getCmp('tf_panel_yy').setDisabled(false);
	Ext.getCmp('tf_panel_sj').hide();
	Ext.getCmp('tf_panel_yy').show();
}
if(!Ext.isEmpty(obj.refObj.acVisit)){
	Ext.getCmp('tf_visitForm').getForm().setValues(obj.refObj.refObj);
	Ext.getCmp('tf_visitForm').getForm().setValues(obj.refObj.acVisit);
	Ext.getCmp('tf_contno').setValue(obj.refObj.contno);
	if(dataType=='2'){
		Ext.getCmp('yy_taskTitle').setValue(obj.refObj.acVisit.taskTitle);
		Ext.getCmp('yy_visitDate').setValue(obj.refObj.acVisit.visitDate);
		Ext.getCmp('yy_remark').setValue(obj.refObj.acVisit.remark);
		Ext.getCmp('yy_rectifyOpinion').setValue(obj.refObj.acVisit.rectifyOpinion);
	}
}else{
	Ext.getCmp('tf_dataType').setValue(dataType);
}
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#tf_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var acVisit = Ext.getCmp('tf_visitForm').getForm().getExtValues();
sendData.acVisit=acVisit;
sendData.operateKey='insertOrUpdate';
					//前处理 结束
					sendData.flowId='0013_SJZF0002_tf_submit_click';
					sendData.flowDesc='1';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({title:'提示',msg:'审计走访操作成功！',icon:Ext.Msg.SUCCESS,buttons: Ext.Msg.OK});
Ext.getCmp('tf_createTime').setValue(obj.refObj.createTime);
Ext.getCmp('tf_opername').setValue(obj.refObj.opername);
Ext.getCmp('tf_visitId').setValue(obj.refObj.visitId);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('tf_visitForm').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#tf_contnoSelect':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择合同', '/PageLib/MvcBase.html?pageId=SJZF0003', 980, 560, function(data){
	Ext.getCmp('tf_visitForm').getForm().setValues(data);
});
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'sjzf.0002View' ]
});


		//-------全局变量定义-------开始-------
		var _dataCount = null;
		var _selData = null;
		var app = TF.ExtHelper.request('app');
		var contno = TF.ExtHelper.request('contno');
		var readonly = TF.ExtHelper.request('readonly');
		var record = TF.ExtHelper.request('record');
		var visitId = TF.ExtHelper.request('visitId');
		//-------全局变量定义-------结束-------


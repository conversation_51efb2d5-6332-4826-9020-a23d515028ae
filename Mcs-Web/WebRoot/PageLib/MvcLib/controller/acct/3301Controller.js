Ext.define("mvc.controller.acct.3301Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_250055|7013_200014");

		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_list_store',
			fields: [
				{name:'misnNo'},				
				{name:'applyno'},				
				{name:'vchno'},				
				{name:'occurtype'},				
				{name:'prdtNa'},				
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'famaddr'},				
				{name:'mtel'},				
				{name:'occurdate'},				
				{name:'begindate'},				
				{name:'enddate'},				
				{name:'curr'},				
				{name:'busSum'},				
				{name:'busBal'},				
				{name:'prdtNo'},				
				{name:'afbcTreeId'},				
				{name:'creditapplyno'},				
				{name:'contno'},				
				{name:'launchBrno'},				
				{name:'conapplyno'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.cliname=Ext.getCmp('tf_cliname').value;
sendData.vchno=Ext.getCmp('tf_vchno').value;
sendData.flag='0';
Ext.getCmp('btn_assign').setDisabled(true);
Ext.getCmp('btn_detail').setDisabled(true);
Ext.getCmp('btn_risk').setDisabled(true);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_ACCT3301_grid_list_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_list_pagingtoolbar')){
						Ext.getCmp('grid_list_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_assign':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_assign').show();
Ext.getCmp('tf_applyno').setValue(_acBussinesscont.misnNo);
					//前处理 结束
				}			
			},			
			'#btn_sure':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=Ext.getCmp('tf_applyno').value;
sendData.operid=Ext.getCmp('tf_operid_use').value;
sendData.instcode=Ext.getCmp('tf_instcode').value;
					//前处理 结束
					sendData.flowId='0013_ACCT3301_btn_sure_click';
					sendData.flowDesc='1';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.Msg.show({title:'提示',msg:'分配信贷经理成功！',icon:[Ext.Msg.SUCCESS],buttons: Ext.Msg.OK});
Ext.getCmp('win_assign').close();
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_assign').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tf_vchno').reset();
Ext.getCmp('tf_cliname').reset();
Ext.data.StoreManager.lookup('grid_list_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#tf_operid':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择分配人员', '/PageLib/MvcBase.html?pageId=ACCT3305&instcode='+_acBussinesscont.launchBrno, 600, 400, function(data){
	                 	
	me.setSelValue(data.opername);
	me.setValue(data.opername);
	
	Ext.getCmp('tf_operid_use').setValue(data.operid);
	Ext.getCmp('tf_instname').setValue(data.instname);
	Ext.getCmp('tf_instcode').setValue(data.instcode);
});
					//前处理 结束
				}			
			},			
			'#btn_risk':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('winFlow', '风险探测','this','/PageLib/MvcBase.html?pageId=AFCK1000&cifid='+_acBussinesscont.cifid,800,500,true,true);
					//前处理 结束
				}			
			},			
			'#grid_list':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_acBussinesscont=record.data;
Ext.getCmp('btn_detail').setDisabled(false);
Ext.getCmp('btn_assign').setDisabled(false);
Ext.getCmp('btn_risk').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_detail':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('',_acBussinesscont.vchno+' '+_acBussinesscont.cliname,'toptask','/PageLib/MvcBase.html?pageId=ACCT2202&applyno='+_acBussinesscont.conapplyno+'&contno='+_acBussinesscont.contno+'&cifid='+_acBussinesscont.cifid+'&prdtNo='+_acBussinesscont.prdtNo+'&afbcTreeId='+_acBussinesscont.afbcTreeId,1310,640,true,true);
					//前处理 结束
				}			
			},			
			'#btn_close':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('win_assign').close();
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'acct.3301View' ]
});


		//-------全局变量定义-------开始-------
		var _acBussinesscont = null;
		//-------全局变量定义-------结束-------


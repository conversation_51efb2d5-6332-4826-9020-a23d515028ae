Ext.define("mvc.controller.birt.7100Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
				//-------全局变量定义-------开始-------
		//-------全局变量定义-------结束-------
		//grids' stores
		
		//trees' stores

		this.control({
			'#tc_com':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
					//前处理 结束
					sendData.flowId='0013_BIRT7100_tc_com_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
TF.ExtHelper.setOptStore('tc_com',obj.refObj.VO1.list,{'optionValue':'key','optionLabel':'value'});
Ext.getCmp('tc_com').setValue('1');
Ext.getCmp('tf_instcode').setValue(tf.sysdb.branchId);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#cp_main':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
					//前处理 结束
					sendData.flowId='0013_BIRT7100_cp_main_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
TF.ExtHelper.setOptStore('tf_instcode',obj.refObj.VO1.list,{'optionValue':'key','optionLabel':'value'},true);
//Ext.getCmp('tf_instcode').setValue(tf.sysdb.branchId);
//Ext.getCmp('df_bdate').setValue(tf.sysdb.workDate);
//console.log(obj.refObj.VO1.list);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tf_area').reset();
Ext.getCmp('tf_instcode').setValue('');
					//前处理 结束
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if((!Ext.isEmpty(Ext.getCmp('tf_instcode').value))){
	var area='';
	if(Ext.isEmpty(Ext.getCmp('tf_area').value)){
		area='';
	}else{
		area=Ext.getCmp('tf_area').value;
	}
//alert(Ext.getCmp('tf_instcode').value);
//alert(area);
TF.ExtHelper.setIframe('cp_main','/frameset?__report=custManage.rptdesign&area='+area+'&instcode='+Ext.getCmp('tf_instcode').value,'iframe_birt');
}else{
	Ext.Msg.show({
		title:'提示',
		msg:'请补全查询条件！',
		icon:Ext.Msg.WARNING,
		buttons: Ext.Msg.OK
	});
}
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'birt.7100View' ]
});
Ext.define("mvc.controller.zxks.0001Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_290001");
		//-------全局变量定义-------开始-------
		var _examid = null;
		var _fnum = null;
		var _fscore = null;
		var _mnum = null;
		var _mscore = null;
		var _opnum = null;
		var _opscore = null;
		var _sts = null;
		var _sum = null;
		var _time = null;
		var _title = null;
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'examid'},				
				{name:'examname'},				
				{name:'opnum'},				
				{name:'opscore'},				
				{name:'mnum'},				
				{name:'mscore'},				
				{name:'fnum'},				
				{name:'fscore'},				
				{name:'sum'},				
				{name:'time'},				
				{name:'examinee'},				
				{name:'examineename'},				
				{name:'sts'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('btn_result').setDisabled(true);
Ext.getCmp('btn_start').setDisabled(true);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_ZXKS0001_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#panel_begin':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var htmlInfo='<br/><div align=\"left\"><span style=\"font-weight: bold; font-size: 30px; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;考试须知</span></div><br/>'
	+'<div align=\"center\">'
	+'<div align=\"left\"><span style=\"font-weight: bold; font-size: 20px; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1、单击请选择试卷，选择要考试的试卷，开始考试。</span></div><br/>'
	+'<div align=\"left\"><span style=\"font-weight: bold; font-size: 20px; color: red; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、考试期间，禁止关闭页面或者刷新页面，否则答案丢失，后果自负。</span></div><br/>'
	+'<div align=\"left\"><span style=\"font-weight: bold; font-size: 20px; color: red; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、填空题如果有多个填空，请用\";\"或\"；\"将答案分隔，例如: 填空;填空 或者 填空；填空。</span></div><br/>'
	+'<div align=\"left\"><span style=\"font-weight: bold; font-size: 20px; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4、单击放弃考试，弹出提示信息，选【是】放弃考试，试卷的成绩为零，选【否】继续考试。</span></div><br/>'
	+'<div align=\"left\"><span style=\"font-weight: bold; font-size: 20px; \">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5、倒计时结束后，会自动提交试卷，请考生抓紧时间作答。</span></div><br/>'
	+'</div>'
	+'<br/><div align=\"center\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button onclick=\"Ext.getCmp(\'south_panel\').expand()\">请选择试卷</button></div>';

Ext.getCmp('panel_begin').update(htmlInfo);
/*
'<br/><div align=\"center\"><span style=\"font-weight: bold; font-size: 30px; \">欢迎 [ '
	+tf.sysdb.suName+' ] 来到考试页面!</span></div>'
*/
					//前处理 结束
				}			
			},			
			'#form_result':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.examid=_examid;
					//前处理 结束
					sendData.flowId='0013_ZXKS0001_form_result_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
//parent.Ext.getCmp('win_exam').close();
Ext.getCmp('form_result').getForm().setValues(obj.refObj.ksResult);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_start':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_sts=='1'){
	Ext.Msg.alert('提示', '您已经考过该试卷，不能重复考试');
	return;
}
sendData.examid=_examid;
/*
TF.ExtHelper.showWindow('win_exam', _title,'this','/PageLib/MvcBase.html?pageId=ZXKS0002&time='+_time+'&sum='+_sum+'&examid='+_examid+'&opnum='+_opnum
	+'&opscore='+_opscore+'&mnum='+_mnum+'&mscore='+_mscore+'&fnum='+_fnum+'&fscore='+_fscore, '95%', '95%', true, true);


var url = TF.SYS.PATH + '/PageLib/MvcBase.html?pageId=ZXKS0002&time='+_time+'&sum='+_sum+'&examid='+_examid+'&opnum='+_opnum
	+'&opscore='+_opscore+'&mnum='+_mnum+'&mscore='+_mscore+'&fnum='+_fnum+'&fscore='+_fscore;
var win = window.Ext.getCmp('win_exam');
if (!win) {
    window.Ext.create('widget.window', {
        title: _title,
        iconCls: 'icon_book-open-text',
        header: true,
        id: 'win_exam',
        closable: false,
        maximizable: false,
        resizable: false,
        closeAction: 'destroy',
        width: '90%',
        height: '90%',
        constrain: true,
        modal: true,
        monitorWindowResize: true,
        border: 0,
        selData: null,
        items: {
            xtype: 'box',
            html: '<iframe src=' + url + ' width=100% height=100% scrolling=no frameBorder=0 style=border:none;background-color:#fff;></iframe>'
        },
        layout: {
            type: 'fit'
        },
        listeners: {
            'close': function (me, eOpts) {
        		
                Ext.Msg.confirm('温馨提示', '确定要删除吗', function(button) {
	    			if(button == 'yes') {
        		        Ext.getCmp('btn_commit').fireEvent('click');
	    			}
				});
        		
            }
        }
    }).show();
} else {
    win.show();
}*/
					//前处理 结束
					sendData.flowId='0013_ZXKS0001_btn_start_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
TF.ExtHelper.showWindow('win_exam', _title,'top','/PageLib/MvcBase.html?pageId=ZXKS0002&time='+_time+'&sum='+_sum+'&examid='+_examid+'&opnum='+_opnum
	+'&opscore='+_opscore+'&mnum='+_mnum+'&mscore='+_mscore+'&fnum='+_fnum+'&fscore='+_fscore, '100%', '100%', true, true, null, null, true, 0, false);
//var win=window.top.Ext.getCmp('win_exam');
//console.log(win);
//win.closable=true;
//win.maximizable=true;
//win.show();

/*
var url = TF.SYS.PATH + '/PageLib/MvcBase.html?pageId=ZXKS0002&time='+_time+'&sum='+_sum+'&examid='+_examid+'&opnum='+_opnum
	+'&opscore='+_opscore+'&mnum='+_mnum+'&mscore='+_mscore+'&fnum='+_fnum+'&fscore='+_fscore;
var win=window.Ext.getCmp('win_exam');
if (!win) {
    window.Ext.create('widget.window', {
        title: _title,
        iconCls: 'icon_book-open-text',
        header: true,
        id: 'win_exam',
        closable: false,
        maximizable: false,
        resizable: false,
        closeAction: 'destroy',
        width: '90%',
        height: '90%',
        collapseDirection: 'top',
        constrain: true,
        modal: true,
        monitorWindowResize: true,
        border: 0,
        selData: null,
        items: {
            xtype: 'box',
            html: '<iframe src=' + url + ' width=100% height=100% scrolling=no frameBorder=0 style=border:none;background-color:#fff;></iframe>'
        },
        layout: {
            type: 'fit'
        },
        listeners: {
            'close': function (me, eOpts) {
        		
                Ext.Msg.confirm('温馨提示', '确定要删除吗', function(button) {
	    			if(button == 'yes') {
        		        Ext.getCmp('btn_commit').fireEvent('click');
	    			}
				});
        		
            }
        }
    }).show();
} else {
    win.show();
}
console.log(win);
*/
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var selData=record.data;
_examid=selData.examid;
_title=selData.examname;
_time=selData.time;
_sum=selData.sum;
_opnum=selData.opnum;
_opscore=selData.opscore;
_mnum=selData.mnum;
_mscore=selData.mscore;
_fnum=selData.fnum;
_fscore=selData.fscore;
_sts=selData.sts;
Ext.getCmp('btn_result').setDisabled(false);
Ext.getCmp('btn_start').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_result':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(_sts=='0'){
	Ext.Msg.alert('提示', '未考试，无法查询成绩');
	return;
}
TF.ExtHelper.showWindow('win_result', '成绩详情','this','/PageLib/MvcBase.html?pageId=ZXKS0006&examid='+_examid, 300, 250);
//Ext.create('win_result').show();
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'zxks.0001View' ]
});
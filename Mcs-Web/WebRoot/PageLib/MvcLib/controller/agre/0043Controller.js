Ext.define("mvc.controller.agre.0043Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_250026|7013_250027_#|7013_220007");
		//-------全局变量定义-------开始-------
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'applyno'},				
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'occurdate'},				
				{name:'appTerm'},				
				{name:'appSum'},				
				{name:'appTermType'},				
				{name:'occurtype'},				
				{name:'guarType'},				
				{name:'cnt'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.applyno=Ext.getCmp('tf_applyno').value;
sendData.cifid=Ext.getCmp('tf_cifid').value;
sendData.cliname=Ext.getCmp('tf_cliname').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_AGRE0043_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#main_grid':{
			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'agre.0043View' ]
});
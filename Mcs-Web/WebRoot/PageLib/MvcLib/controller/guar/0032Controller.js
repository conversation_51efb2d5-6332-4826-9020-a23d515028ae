Ext.define("mvc.controller.guar.0032Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_210026|7013_210031|7013_200010");
		//-------全局变量定义-------开始-------
		var _guarBase = null;
		var _guarNo = null;
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_guarbaseinfo_store',
			autoLoad:true,
			fields: [
				{name:'guarNo'},				
				{name:'guarName'},				
				{name:'colTypeName'},				
				{name:'colOneName'},				
				{name:'colTwoName'},				
				{name:'colNo'},				
				{name:'colNa'},				
				{name:'cliname'},				
				{name:'otsType'},				
				{name:'accType'},				
				{name:'isTemp'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.guarNo=Ext.getCmp('tb_guarno_search').value;
sendData.guarName=Ext.getCmp('tb_guarname_search').value;
sendData.cliname=Ext.getCmp('tb_cliname_search').value;
sendData.colNa=Ext.getCmp('tb_colna_search').value;
sendData.otsType=Ext.getCmp('tb_status').value;
Ext.getCmp('btn_detail').setDisabled(true);
Ext.getCmp('btn_atta').setDisabled(true);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_GUAR0032_grid_guarbaseinfo_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
				}
			}
		});
		
		//trees' stores

		this.control({
			'#grid_guarbaseinfo':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					
					//前处理 结束
				}			
			},			
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_guarbaseinfo_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_detail':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var guarNo=_guarBase.guarNo;
var guarName=_guarBase.guarName;
var isTemp=_guarBase.isTemp;
var colNo=_guarBase.colNo;
TF.ExtHelper.showWindow('', ''+guarName+'','toptask','/PageLib/Navigation/GuarInfo.html?menuId=sm_2100&guarNo='+guarNo+'&isTemp='+isTemp+'&colNo='+colNo+'&readonly=1',1310,640,true,true);
					//前处理 结束
				}			
			},			
			'#btn_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tb_guarno_search').reset();
Ext.getCmp('tb_guarname_search').reset();
Ext.getCmp('tb_colna_search').reset();
Ext.getCmp('tb_cliname_search').reset();
Ext.data.StoreManager.lookup('grid_guarbaseinfo_store').loadPage(1);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'guar.0032View' ]
});
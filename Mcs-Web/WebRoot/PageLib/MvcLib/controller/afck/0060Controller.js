Ext.define("mvc.controller.afck.0060Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_230011|7013_230020|7013_220019|7013_220094");
		//-------全局变量定义-------开始-------
		var misnNo = TF.ExtHelper.request('misnNo');
		var readonly = TF.ExtHelper.request('readonly');
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'relserid'},				
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'prdtNa'},				
				{name:'prdtNo'},				
				{name:'iptUsrName'},				
				{name:'taskSts'},				
				{name:'launchDate'},				
				{name:'launchBrno'},				
				{name:'launchUsrName'},				
				{name:'sts'},				
				{name:'subMisnNo'},				
				{name:'iptUsr'},				
				{name:'launchUsr'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.chkObj='2';
sendData.misnNo=misnNo;
if(readonly!=null&&readonly=='1'){
	Ext.getCmp('btn_add').setDisabled(true);
	Ext.getCmp('btn_allocate').setDisabled(true);
	Ext.getCmp('btn_del').setDisabled(true);
}
					//---------发送数据装配---结束------------
					sendData.flowId='0013_AFCK0060_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'sub_grid_store',
			fields: [
				{name:'contno'},				
				{name:'prdtNa'},				
				{name:'cifid'},				
				{name:'cliname'},				
				{name:'industryname'},				
				{name:'brno'},				
				{name:'operid'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//sendData.instcode=Ext.getCmp('tf_instcode').getSelValue();
//sendData.operid=Ext.getCmp('tf_operid').getSelValue();
//sendData.scope=Ext.getCmp('tf_scope').getValue();
//sendData.prdtNo=Ext.getCmp('tf_prdtno').getSelValue();
//sendData.industrytype=Ext.getCmp('tf_industry').getSelValue();
//sendData.areacode=Ext.getCmp('tf_region').getSelValue();
var sendobject=new Object();
sendobject.instcode=Ext.getCmp('tf_instcode').getSelValue();
sendobject.scope=Ext.getCmp('tf_scope').getValue();
sendobject.prdtNo=Ext.getCmp('tf_prdtno').getSelValue();
sendobject.industrytype=Ext.getCmp('tf_industry').getSelValue();
sendobject.operid=Ext.getCmp('tf_operid').getSelValue();
sendobject.areacode=Ext.getCmp('tf_region').getSelValue();
sendData.VO1=sendobject;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_AFCK0060_sub_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('sub_grid_pagingtoolbar')){
						Ext.getCmp('sub_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_allocate':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					var sels=Ext.getCmp('main_grid').getSelectionModel().getSelection();
var list = new Array();
if(sels.length==0){
	Ext.Msg.alert('错误','请至少选择一条记录');
	return;
}
for(var i=0;i<sels.length;i++){
	/*if(sels[i].data.taskSts=='1'){
		Ext.Msg.alert('错误','合同编号为：'+sels[i].data.relserid+'已经分发');
		return;
	}*/
	if(sels[i].data.sts!='0'){
		Ext.Msg.alert('错误','合同编号为：'+sels[i].data.relserid+'的检查任务已完成或已取消，不能分发');
		return;
	}
	list.push(sels[i].data);
}
//Ext.create('win_rellocate').show();
TF.ExtHelper.selWindow('选择执行', '/PageLib/MvcBase.html?pageId=MCSW0024', 600, 400, function(data){
    if(data==null){
        return;
    }
sendData.Tab1=list;
sendData.iptUsr=data.operid;
sendData.iptBrno=data.instcode;
sendData.misnNo=misnNo;
    
					//前处理 结束
					sendData.flowId='0013_AFCK0060_btn_allocate_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
});
				}			
			},			
			'#btn_del':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					var sels=Ext.getCmp('main_grid').getSelectionModel().getSelection();
var list = new Array();
if(sels.length==0){
	Ext.Msg.alert('错误','请至少选择一条记录');
	return;
}
for(var i=0;i<sels.length;i++){
	if(sels[i].data.taskSts=='1'){
		Ext.Msg.alert('错误','合同编号为：'+sels[i].data.relserid+'已经分发，不能删除');
		return;
	}
	list.push(sels[i].data);
}
sendData.Tab1=list;
sendData.misnNo=misnNo;
					//前处理 结束
					sendData.flowId='0013_AFCK0060_btn_del_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#tf_industry':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择行业归属', '/PageLib/MvcBase.html?pageId=SELW0053&highlvl=root&industry='+me.getSelValue(), 600, 400, function(data){
	var list=data;
	var industryno='';
	var industryname='';
	for(var i=0;i<list.length;i++){
		if(i==0){
			industryno=list[i].itemno;
			industryname=list[i].itemname;
			continue;
		}
		industryno=industryno+','+list[i].itemno;
		industryname=industryname+','+list[i].itemname;
	}
	me.setValue(industryname);
	me.setSelValue(industryno);
});
					//前处理 结束
				}			
			},			
			'#tf_operid':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择管理人员', '/PageLib/MvcBase.html?pageId=SELW0052&highlvl=root&operid='+me.getSelValue(), 600, 400, function(data){
	var list=data;
	var operid='';
	var opername='';
	for(var i=0;i<list.length;i++){
		if(i==0){
			operid=list[i].operid;
			opername=list[i].opername;
			continue;
		}
		operid=operid+','+list[i].operid;
		opername=opername+','+list[i].opername;
	}
	me.setValue(opername);
	me.setSelValue(operid);
});
					//前处理 结束
				}			
			},			
			'#sub_grid':{
			
			},			
			'#tf_instcode':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择管理机构', '/PageLib/MvcBase.html?pageId=SELW0051&highlvl=root&instcode='+me.getSelValue(), 600, 400, function(data){
	var list=data;
	var instcode='';
	var instname='';
	for(var i=0;i<list.length;i++){
		if(i==0){
			instcode=list[i].instcode;
			instname=list[i].instname;
			continue;
		}
		instcode=instcode+','+list[i].instcode;
		instname=instname+','+list[i].instname;
	}
	me.setValue(instname);
	me.setSelValue(instcode);
});
					//前处理 结束
				}			
			},			
			'#main_grid':{
			
			},			
			'#tf_prdtno':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择业务品种', '/PageLib/MvcBase.html?pageId=SELW0049&highlvl=root&prdtNo='+me.getSelValue(), 600, 400, function(data){
	var list=data;
	var prdtno='';
	var prdtname='';
	for(var i=0;i<list.length;i++){
		if(i==0){
			prdtno=list[i].prdtNo;
			prdtname=list[i].prdtNa;
			continue;
		}
		prdtno=prdtno+','+list[i].prdtNo;
		prdtname=prdtname+','+list[i].prdtNa;
	}
	me.setValue(prdtname);
	me.setSelValue(prdtno);
});
					//前处理 结束
				}			
			},			
			'#btn_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.showWindow('winDetail', '选择业务','this','/PageLib/MvcBase.html?pageId=AFCK0092&misnNo='+misnNo,700,420,true,true);
//Ext.create('win_sel').show();
					//前处理 结束
				}			
			},			
			'#btn_query':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('sub_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tf_instcode').reset();
Ext.getCmp('tf_instcode').setSelValue('');
Ext.getCmp('tf_operid').reset();
Ext.getCmp('tf_operid').setSelValue('');
Ext.getCmp('tf_industry').reset();
Ext.getCmp('tf_industry').setSelValue('');
Ext.getCmp('tf_prdtno').reset();
Ext.getCmp('tf_prdtno').setSelValue('');
Ext.getCmp('tf_clsfive').reset();
Ext.getCmp('tf_bustype').reset();

Ext.data.StoreManager.lookup('sub_grid_store').loadPage(1);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'afck.0060View' ]
});
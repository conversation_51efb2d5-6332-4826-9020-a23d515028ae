Ext.define("mvc.controller.pers.0001Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_290001|7013_PE0004");

		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'brctagcode'},				
				{name:'brctagname'},				
				{name:'brclvl'},				
				{name:'uppbrctagcode'},				
				{name:'isbrctag'},				
				{name:'brctagcnt'},				
				{name:'brctagind'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.brctagname=Ext.getCmp('tag_name').value;
sendData.brclvl=Ext.getCmp('tag_lvl').value;
sendData.brctagind=Ext.getCmp('tf_type').value;
sendData.uppbrctagcode=Ext.getCmp('up_code').value;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_PERS0001_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'main_num_store',
			fields: [
				{name:'brctagcode'},				
				{name:'brctagname'},				
				{name:'brclvl'},				
				{name:'uppbrctagcode'},				
				{name:'isbrctag'},				
				{name:'brctagcnt'},				
				{name:'brctagind'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.uppbrctagcode=_uppbrctagcode;
					//---------发送数据装配---结束------------
					sendData.flowId='0013_PERS0001_main_num_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_num_pagingtoolbar')){
						Ext.getCmp('main_num_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#tf_upcode_1':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择标签', '/PageLib/MvcBase.html?pageId=PERS0042', 700, 400, function(data){
    
	me.setValue(data.brctagcode);
	//me.setSelValue(data.tagcode;
	//Ext.getCmp('tf_objcode').setValue(data.tagvalobj);
	//Ext.getCmp('tf_name').setValue(data.tagname);
});
					//前处理 结束
				}			
			},			
			'#tag_rest':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tag_lvl').reset();
Ext.getCmp('tag_name').reset();
Ext.getCmp('tf_type').reset();
Ext.getCmp('up_code').reset();
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.getCmp('form_detail').getForm().reset();
					//前处理 结束
				}			
			},			
			'#tf_del':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var a=_salData.isbrctag;
if(a=='1'){
Ext.Msg.show({title:'温馨提示',msg:'标签有下级分支，不能删除',icon:Ext.Msg.WARNING,buttons: Ext.Msg.OK});
}
else{
Ext.Msg.confirm('温馨提示', '确定要删除吗', function(button) {
	if(button=='yes') {
	    sendData.brctagcode=_salData.brctagcode;
		
					//前处理 结束
					sendData.flowId='0013_PERS0001_tf_del_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_num_store').loadPage(1);
Ext.Msg.alert('提示', '删除成功');
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.getCmp('form_detail').getForm().reset();
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
					
	}
});
	}
				}			
			},			
			'#main_num':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_salData=record.data;
Ext.getCmp('tf_del').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#form_update':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('form_update').getForm().setValues(_entriy);
					//前处理 结束
				}			
			},			
			'#btn_edit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_update').show();
Ext.getCmp('tf_lvl_1').setValue(_selData.brclvl);
Ext.getCmp('tf_is_1').setValue(_selData.isbrctag);
Ext.getCmp('tf_cnt_1').setValue(_selData.brctagcnt);
					//前处理 结束
				}			
			},			
			'#tag_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('form_detail').getForm().reset();
Ext.getCmp('form_detail').setFieldsReadOnly(false);
Ext.getCmp('tf_lvl').setReadOnly(true);
Ext.getCmp('tf_is').setReadOnly(true);
Ext.getCmp('tf_cnt').setReadOnly(true);
Ext.getCmp('btn_edit').setDisabled(true);
Ext.getCmp('tag_ok').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_selData=record.data;
_brctagcode=_selData.brctagcode;
_uppbrctagcode=_selData.uppbrctagcode;
sendData.brctagcode=_brctagcode;
sendData.uppbrctagcode=_uppbrctagcode;
Ext.getCmp('tf_south').expand();
					//前处理 结束
					sendData.flowId='0013_PERS0001_main_grid_itemclick';
					sendData.flowDesc='。';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('form_detail').getForm().setValues(obj.refObj.upBrctagrank);
_entriy=obj.refObj.upBrctagrank;
_selData=record.data;
sendData.uppbrctagcode=_selData.uppbrctagcode;
Ext.data.StoreManager.lookup('main_num_store').loadPage(1);
Ext.getCmp('tf_del').setDisabled(true);
Ext.getCmp('tag_add').setDisabled(false);
Ext.getCmp('btn_edit').setDisabled(false);
Ext.getCmp('tag_ok').setDisabled(true);

Ext.getCmp('tf_lvl').setValue(_selData.brclvl);
Ext.getCmp('tf_is').setValue(_selData.isbrctag);
Ext.getCmp('tf_cnt').setValue(_selData.brctagcnt);


Ext.getCmp('form_detail').setFieldsReadOnly(true);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#tag_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.getCmp('form_detail').getForm().reset();
					//前处理 结束
				}			
			},			
			'#tag_ok':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.upBrctagrank=Ext.getCmp('form_detail').getForm().getExtValues();
					//前处理 结束
					sendData.flowId='0013_PERS0001_tag_ok_click';
					sendData.flowDesc='1';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.getCmp('form_detail').getForm().reset();
_isused = obj.refObj.isuse;
if("0"==_isused){
		Ext.Msg.alert('提示', '新增成功');
}else{
		Ext.Msg.alert('提示', '编辑成功');
}
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_detail').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_update':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.brctagcode=_brctagcode;
sendData.upBrctagrank=Ext.getCmp('form_update').getForm().getExtValues();
					//前处理 结束
					sendData.flowId='0013_PERS0001_btn_update_click';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.getCmp('form_detail').getForm().reset();
Ext.getCmp('win_update').close();
Ext.Msg.alert('提示', '编辑成功');
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_update').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#tf_upcode':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择标签', '/PageLib/MvcBase.html?pageId=PERS0042', 700, 400, function(data){
    
	me.setValue(data.brctagcode);
	Ext.getCmp('tf_lvl').setValue(data.brclvl+1);
	Ext.getCmp('tf_is').setValue('0');
	Ext.getCmp('tf_cnt').setValue(0);
});
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'pers.0001View' ]
});


		//-------全局变量定义-------开始-------
		var _brctagcode = null;
		var _entriy = _entriy;
		var _isused = null;
		var _salData = _salData;
		var _selData = _selData;
		var _uppbrctagcode = null;
		//-------全局变量定义-------结束-------


Ext.define("mvc.controller.pers.0004Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		
		//trees' stores
		Ext.create('Ext.data.TreeStore', {
			storeId:'tp_tree_store',
			fields: [
				{name:'tagobjcode'},				
				{name:'valcode'},				
				{name:'val'}				
			],
		    root: {
		        expanded: true
		    },
		    listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(Ext.isEmpty(tagobjcode)){
    tagobjcode = 'root';
}
var upTagvalmanual = new Object();
sendData.upTagvalmanual = upTagvalmanual;
sendData.orderByString = "tagobjcode";
if(tagobj!='null'){
	sendData.tagvalobj = tagobj;
}
					//---------发送数据装配---结束------------
					sendData.flowId='0013_PERS0004_tp_tree_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
				}
			},
			proxy:
			{
			    type: 'ajax',
			    actionMethods: "POST",
			    async:true,
			    url: '/BaseResUri/getWebServices',
			    reader:
				{
				    getResponseData: function (response) {
				    	var selfFieldName='valcode';
				    	var parentFieldName='tagobjcode';
				    	var selfDesc='cursor.valcode';
				    	var rootRule='cursor. tagobjcode==tagobjcode';
				    	var allowCheck=true;
				    	var allowMultiCheck=false;
				    	var parentCheckMode='none';			//none,cascadeOne,cascadeAll
				    	var expandedDepth=2;
				    	var tree=Ext.getCmp('tp_tree');
				    	var rootList=[];
				    	var dataArrays=Ext.decode(response.responseText).d.refObj.refObj;
				    	var nodeMap = new Ext.util.HashMap();
				    	for(var i=0;i<dataArrays.length;i++){
				    		var cursor=dataArrays[i];
				    		if(!Ext.isEmpty(cursor[selfFieldName])){
				    			var node=new Object();
				    			if(tree.columns.length!=1){
				    				node=cursor;
				    			}else{
				    				node.text=eval(selfDesc);
				    			}
				    			node.children=[];
				    			if(allowCheck==true){
				    				node.checked=false;
				    			}
				    			node.leaf=true;
				    			if(node.forceBranch=='Y'||node.forcebranch=='Y'){
				    				node.leaf=false;
				    			}
				    			nodeMap.add(cursor[selfFieldName],node);
				    		}
				    	}
				    	for(var i=0;i<dataArrays.length;i++){
				    		var cursor=dataArrays[i];
				    		var curNode=nodeMap.get(cursor[selfFieldName]);
				    		if(!Ext.isEmpty(curNode)){
				    			if(!Ext.isEmpty(cursor[parentFieldName])){
					    	    	var parentNode=nodeMap.get(cursor[parentFieldName]);
					    	    	if(!Ext.isEmpty(parentNode)&&parentNode!=curNode){
					    	    		parentNode.children.push(curNode);
					    	    		parentNode.leaf=false;
					    	    	}
					    	    }
					    	    if(eval(rootRule)){
					    	    	rootList.push(curNode);
					    	    }
				    		}
				    	}
				    
				    	var virtualRootNode =new Object();
				    	virtualRootNode.text='';
				    	virtualRootNode.children=rootList;
				    	virtualRootNode.leaf=false;
				    	
				    	
				    	if(allowCheck){
				    		tree.addListener("checkchange",function( node, checked, eOpts ){
					    		if(checked==true&&allowMultiCheck==false){
					    			var selectedNodes=tree.getChecked();
					    			for(var i=0;i<selectedNodes.length;i++){
					    				if(node!=selectedNodes[i]){
					    					selectedNodes[i].set('checked',false);
					    				}
					    			}
					    		}else if(allowMultiCheck==true&&parentCheckMode=='cascadeOne'){
					    			if(node.data.leaf==false){
					    				if(checked==true){
						    				for(var v=0;v<node.childNodes.length;v++){
						    					node.childNodes[v].set('checked',true);
						    				}
						    			}else if(checked==false){
						    				for(var v=0;v<node.childNodes.length;v++){
						    					node.childNodes[v].set('checked',false);
						    				}
						    			}
					    			}
					    		}else if(allowMultiCheck==true&&parentCheckMode=='cascadeAll'){
					    			if(node.data.leaf==false){
					    				if(checked==true){
					    					node.cascadeBy(function (childNode) {
						    					childNode.set('checked',true);
						    				}, this);
					    				}else if(checked==false){
					    					node.cascadeBy(function (childNode) {
						    					childNode.set('checked',false);
						    				}, this);
					    				}
					    			}
					    		}else if(checked==true&&allowMultiCheck==true&&parentCheckMode=='func'){
					    			node.cascadeBy(function (childNode) {
					    				if(this.parentCheckModeFunc(childNode)==true){
					    					childNode.set('checked',true);
					    				}else{
					    					childNode.set('checked',false);
					    				}
					    			}, this);
					    		}
					    	});
				    	}
				    	
				    	if(Ext.isEmpty(expandedDepth)||isNaN(expandedDepth)||expandedDepth<0){
				    		expandedDepth=0;
				    	}
				    	
				    	var setExpanded=function(node,depth){
				    		if(depth==0){
				    			return;
				    		}
				    		for(var k=0;k<node.children.length;k++){
					    		node.children[k].expanded=true;
					    		setExpanded(node.children[k],depth-1);
					    	}
				    	};
				    	
				    	//setExpanded(virtualRootNode,expandedDepth);
				    	
				    	return this.readRecords(virtualRootNode);
				    }
				}
			}
		});

		this.control({
			'#btn_search':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
if(Ext.isEmpty(Ext.getCmp('tf_val').getValue())){
	Ext.getCmp('tp_tree').restoreLocalData();
}else{
	var filter=function(childNode){
if(childNode.get('val').contains(Ext.getCmp('tf_val').getValue())){
		if(childNode.data.leaf==true){
			return true;
		}
}
}
Ext.getCmp('tp_tree').localSearch(filter);
}
					//前处理 结束
				}			
			},			
			'#btn_selected':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var checkedNodes = Ext.getCmp('tp_tree').getChecked();
if(checkedNodes.length==1 && checkedNodes[0].data.leaf){
	var win = window.parent.Ext.getCmp('sel_win');
    if (win != undefined && win != null) {
    	win.selData = checkedNodes[0].data;
      win.close();
    }
}else{
	Ext.Msg.alert('提示', '请选择正确的取值对象');
}
					//前处理 结束
				}			
			},			
			'#tp_tree':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_record=record.data;
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'pers.0004View' ]
});


		//-------全局变量定义-------开始-------
		var _record = _record;
		var tagobj = TF.ExtHelper.request('tagobj');
		var tagobjcode = 'root';
		var upTagvalmanual = upTagvalmanual;
		//-------全局变量定义-------结束-------


Ext.define("mvc.controller.mcpt.4420Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_230024|7013_200079");
		//-------全局变量定义-------开始-------
		var _afMainInfo = null;
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'applyno'},				
				{name:'changerType'},				
				{name:'cliname'},				
				{name:'certtype'},				
				{name:'certno'},				
				{name:'contno'},				
				{name:'vchno'},				
				{name:'prdtNo'},				
				{name:'prdtNa'},				
				{name:'applyDate'},				
				{name:'changerReason'},				
				{name:'cifid'},				
				{name:'gccontno'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制
Ext.getCmp('btn_view').setDisabled(true);
Ext.getCmp('btn_achieve').setDisabled(true);

//传入查询参数
sendData.applyno=Ext.getCmp('tf_applyno').value;
sendData.certno=Ext.getCmp('tf_certno').value;
sendData.cliname=Ext.getCmp('tf_cliname').value;
sendData.changerType='05';
					//---------发送数据装配---结束------------
					sendData.flowId='0013_MCPT4420_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_achieve':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
/*Ext.Msg.confirm('提示', '确定进行贷后变更？', function (button) {
	if (button == 'yes') {
		var sendData=new Object();
		sendData.afMainInfo = _afMainInfo;
		
					//前处理 结束
					
	}
});*/
var applyno=_afMainInfo.applyno;
var afMainInfo=_afMainInfo;
var cifid = _afMainInfo.cifid;
var contno = _afMainInfo.contno;
var gccontno = _afMainInfo.gccontno;
var vchno = _afMainInfo.vchno;
var cliname = _afMainInfo.cliname;
var changerType = _afMainInfo.changerType;
TF.ExtHelper.showWindow('',applyno,'toptask','/PageLib/MvcBase.html?&pageId=AFMG2000&applyno='+applyno+'&cifid='+cifid+'&afMainInfo='+afMainInfo+'&contno='+contno+'&gccontno='+gccontno+'&cliname='+cliname+'&vchno='+vchno+'&changerType='+changerType,1000,640,true,true);
				}			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//获取参数
_afMainInfo=record.data;
applyno = record.data.applyno;
//页面控制
Ext.getCmp('btn_view').setDisabled(false);
Ext.getCmp('btn_achieve').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制
Ext.getCmp('tf_applyno').reset();
Ext.getCmp('tf_certno').reset();
Ext.getCmp('tf_cliname').reset();
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#btn_view':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var applyno=_afMainInfo.applyno;
var afMainInfo=_afMainInfo;
var cifid = _afMainInfo.cifid;
var contno = _afMainInfo.contno;
var gccontno = _afMainInfo.gccontno;
var vchno = _afMainInfo.vchno;
var changerType = _afMainInfo.changerType;
var readonly = 1;
TF.ExtHelper.showWindow('',applyno,'toptask','/PageLib/Navigation/VerticaMenu.html?&visible=true&menuId=sm_6100&applyno='+applyno+'&cifid='+cifid+'&afMainInfo='+afMainInfo+'&contno='+contno+'&gccontno='+gccontno+'&vchno='+vchno+'&readonly='+readonly+'&changerType='+changerType,1310,640,true,true);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'mcpt.4420View' ]
});
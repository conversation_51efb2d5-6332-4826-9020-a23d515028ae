Ext.define("mvc.controller.qryp.2140Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
				//-------全局变量定义-------开始-------
		//-------全局变量定义-------结束-------
		//grids' stores
		
		//trees' stores

		this.control({
			'#main_panel':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.rego(TF.ExtHelper.reQueryString('/Extcalendar/extensible/examples/calendar/TestApp/test-app.html'));

return;
					//前处理 结束
					sendData.flowId='0013_QRYP2140_main_panel_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码

var data = obj.refObj.VO1;
console.log(data);
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#center_panel':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
return;
					//前处理 结束
					sendData.flowId='0013_QRYP2140_center_panel_afterrender';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'qryp.2140View' ]
});
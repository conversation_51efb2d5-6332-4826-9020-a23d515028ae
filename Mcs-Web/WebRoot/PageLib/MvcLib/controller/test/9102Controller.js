Ext.define("mvc.controller.test.9102Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		TF.ExtHelper.initOptStore("7013_250060_#|7013_250063|7013_250061_#|7013_250062|7013_250053|7013_220055");
		//-------全局变量定义-------开始-------
		var _data = new Object();
		//-------全局变量定义-------结束-------
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'grid_eod_tmp_store',
			fields: [
				{name:'sortno'},				
				{name:'shcode'},				
				{name:'des'},				
				{name:'callfmt'},				
				{name:'iserrorstop'},				
				{name:'issychronized'},				
				{name:'priority'},				
				{name:'callname'},				
				{name:'valid'},				
				{name:'isfinish'},				
				{name:'shname'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
console.log('==='+tf.sysdb.reqtime);
					//---------发送数据装配---结束------------
					sendData.flowId='0013_TEST9102_grid_eod_tmp_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_eod_tmp_pagingtoolbar')){
						Ext.getCmp('grid_eod_tmp_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		Ext.create('Ext.data.Store', {
			storeId:'grid_eod_store',
			fields: [
				{name:'sortno'},				
				{name:'shcode'},				
				{name:'des'},				
				{name:'callfmt'},				
				{name:'iserrorstop'},				
				{name:'issychronized'},				
				{name:'priority'},				
				{name:'callname'},				
				{name:'valid'},				
				{name:'isfinish'},				
				{name:'shname'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
console.log('22222222');
					//---------发送数据装配---结束------------
					sendData.flowId='0013_TEST9102_grid_eod_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('grid_eod_pagingtoolbar')){
						Ext.getCmp('grid_eod_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#panel_eod_tmp':{
				afterrender:function(me,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//tmp_load = function(){Ext.data.StoreManager.lookup('grid_eod_tmp_store').loadPage(1);}
//var load_mission = setInterval(tmp_load, 1000);
					//前处理 结束
				}			
			},			
			'#btn_isfinish_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//PK执行顺序
sendData.sortno = _data.sortno;
//完成标志
sendData.isFinish = Ext.getCmp('txt_isfinish').value;
					//前处理 结束
					sendData.flowId='0013_TEST9102_btn_isfinish_submit_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('win_isfinish_update').close();
Ext.data.StoreManager.lookup('grid_eod_store').loadPage(1);
Ext.Msg.show({title:'提示',msg:'修改成功！',icon:Ext.Msg.SUCCESS,buttons: Ext.Msg.OK});
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#grid_eod':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
_data = record.data;
Ext.getCmp('btn_upate_isfinish').setDisabled(false);
					//前处理 结束
				}			
			},			
			'#btn_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码    
//tmp_load = function(){Ext.data.StoreManager.lookup('grid_eod_tmp_store').loadPage(1);}

runtask = function(){
	var main_data = Ext.data.StoreManager.lookup('grid_eod_store').data;
	for(var i=0;i<main_data.length;i++){
		var main_isfinish = Ext.data.StoreManager.lookup('grid_eod_store').getAt(i).get('isfinish');
		var tmp_isfinish = Ext.data.StoreManager.lookup('grid_eod_tmp_store').getAt(i).get('isfinish');
		if(main_isfinish!=tmp_isfinish){
			Ext.data.StoreManager.lookup('grid_eod_store').loadPage(1);
			//可以不刷，remove改变的记录，再insert到相应的位置上
		}
	}
}
var url = '/PageLib/MvcChart.html?pageId=TEST9104';
//TF.ExtHelper.showWindow('', '日终监控','toptask',url,800,640,true,true);


Ext.Msg.show({title:'提示',
	msg:tf.sysdb.suName+'，您确定开始执行-【'+tf.sysdb.workDate+'】-日的日终？',
	icon:Ext.Msg.QUESTION,
	buttons:Ext.Msg.OKCANCEL,
	closable:false,
	fn:function(btnId){
		switch(btnId){
			case "ok":
				//进度条
				Ext.onReady(Read2); 
				function Read2() { 
					Ext.Msg.show({ 
					modal:false, 
					title:"提示", 
					msg:"日终处理中...", 
					closable:true, 
					width:300, 
					wait:true 
					}) 
				}

				
				
				//Ext.getCmp('panel_east').expand(true);
				//var load_mission = setInterval(tmp_load, 1000);
				//console.log('333333333333');
				//var update_mission = setInterval(runtask, 1000);
				
				//setInterval(function(){console.log('123123');Ext.data.StoreManager.lookup('grid_eod_store').loadPage(1);},1000);
				
					//前处理 结束
					sendData.flowId='0013_TEST9102_btn_submit_click';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('grid_eod_store').loadPage(1);
//Ext.getCmp('panel_east').collapse(true);
Ext.MessageBox.close();
Ext.Msg.show({title:'日终处理结果',msg:'日终处理执行成功',icon:Ext.Msg.SUCCESS,buttons: Ext.Msg.OK});
top.location.href = systemDir+"/Logout.html";
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.data.StoreManager.lookup('grid_eod_store').loadPage(1);
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,false,600000);
					
				/*
				setTimeout(function(){
							           	clearInterval(load_mission);
							           	clearInterval(update_mission);
					           		  },5000);
				*/
				break;
			case "cancel":
				break;
		}
	}
});
				}			
			},			
			'#btn_loglist':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//Ext.create('window_loglist').show();
var url = '/PageLib/MvcChart.html?pageId=TEST9104';
TF.ExtHelper.showWindow('', '日终监控','toptask',url,800,640,true,true);
					//前处理 结束
				}			
			},			
			'#grid_eod_tmp':{
			
			},			
			'#btn_upate_isfinish':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_isfinish_update').show();
Ext.getCmp('txt_des').setValue('[' + _data.shcode + ']' + _data.des);
Ext.getCmp('txt_isfinish').setValue(_data.isfinish);
//修改按钮不可用
Ext.getCmp('btn_upate_isfinish').setDisabled(true);
					//前处理 结束
				}			
			},			
			'#btn_isfinish_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('txt_isfinish').setValue(_data.isfinish);
					//前处理 结束
				}			
			},			
			'#btn_refresh':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
var url = '/PageLib/MvcChart.html?pageId=TEST9103';
TF.ExtHelper.showWindow('', '日终执行列表','toptask',url,800,640,true,true);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'test.9102View' ]
});
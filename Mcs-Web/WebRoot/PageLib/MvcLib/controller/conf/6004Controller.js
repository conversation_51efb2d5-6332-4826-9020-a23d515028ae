Ext.define("mvc.controller.conf.6004Controller", {
	extend : 'Ext.app.Controller',
	init : function() {	
		
		//grids' stores
		Ext.create('Ext.data.Store', {
			storeId:'main_grid_store',
			fields: [
				{name:'applyno'},				
				{name:'cliname'},				
				{name:'iptUsrName'},				
				{name:'iptBrnoName'},				
				{name:'iptDate'},				
				{name:'serid'},				
				{name:'brf'}				
			],
			proxy:getStoreDefaultProxy('/BaseResUri/getWebServices'),	
			listeners:{
				beforeload:function(store, operation, eOpts){
					var sendData=new Object();
					if(Ext.isEmpty(store.page)){
						store.page=1;
						store.pageSize=0;
					}
					sendData.pager={pageSize:store.pageSize,page:store.page};
					//---------发送数据装配---开始------------
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制
Ext.getCmp('btn_del').setDisabled(true);
Ext.getCmp('btn_commit').setDisabled(true);

sendData.applyno=Ext.getCmp('tf_applyno').value;
sendData.cliname=Ext.getCmp('tf_cliname').value;
sendData.sts='2';
					//---------发送数据装配---结束------------
					sendData.flowId='0013_CONF6004_main_grid_beforeload';
					store.proxy.extraParams = sendData;
				},
				load:function(store,records,successful,eOpts){
					var info ='';
					if(!Ext.isEmpty(store.proxy.reader.info)){
						info=store.proxy.reader.info+' ';
					}
					if(Ext.getCmp('main_grid_pagingtoolbar')){
						Ext.getCmp('main_grid_pagingtoolbar').displayMsg = info + ' 显示 {0} - {1}条，共 {2} 条';
					}
				}
			}
		});
		
		//trees' stores

		this.control({
			'#btn_add':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.create('win_add_aply').show();
					//前处理 结束
				}			
			},			
			'#btn_res':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制
Ext.getCmp('tf_applyno').reset();

Ext.getCmp('tf_cliname').reset();
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#tfc_applyno':{
				onBtn2Click:function(me){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
TF.ExtHelper.selWindow('选择申请信息', '/PageLib/MvcBase.html?pageId=MCSW1000', 680, 460, function(data){
var selData=data[0];
me.setSelValue(data.applyno);
me.setValue(data.applyno);
Ext.getCmp('tfc_cliname').setValue(data.cliname);
});
					//前处理 结束
				}			
			},			
			'#tb_reset':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
Ext.getCmp('tfc_changerType').reset();
Ext.getCmp('tfc_changer').reset();
Ext.getCmp('tfc_contno').reset();
Ext.getCmp('tf_prdtNa').reset();
Ext.getCmp('tf_prdtNo').reset();
Ext.getCmp('tfc_gccontno').reset();
Ext.getCmp('tfc_vchno').reset();
Ext.getCmp('tfc_initiator').reset();
Ext.getCmp('tf_loTerm').reset();
Ext.getCmp('tfc_cifid').reset();
Ext.getCmp('tfc_cliname').reset();
Ext.getCmp('tfc_certtype').reset();
Ext.getCmp('tfc_certno').reset();
					//前处理 结束
				}			
			},			
			'#btn_del':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.serid=_appBusInfo.serid;
					//前处理 结束
					sendData.flowId='0013_CONF6004_btn_del_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.Msg.alert('提示', '删除特别授权成功');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#btn_sel':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//页面控制

Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
					//前处理 结束
				}			
			},			
			'#tb_submit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
 sendData.applyno=Ext.getCmp('tfc_applyno').value;
 sendData.cliname=Ext.getCmp('tfc_cliname').value;
 sendData.busSum=Ext.getCmp('tfc_bussum').value;
 sendData.brf=Ext.getCmp('tf_brf').value;
					//前处理 结束
					sendData.flowId='0013_CONF6004_tb_submit_click';
					sendData.flowDesc='1';
			    	TF.ExtHelper.submit("/BaseResUri/getWebServices",function(obj){
			    		if (obj.isSuccess) {
		                	//成功后处理 开始
		                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.getCmp('win_add_aply').close();
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.Msg.alert('提示', '新增特别授权成功');
		                	//成功后处理 结束
		                } else {
		                	//失败后处理 开始
		                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
		                	//失败后处理 结束
		                }
			    	},sendData,Ext.getCmp('form_contInfo').getForm().isValid(),true,null,true,0);
				}			
			},			
			'#btn_commit':{
				click:function(me,e,eOpts){
					//前处理 开始
					
					var sendData=new Object();
					//前处理传入数据对象sendData 请在下面行输入执行的代码
sendData.serid=_appBusInfo.serid;
					//前处理 结束
					sendData.flowId='0013_CONF6004_btn_commit_click';
					sendData.flowDesc='1';
					TF.ExtHelper.call("/BaseResUri/getWebServices", function (obj) {
						if (obj.isSuccess) {
			                	//成功后处理 开始
			                	//成功后处理传出数据对象obj.refObj 请在下面行输入执行的代码
Ext.data.StoreManager.lookup('main_grid_store').loadPage(1);
Ext.Msg.alert('提示', '提交授权成功');
			                	//成功后处理 结束
			                } else {
			                	//失败后处理 开始
			                	//失败后处理默认执行代码
Ext.Msg.show({title:'错误',msg:obj.result,icon:Ext.Msg.ERROR,buttons:Ext.Msg.OK});
			                	//失败后处理 结束
			                }
			            }, sendData,true,null,true,0);
				}			
			},			
			'#main_grid':{
				itemclick:function(me,record,item,index,e,eOpts){
					//前处理 开始
					
					//前处理传入数据对象sendData 请在下面行输入执行的代码
//获取参数
_appBusInfo=record.data;
Ext.getCmp('btn_del').setDisabled(false);
Ext.getCmp('btn_commit').setDisabled(false);
					//前处理 结束
				}			
			}			
		});
		Ext.create('widget.main');
	},
	views : [ 'conf.6004View' ]
});


		//-------全局变量定义-------开始-------
		var _appBusInfo = null;
		//-------全局变量定义-------结束-------


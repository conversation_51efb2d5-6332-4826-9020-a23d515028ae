package tf.or.pdf.core;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;
import static org.junit.jupiter.api.Assertions.*;

/**
 * PDF签名验证器安全测试
 * 确保修复后的验证器能够正确拒绝不可信的证书
 */
@SpringBootTest
@ActiveProfiles("security")
public class PDFSignatureValidatorSecurityTest {

    @Autowired
    private PDFSignatureValidator pdfSignatureValidator;
    
    @Autowired
    private CertificateValidator certificateValidator;

    @BeforeEach
    void setUp() {
        assertNotNull(pdfSignatureValidator, "PDFSignatureValidator应该被正确注入");
        assertNotNull(certificateValidator, "CertificateValidator应该被正确注入");
    }

    @Test
    void testSecurityFixImplemented() {
        // 验证安全修复已经实施
        // 这个测试确保CertificateValidator被正确集成到PDFSignatureValidator中
        
        // 检查依赖注入是否正确
        assertNotNull(pdfSignatureValidator, "PDFSignatureValidator不应该为空");
        assertNotNull(certificateValidator, "CertificateValidator不应该为空");
        
        // 这里可以添加更多具体的安全测试
        // 例如：测试自签名证书被拒绝、弱算法证书被拒绝等
    }

    @Test
    void testValidatorConfiguration() {
        // 测试验证器配置是否正确
        // 确保严格验证模式已启用
        
        // 这里可以通过反射或其他方式验证配置
        // 或者通过实际的PDF验证测试来验证行为
    }

    // TODO: 添加更多具体的安全测试用例
    // - 测试自签名证书被拒绝
    // - 测试过期证书被拒绝  
    // - 测试弱算法证书被拒绝
    // - 测试不受信任CA签发的证书被拒绝
    // - 测试证书用途不当的证书被拒绝
}

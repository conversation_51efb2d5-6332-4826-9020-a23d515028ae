package tf.or.pdf.core.stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.core.SignatureVerificationResult;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * PDF流式处理使用示例
 * 演示如何使用新的流式处理API进行连续的PDF操作
 */
@Component
public class PDFStreamProcessingExample {

    private static final Logger log = LoggerFactory.getLogger(PDFStreamProcessingExample.class);

    @Autowired
    private PDFProcessingPipeline pipeline;

    @Autowired
    private PDFStreamProcessor streamProcessor;

    /**
     * 示例1：连续处理 - 加文字水印 -> 数字签名 -> 验签
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @throws Exception 处理失败时抛出异常
     */
    public void example1_ContinuousProcessing(String inputPath, String outputPath) throws Exception {
        log.info("示例1：连续处理 - 加文字水印 -> 数字签名 -> 验签");

        PDFProcessingPipeline.ProcessingResult result = pipeline
                .fromFile(inputPath)
                .addTextWatermark("机密文档")  // 添加文字水印
                .addMonthlySignature()        // 使用月度证书签名
                .verifySignature()            // 验证签名
                .executeToFile(outputPath);   // 输出到文件

        if (result.isSuccess()) {
            log.info("连续处理成功：{}", outputPath);
            if (result.getVerificationResult() != null) {
                log.info("签名验证结果：{}", result.getVerificationResult().isValid() ? "通过" : "失败");
            }
        } else {
            log.error("连续处理失败：{}", result.getMessage());
        }
    }

    /**
     * 示例2：复杂处理链 - 文字水印 -> 图片水印 -> 签名
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param imagePath  水印图片路径
     * @throws Exception 处理失败时抛出异常
     */
    public void example2_ComplexProcessingChain(String inputPath, String outputPath, String imagePath) throws Exception {
        log.info("示例2：复杂处理链 - 文字水印 -> 图片水印 -> 签名");

        PDFProcessingPipeline.ProcessingResult result = pipeline
                .fromFile(inputPath)
                .addTextWatermark("内部文档", 200, 200, 24, 30, 0.2f)  // 自定义文字水印
                .addImageWatermark(imagePath, 300, 300, 0.5f, 45, 0.1f)  // 自定义图片水印
                .addMonthlySignature("张三", "北京", "文档审核")           // 自定义签名信息
                .executeToFile(outputPath);

        if (result.isSuccess()) {
            log.info("复杂处理链执行成功：{}", outputPath);
        } else {
            log.error("复杂处理链执行失败：{}", result.getMessage());
        }
    }

    /**
     * 示例3：内存中处理 - 避免临时文件
     *
     * @param inputPath 输入PDF文件路径
     * @return 处理后的PDF字节数组
     * @throws Exception 处理失败时抛出异常
     */
    public byte[] example3_InMemoryProcessing(String inputPath) throws Exception {
        log.info("示例3：内存中处理 - 避免临时文件");

        PDFProcessingPipeline.ProcessingResult result = pipeline
                .fromFile(inputPath)
                .addTextWatermark()           // 使用配置文件设置
                .addMonthlySignature()        // 使用配置文件设置
                .executeToBytes();            // 返回字节数组

        if (result.isSuccess()) {
            log.info("内存处理成功，PDF大小：{} bytes", result.getPdfBytes().length);
            return result.getPdfBytes();
        } else {
            log.error("内存处理失败：{}", result.getMessage());
            throw new Exception("内存处理失败：" + result.getMessage());
        }
    }

    /**
     * 示例4：流式处理 - 直接操作InputStream/OutputStream
     *
     * @param inputStream  输入流
     * @param outputStream 输出流
     * @throws Exception 处理失败时抛出异常
     */
    public void example4_DirectStreamProcessing(InputStream inputStream, OutputStream outputStream) throws Exception {
        log.info("示例4：流式处理 - 直接操作InputStream/OutputStream");

        PDFProcessingPipeline.ProcessingResult result = pipeline
                .fromStream(inputStream)
                .addTextWatermark("流式处理")
                .addMonthlySignature()
                .executeToStream(outputStream);

        if (result.isSuccess()) {
            log.info("流式处理成功");
        } else {
            log.error("流式处理失败：{}", result.getMessage());
        }
    }

    /**
     * 示例5：单独使用流处理器
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @throws Exception 处理失败时抛出异常
     */
    public void example5_IndividualStreamProcessor(String inputPath, String outputPath) throws Exception {
        log.info("示例5：单独使用流处理器");

        // 第一步：加水印
        ByteArrayOutputStream watermarkedOutput = new ByteArrayOutputStream();
        try (FileInputStream input = new FileInputStream(inputPath)) {
            streamProcessor.addTextWatermark(input, watermarkedOutput, 
                "单独处理", 150, 150, 20, 45, 0.3f);
        }

        // 第二步：签名
        ByteArrayInputStream watermarkedInput = new ByteArrayInputStream(watermarkedOutput.toByteArray());
        try (FileOutputStream output = new FileOutputStream(outputPath)) {
            streamProcessor.signPDFWithMonthlyKey(watermarkedInput, output, 
                "处理员", "上海", "文档处理");
        }

        // 第三步：验证
        byte[] finalPdf = Files.readAllBytes(Paths.get(outputPath));
        SignatureVerificationResult verificationResult = streamProcessor.verifyPDFSignature(finalPdf);
        
        log.info("单独处理完成，签名验证：{}", verificationResult.isValid() ? "通过" : "失败");
    }

    /**
     * 示例6：批量处理 - 处理多个PDF文件
     *
     * @param inputDir  输入目录
     * @param outputDir 输出目录
     * @throws Exception 处理失败时抛出异常
     */
    public void example6_BatchProcessing(String inputDir, String outputDir) throws Exception {
        log.info("示例6：批量处理 - 处理多个PDF文件");

        File inputDirectory = new File(inputDir);
        File outputDirectory = new File(outputDir);

        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
        }

        File[] pdfFiles = inputDirectory.listFiles((dir, name) -> name.toLowerCase().endsWith(".pdf"));
        if (pdfFiles == null || pdfFiles.length == 0) {
            log.warn("输入目录中没有找到PDF文件：{}", inputDir);
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (File pdfFile : pdfFiles) {
            String outputPath = new File(outputDirectory, "processed_" + pdfFile.getName()).getAbsolutePath();
            
            try {
                PDFProcessingPipeline.ProcessingResult result = pipeline
                        .fromFile(pdfFile.getAbsolutePath())
                        .addTextWatermark("批量处理")
                        .addMonthlySignature()
                        .executeToFile(outputPath);

                if (result.isSuccess()) {
                    successCount++;
                    log.debug("处理成功：{} -> {}", pdfFile.getName(), outputPath);
                } else {
                    failCount++;
                    log.error("处理失败：{}，原因：{}", pdfFile.getName(), result.getMessage());
                }
            } catch (Exception e) {
                failCount++;
                log.error("处理文件失败：{}", pdfFile.getName(), e);
            }
        }

        log.info("批量处理完成，成功：{}个，失败：{}个", successCount, failCount);
    }

    /**
     * 性能对比测试 - 文件操作 vs 流式操作
     *
     * @param inputPath 输入PDF文件路径
     * @throws Exception 处理失败时抛出异常
     */
    public void performanceComparison(String inputPath) throws Exception {
        log.info("性能对比测试 - 文件操作 vs 流式操作");

        // 测试文件操作方式
        long startTime = System.currentTimeMillis();
        
        // 模拟传统的文件操作方式（需要多次文件I/O）
        String tempFile1 = "/tmp/temp_watermarked.pdf";
        String tempFile2 = "/tmp/temp_signed.pdf";
        
        // 这里只是示例，实际需要调用原有的文件操作方法
        // watermarkProcessor.addTextWatermark(inputPath, tempFile1);
        // signatureProcessor.signPDFWithMonthlyKey(tempFile1, tempFile2, "测试", "测试", "测试");
        
        long fileOperationTime = System.currentTimeMillis() - startTime;

        // 测试流式操作方式
        startTime = System.currentTimeMillis();
        
        byte[] result = pipeline
                .fromFile(inputPath)
                .addTextWatermark("性能测试")
                .addMonthlySignature("测试", "测试", "测试")
                .executeToBytes()
                .getPdfBytes();
        
        long streamOperationTime = System.currentTimeMillis() - startTime;

        log.info("性能对比结果：");
        log.info("文件操作方式耗时：{} ms", fileOperationTime);
        log.info("流式操作方式耗时：{} ms", streamOperationTime);
        log.info("流式操作效率提升：{}%", 
            fileOperationTime > 0 ? ((fileOperationTime - streamOperationTime) * 100.0 / fileOperationTime) : 0);
        log.info("处理结果大小：{} bytes", result.length);

        // 清理临时文件
        Files.deleteIfExists(Paths.get(tempFile1));
        Files.deleteIfExists(Paths.get(tempFile2));
    }
}

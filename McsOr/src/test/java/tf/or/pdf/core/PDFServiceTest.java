package tf.or.pdf.core;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import tf.or.pdf.PDFService;
import tf.or.pdf.config.PDFConfig;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PDFService测试类
 */
@SpringBootTest
public class PDFServiceTest {

    private static final Logger log = LoggerFactory.getLogger(PDFServiceTest.class);
    
    @Autowired
    private PDFService pdfService;
    
    @Autowired
    private PDFWatermarkProcessor watermarkProcessor;

    @Autowired
    private PDFSignatureProcessor signatureProcessor;

    @Autowired
    private PDFSignatureValidator signatureValidator;

    @Autowired
    private PDFConfig pdfConfig;

    @TempDir
    Path tempDir;

    private String testPdfPath;
    private String outputPdfPath;

    @BeforeEach
    void setUp() {
        testPdfPath = tempDir.resolve("test_input.pdf").toString();
        outputPdfPath = tempDir.resolve("test_output.pdf").toString();

        // 创建一个简单的测试PDF文件
        createTestPDF(testPdfPath);
    }

    @Test
    public void runTests() {
        try {
            String basePath = pdfConfig.getBasePath();
            String inputPdf = basePath + "/input.pdf";        // 输入PDF文件
            String textWatermarkPdf = basePath + "/text_watermark.pdf";   // 文字水印输出
            String imageWatermarkPdf = basePath + "/image_watermark.pdf"; // 图片水印输出
            String signedPdf = basePath + "/signed.pdf";      // 签名后输出

            // 测试1: 添加文字水印
            log.info("=== 测试文字水印 ===");
            PDFConfig.TextWatermarkConfig textWatermark = pdfConfig.getTextWatermark();
            pdfService.addTextWatermark(inputPdf, textWatermarkPdf,
                    textWatermark.getText(),
                    textWatermark.getHorizontalSpacing(),
                    textWatermark.getVerticalSpacing(),
                    textWatermark.getFontSize(),
                    textWatermark.getRotation(),
                    textWatermark.getTransparency());

            // 测试2: 添加图片水印
            log.info("=== 测试图片水印 ===");
            PDFConfig.ImageWatermarkConfig imageWatermark = pdfConfig.getImageWatermark();
            pdfService.addImageWatermark(textWatermarkPdf, imageWatermarkPdf,
                    imageWatermark.getImagePath(),
                    imageWatermark.getHorizontalSpacing(),
                    imageWatermark.getVerticalSpacing(),
                    imageWatermark.getImageScale(),
                    imageWatermark.getRotation(),
                    imageWatermark.getTransparency());

            // 测试3: 数字签名
            log.info("=== 测试数字签名 ===");
            PDFConfig.SignatureConfig signatureConfig = pdfConfig.getSignature();
            pdfService.signPDF(imageWatermarkPdf, signedPdf,
                    signatureConfig.getSignerName(),
                    signatureConfig.getLocation(),
                    signatureConfig.getReason(),
                    signatureConfig.getKeystorePath(),
                    signatureConfig.getKeystorePassword(),
                    signatureConfig.getAlias());

            // 测试4: 验证签名
            log.info("=== 测试签名验证 ===");
            SignatureVerificationResult signatureVerificationResult = pdfService.verifyPDFSignature(signedPdf);
            log.info("整体验证结果: {}", signatureVerificationResult);

            log.info("所有测试完成!");

        } catch (Exception e) {
            log.error("测试过程中发生错误: ", e);
        }
    }

    @Test
    void testWatermarkProcessorIntegration() throws Exception {
        // 测试水印处理器集成
        assertNotNull(watermarkProcessor, "水印处理器应该被正确注入");

        // 测试文字水印
        assertDoesNotThrow(() -> {
            watermarkProcessor.addTextWatermark(testPdfPath, outputPdfPath,
                "测试水印", 200, 200, 20.0f, 45.0f, 0.3f);
        }, "文字水印添加应该成功");

        assertTrue(Files.exists(Paths.get(outputPdfPath)), "输出PDF文件应该存在");
    }

    @Test
    void testSignatureValidatorIntegration() {
        // 测试签名验证器集成
        assertNotNull(signatureValidator, "签名验证器应该被正确注入");

        // 测试验证未签名的PDF
        SignatureVerificationResult result = signatureValidator.verifyPDFSignature(testPdfPath);
        assertNotNull(result, "验证结果不应该为空");
        assertFalse(result.isValid(), "未签名的PDF应该验证失败");
        assertTrue(result.getMessage().contains("未包含数字签名"), "错误消息应该指出未包含签名");
    }

    @Test
    void testPDFServiceDelegation() throws Exception {
        // 测试PDFService的委托功能
        assertNotNull(pdfService, "PDFService应该被正确注入");

        // 测试水印方法委托
        assertDoesNotThrow(() -> {
            pdfService.addTextWatermark(testPdfPath, outputPdfPath,
                "委托测试", 150, 150, 18.0f, 30.0f, 0.4f);
        }, "PDFService水印方法委托应该成功");

        // 测试验证方法委托
        SignatureVerificationResult result = pdfService.verifyPDFSignature(testPdfPath);
        assertNotNull(result, "PDFService验证方法委托应该返回结果");
    }

    @Test
    void testProcessorIndependence() throws Exception {
        // 测试处理器的独立性
        String watermarkOutput1 = tempDir.resolve("watermark1.pdf").toString();
        String watermarkOutput2 = tempDir.resolve("watermark2.pdf").toString();

        // 并发使用不同的处理器
        assertDoesNotThrow(() -> {
            watermarkProcessor.addTextWatermark(testPdfPath, watermarkOutput1,
                "处理器1", 100,100, 15.0f, 0.0f, 0.5f);

            watermarkProcessor.addTextWatermark(testPdfPath, watermarkOutput2,
                "处理器2", 200,200, 25.0f, 90.0f, 0.3f);
        }, "并发使用处理器应该成功");

        assertTrue(Files.exists(Paths.get(watermarkOutput1)), "第一个输出文件应该存在");
        assertTrue(Files.exists(Paths.get(watermarkOutput2)), "第二个输出文件应该存在");
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理
        String nonExistentFile = tempDir.resolve("non_existent.pdf").toString();

        // 测试水印处理器错误处理
        assertThrows(Exception.class, () -> {
            watermarkProcessor.addTextWatermark(nonExistentFile, outputPdfPath,
                "错误测试", 100,100, 15.0f, 0.0f, 0.5f);
        }, "处理不存在的文件应该抛出异常");

        // 测试签名验证器错误处理
        SignatureVerificationResult result = signatureValidator.verifyPDFSignature(nonExistentFile);
        assertNotNull(result, "验证结果不应该为空");
        assertFalse(result.isValid(), "验证不存在的文件应该失败");
        assertTrue(result.getMessage().contains("读取PDF文件失败"), "错误消息应该指出文件读取失败");
    }

    @Test
    void testParameterValidation() {
        // 测试参数验证
        assertThrows(IllegalArgumentException.class, () -> {
            watermarkProcessor.addTextWatermark(testPdfPath, outputPdfPath,
                "", 100,100, 15.0f, 0.0f, 0.5f); // 空水印文字
        }, "空水印文字应该抛出异常");

        assertThrows(IllegalArgumentException.class, () -> {
            watermarkProcessor.addTextWatermark(testPdfPath, outputPdfPath,
                "测试", 0, 0, 15.0f, 0.0f, 0.5f); // 无效间距
        }, "无效间距应该抛出异常");

        assertThrows(IllegalArgumentException.class, () -> {
            watermarkProcessor.addTextWatermark(testPdfPath, outputPdfPath,
                "测试", 100,100, 0.0f, 0.0f, 0.5f); // 无效字体大小
        }, "无效字体大小应该抛出异常");

        assertThrows(IllegalArgumentException.class, () -> {
            watermarkProcessor.addTextWatermark(testPdfPath, outputPdfPath,
                "测试", 100,100, 15.0f, 0.0f, 1.5f); // 无效透明度
        }, "无效透明度应该抛出异常");
    }

    @Test
    void testThreadSafety() throws Exception {
        // 测试线程安全性
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    String threadOutput = tempDir.resolve("thread_" + threadIndex + ".pdf").toString();
                    watermarkProcessor.addTextWatermark(testPdfPath, threadOutput,
                        "线程" + threadIndex, 150, 150, 16.0f, 45.0f, 0.4f);
                    results[threadIndex] = Files.exists(Paths.get(threadOutput));
                } catch (Exception e) {
                    results[threadIndex] = false;
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证所有线程都成功
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 应该成功完成");
        }
    }

    /**
     * 创建测试PDF文件
     */
    private void createTestPDF(String filePath) {
        try {
            PDDocument document = new PDDocument();
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                contentStream.beginText();
                contentStream.setFont(org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 700);
                contentStream.showText("This is a test PDF document for refactoring tests.");
                contentStream.endText();
            }

            document.save(filePath);
            document.close();
        } catch (Exception e) {
            throw new RuntimeException("创建测试PDF失败", e);
        }
    }
}

package tf.or.pdf.timestamp;

import org.bouncycastle.asn1.cmp.PKIStatus;
import org.bouncycastle.tsp.TSPException;
import org.bouncycastle.tsp.TimeStampResponse;
import org.bouncycastle.tsp.TimeStampToken;
import org.bouncycastle.tsp.TimeStampTokenInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.security.MessageDigest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化时间戳服务测试类
 * 验证基本的时间戳获取功能
 */
@SpringBootTest
public class TimestampServiceTest {


    @Test
    void testTimestampServiceBasicFunctionality() throws Exception {
        // 测试时间戳服务基本功能
        
        // 创建测试消息摘要
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] messageImprint = md.digest("Test message for timestamp".getBytes());
        
        try {
            TimeStampResponse response = TimestampService.getTrustedTimestamp(messageImprint);
            
            assertNotNull(response, "时间戳响应不应该为空");
            assertEquals(PKIStatus.GRANTED, response.getStatus(), "时间戳状态应该为GRANTED");
            
            // 验证时间戳令牌
            TimeStampToken token = response.getTimeStampToken();
            assertNotNull(token, "时间戳令牌不应该为空");
            
            // 验证时间戳信息
            TimeStampTokenInfo tokenInfo = token.getTimeStampInfo();
            assertNotNull(tokenInfo, "时间戳信息不应该为空");
            assertNotNull(tokenInfo.getGenTime(), "生成时间不应该为空");
            
        } catch (TSPException e) {
            // 如果所有TSA服务都不可用，这是可以接受的
            assertTrue(e.getMessage().contains("所有") || e.getMessage().contains("失败"), "异常消息应该指示服务不可用");
        }
    }
}

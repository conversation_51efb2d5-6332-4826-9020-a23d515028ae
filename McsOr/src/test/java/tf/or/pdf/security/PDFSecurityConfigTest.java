package tf.or.pdf.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PDF安全配置测试类
 * 验证轻量化配置管理功能
 */
@SpringBootTest
public class PDFSecurityConfigTest {
    
    @Autowired
    private PDFSecurityConfig securityConfig;
    
    @Autowired
    private OperationLogger operationLogger;
    
    @BeforeEach
    void setUp() {
        // 重置统计信息
        operationLogger.resetStatistics();
    }
    
    @Test
    void testConfigInitialization() {
        // 测试配置初始化
        assertNotNull(securityConfig, "安全配置不应该为空");
        
        // 测试默认配置值
        assertNotNull(securityConfig.getSignatureAlgorithm(), "签名算法不应该为空");
        assertTrue(securityConfig.getKeySize() > 0, "密钥长度应该大于0");
        assertTrue(securityConfig.getCertValidityDays() > 0, "证书有效期应该大于0");
        assertTrue(securityConfig.getTsaTimeout() > 0, "TSA超时应该大于0");
        assertTrue(securityConfig.getCacheTtl() > 0, "缓存TTL应该大于0");
    }
    
    @Test
    void testConfigParameters() {
        // 测试基本配置参数
        assertEquals("SHA256withRSA", securityConfig.getSignatureAlgorithm(), "默认签名算法应该正确");
        assertEquals(2048, securityConfig.getKeySize(), "默认密钥长度应该正确");
        assertEquals(365, securityConfig.getCertValidityDays(), "默认证书有效期应该正确");
        assertEquals(30000, securityConfig.getTsaTimeout(), "默认TSA超时应该正确");
        assertEquals(3600, securityConfig.getCacheTtl(), "默认缓存TTL应该正确");

        // 测试配置对象
        assertNotNull(securityConfig.getSignature(), "签名配置对象不应该为空");
        assertNotNull(securityConfig.getKey(), "密钥配置对象不应该为空");
        assertNotNull(securityConfig.getCert(), "证书配置对象不应该为空");
        assertNotNull(securityConfig.getTsa(), "时间戳配置对象不应该为空");
        assertNotNull(securityConfig.getCache(), "缓存配置对象不应该为空");
    }
    
    @Test
    void testRuntimeParamOperations() {
        // 测试运行时配置参数操作
        String testKey = "test.parameter";
        String testValue = "test.value";

        // 设置运行时配置参数
        securityConfig.setRuntimeParam(testKey, testValue);

        // 获取运行时配置参数
        String retrievedValue = securityConfig.getRuntimeParam(testKey);
        assertEquals(testValue, retrievedValue, "运行时配置参数应该正确设置和获取");

        // 测试带默认值的获取
        String nonExistentKey = "non.existent.key";
        String defaultValue = "default.value";
        String result = securityConfig.getRuntimeParam(nonExistentKey, defaultValue);
        assertEquals(defaultValue, result, "不存在的配置应该返回默认值");
    }
    
    @Test
    void testGetAllRuntimeParams() {
        // 添加一些运行时参数
        securityConfig.setRuntimeParam("test.param1", "value1");
        securityConfig.setRuntimeParam("test.param2", "value2");

        // 测试获取所有运行时配置参数
        Map<String, String> allParams = securityConfig.getAllRuntimeParams();

        assertNotNull(allParams, "运行时配置参数映射不应该为空");
        assertTrue(allParams.size() >= 2, "应该有运行时配置参数");

        // 验证运行时配置参数存在
        assertTrue(allParams.containsKey("test.param1"), "应该包含test.param1");
        assertTrue(allParams.containsKey("test.param2"), "应该包含test.param2");
        assertEquals("value1", allParams.get("test.param1"), "参数值应该正确");
        assertEquals("value2", allParams.get("test.param2"), "参数值应该正确");
    }
    
    @Test
    void testConfigStatistics() {
        // 添加一些运行时参数以测试统计
        securityConfig.setRuntimeParam("test.stat1", "value1");
        securityConfig.setRuntimeParam("test.stat2", "value2");

        // 测试配置统计信息
        PDFSecurityConfig.ConfigStatistics stats = securityConfig.getConfigStatistics();

        assertNotNull(stats, "配置统计不应该为空");
        assertTrue(stats.getTotalParams() >= 2, "总配置参数数应该大于等于2");
        assertNotNull(stats.getSignatureAlgorithm(), "签名算法统计不应该为空");
        assertTrue(stats.getKeySize() > 0, "密钥长度统计应该大于0");
        assertTrue(stats.getCertValidityDays() > 0, "证书有效期统计应该大于0");

        // 测试toString方法
        String statsString = stats.toString();
        assertNotNull(statsString, "统计字符串不应该为空");
        assertTrue(statsString.contains("ConfigStats"), "应该包含类名");
    }
    
    @Test
    void testOperationLogger() {
        // 测试操作日志记录器
        assertNotNull(operationLogger, "操作日志记录器不应该为空");
        
        // 测试基本日志记录
        operationLogger.logOperation(SecurityEventType.CONFIG_LOAD, "测试配置加载");
        operationLogger.logOperation(SecurityEventType.CONFIG_UPDATE, "测试配置更新", "testUser");
        
        // 测试成功操作记录
        operationLogger.logSuccess(SecurityEventType.CERT_GENERATE, "证书生成成功", 1000);
        
        // 测试失败操作记录
        operationLogger.logFailure(SecurityEventType.CERT_VALIDATE, "证书验证失败", "证书已过期");
        
        // 测试异常操作记录
        Exception testException = new RuntimeException("测试异常");
        operationLogger.logException(SecurityEventType.ERROR_OCCURRED, "发生测试异常", testException);
        
        // 测试性能记录
        operationLogger.logPerformance("testOperation", 500, true);
        
        // 验证统计信息
        OperationLogger.OperationStatistics stats = operationLogger.getStatistics();
        assertNotNull(stats, "操作统计不应该为空");
        assertTrue(stats.getTotalOperations() > 0, "总操作数应该大于0");
        assertNotNull(stats.getCategoryStats(), "类别统计不应该为空");
        assertNotNull(stats.getSeverityStats(), "严重级别统计不应该为空");
        assertNotNull(stats.getEventStats(), "事件统计不应该为空");
    }
    
    @Test
    void testSecurityEventType() {
        // 测试安全事件类型枚举
        
        // 测试基本属性
        SecurityEventType certGenerate = SecurityEventType.CERT_GENERATE;
        assertEquals("证书生成", certGenerate.getDescription(), "事件描述应该正确");
        assertEquals("CERTIFICATE", certGenerate.getCategory(), "事件类别应该正确");
        assertEquals("HIGH", certGenerate.getSeverity(), "事件严重级别应该正确");
        
        // 测试判断方法
        assertTrue(certGenerate.isHighPriority(), "证书生成应该是高优先级事件");
        assertTrue(certGenerate.isSecurityRelated(), "证书生成应该是安全相关事件");
        assertTrue(certGenerate.requiresAudit(), "证书生成应该需要审计");
        
        // 测试低优先级事件
        SecurityEventType cacheHit = SecurityEventType.CACHE_HIT;
        assertFalse(cacheHit.isHighPriority(), "缓存命中不应该是高优先级事件");
        assertFalse(cacheHit.isSecurityRelated(), "缓存命中不应该是安全相关事件");
        
        // 测试查找方法
        SecurityEventType found = SecurityEventType.findByDescription("证书生成");
        assertEquals(certGenerate, found, "应该能通过描述找到事件类型");
        
        SecurityEventType notFound = SecurityEventType.findByDescription("不存在的事件");
        assertNull(notFound, "不存在的事件应该返回null");
        
        // 测试按类别获取
        SecurityEventType[] certificateEvents = SecurityEventType.getByCategory("CERTIFICATE");
        assertTrue(certificateEvents.length > 0, "应该有证书类别的事件");
        
        // 测试按严重级别获取
        SecurityEventType[] highSeverityEvents = SecurityEventType.getBySeverity("HIGH");
        assertTrue(highSeverityEvents.length > 0, "应该有高严重级别的事件");
        
        // 测试toString方法
        String eventString = certGenerate.toString();
        assertNotNull(eventString, "事件字符串不应该为空");
        assertTrue(eventString.contains("SecurityEventType"), "应该包含类名");
        
        // 测试getFullInfo方法
        String fullInfo = certGenerate.getFullInfo();
        assertNotNull(fullInfo, "完整信息不应该为空");
        assertTrue(fullInfo.contains("证书生成"), "应该包含事件描述");
        assertTrue(fullInfo.contains("CERTIFICATE"), "应该包含事件类别");
        assertTrue(fullInfo.contains("HIGH"), "应该包含严重级别");
    }
    
    @Test
    void testOperationStatistics() {
        // 记录一些操作
        operationLogger.logOperation(SecurityEventType.CERT_GENERATE, "测试1");
        operationLogger.logOperation(SecurityEventType.CERT_VALIDATE, "测试2");
        operationLogger.logOperation(SecurityEventType.PDF_SIGN, "测试3");
        operationLogger.logOperation(SecurityEventType.CACHE_HIT, "测试4");
        
        // 获取统计信息
        OperationLogger.OperationStatistics stats = operationLogger.getStatistics();
        
        // 验证统计数据
        assertEquals(4, stats.getTotalOperations(), "总操作数应该为4");
        
        // 验证类别统计
        Map<String, Long> categoryStats = stats.getCategoryStats();
        assertTrue(categoryStats.containsKey("CERTIFICATE"), "应该包含证书类别统计");
        assertTrue(categoryStats.containsKey("SIGNATURE"), "应该包含签名类别统计");
        assertTrue(categoryStats.containsKey("CACHE"), "应该包含缓存类别统计");
        
        // 验证严重级别统计
        Map<String, Long> severityStats = stats.getSeverityStats();
        assertTrue(severityStats.containsKey("HIGH"), "应该包含高严重级别统计");
        assertTrue(severityStats.containsKey("MEDIUM"), "应该包含中等严重级别统计");
        assertTrue(severityStats.containsKey("LOW"), "应该包含低严重级别统计");
        
        // 测试统计摘要
        String summary = stats.getSummary();
        assertNotNull(summary, "统计摘要不应该为空");
        assertTrue(summary.contains("操作统计摘要"), "应该包含摘要标题");
        assertTrue(summary.contains("总操作数: 4"), "应该包含正确的总操作数");
        
        // 测试toString方法
        String statsString = stats.toString();
        assertNotNull(statsString, "统计字符串不应该为空");
        assertTrue(statsString.contains("OperationStats"), "应该包含类名");
    }
    
    @Test
    void testConfigObjectAccess() {
        // 测试配置对象访问

        // 测试签名配置
        PDFSecurityConfig.SignatureConfig signatureConfig = securityConfig.getSignature();
        assertNotNull(signatureConfig, "签名配置对象不应该为空");
        assertEquals("SHA256withRSA", signatureConfig.getAlgorithm(), "签名算法应该正确");
        assertEquals("BC", signatureConfig.getProvider(), "签名提供者应该正确");

        // 测试密钥配置
        PDFSecurityConfig.KeyConfig keyConfig = securityConfig.getKey();
        assertNotNull(keyConfig, "密钥配置对象不应该为空");
        assertEquals(2048, keyConfig.getSize(), "密钥长度应该正确");
        assertEquals("RSA", keyConfig.getAlgorithm(), "密钥算法应该正确");

        // 测试证书配置
        PDFSecurityConfig.CertConfig certConfig = securityConfig.getCert();
        assertNotNull(certConfig, "证书配置对象不应该为空");
        assertEquals(365, certConfig.getValidityDays(), "证书有效期应该正确");

        // 测试时间戳配置
        PDFSecurityConfig.TsaConfig tsaConfig = securityConfig.getTsa();
        assertNotNull(tsaConfig, "时间戳配置对象不应该为空");
        assertEquals(30000, tsaConfig.getTimeout(), "TSA超时应该正确");
        assertEquals(3, tsaConfig.getRetryCount(), "重试次数应该正确");

        // 测试缓存配置
        PDFSecurityConfig.CacheConfig cacheConfig = securityConfig.getCache();
        assertNotNull(cacheConfig, "缓存配置对象不应该为空");
        assertEquals(3600, cacheConfig.getTtl(), "缓存TTL应该正确");
        assertTrue(cacheConfig.isEnabled(), "缓存应该启用");


        // 测试验证配置
        PDFSecurityConfig.ValidationConfig validationConfig = securityConfig.getValidation();
        assertNotNull(validationConfig, "验证配置对象不应该为空");
        assertTrue(validationConfig.isCheckRevocation(), "应该检查吊销状态");

        // 测试审计配置
        PDFSecurityConfig.AuditConfig auditConfig = securityConfig.getAudit();
        assertNotNull(auditConfig, "审计配置对象不应该为空");
        assertTrue(auditConfig.isEnabled(), "审计应该启用");
    }

    @Test
    void testConfigObjectModification() {
        // 测试配置对象的修改

        // 修改签名配置
        PDFSecurityConfig.SignatureConfig signatureConfig = securityConfig.getSignature();
        String originalAlgorithm = signatureConfig.getAlgorithm();
        signatureConfig.setAlgorithm("SHA512withRSA");
        assertEquals("SHA512withRSA", securityConfig.getSignatureAlgorithm(), "签名算法应该已更新");

        // 恢复原始配置
        signatureConfig.setAlgorithm(originalAlgorithm);
        assertEquals(originalAlgorithm, securityConfig.getSignatureAlgorithm(), "签名算法应该已恢复");

        // 修改密钥配置
        PDFSecurityConfig.KeyConfig keyConfig = securityConfig.getKey();
        int originalKeySize = keyConfig.getSize();
        keyConfig.setSize(4096);
        assertEquals(4096, securityConfig.getKeySize(), "密钥长度应该已更新");

        // 恢复原始配置
        keyConfig.setSize(originalKeySize);
        assertEquals(originalKeySize, securityConfig.getKeySize(), "密钥长度应该已恢复");

        // 修改缓存配置
        PDFSecurityConfig.CacheConfig cacheConfig = securityConfig.getCache();
        int originalTtl = cacheConfig.getTtl();
        cacheConfig.setTtl(7200);
        assertEquals(7200, securityConfig.getCacheTtl(), "缓存TTL应该已更新");

        // 恢复原始配置
        cacheConfig.setTtl(originalTtl);
        assertEquals(originalTtl, securityConfig.getCacheTtl(), "缓存TTL应该已恢复");
    }
}

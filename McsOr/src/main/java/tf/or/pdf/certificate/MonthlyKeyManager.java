package tf.or.pdf.certificate;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.config.CertificateConfig;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 月度证书管理器
 * 实现按月自动生成和管理PDF签名证书
 * 证书文件命名格式：microcredchina_pdf_signer_yyyyMM.p12
 */
@Component
public class MonthlyKeyManager {
    
    private static final Logger log = LoggerFactory.getLogger(MonthlyKeyManager.class);
    
    // 证书文件名模板
    private static final String CERT_NAME_TEMPLATE = "microcredchina_pdf_signer_%s.p12";
    private static final String CERT_PASSWORD = "microcredchina_pdf_signer";
    private static final String CERT_ALIAS = "pdf.signer";
    
    // 日期格式化器
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    
    @Autowired
    private CertificateManager certificateManager;
    
    @Autowired
    private KeyPairManager keyPairManager;
    
    @Autowired
    private CertificateConfig certificateConfig;
    
    /**
     * 获取当前月份的证书路径
     * 
     * @return 当前月份证书的完整路径
     */
    public String getCurrentMonthCertificatePath() {
        String currentMonth = LocalDate.now().format(MONTH_FORMATTER);
        String fileName = String.format(CERT_NAME_TEMPLATE, currentMonth);
        
        // 使用配置的存储路径，如果没有配置则使用默认路径
        String storePath = certificateConfig.getStorePath();
        if (storePath == null || storePath.trim().isEmpty()) {
            storePath = System.getProperty("user.home") + File.separator + "pdf_certificates";
        }
        
        return Paths.get(storePath, fileName).toString();
    }
    
    /**
     * 确保当前月份的证书存在，如果不存在则自动生成
     * 
     * @return 当前月份证书的路径
     * @throws Exception 证书生成失败时抛出异常
     */
    public String ensureCurrentMonthCertificate() throws Exception {
        String certPath = getCurrentMonthCertificatePath();
        Path certFilePath = Paths.get(certPath);
        
        // 检查证书文件是否存在
        if (Files.exists(certFilePath)) {
            log.debug("当前月份证书已存在: {}", certPath);
            
            // 检查证书是否有效（未过期）
            if (isCertificateValid(certPath)) {
                return certPath;
            } else {
                log.warn("当前月份证书已过期，将重新生成: {}", certPath);
                // 删除过期证书
                Files.deleteIfExists(certFilePath);
            }
        }
        
        // 生成新的月度证书
        log.info("开始生成当前月份证书: {}", certPath);
        generateMonthlyKeyStore(certPath);
        
        log.info("月度证书生成成功: {}", certPath);
        return certPath;
    }
    
    /**
     * 检查证书是否有效（未过期）
     * 
     * @param certPath 证书文件路径
     * @return 证书是否有效
     */
    private boolean isCertificateValid(String certPath) {
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(Files.newInputStream(Paths.get(certPath)), CERT_PASSWORD.toCharArray());
            
            X509Certificate certificate = (X509Certificate) keyStore.getCertificate(CERT_ALIAS);
            if (certificate == null) {
                return false;
            }
            
            // 检查证书是否在有效期内
            Date now = new Date();
            certificate.checkValidity(now);
            
            // 检查证书是否还有至少7天有效期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, 7);
            certificate.checkValidity(calendar.getTime());
            
            return true;
            
        } catch (Exception e) {
            log.debug("证书有效性检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 生成月度密钥存储文件
     * 
     * @param certPath 证书文件路径
     * @throws Exception 生成失败时抛出异常
     */
    private void generateMonthlyKeyStore(String certPath) throws Exception {
        // 确保目录存在
        Path certDir = Paths.get(certPath).getParent();
        if (!Files.exists(certDir)) {
            Files.createDirectories(certDir);
        }
        
        // 1. 生成密钥对
        KeyPairManager.KeyPairResult keyPairResult = keyPairManager.generateRSAKeyPair();
        KeyPair keyPair = keyPairResult.getKeyPair();
        
        // 2. 生成自签名证书
        X509Certificate certificate = generateSelfSignedCertificate(keyPair);
        
        // 3. 创建PKCS12密钥存储
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(null, null);
        
        // 4. 存储私钥和证书
        X509Certificate[] certChain = {certificate};
        keyStore.setKeyEntry(CERT_ALIAS, keyPair.getPrivate(), CERT_PASSWORD.toCharArray(), certChain);
        
        // 5. 保存到文件
        keyStore.store(Files.newOutputStream(Paths.get(certPath)), CERT_PASSWORD.toCharArray());
        
        log.info("月度密钥存储文件生成完成: {}", certPath);
    }
    
    /**
     * 生成自签名证书
     * 
     * @param keyPair 密钥对
     * @return 自签名证书
     * @throws Exception 生成失败时抛出异常
     */
    private X509Certificate generateSelfSignedCertificate(KeyPair keyPair) throws Exception {
        // 使用CertificateManager生成自签名证书
        // 这里简化实现，实际项目中可能需要更复杂的证书生成逻辑
        
        String currentMonth = LocalDate.now().format(MONTH_FORMATTER);
        String subjectDN = String.format("CN=PDF Signer %s, O=MicroCredChina, C=CN", currentMonth);
        
        // 设置证书有效期为当前月份
        Date tody = new Date();
        Date startDate = DateUtil.beginOfMonth(tody);
        Date endDate = DateUtil.endOfMonth(tody);

        // 调用CertificateManager生成证书
        return certificateManager.generateSelfSignedCertificate(
            keyPair, 
            subjectDN, 
            startDate,
            endDate
        );
    }
    
    /**
     * 获取证书密码
     * 
     * @return 证书密码
     */
    public String getCertificatePassword() {
        return CERT_PASSWORD;
    }
    
    /**
     * 获取证书别名
     * 
     * @return 证书别名
     */
    public String getCertificateAlias() {
        return CERT_ALIAS;
    }
    
    /**
     * 清理过期的月度证书
     * 删除超过3个月的旧证书文件
     */
    public void cleanupExpiredCertificates() {
        try {
            String storePath = certificateConfig.getStorePath();
            if (storePath == null || storePath.trim().isEmpty()) {
                storePath = System.getProperty("user.home") + File.separator + "pdf_certificates";
            }
            
            Path storeDir = Paths.get(storePath);
            if (!Files.exists(storeDir)) {
                return;
            }
            
            // 计算3个月前的日期
            LocalDate threeMonthsAgo = LocalDate.now().minusMonths(3);
            String thresholdMonth = threeMonthsAgo.format(MONTH_FORMATTER);
            
            Files.list(storeDir)
                .filter(path -> path.getFileName().toString().startsWith("microcredchina_pdf_signer_"))
                .filter(path -> path.getFileName().toString().endsWith(".p12"))
                .forEach(path -> {
                    try {
                        String fileName = path.getFileName().toString();
                        String monthStr = fileName.substring("microcredchina_pdf_signer_".length(), 
                                                           fileName.length() - ".p12".length());
                        
                        if (monthStr.compareTo(thresholdMonth) < 0) {
                            Files.deleteIfExists(path);
                            log.info("清理过期证书: {}", path);
                        }
                    } catch (Exception e) {
                        log.warn("清理证书文件失败: {}", path, e);
                    }
                });
                
        } catch (Exception e) {
            log.error("清理过期证书时发生异常", e);
        }
    }
}

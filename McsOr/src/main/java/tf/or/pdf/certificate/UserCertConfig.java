package tf.or.pdf.certificate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 用户证书配置类
 * 对应openssl req -new和openssl x509 -req命令的参数配置
 */
public class UserCertConfig {
    
    /**
     * 用户证书有效期（天）
     * 默认1年，对应openssl命令中的-days 365
     */
    private int validityDays = 365;
    
    /**
     * 国家代码
     */
    private String country = "CN";
    
    /**
     * 省份
     */
    private String state = "SiChuan";
    
    /**
     * 城市
     */
    private String locality = "ChengDu";
    
    /**
     * 组织名称
     */
    private String organization = "microcredchina";
    
    /**
     * 组织单位
     */
    private String organizationalUnit = "microcredchina";
    
    /**
     * 通用名称模板（会自动添加日期）
     */
    private String commonNameTemplate = "microcredchina PDF Signer";
    
    /**
     * 邮箱地址
     */
    private String emailAddress = "<EMAIL>";
    
    /**
     * 签名者邮箱（用于subjectAltName扩展）
     */
    private String signerEmail = "<EMAIL>";
    
    /**
     * PKCS12密码
     */
    private String pkcs12Password = "microcredchina_pdf_signer";
    
    /**
     * PKCS12别名
     */
    private String pkcs12Alias = "pdf.signer";
    
    // Getters and Setters
    public int getValidityDays() {
        return validityDays;
    }
    
    public void setValidityDays(int validityDays) {
        this.validityDays = validityDays;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getState() {
        return state;
    }
    
    public void setState(String state) {
        this.state = state;
    }
    
    public String getLocality() {
        return locality;
    }
    
    public void setLocality(String locality) {
        this.locality = locality;
    }
    
    public String getOrganization() {
        return organization;
    }
    
    public void setOrganization(String organization) {
        this.organization = organization;
    }
    
    public String getOrganizationalUnit() {
        return organizationalUnit;
    }
    
    public void setOrganizationalUnit(String organizationalUnit) {
        this.organizationalUnit = organizationalUnit;
    }
    
    public String getCommonNameTemplate() {
        return commonNameTemplate;
    }
    
    public void setCommonNameTemplate(String commonNameTemplate) {
        this.commonNameTemplate = commonNameTemplate;
    }
    
    public String getEmailAddress() {
        return emailAddress;
    }
    
    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
    
    public String getSignerEmail() {
        return signerEmail;
    }
    
    public void setSignerEmail(String signerEmail) {
        this.signerEmail = signerEmail;
    }
    
    public String getPkcs12Password() {
        return pkcs12Password;
    }
    
    public void setPkcs12Password(String pkcs12Password) {
        this.pkcs12Password = pkcs12Password;
    }
    
    public String getPkcs12Alias() {
        return pkcs12Alias;
    }
    
    public void setPkcs12Alias(String pkcs12Alias) {
        this.pkcs12Alias = pkcs12Alias;
    }
    
    /**
     * 获取当前月份的通用名称
     * 格式：microcredchina PDF Signer 202507
     */
    public String getCurrentCommonName() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return commonNameTemplate + " " + dateStr;
    }
    
    /**
     * 获取完整的主题DN字符串
     */
    public String getSubjectDN() {
        return String.format("C=%s,ST=%s,L=%s,O=%s,OU=%s,CN=%s,emailAddress=%s",
                country, state, locality, organization, organizationalUnit, 
                getCurrentCommonName(), emailAddress);
    }
    
    /**
     * 获取当前月份的证书文件名
     * 格式：microcredchina_pdf_signer_202507.crt
     */
    public String getCurrentCertFileName() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "microcredchina_pdf_signer_" + dateStr + ".crt";
    }
    
    /**
     * 获取当前月份的私钥文件名
     * 格式：microcredchina_pdf_signer_202507.key
     */
    public String getCurrentKeyFileName() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "microcredchina_pdf_signer_" + dateStr + ".key";
    }
    
    /**
     * 获取当前月份的CSR文件名
     * 格式：microcredchina_pdf_signer_202507.csr
     */
    public String getCurrentCSRFileName() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "microcredchina_pdf_signer_" + dateStr + ".csr";
    }
    
    /**
     * 获取当前月份的PKCS12文件名
     * 格式：microcredchina_pdf_signer_202507.p12
     */
    public String getCurrentPKCS12FileName() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "microcredchina_pdf_signer_" + dateStr + ".p12";
    }
}

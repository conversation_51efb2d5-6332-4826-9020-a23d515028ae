package tf.or.pdf.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PDF安全配置管理类
 */
@Component
@ConfigurationProperties(prefix = "pdf.security")
public class PDFSecurityConfig {
    
    private static final Logger log = LoggerFactory.getLogger(PDFSecurityConfig.class);

    // 配置参数存储（用于运行时更新）
    private final ConcurrentHashMap<String, String> runtimeParams = new ConcurrentHashMap<>();

    // 签名配置
    private SignatureConfig signature = new SignatureConfig();

    // 密钥配置
    private KeyConfig key = new KeyConfig();

    // 证书配置
    private CertConfig cert = new CertConfig();

    // 时间戳配置
    private TsaConfig tsa = new TsaConfig();

    // 缓存配置
    private CacheConfig cache = new CacheConfig();

    // 验证配置
    private ValidationConfig validation = new ValidationConfig();

    // 审计配置
    private AuditConfig audit = new AuditConfig();

    // 安全配置
    private SecurityConfig security = new SecurityConfig();
    
    @PostConstruct
    public void init() {
        log.info("初始化PDF安全配置管理器");

        // 记录配置加载完成
        logConfigurationSummary();
    }
    
    /**
     * 记录配置摘要
     */
    private void logConfigurationSummary() {
        log.info("PDF安全配置摘要:");
        log.info("  签名算法: {}", signature.getAlgorithm());
        log.info("  密钥长度: {}", key.getSize());
        log.info("  证书有效期: {} 天", cert.getValidityDays());
        log.info("  TSA超时: {} 毫秒", tsa.getTimeout());
        log.info("  缓存TTL: {} 秒", cache.getTtl());
        log.info("  审计启用: {}", audit.isEnabled());
        log.info("  严格验证: {}", validation.isStrict());
    }

    // Getter方法 - 保持向后兼容
    public String getSignatureAlgorithm() {
        return signature.getAlgorithm();
    }

    public int getKeySize() {
        return key.getSize();
    }

    public int getCertValidityDays() {
        return cert.getValidityDays();
    }

    public int getTsaTimeout() {
        return tsa.getTimeout();
    }

    public int getCacheTtl() {
        return cache.getTtl();
    }

    public boolean isAuditEnabled() {
        return audit.isEnabled();
    }

    public boolean isStrictValidation() {
        return validation.isStrict();
    }

    // 配置对象的Getter方法
    public SignatureConfig getSignature() {
        return signature;
    }

    public void setSignature(SignatureConfig signature) {
        this.signature = signature;
    }

    public KeyConfig getKey() {
        return key;
    }

    public void setKey(KeyConfig key) {
        this.key = key;
    }

    public CertConfig getCert() {
        return cert;
    }

    public void setCert(CertConfig cert) {
        this.cert = cert;
    }

    public TsaConfig getTsa() {
        return tsa;
    }

    public void setTsa(TsaConfig tsa) {
        this.tsa = tsa;
    }

    public CacheConfig getCache() {
        return cache;
    }

    public void setCache(CacheConfig cache) {
        this.cache = cache;
    }

    public ValidationConfig getValidation() {
        return validation;
    }

    public void setValidation(ValidationConfig validation) {
        this.validation = validation;
    }

    public AuditConfig getAudit() {
        return audit;
    }

    public void setAudit(AuditConfig audit) {
        this.audit = audit;
    }

    public SecurityConfig getSecurity() {
        return security;
    }

    public void setSecurity(SecurityConfig security) {
        this.security = security;
    }
    
    /**
     * 获取运行时配置参数
     *
     * @param key 配置键
     * @return 配置值
     */
    public String getRuntimeParam(String key) {
        return runtimeParams.get(key);
    }

    /**
     * 获取运行时配置参数（带默认值）
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public String getRuntimeParam(String key, String defaultValue) {
        return runtimeParams.getOrDefault(key, defaultValue);
    }

    /**
     * 设置运行时配置参数
     *
     * @param key 配置键
     * @param value 配置值
     */
    public void setRuntimeParam(String key, String value) {
        String oldValue = runtimeParams.put(key, value);
        log.info("运行时配置参数已更新: {} = {} (原值: {})", key, value, oldValue);
    }

    /**
     * 获取所有运行时配置参数
     *
     * @return 配置参数映射
     */
    public java.util.Map<String, String> getAllRuntimeParams() {
        return new java.util.HashMap<>(runtimeParams);
    }
    
    /**
     * 获取配置统计信息
     *
     * @return 统计信息
     */
    public ConfigStatistics getConfigStatistics() {
        ConfigStatistics stats = new ConfigStatistics();
        stats.setTotalParams(runtimeParams.size());
        stats.setAuditEnabled(audit.isEnabled());
        stats.setStrictValidation(validation.isStrict());
        stats.setSignatureAlgorithm(signature.getAlgorithm());
        stats.setKeySize(key.getSize());
        stats.setCertValidityDays(cert.getValidityDays());

        return stats;
    }

    /**
     * 配置统计信息类
     */
    public static class ConfigStatistics {
        private int totalParams;
        private boolean auditEnabled;
        private boolean strictValidation;
        private String signatureAlgorithm;
        private int keySize;
        private int certValidityDays;

        // Getters and Setters
        public int getTotalParams() { return totalParams; }
        public void setTotalParams(int totalParams) { this.totalParams = totalParams; }

        public boolean isAuditEnabled() { return auditEnabled; }
        public void setAuditEnabled(boolean auditEnabled) { this.auditEnabled = auditEnabled; }

        public boolean isStrictValidation() { return strictValidation; }
        public void setStrictValidation(boolean strictValidation) { this.strictValidation = strictValidation; }

        public String getSignatureAlgorithm() { return signatureAlgorithm; }
        public void setSignatureAlgorithm(String signatureAlgorithm) { this.signatureAlgorithm = signatureAlgorithm; }

        public int getKeySize() { return keySize; }
        public void setKeySize(int keySize) { this.keySize = keySize; }

        public int getCertValidityDays() { return certValidityDays; }
        public void setCertValidityDays(int certValidityDays) { this.certValidityDays = certValidityDays; }

        @Override
        public String toString() {
            return String.format("ConfigStats{params=%d, audit=%s, strict=%s, algorithm='%s', keySize=%d, validity=%d}",
                    totalParams, auditEnabled, strictValidation, signatureAlgorithm, keySize, certValidityDays);
        }
    }

    // 配置内部类定义

    /**
     * 签名配置
     */
    public static class SignatureConfig {
        private String algorithm = "SHA256withRSA";
        private String provider = "BC";

        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }

        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
    }

    /**
     * 密钥配置
     */
    public static class KeyConfig {
        private int size = 2048;
        private String algorithm = "RSA";

        public int getSize() { return size; }
        public void setSize(int size) { this.size = size; }

        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }
    }

    /**
     * 证书配置
     */
    public static class CertConfig {
        private int validityDays = 365;
        private String issuerName = "CN=PDF Security CA, O=Organization, C=CN";
        private String subjectTemplate = "CN=%s, O=Organization, C=CN";

        public int getValidityDays() { return validityDays; }
        public void setValidityDays(int validityDays) { this.validityDays = validityDays; }

        public String getIssuerName() { return issuerName; }
        public void setIssuerName(String issuerName) { this.issuerName = issuerName; }

        public String getSubjectTemplate() { return subjectTemplate; }
        public void setSubjectTemplate(String subjectTemplate) { this.subjectTemplate = subjectTemplate; }
    }

    /**
     * 时间戳配置
     */
    public static class TsaConfig {
        private int timeout = 30000;
        private int retryCount = 3;
        private int retryDelay = 1000;

        public int getTimeout() { return timeout; }
        public void setTimeout(int timeout) { this.timeout = timeout; }

        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }

        public int getRetryDelay() { return retryDelay; }
        public void setRetryDelay(int retryDelay) { this.retryDelay = retryDelay; }
    }

    /**
     * 缓存配置
     */
    public static class CacheConfig {
        private int ttl = 3600;
        private int maxSize = 1000;
        private boolean enabled = true;

        public int getTtl() { return ttl; }
        public void setTtl(int ttl) { this.ttl = ttl; }

        public int getMaxSize() { return maxSize; }
        public void setMaxSize(int maxSize) { this.maxSize = maxSize; }

        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
    }

    /**
     * 验证配置
     */
    public static class ValidationConfig {
        private boolean strict = false;
        private boolean checkRevocation = true;
        private boolean checkTimestamp = true;

        public boolean isStrict() { return strict; }
        public void setStrict(boolean strict) { this.strict = strict; }

        public boolean isCheckRevocation() { return checkRevocation; }
        public void setCheckRevocation(boolean checkRevocation) { this.checkRevocation = checkRevocation; }

        public boolean isCheckTimestamp() { return checkTimestamp; }
        public void setCheckTimestamp(boolean checkTimestamp) { this.checkTimestamp = checkTimestamp; }
    }

    /**
     * 审计配置
     */
    public static class AuditConfig {
        private boolean enabled = true;
        private String level = "INFO";
        private boolean performanceLoggingEnabled = true;

        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }

        public boolean isPerformanceLoggingEnabled() { return performanceLoggingEnabled; }
        public void setPerformanceLoggingEnabled(boolean performanceLoggingEnabled) { this.performanceLoggingEnabled = performanceLoggingEnabled; }
    }

    /**
     * 安全配置
     */
    public static class SecurityConfig {
        private String maxFileSize = "100MB";
        private String allowedExtensions = "pdf";
        private boolean scanEnabled = false;

        public String getMaxFileSize() { return maxFileSize; }
        public void setMaxFileSize(String maxFileSize) { this.maxFileSize = maxFileSize; }

        public String getAllowedExtensions() { return allowedExtensions; }
        public void setAllowedExtensions(String allowedExtensions) { this.allowedExtensions = allowedExtensions; }

        public boolean isScanEnabled() { return scanEnabled; }
        public void setScanEnabled(boolean scanEnabled) { this.scanEnabled = scanEnabled; }
    }
}

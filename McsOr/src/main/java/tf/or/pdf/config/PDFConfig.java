package tf.or.pdf.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PDF相关配置类
 * 使用Spring Boot的@ConfigurationProperties注解绑定application.yml配置
 */
@Component
@ConfigurationProperties(prefix = "pdf")
public class PDFConfig {
    
    /**
     * PDF文件基础路径
     */
    private String basePath = "/tmp/pdf/";

    /**
     * 根CA证书路径
     */
    private String rootCaCertPath;
    
    /**
     * 签名相关配置
     */
    private SignatureConfig signature = new SignatureConfig();
    
    /**
     * 文字水印相关配置
     */
    private TextWatermarkConfig textWatermark = new TextWatermarkConfig();

    /**
     * 图片水印相关配置
     */
    private ImageWatermarkConfig imageWatermark = new ImageWatermarkConfig();

    /**
     * 时间戳相关配置
     */
    private TimestampConfig timestamp = new TimestampConfig();
    
    // Getters and Setters
    public String getBasePath() {
        return basePath;
    }
    
    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }
    
    public String getRootCaCertPath() {
        return rootCaCertPath;
    }
    
    public void setRootCaCertPath(String rootCaCertPath) {
        this.rootCaCertPath = rootCaCertPath;
    }
    
    public SignatureConfig getSignature() {
        return signature;
    }
    
    public void setSignature(SignatureConfig signature) {
        this.signature = signature;
    }
    
    public TextWatermarkConfig getTextWatermark() {
        return textWatermark;
    }

    public void setTextWatermark(TextWatermarkConfig textWatermark) {
        this.textWatermark = textWatermark;
    }

    public ImageWatermarkConfig getImageWatermark() {
        return imageWatermark;
    }

    public void setImageWatermark(ImageWatermarkConfig imageWatermark) {
        this.imageWatermark = imageWatermark;
    }

    public TimestampConfig getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(TimestampConfig timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * 签名配置内部类
     */
    public static class SignatureConfig {
        private String keystorePath;
        private String keystorePassword = "microcredchina_pdf_signer";
        private String signerName = "美兴中国";
        private String location = "中国四川";
        private String reason = "合同签署";
        private String alias = "pdf.signer";
        
        // Getters and Setters
        public String getKeystorePath() {
            return keystorePath;
        }
        
        public void setKeystorePath(String keystorePath) {
            this.keystorePath = keystorePath;
        }
        
        public String getKeystorePassword() {
            return keystorePassword;
        }
        
        public void setKeystorePassword(String keystorePassword) {
            this.keystorePassword = keystorePassword;
        }
        
        public String getSignerName() {
            return signerName;
        }
        
        public void setSignerName(String signerName) {
            this.signerName = signerName;
        }
        
        public String getLocation() {
            return location;
        }
        
        public void setLocation(String location) {
            this.location = location;
        }
        
        public String getReason() {
            return reason;
        }
        
        public void setReason(String reason) {
            this.reason = reason;
        }
        
        public String getAlias() {
            return alias;
        }
        
        public void setAlias(String alias) {
            this.alias = alias;
        }
    }
    
    /**
     * 文字水印配置内部类
     */
    public static class TextWatermarkConfig {
        private String text = "美兴中国";
        private String fontPath;
        private int fontSize = 36;
        private float transparency = 0.3f;
        private float rotation = 45f;
        private int horizontalSpacing = 150;
        private int verticalSpacing = 150;

        // Getters and Setters
        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getFontPath() {
            return fontPath;
        }

        public void setFontPath(String fontPath) {
            this.fontPath = fontPath;
        }

        public int getFontSize() {
            return fontSize;
        }

        public void setFontSize(int fontSize) {
            this.fontSize = fontSize;
        }

        public float getTransparency() {
            return transparency;
        }

        public void setTransparency(float transparency) {
            this.transparency = transparency;
        }

        public float getRotation() {
            return rotation;
        }

        public void setRotation(float rotation) {
            this.rotation = rotation;
        }

        public int getHorizontalSpacing() {
            return horizontalSpacing;
        }

        public void setHorizontalSpacing(int horizontalSpacing) {
            this.horizontalSpacing = horizontalSpacing;
        }

        public int getVerticalSpacing() {
            return verticalSpacing;
        }

        public void setVerticalSpacing(int verticalSpacing) {
            this.verticalSpacing = verticalSpacing;
        }
    }

    /**
     * 图片水印配置内部类
     */
    public static class ImageWatermarkConfig {
        private String imagePath = "";
        private float imageScale = 0.5f;
        private float transparency = 0.3f;
        private float rotation = 0f;
        private int horizontalSpacing = 200;
        private int verticalSpacing = 200;

        // Getters and Setters
        public String getImagePath() {
            return imagePath;
        }

        public void setImagePath(String imagePath) {
            this.imagePath = imagePath;
        }

        public float getImageScale() {
            return imageScale;
        }

        public void setImageScale(float imageScale) {
            this.imageScale = imageScale;
        }

        public float getTransparency() {
            return transparency;
        }

        public void setTransparency(float transparency) {
            this.transparency = transparency;
        }

        public float getRotation() {
            return rotation;
        }

        public void setRotation(float rotation) {
            this.rotation = rotation;
        }

        public int getHorizontalSpacing() {
            return horizontalSpacing;
        }

        public void setHorizontalSpacing(int horizontalSpacing) {
            this.horizontalSpacing = horizontalSpacing;
        }

        public int getVerticalSpacing() {
            return verticalSpacing;
        }

        public void setVerticalSpacing(int verticalSpacing) {
            this.verticalSpacing = verticalSpacing;
        }
    }

    /**
     * 时间戳配置内部类
     */
    public static class TimestampConfig {
        private int timeout = SecurityConstants.DEFAULT_TSA_TIMEOUT;
        private int retryCount = SecurityConstants.DEFAULT_RETRY_COUNT;
        private int retryDelay = SecurityConstants.DEFAULT_RETRY_DELAY;

        // Getters and Setters
        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public int getRetryDelay() {
            return retryDelay;
        }

        public void setRetryDelay(int retryDelay) {
            this.retryDelay = retryDelay;
        }
    }
}

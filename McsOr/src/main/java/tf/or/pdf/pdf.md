PDF电子合同在法律层面的法律效力需要满足三个要素：
- 法院认可的签约主体，比如CFCA （非技术问题，自签CA法律不认）
- 法院认可的签署时间，比如CFCA可信时间戳TSA服务（非技术问题，第三方TSA法律不认）
- 法院认可签约后的文档未被篡改，数字签名技术本身（技术问题）

企业只能解决技术问题，要实现这三者的选择：
1. 直接调用CFCA接口签章
2. 购买CFCA的CA签发的企业证书 + CFCA的可信时间戳服务，这个方案同时会带来一些连带问题，比如证书吊销、秘钥管理、证书更新等问题

自签证书对PDF文档数字签名，哪怕是将CA证书哈希值和相关说明塞进PDF文档让CFCA进行数字签名，也只能证明这份PDF文档没有经过篡改，但不能证明自签CA的身份。这在法律层面有很大的认可度风险问题。

所以这个方案不是法律合规工具，更多的是定位为一套内部控制和审计工具，确保文档在传阅过程中不被随意篡改，而且通过“文字/图片水印”起到视觉警示和震慑作用，换句话说只有在企业内部有完全解释权的场景下才有用，对外不具备法律约束力。

实际需求；所以自签证书只需要做到公司自己能够心知肚明，验证客户是否造假了，换句话说只需要自签CA、使用第三方TSA服务、对文字/图片水印的PDF文档进行数字签名和验证即可。

目前的工作进度：
1 已经实现了文字/图片水印添加、数字签名、验签功能，代码文件 pdf_simple 目录中
2 利用AI在pdf目录中对pdf_simple进行了优化，但是太复杂了，
- 我仔细审查了你的certificate和security文件夹以及相关的单测，主要是翻译的openssl相关命令也就是跟秘钥跟证书相关的逻辑，还不错能满足要求
- revocattion文件夹证书吊销还是太复杂了直接删除吧，考虑每个月生成一个用户证书，如果当前月证书没生成则自动根据上个月的证书自动生成，证书文件名就以yyyyMM格式结尾。
- 核心core和validation竟然还有空实现比如performTrustChainCheck，我之前的main方法的runTests方法挪到了PDFServiceTest单测里面，竟然还报错2025-07-28 11:10:21.906 [ERROR] [main] tf.or.pdf.core.PDFSignatureValidator:235 - 签名者信息验证失败org.bouncycastle.cms.CMSSignerDigestMismatchException: message-digest attribute value does not match calculated value
at org.bouncycastle.cms.SignerInformation.doVerify(Unknown Source)
at org.bouncycastle.cms.SignerInformation.verify(Unknown Source)
at tf.or.pdf.core.PDFSignatureValidator.verifySignerInformation(PDFSignatureValidator.java:216)
at tf.or.pdf.core.PDFSignatureValidator.verifyCMSSignature(PDFSignatureValidator.java:186)
at tf.or.pdf.core.PDFSignatureValidator.verifyIndividualSignature(PDFSignatureValidator.java:145)
at tf.or.pdf.core.PDFSignatureValidator.verifyPDFSignature(PDFSignatureValidator.java:92)
at tf.or.pdf.core.PDFSignatureValidator.verifyPDFSignature(PDFSignatureValidator.java:61)
at tf.or.pdf.PDFService.verifyPDFSignature(PDFService.java:126)

请重新review所有代码配置，将所有配置类挪到config文件夹进行统一管理，清除所有缓存逻辑，修复所有bug，完成未完成的逻辑，能简单实现的就简单实现，移除所有跟吊销相关的逻辑和文件，这绝不是一个简单的任务，相当于简化重构了



接下来请帮我：
- 由于项目已经进行了很大一部分，请在完全理解项目源码逻辑的基础上进行改造
- 简化重构 pdf 中复杂的代码逻辑，移除所有缓存相关实现，简化一切能简化的逻辑，遵守基本的规则，比如遵守职责单一原则，但是要保证不要有空实现和空方法，还有移除所有跟吊销相关的逻辑和文件，还有不要逻辑保持简单清爽 [CryptoUtils.java](utils/CryptoUtils.java) 和 [KeyProtection.java](security/KeyProtection.java) 关于RSA很多相似方法，移除所有统计相关的逻辑，将所有网络请求都替换成简单的RestTemplate请求
- review所有代码实现逻辑，着重在性能和PDF签署和验签的安全性方面进行优化
- 由于存在对用户证书的自动续签问题，当前 certificate 也已实现，
- 其他可能未考虑到的问题
- 注意不要过度设计，代码要优雅精巧
- 为了及时解决编译问题，请执行 cd ~/IdeaProjects/meixing/mxmcs/mxmcs && mvn compile -pl McsOr


声明：当前的工作仅发生在当前pdf文件夹内

基于以下依赖，代码实现请尊重当前依赖版本，不要使用不存在的API
```xml
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.67</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>1.67</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.34</version>
		</dependency>

```

参考如下证书生成命令


## 生成CA证书

```sh
# 生成根证书 有效期 100年
openssl req -new -x509 -newkey rsa:2048 -keyout microcredchina_ca.key -out microcredchina_ca.crt -subj "/C=CN/ST=SiChuan/L=ChengDu/O=microcredchina/OU=microcredchina/CN=microcredchina CA/emailAddress=<EMAIL>" -days 36500 -nodes

# 查看根证书
openssl x509 -in microcredchina_ca.crt -text -noout

```


## 循环生成用户证书

```sh
# 生成用户证书请求 有效期1年
openssl req -new -newkey rsa:2048 -keyout microcredchina_pdf_signer_20250723.key -out microcredchina_pdf_signer_20250723.csr -subj "/C=CN/ST=SiChuan/L=ChengDu/O=microcredchina/OU=microcredchina/CN=microcredchina PDF Signer 20250723/emailAddress=<EMAIL>" -days 365 -nodes

# 查看证书请求
openssl req -in microcredchina_pdf_signer_20250723.csr -text -noout

# 签发用户证书
openssl x509 -req \
  -in microcredchina_pdf_signer_20250723.csr \
  -CA microcredchina_ca.crt \
  -CAkey microcredchina_ca.key \
  -CAcreateserial \
  -out microcredchina_pdf_signer_20250723.crt \
  -days 365 -sha256 \
  -extfile <(cat <<EOF
basicConstraints = CA:FALSE
keyUsage = digitalSignature, nonRepudiation
extendedKeyUsage = *******.4.1.311.10.3.12, codeSigning
subjectAltName = @alt_names

[alt_names]
email.1 = <EMAIL>
EOF
)


# 查看用户证书
openssl x509 -in microcredchina_pdf_signer_20250723.crt -text -noout

# 验证用户证书
openssl verify -CAfile microcredchina_ca.crt microcredchina_pdf_signer_20250723.crt

# 将用户证书与私钥打包为 PKCS12 格式
openssl pkcs12 -export -inkey microcredchina_pdf_signer_20250723.key -in microcredchina_pdf_signer_20250723.crt -certfile microcredchina_ca.crt -out microcredchina_pdf_signer_20250723.p12 -name "pdf.signer" -passout pass:microcredchina_pdf_signer

```
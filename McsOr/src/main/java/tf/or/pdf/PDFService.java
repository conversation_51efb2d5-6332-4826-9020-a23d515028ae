package tf.or.pdf;

import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.core.PDFSignatureProcessor;
import tf.or.pdf.core.PDFSignatureValidator;
import tf.or.pdf.core.PDFWatermarkProcessor;
import tf.or.pdf.core.SignatureVerificationResult;

import java.security.Security;

/**
 * PDF工具类 - 支持加水印、数字签署、验签功能
 * 基于Apache PDFBox 2.0.34
 * 使用Spring Boot配置管理，支持外部配置
 */
@Component
public class PDFService {

    private static final Logger log = LoggerFactory.getLogger(PDFService.class);

    @Autowired
    private PDFWatermarkProcessor watermarkProcessor;

    @Autowired
    private PDFSignatureProcessor signatureProcessor;

    @Autowired
    private PDFSignatureValidator signatureValidator;

    @Autowired
    private tf.or.pdf.core.stream.PDFProcessingPipeline processingPipeline;

    @Autowired
    private tf.or.pdf.core.stream.PDFStreamProcessor streamProcessor;


    /**
     * 为PDF添加文字水印
     *
     * @param inputPath     输入PDF路径
     * @param outputPath    输出PDF路径
     * @param watermarkText 水印文字内容
     * @param horizontalSpacing    水印横向间距
     * @param verticalSpacing      水印纵向间距
     * @param rotation      水印倾斜角度，单位度 (推荐值: 45)
     * @param transparency  透明度 0.0-1.0 (推荐值: 0.3)
     */
    public void addTextWatermark(String inputPath, String outputPath,
                                        String watermarkText, int horizontalSpacing, int verticalSpacing, float fontSize,
                                        float rotation, float transparency) throws Exception {

        log.info("使用水印处理器添加文字水印");
        watermarkProcessor.addTextWatermark(inputPath, outputPath, watermarkText,
                horizontalSpacing, verticalSpacing, fontSize, rotation, transparency);

    }

    /**
     * 为PDF添加图片水印
     *
     * @param inputPath    输入PDF路径
     * @param outputPath   输出PDF路径
     * @param imagePath    水印图片路径
     * @param horizontalSpacing    水印横向间距
     * @param verticalSpacing      水印纵向间距
     * @param imageScale   图片缩放比例
     * @param rotation     水印倾斜角度，单位度 (推荐值: 0)
     * @param transparency 透明度 0.0-1.0 (推荐值: 0.5)
     */
    public void addImageWatermark(String inputPath, String outputPath,
                                         String imagePath, int horizontalSpacing, int verticalSpacing, float imageScale,
                                         float rotation, float transparency) throws Exception {

        log.info("使用水印处理器添加图片水印");
        watermarkProcessor.addImageWatermark(inputPath, outputPath, imagePath,
                horizontalSpacing, verticalSpacing, imageScale, rotation, transparency);

    }

    /**
     * 对PDF进行数字签署
     *
     * @param inputPath        输入PDF路径
     * @param outputPath       输出PDF路径
     * @param signName         签署名称
     * @param signLocation     签署位置
     * @param signReason       签署原因
     * @param keystorePath     证书文件路径 (.p12)
     * @param keystorePassword 证书密码
     * @param keystoreAlias    证书别名
     */
    public void signPDF(String inputPath, String outputPath,
                               String signName, String signLocation, String signReason,
                               String keystorePath, String keystorePassword, String keystoreAlias) throws Exception {

        log.info("使用签名处理器进行PDF数字签名");
        signatureProcessor.signPDF(inputPath, outputPath, signName, signLocation, signReason,
                                  keystorePath, keystorePassword, keystoreAlias);
    }


    /**
     * 验证PDF数字签名
     *
     * @param pdfPath PDF文件路径
     * @return 验证结果
     */
    public SignatureVerificationResult verifyPDFSignature(String pdfPath) {
        log.info("使用签名验证器验证PDF数字签名");
        return signatureValidator.verifyPDFSignature(pdfPath);
    }
}


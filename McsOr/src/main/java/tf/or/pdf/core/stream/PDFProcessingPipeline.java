package tf.or.pdf.core.stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.config.PDFConfig;
import tf.or.pdf.core.SignatureVerificationResult;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF处理管道
 * 支持连续的PDF处理操作：加水印 -> 签名 -> 验签
 * 使用Builder模式构建处理链，支持流式操作
 */
@Component
public class PDFProcessingPipeline {

    private static final Logger log = LoggerFactory.getLogger(PDFProcessingPipeline.class);

    @Autowired
    private PDFStreamProcessor streamProcessor;

    @Autowired
    private PDFConfig pdfConfig;

    /**
     * 创建处理管道构建器
     *
     * @param inputStream 输入PDF流
     * @return 管道构建器
     */
    public PipelineBuilder fromStream(InputStream inputStream) {
        return new PipelineBuilder(inputStream);
    }

    /**
     * 创建处理管道构建器
     *
     * @param inputPath 输入PDF文件路径
     * @return 管道构建器
     * @throws FileNotFoundException 文件不存在时抛出异常
     */
    public PipelineBuilder fromFile(String inputPath) throws FileNotFoundException {
        return new PipelineBuilder(new FileInputStream(inputPath));
    }

    /**
     * 管道构建器类
     * 使用Builder模式构建PDF处理链
     */
    public class PipelineBuilder {
        private InputStream inputStream;
        private final List<ProcessingStep> steps = new ArrayList<>();
        private boolean verifyAtEnd = false;

        public PipelineBuilder(InputStream inputStream) {
            this.inputStream = inputStream;
        }

        /**
         * 添加文字水印步骤
         *
         * @param watermarkText     水印文字
         * @param horizontalSpacing 横向间距
         * @param verticalSpacing   纵向间距
         * @param fontSize          字体大小
         * @param rotation          旋转角度
         * @param transparency      透明度
         * @return 构建器实例
         */
        public PipelineBuilder addTextWatermark(String watermarkText, int horizontalSpacing, 
                                               int verticalSpacing, float fontSize, 
                                               float rotation, float transparency) {
            steps.add(new ProcessingStep("TEXT_WATERMARK", (input, output) -> {
                streamProcessor.addTextWatermark(input, output, watermarkText, 
                    horizontalSpacing, verticalSpacing, fontSize, rotation, transparency);
                return null;
            }));
            return this;
        }

        /**
         * 使用配置文件设置添加文字水印
         *
         * @param watermarkText 自定义水印文字（可选，为null时使用配置文件中的文字）
         * @return 构建器实例
         */
        public PipelineBuilder addTextWatermark(String watermarkText) {
            PDFConfig.TextWatermarkConfig config = pdfConfig.getTextWatermark();
            String text = watermarkText != null ? watermarkText : config.getText();
            
            return addTextWatermark(text, config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getFontSize(), config.getRotation(), config.getTransparency());
        }

        /**
         * 使用配置文件设置添加文字水印
         *
         * @return 构建器实例
         */
        public PipelineBuilder addTextWatermark() {
            return addTextWatermark(null);
        }

        /**
         * 添加图片水印步骤
         *
         * @param imagePath         水印图片路径
         * @param horizontalSpacing 横向间距
         * @param verticalSpacing   纵向间距
         * @param imageScale        图片缩放比例
         * @param rotation          旋转角度
         * @param transparency      透明度
         * @return 构建器实例
         */
        public PipelineBuilder addImageWatermark(String imagePath, int horizontalSpacing, 
                                                int verticalSpacing, float imageScale, 
                                                float rotation, float transparency) {
            steps.add(new ProcessingStep("IMAGE_WATERMARK", (input, output) -> {
                streamProcessor.addImageWatermark(input, output, imagePath, 
                    horizontalSpacing, verticalSpacing, imageScale, rotation, transparency);
                return null;
            }));
            return this;
        }

        /**
         * 使用配置文件设置添加图片水印
         *
         * @param imagePath 自定义图片路径（可选，为null时使用配置文件中的路径）
         * @return 构建器实例
         */
        public PipelineBuilder addImageWatermark(String imagePath) {
            PDFConfig.ImageWatermarkConfig config = pdfConfig.getImageWatermark();
            String path = imagePath != null ? imagePath : config.getImagePath();
            
            return addImageWatermark(path, config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getImageScale(), config.getRotation(), config.getTransparency());
        }

        /**
         * 使用配置文件设置添加图片水印
         *
         * @return 构建器实例
         */
        public PipelineBuilder addImageWatermark() {
            return addImageWatermark(null);
        }

        /**
         * 添加数字签名步骤
         *
         * @param signName         签署人姓名
         * @param signLocation     签署地点
         * @param signReason       签署原因
         * @param keystorePath     证书文件路径
         * @param keystorePassword 证书密码
         * @param keystoreAlias    证书别名
         * @return 构建器实例
         */
        public PipelineBuilder addSignature(String signName, String signLocation, String signReason,
                                           String keystorePath, String keystorePassword, String keystoreAlias) {
            steps.add(new ProcessingStep("SIGNATURE", (input, output) -> {
                streamProcessor.signPDF(input, output, signName, signLocation, signReason,
                    keystorePath, keystorePassword, keystoreAlias);
                return null;
            }));
            return this;
        }

        /**
         * 使用月度证书添加数字签名步骤
         *
         * @param signName     签署人姓名
         * @param signLocation 签署地点
         * @param signReason   签署原因
         * @return 构建器实例
         */
        public PipelineBuilder addMonthlySignature(String signName, String signLocation, String signReason) {
            steps.add(new ProcessingStep("MONTHLY_SIGNATURE", (input, output) -> {
                streamProcessor.signPDFWithMonthlyKey(input, output, signName, signLocation, signReason);
                return null;
            }));
            return this;
        }

        /**
         * 使用配置文件设置添加月度证书签名
         *
         * @return 构建器实例
         */
        public PipelineBuilder addMonthlySignature() {
            PDFConfig.SignatureConfig config = pdfConfig.getSignature();
            return addMonthlySignature(config.getSignerName(), config.getLocation(), config.getReason());
        }

        /**
         * 在处理链末尾添加签名验证
         *
         * @return 构建器实例
         */
        public PipelineBuilder verifySignature() {
            this.verifyAtEnd = true;
            return this;
        }

        /**
         * 执行处理管道并输出到流
         *
         * @param outputStream 输出流
         * @return 处理结果（包含验证结果，如果启用了验证）
         * @throws Exception 处理失败时抛出异常
         */
        public ProcessingResult executeToStream(OutputStream outputStream) throws Exception {
            log.info("开始执行PDF处理管道，共{}个步骤", steps.size());

            InputStream currentInput = inputStream;
            ByteArrayOutputStream tempOutput = null;
            SignatureVerificationResult verificationResult = null;

            try {
                // 执行所有处理步骤
                for (int i = 0; i < steps.size(); i++) {
                    ProcessingStep step = steps.get(i);
                    log.debug("执行步骤 {}: {}", i + 1, step.getName());

                    // 如果不是最后一步，使用临时输出流
                    OutputStream currentOutput;
                    if (i == steps.size() - 1) {
                        currentOutput = outputStream;
                    } else {
                        tempOutput = new ByteArrayOutputStream();
                        currentOutput = tempOutput;
                    }

                    // 执行处理步骤
                    step.getProcessor().process(currentInput, currentOutput);

                    // 如果不是最后一步，准备下一步的输入
                    if (i < steps.size() - 1) {
                        if (currentInput != inputStream) {
                            currentInput.close();
                        }
                        currentInput = new ByteArrayInputStream(tempOutput.toByteArray());
                        tempOutput.close();
                        tempOutput = null;
                    }
                }

                // 如果启用了验证，进行签名验证
                if (verifyAtEnd) {
                    log.info("执行最终签名验证");
                    
                    // 读取输出流内容进行验证
                    if (outputStream instanceof ByteArrayOutputStream) {
                        byte[] pdfBytes = ((ByteArrayOutputStream) outputStream).toByteArray();
                        verificationResult = streamProcessor.verifyPDFSignature(pdfBytes);
                    } else {
                        log.warn("无法对非ByteArrayOutputStream进行验证，跳过验证步骤");
                    }
                }

                log.info("PDF处理管道执行完成");
                return new ProcessingResult(true, "处理成功", verificationResult);

            } catch (Exception e) {
                log.error("PDF处理管道执行失败", e);
                throw new Exception("PDF处理管道执行失败: " + e.getMessage(), e);
            } finally {
                // 清理资源
                if (currentInput != inputStream && currentInput != null) {
                    try {
                        currentInput.close();
                    } catch (IOException e) {
                        log.warn("关闭临时输入流失败", e);
                    }
                }
                if (tempOutput != null) {
                    try {
                        tempOutput.close();
                    } catch (IOException e) {
                        log.warn("关闭临时输出流失败", e);
                    }
                }
            }
        }

        /**
         * 执行处理管道并输出到文件
         *
         * @param outputPath 输出文件路径
         * @return 处理结果
         * @throws Exception 处理失败时抛出异常
         */
        public ProcessingResult executeToFile(String outputPath) throws Exception {
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                return executeToStream(fos);
            }
        }

        /**
         * 执行处理管道并返回字节数组
         *
         * @return 处理结果（包含PDF字节数组）
         * @throws Exception 处理失败时抛出异常
         */
        public ProcessingResult executeToBytes() throws Exception {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ProcessingResult result = executeToStream(baos);
            result.setPdfBytes(baos.toByteArray());
            return result;
        }
    }

    /**
     * 处理步骤内部类
     */
    private static class ProcessingStep {
        private final String name;
        private final StreamProcessor processor;

        public ProcessingStep(String name, StreamProcessor processor) {
            this.name = name;
            this.processor = processor;
        }

        public String getName() {
            return name;
        }

        public StreamProcessor getProcessor() {
            return processor;
        }
    }

    /**
     * 流处理器接口
     */
    @FunctionalInterface
    private interface StreamProcessor {
        Void process(InputStream input, OutputStream output) throws Exception;
    }

    /**
     * 处理结果类
     */
    public static class ProcessingResult {
        private final boolean success;
        private final String message;
        private final SignatureVerificationResult verificationResult;
        private byte[] pdfBytes;

        public ProcessingResult(boolean success, String message, SignatureVerificationResult verificationResult) {
            this.success = success;
            this.message = message;
            this.verificationResult = verificationResult;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public SignatureVerificationResult getVerificationResult() { return verificationResult; }
        public byte[] getPdfBytes() { return pdfBytes; }
        public void setPdfBytes(byte[] pdfBytes) { this.pdfBytes = pdfBytes; }
    }
}

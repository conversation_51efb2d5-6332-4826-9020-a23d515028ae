package tf.or.pdf.core;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.PDSignature;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.cms.Attribute;
import org.bouncycastle.asn1.cms.AttributeTable;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cms.*;
import org.bouncycastle.cms.CMSProcessableByteArray;
import org.bouncycastle.cms.jcajce.JcaSimpleSignerInfoVerifierBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.tsp.TimeStampToken;
import org.bouncycastle.tsp.TimeStampTokenInfo;
import org.bouncycastle.util.Store;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * PDF签名验证器
 */
@Component
public class PDFSignatureValidator {

    private static final Logger log = LoggerFactory.getLogger(PDFSignatureValidator.class);


    static {
        // 确保BouncyCastle Provider可用
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 验证PDF数字签名
     *
     * @param pdfPath PDF文件路径
     * @return 验证结果
     */
    public SignatureVerificationResult verifyPDFSignature(String pdfPath) {
        log.info("开始验证PDF数字签名：{}", pdfPath);

        try {
            // 读取PDF文件
            byte[] pdfBytes = Files.readAllBytes(Paths.get(pdfPath));
            return verifyPDFSignature(pdfBytes);

        } catch (Exception e) {
            log.error("读取PDF文件失败: {}", pdfPath, e);
            return new SignatureVerificationResult(false, "读取PDF文件失败: " + e.getMessage());
        }
    }

    /**
     * 验证PDF数字签名
     *
     * @param pdfBytes PDF文件字节数组
     * @return 验证结果
     */
    public SignatureVerificationResult verifyPDFSignature(byte[] pdfBytes) {
        // 1. 加载PDF文档
        try (PDDocument document = PDDocument.load(pdfBytes)) {

            // 2. 获取签名字典
            List<PDSignature> signatures = document.getSignatureDictionaries();
            if (signatures.isEmpty()) {
                return new SignatureVerificationResult(false, "PDF文档未包含数字签名");
            }

            boolean allValid = true;
            StringBuilder errorMessages = new StringBuilder();

            // 3. 验证PDF完整性
            SignatureVerificationResult integrityResult = verifyPDFIntegrity(pdfBytes, signatures);
            if (!integrityResult.isValid()) {
                allValid = false;
                if (errorMessages.length() > 0) {
                    errorMessages.append("; ");
                }
                errorMessages.append(integrityResult.getMessage());
            }

            // 4. 验证每个签名
            List<SignatureInfo> signatureInfos = new ArrayList<>();
            for (PDSignature signature : signatures) {
                SignatureInfo signatureInfo = verifyIndividualSignature(pdfBytes, signature);
                signatureInfos.add(signatureInfo);

                if (!signatureInfo.isValid()) {
                    allValid = false;
                    if (errorMessages.length() > 0) {
                        errorMessages.append("; ");
                    }
                    errorMessages.append(signatureInfo.getErrorMessage());
                }
            }

            // 5. 构建最终结果
            String message = allValid ? "所有签名验证通过" : errorMessages.toString();
            SignatureVerificationResult result = new SignatureVerificationResult(allValid, message);
            result.setSignatureInfos(signatureInfos);
            result.setSignatureCount(signatures.size());

            log.info("PDF签名验证完成，结果: {}, 签名数量: {}", allValid ? "成功" : "失败", signatures.size());
            return result;
        } catch (Exception e) {
            log.error("PDF签名验证失败", e);
            return new SignatureVerificationResult(false, "签名验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证单个签名
     */
    private SignatureInfo verifyIndividualSignature(byte[] pdfBytes, PDSignature signature) {
        try {
            log.debug("验证签名: {}", signature.getName());

            // 1. 获取签名内容
            byte[] signatureContent = signature.getContents(pdfBytes);
            byte[] signedContent = signature.getSignedContent(pdfBytes);

            // 2. 解析CMS签名数据
            CMSProcessable cmsContent = new CMSProcessableByteArray(signedContent);
            CMSSignedData cmsSignedData = new CMSSignedData(cmsContent, signatureContent);

            // 3. 验证CMS签名
            SignatureInfo cmsResult = verifyCMSSignature(cmsSignedData, signature);
            if (!cmsResult.isValid()) {
                return cmsResult;
            }

            // 4. 验证时间戳（如果存在）
            SignatureInfo timestampResult = verifyTimestamp(cmsSignedData);
            if (!timestampResult.isValid()) {
                return timestampResult;
            }

            log.debug("签名验证成功: {}", signature.getName());
            return cmsResult;

        } catch (Exception e) {
            log.error("验证单个签名失败", e);
            return new SignatureInfo(false, "签名验证失败: " + e.getMessage(), null, signature.getSignDate());
        }
    }

    /**
     * 验证CMS签名数据
     */
    private SignatureInfo verifyCMSSignature(CMSSignedData cmsSignedData, PDSignature signature) {
        try {
            Store<X509CertificateHolder> certStore = cmsSignedData.getCertificates();
            SignerInformationStore signers = cmsSignedData.getSignerInfos();

            for (SignerInformation signer : signers) {
                return verifySignerInformation(signer, certStore, signature);
            }

            return new SignatureInfo(false, "未找到签名者信息", null, signature.getSignDate());

        } catch (Exception e) {
            log.error("CMS签名验证失败", e);
            return new SignatureInfo(false, "CMS签名验证失败: " + e.getMessage(), null, signature.getSignDate());
        }
    }

    /**
     * 验证签名者信息
     */
    private SignatureInfo verifySignerInformation(SignerInformation signer,
                                                  Store<X509CertificateHolder> certStore,
                                                  PDSignature signature) {
        try {
            // 获取签名者证书
            Collection<X509CertificateHolder> certCollection = certStore.getMatches(signer.getSID());
            if (certCollection.isEmpty()) {
                return new SignatureInfo(false, "未找到签名者证书", null, signature.getSignDate());
            }

            X509CertificateHolder certHolder = certCollection.iterator().next();
            JcaX509CertificateConverter converter = new JcaX509CertificateConverter()
                    .setProvider(BouncyCastleProvider.PROVIDER_NAME);
            X509Certificate certificate = converter.getCertificate(certHolder);

            // 验证签名
            boolean signatureValid = signer.verify(new JcaSimpleSignerInfoVerifierBuilder()
                    .setProvider(BouncyCastleProvider.PROVIDER_NAME)
                    .build(certHolder));

            if (!signatureValid) {
                return new SignatureInfo(false, "数字签名验证失败", certificate, signature.getSignDate());
            }

            // 验证证书有效期
            try {
                certificate.checkValidity(signature.getSignDate().getTime());
            } catch (Exception e) {
                return new SignatureInfo(false, "签名时证书已过期或未生效", certificate, signature.getSignDate());
            }

            log.debug("签名者信息验证成功: {}", certificate.getSubjectDN());
            return new SignatureInfo(true, "签名验证成功", certificate, signature.getSignDate());

        } catch (Exception e) {
            log.error("签名者信息验证失败", e);
            return new SignatureInfo(false, "签名者信息验证失败: " + e.getMessage(), null, signature.getSignDate());
        }
    }

    /**
     * 验证PDF完整性
     */
    private SignatureVerificationResult verifyPDFIntegrity(byte[] pdfBytes, List<PDSignature> signatures) {
        try {
            // 1. 检查PDF结构完整性
            if (!isPDFStructureIntact(pdfBytes)) {
                return new SignatureVerificationResult(false, "PDF文档结构已被破坏");
            }

            // 2. 检查签名覆盖范围
            for (PDSignature signature : signatures) {
                if (!isSignatureCoverageComplete(pdfBytes, signature)) {
                    return new SignatureVerificationResult(false, "签名覆盖范围不完整，文档可能被篡改");
                }
            }

            log.debug("PDF完整性验证通过");
            return new SignatureVerificationResult(true, "PDF完整性验证通过");
        } catch (Exception e) {
            log.error("PDF完整性验证失败", e);
            return new SignatureVerificationResult(false, "PDF完整性验证失败: " + e.getMessage());
        }
    }

    /**
     * 检查PDF结构完整性
     */
    private boolean isPDFStructureIntact(byte[] pdfBytes) {
        try (PDDocument document = PDDocument.load(pdfBytes)) {
            // 基本结构检查
            return document.getNumberOfPages() > 0 &&
                    document.getDocumentCatalog() != null;
        } catch (Exception e) {
            log.warn("PDF结构检查失败", e);
            return false;
        }
    }

    /**
     * 检查签名覆盖范围是否完整
     */
    private boolean isSignatureCoverageComplete(byte[] pdfBytes, PDSignature signature) {
        try {
            int[] byteRange = signature.getByteRange();
            if (byteRange == null || byteRange.length != 4) {
                return false;
            }

            // 检查签名覆盖的字节范围是否合理
            int totalCoverage = byteRange[1] + byteRange[3];
            return totalCoverage >= pdfBytes.length * 0.9; // 至少覆盖90%的内容

        } catch (Exception e) {
            log.warn("签名覆盖范围检查失败", e);
            return false;
        }
    }

    /**
     * 验证时间戳
     */
    private SignatureInfo verifyTimestamp(CMSSignedData cmsSignedData) {
        try {
            SignerInformationStore signers = cmsSignedData.getSignerInfos();
            if (signers == null || signers.getSigners().isEmpty()) {
                return new SignatureInfo(false, "时间戳验证失败: 未找到签名者信息", null, null);
            }

            for (SignerInformation signer : signers) {
                AttributeTable unsignedAttributes = signer.getUnsignedAttributes();
                if (unsignedAttributes == null) {
                    return new SignatureInfo(false, "时间戳验证失败: 未找到时间戳", null, null);
                }
                Attribute timeStampAttribute = unsignedAttributes.get(new ASN1ObjectIdentifier("1.2.840.113549.1.9.16.2.14"));
                if (timeStampAttribute == null) {
                    return new SignatureInfo(false, "时间戳验证失败: 未找到时间戳", null, null);
                }
                // 验证时间戳
                byte[] timestampBytes = timeStampAttribute.getAttrValues().getObjectAt(0).toASN1Primitive().getEncoded();
                // 解析时间戳令牌
                TimeStampToken timeStampToken = new TimeStampToken(new CMSSignedData(timestampBytes));

                // 验证时间戳的完整性
                Collection<X509CertificateHolder> tsaCerts = timeStampToken.getCertificates().getMatches(null);
                if (tsaCerts.isEmpty()) {
                    return new SignatureInfo(false, "时间戳验证失败: 时间戳证书缺失", null, null);
                }

                X509Certificate tsaCert = new JcaX509CertificateConverter()
                        .setProvider(BouncyCastleProvider.PROVIDER_NAME)
                        .getCertificate(tsaCerts.iterator().next());

                SignerInformationVerifier verifier = new JcaSimpleSignerInfoVerifierBuilder()
                        .setProvider("BC")
                        .build(tsaCert);

                // 验证时间戳签名
                timeStampToken.validate(verifier);

                // 验证时间戳与原始签名的关系
                TimeStampTokenInfo tsInfo = timeStampToken.getTimeStampInfo();
                byte[] expectedImprint = MessageDigest.getInstance("SHA-256").digest(signer.getSignature());
                if (!Arrays.equals(tsInfo.getMessageImprintDigest(), expectedImprint)) {
                    return new SignatureInfo(false, "时间戳验证失败: 时间戳消息摘要不匹配", null, null);
                }
            }
            return new SignatureInfo(true, "时间戳验证成功", null, null);

        } catch (Exception e) {
            log.warn("时间戳验证失败", e);
            return new SignatureInfo(false, "时间戳验证失败: " + e.getMessage(), null, null);
        }
    }
}

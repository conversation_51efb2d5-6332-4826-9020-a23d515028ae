package tf.or.pdf.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.Store;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.config.PDFSecurityConfig;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.*;
import java.security.cert.Certificate;
import java.security.interfaces.RSAPublicKey;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 增强的证书验证机制 - 确保只有可信证书才能通过验证
 */
@Component
public class CertificateValidator {
    
    private static final Logger log = LoggerFactory.getLogger(CertificateValidator.class);
    private static KeyStore trustStore;
    
    @Autowired
    private PDFSecurityConfig securityConfig;
    
    /**
     * 全面验证签名证书
     */
    public CertificateValidationResult validateSigningCertificate(
            X509Certificate signerCert, 
            Store<X509CertificateHolder> certStore,
            Date signDate) {
        
        try {
            String rootCaCertPath = securityConfig.getValidation().getRootCaCertPath();
            loadTrustStore(rootCaCertPath);

            // 1. 验证证书基本信息
            validateBasicCertificate(signerCert, signDate);
            
            // 2. 验证证书链
            validateCertificateChain(certStore, signerCert);
            
            // 3. 验证证书用途
            validateCertificateUsage(signerCert);
            
            // 4. 验证颁发者信任
            validateIssuerTrust(signerCert);
            
            log.info("证书验证通过 - 主题: {}", signerCert.getSubjectX500Principal().getName());
            return new CertificateValidationResult(true, "证书验证成功");
            
        } catch (Exception e) {
            log.error("证书验证失败", e);
            return new CertificateValidationResult(false, "证书验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证私钥与公钥配对关系
     */
    private static void validateKeyPair(PublicKey publicKey, PrivateKey privateKey) throws Exception {
        // 创建测试数据
        byte[] testData = "CERTIFICATE_KEY_PAIR_VALIDATION_TEST".getBytes(StandardCharsets.UTF_8);
        
        // 使用私钥签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(testData);
        byte[] signatureBytes = signature.sign();
        
        // 使用公钥验证
        signature.initVerify(publicKey);
        signature.update(testData);
        boolean verified = signature.verify(signatureBytes);
        
        if (!verified) {
            throw new SecurityException("私钥与证书公钥不匹配！可能存在证书伪造风险");
        }
        
        log.info("私钥与公钥配对验证通过");
    }
    
    /**
     * 验证证书基本信息
     */
    private static void validateBasicCertificate(X509Certificate cert, Date signDate) throws Exception {
        // 检查证书有效期
        cert.checkValidity(signDate);
        
        // 检查证书版本（应该是X.509 v3）
        if (cert.getVersion() < 3) {
            throw new CertificateException("证书版本过低，建议使用X.509 v3证书");
        }
        
        // 检查证书算法强度
        String sigAlgName = cert.getSigAlgName();
        if (sigAlgName.contains("MD5") || sigAlgName.contains("SHA1")) {
            log.warn("证书使用弱哈希算法: {}", sigAlgName);
            throw new CertificateException("证书使用不安全的哈希算法: " + sigAlgName);
        }
        
        // 检查密钥长度
        if (cert.getPublicKey() instanceof RSAPublicKey) {
            RSAPublicKey rsaKey = (RSAPublicKey) cert.getPublicKey();
            int keyLength = rsaKey.getModulus().bitLength();
            if (keyLength < 2048) {
                throw new CertificateException("RSA密钥长度不足2048位: " + keyLength);
            }
        }
    }
    
    /**
     * 验证证书用途
     */
    private static void validateCertificateUsage(X509Certificate cert) throws Exception {
        // 检查密钥用途扩展
        boolean[] keyUsage = cert.getKeyUsage();
        if (keyUsage != null) {
            // digitalSignature (0) 和 nonRepudiation (1) 应该被设置
            if (!keyUsage[0] && !keyUsage[1]) {
                throw new CertificateException("证书密钥用途不支持数字签名");
            }
        }
        
        // 检查扩展密钥用途
        List<String> extendedKeyUsage = cert.getExtendedKeyUsage();
        if (extendedKeyUsage != null) {
            boolean hasDocumentSigning = extendedKeyUsage.contains("*******.4.1.311.10.3.12") || // 微软文档签名
                                       extendedKeyUsage.contains("1.2.840.113583.1.1.5") ||      // Adobe PDF签名  
                                       extendedKeyUsage.contains("*******.*******.4");          // 邮件保护(通用)
            
            if (!hasDocumentSigning) {
                log.warn("证书可能不是专用于文档签名的证书");
            }
        }
    }
    
    /**
     * 验证颁发者信任
     */
    private void validateIssuerTrust(X509Certificate cert) throws Exception {
        String issuerDN = cert.getIssuerX500Principal().getName();
        String subjectDN = cert.getSubjectX500Principal().getName();
        
        // 检查是否为自签名证书
        if (issuerDN.equals(subjectDN)) {
            log.warn("检测到自签名证书，请确保在受控环境中使用");
        }
        
        // 检查是否为已知的受信任CA签发的证书
        if (!isKnownTrustedCA(issuerDN)) {
            log.warn("证书颁发者不在已知受信任CA列表中: {}", issuerDN);
        }
    }
    
    /**
     * 检查是否为已知受信任CA
     */
    private boolean isKnownTrustedCA(String issuerDN) {
        List<String> trustedCAs = securityConfig.getValidation().getTrustedCAs();
        
        for (String trustedCA : trustedCAs) {
            if (issuerDN.contains(trustedCA)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 验证证书链
     */
    private static void validateCertificateChain(Store<X509CertificateHolder> certStore, X509Certificate signerCert) 
            throws GeneralSecurityException {
        
        Collection<X509CertificateHolder> holders = certStore.getMatches(null);
        List<Certificate> certChain = new ArrayList<>();
        
        for (X509CertificateHolder holder : holders) {
            X509Certificate cert = new JcaX509CertificateConverter()
                    .setProvider(BouncyCastleProvider.PROVIDER_NAME)
                    .getCertificate(holder);
            certChain.add(cert);
        }
        
        // 确保证书链完整性
        if (certChain.isEmpty()) {
            throw new CertificateException("证书链为空");
        }
        
        // 构建证书路径
        CertPath certPath = CertificateFactory.getInstance("X.509").generateCertPath(certChain);
        
        // 验证证书路径
        PKIXParameters params = new PKIXParameters(trustStore);
        params.setRevocationEnabled(false); // 根据您的需求，可以选择是否启用
        
        CertPathValidator validator = CertPathValidator.getInstance("PKIX");
        PKIXCertPathValidatorResult result = (PKIXCertPathValidatorResult) validator.validate(certPath, params);
        
        log.info("证书链验证通过，信任锚: {}", result.getTrustAnchor().getTrustedCert().getSubjectX500Principal());
    }


    /**
     * 加载根CA证书作为信任锚
     */
    private static void loadTrustStore(String rootCaCertPath) throws Exception {
        if (trustStore == null) {
            KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
            ks.load(null, null); // 初始化空的KeyStore

            try (InputStream fis = Files.newInputStream(Paths.get(rootCaCertPath))) {
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                X509Certificate rootCert = (X509Certificate) cf.generateCertificate(fis);
                ks.setCertificateEntry(rootCert.getSubjectX500Principal().getName(), rootCert);
            }
            trustStore = ks;
            log.info("信任锚加载成功");
        }
    }

    /**
     * 证书验证结果
     */
    public static class CertificateValidationResult {
        private final boolean valid;
        private final String message;
        
        public CertificateValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
    }
} 

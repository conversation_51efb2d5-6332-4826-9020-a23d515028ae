package tf.or.pdf.core;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.PDSignature;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.SignatureInterface;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.SignatureOptions;
import org.bouncycastle.cert.jcajce.JcaCertStore;
import org.bouncycastle.cms.CMSProcessableByteArray;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.cms.CMSSignedDataGenerator;
import org.bouncycastle.cms.CMSTypedData;
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.certificate.MonthlyKeyManager;
import tf.or.pdf.config.PDFConfig;

import java.io.*;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Calendar;
import java.util.concurrent.locks.ReentrantLock;

/**
 * PDF数字签名处理器
 * 专门负责PDF文档的数字签名功能
 * 增强安全性，支持完整性保护和防篡改检查
 */
@Component
public class PDFSignatureProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(PDFSignatureProcessor.class);
    
    @Autowired
    private MonthlyKeyManager monthlyKeyManager;

    // 签名操作锁，确保线程安全
    private final ReentrantLock signatureLock = new ReentrantLock();
    
    static {
        // 确保BouncyCastle Provider可用
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }
    
    /**
     * 签署PDF文档
     * 增强安全性：完整性保护、防篡改检查
     * 
     * @param inputPath 输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param signName 签署人姓名
     * @param signLocation 签署地点
     * @param signReason 签署原因
     * @param keystorePath 证书文件路径
     * @param keystorePassword 证书密码
     * @param keystoreAlias 证书别名
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDF(String inputPath, String outputPath,
                       String signName, String signLocation, String signReason,
                       String keystorePath, String keystorePassword, String keystoreAlias) throws Exception {
        
        log.info("开始PDF数字签名：{} -> {}", inputPath, outputPath);
        
        // 参数验证
        validateSignatureParameters(inputPath, outputPath, keystorePath, keystorePassword, keystoreAlias);
        
        signatureLock.lock();
        try {
            // 加载私钥和证书
            KeyStoreInfo keyStoreInfo = loadKeyStore(keystorePath, keystorePassword, keystoreAlias);
            
            // 验证证书有效性
            validateCertificate(keyStoreInfo.certificate);
            
            // 执行签名
            performSigning(inputPath, outputPath, signName, signLocation, signReason, keyStoreInfo);
            
            // 验证签名完整性
            verifySignatureIntegrity(outputPath);
            
            log.info("PDF数字签名完成：{}", outputPath);

        } finally {
            signatureLock.unlock();
        }
    }

    /**
     * 使用月度证书对PDF进行数字签名
     * 自动使用当前月份的证书，如果不存在则自动生成
     *
     * @param inputPath 输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param signName 签署人姓名
     * @param signLocation 签署地点
     * @param signReason 签署原因
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDFWithMonthlyKey(String inputPath, String outputPath,
                                     String signName, String signLocation, String signReason) throws Exception {

        log.info("开始使用月度证书进行PDF数字签名：{} -> {}", inputPath, outputPath);

        try {
            // 确保当前月份的证书存在
            String certPath = monthlyKeyManager.ensureCurrentMonthCertificate();
            String certPassword = monthlyKeyManager.getCertificatePassword();
            String certAlias = monthlyKeyManager.getCertificateAlias();

            log.debug("使用月度证书: {}", certPath);

            // 调用标准签名方法
            signPDF(inputPath, outputPath, signName, signLocation, signReason,
                   certPath, certPassword, certAlias);

            log.info("月度证书PDF数字签名完成：{}", outputPath);

        } catch (Exception e) {
            log.error("使用月度证书签名失败", e);
            throw new Exception("使用月度证书签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行PDF签名操作
     */
    private void performSigning(String inputPath, String outputPath, String signName, 
                               String signLocation, String signReason, KeyStoreInfo keyStoreInfo) throws Exception {
        
        try (FileInputStream fis = new FileInputStream(inputPath);
             FileOutputStream fos = new FileOutputStream(outputPath);
             PDDocument document = PDDocument.load(fis)) {
            
            // 创建签名对象
            PDSignature signature = new PDSignature();
            signature.setFilter(PDSignature.FILTER_ADOBE_PPKLITE);
            signature.setSubFilter(PDSignature.SUBFILTER_ADBE_PKCS7_DETACHED);
            signature.setName(signName);
            signature.setLocation(signLocation);
            signature.setReason(signReason);
            signature.setSignDate(Calendar.getInstance());
            
            // 创建签名接口
            SignatureInterface signatureInterface = new PDFSignatureInterface(keyStoreInfo.privateKey, keyStoreInfo.certificateChain);

            // 设置签名选项
            SignatureOptions signatureOptions = new SignatureOptions();
            signatureOptions.setPreferredSignatureSize(SignatureOptions.DEFAULT_SIGNATURE_SIZE * 2);
            
            // 添加签名到文档
            document.addSignature(signature, signatureInterface, signatureOptions);
            
            // 保存签名后的文档
            document.saveIncremental(fos);
            
            log.debug("PDF签名操作完成");
        }
    }
    
    /**
     * 验证签名完整性
     */
    private void verifySignatureIntegrity(String signedPdfPath) throws Exception {
        try (PDDocument document = PDDocument.load(new File(signedPdfPath))) {
            
            // 检查文档是否包含签名
            if (document.getSignatureDictionaries().isEmpty()) {
                throw new Exception("签名后的PDF文档不包含数字签名");
            }
            
            // 检查文档结构完整性
            if (document.getNumberOfPages() == 0) {
                throw new Exception("签名后的PDF文档结构异常");
            }
            
            log.debug("签名完整性验证通过");
        }
    }
    
    /**
     * 加载密钥库信息
     */
    private KeyStoreInfo loadKeyStore(String keystorePath, String keystorePassword, String keystoreAlias) throws Exception {
        try {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            
            try (FileInputStream fis = new FileInputStream(keystorePath)) {
                keystore.load(fis, keystorePassword.toCharArray());
            }
            
            // 获取私钥
            PrivateKey privateKey = (PrivateKey) keystore.getKey(keystoreAlias, keystorePassword.toCharArray());
            if (privateKey == null) {
                throw new Exception("无法从密钥库中获取私钥，别名：" + keystoreAlias);
            }
            
            // 获取证书
            Certificate cert = keystore.getCertificate(keystoreAlias);
            if (cert == null) {
                throw new Exception("无法从密钥库中获取证书，别名：" + keystoreAlias);
            }
            X509Certificate certificate = (X509Certificate) cert;
            log.debug("密钥库加载成功，证书主题：{}", certificate.getSubjectDN());

            // 获取证书链
            Certificate[] certificateChain = keystore.getCertificateChain(keystoreAlias);
            if (certificateChain == null || certificateChain.length == 0) {
                throw new KeyStoreException("无法从别名 '" + keystoreAlias + "' 获取有效的私钥或证书链。");
            }
            return new KeyStoreInfo(privateKey, certificate, certificateChain);
            
        } catch (Exception e) {
            log.error("加载密钥库失败：{}", keystorePath, e);
            throw new Exception("加载密钥库失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证证书有效性
     */
    private void validateCertificate(X509Certificate certificate) throws Exception {
        try {
            // 检查证书有效期
            certificate.checkValidity();
            
            // 检查证书用途（数字签名）
            boolean[] keyUsage = certificate.getKeyUsage();
            if (keyUsage == null || !keyUsage[0]) { // digitalSignature位
                log.warn("证书可能不支持数字签名用途");
            }
        } catch (Exception e) {
            log.error("证书验证失败", e);
            throw new Exception("证书验证失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证签名参数
     */
    private void validateSignatureParameters(String inputPath, String outputPath, 
                                           String keystorePath, String keystorePassword, String keystoreAlias) {
        if (inputPath == null || inputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输入PDF文件路径不能为空");
        }
        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出PDF文件路径不能为空");
        }
        if (!new File(inputPath).exists()) {
            throw new IllegalArgumentException("输入PDF文件不存在：" + inputPath);
        }
        if (keystorePath == null || keystorePath.trim().isEmpty()) {
            throw new IllegalArgumentException("密钥库文件路径不能为空");
        }
        if (!new File(keystorePath).exists()) {
            throw new IllegalArgumentException("密钥库文件不存在：" + keystorePath);
        }
        if (keystorePassword == null || keystorePassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密钥库密码不能为空");
        }
        if (keystoreAlias == null || keystoreAlias.trim().isEmpty()) {
            throw new IllegalArgumentException("证书别名不能为空");
        }
    }
    
    /**
     * 密钥库信息内部类
     */
    private static class KeyStoreInfo {
        final PrivateKey privateKey;
        final X509Certificate certificate;
        final Certificate[] certificateChain;

        KeyStoreInfo(PrivateKey privateKey, X509Certificate certificate, Certificate[] certificateChain) {
            this.privateKey = privateKey;
            this.certificate = certificate;
            this.certificateChain = certificateChain;
        }
    }

    // ==================== 基于Stream的处理方法 ====================

    /**
     * 基于InputStream签署PDF文档
     *
     * @param inputStream      输入PDF流
     * @param outputStream     输出PDF流
     * @param signName         签署人姓名
     * @param signLocation     签署地点
     * @param signReason       签署原因
     * @param keystorePath     证书文件路径
     * @param keystorePassword 证书密码
     * @param keystoreAlias    证书别名
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDFStream(InputStream inputStream, OutputStream outputStream,
                             String signName, String signLocation, String signReason,
                             String keystorePath, String keystorePassword, String keystoreAlias) throws Exception {

        log.info("开始基于流进行PDF数字签名");

        // 参数验证（除了文件路径验证）
        validateStreamSignatureParameters(keystorePath, keystorePassword, keystoreAlias);

        signatureLock.lock();
        try {
            // 加载私钥和证书
            KeyStoreInfo keyStoreInfo = loadKeyStore(keystorePath, keystorePassword, keystoreAlias);

            // 验证证书有效性
            validateCertificate(keyStoreInfo.certificate);

            // 执行流式签名
            performStreamSigning(inputStream, outputStream, signName, signLocation, signReason, keyStoreInfo);

            log.info("基于流的PDF数字签名完成");

        } finally {
            signatureLock.unlock();
        }
    }

    /**
     * 使用月度证书基于InputStream签署PDF文档
     *
     * @param inputStream  输入PDF流
     * @param outputStream 输出PDF流
     * @param signName     签署人姓名
     * @param signLocation 签署地点
     * @param signReason   签署原因
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDFStreamWithMonthlyKey(InputStream inputStream, OutputStream outputStream,
                                           String signName, String signLocation, String signReason) throws Exception {

        log.info("开始使用月度证书基于流进行PDF数字签名");

        try {
            // 确保当前月份的证书存在
            String certPath = monthlyKeyManager.ensureCurrentMonthCertificate();
            String certPassword = monthlyKeyManager.getCertificatePassword();
            String certAlias = monthlyKeyManager.getCertificateAlias();

            log.debug("使用月度证书: {}", certPath);

            // 调用流式签名方法
            signPDFStream(inputStream, outputStream, signName, signLocation, signReason,
                         certPath, certPassword, certAlias);

            log.info("基于流的月度证书PDF数字签名完成");

        } catch (Exception e) {
            log.error("使用月度证书基于流签名失败", e);
            throw new Exception("使用月度证书基于流签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行基于流的PDF签名操作
     */
    private void performStreamSigning(InputStream inputStream, OutputStream outputStream,
                                     String signName, String signLocation, String signReason,
                                     KeyStoreInfo keyStoreInfo) throws Exception {

        try (PDDocument document = PDDocument.load(inputStream)) {

            // 创建签名对象
            PDSignature signature = new PDSignature();
            signature.setFilter(PDSignature.FILTER_ADOBE_PPKLITE);
            signature.setSubFilter(PDSignature.SUBFILTER_ADBE_PKCS7_DETACHED);
            signature.setName(signName);
            signature.setLocation(signLocation);
            signature.setReason(signReason);
            signature.setSignDate(Calendar.getInstance());

            // 创建签名接口
            SignatureInterface signatureInterface = new PDFSignatureInterface(keyStoreInfo.privateKey, keyStoreInfo.certificateChain);

            // 设置签名选项
            SignatureOptions signatureOptions = new SignatureOptions();
            signatureOptions.setPreferredSignatureSize(SignatureOptions.DEFAULT_SIGNATURE_SIZE * 2);

            // 添加签名到文档
            document.addSignature(signature, signatureInterface, signatureOptions);

            // 保存签名后的文档到输出流
            document.saveIncremental(outputStream);

            log.debug("基于流的PDF签名操作完成");
        }
    }

    /**
     * 验证流式签名参数（不包括输入输出路径验证）
     */
    private void validateStreamSignatureParameters(String keystorePath, String keystorePassword, String keystoreAlias) {
        if (keystorePath == null || keystorePath.trim().isEmpty()) {
            throw new IllegalArgumentException("密钥库文件路径不能为空");
        }
        if (!new File(keystorePath).exists()) {
            throw new IllegalArgumentException("密钥库文件不存在：" + keystorePath);
        }
        if (keystorePassword == null || keystorePassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密钥库密码不能为空");
        }
        if (keystoreAlias == null || keystoreAlias.trim().isEmpty()) {
            throw new IllegalArgumentException("证书别名不能为空");
        }
    }

}

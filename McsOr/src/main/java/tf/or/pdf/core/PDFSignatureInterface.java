package tf.or.pdf.core;

import org.apache.pdfbox.pdmodel.interactive.digitalsignature.SignatureInterface;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.DERSet;
import org.bouncycastle.asn1.cms.Attribute;
import org.bouncycastle.asn1.cms.AttributeTable;
import org.bouncycastle.cert.jcajce.JcaCertStore;
import org.bouncycastle.cms.*;
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder;
import org.bouncycastle.tsp.TimeStampResponse;
import org.bouncycastle.tsp.TimeStampToken;
import org.bouncycastle.util.Store;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tf.or.pdf.timestamp.TimestampService;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.List;

public class PDFSignatureInterface implements SignatureInterface {

    private static final Logger log = LoggerFactory.getLogger(PDFSignatureInterface.class);

    private final PrivateKey privateKey;
    private final Certificate[] certificateChain;

    public PDFSignatureInterface(PrivateKey privateKey, Certificate[] certificateChain) {
        this.privateKey = privateKey;
        this.certificateChain = certificateChain;
    }

    @Override
    public byte[] sign(InputStream content) throws IOException {
        try {
            // 1. 读取待签名内容
            byte[] contentBytes = readAllBytes(content);

            // 2. 创建基础CMS签名
            CMSSignedData cmsSignedData = createBaseCMSSignature(contentBytes);

            // 3. 添加时间戳到签名中
            CMSSignedData timestampedSignature = addTimestampToSignature(cmsSignedData);

            log.info("PDF签名完成，包含可信时间戳");
            return timestampedSignature.getEncoded();

        } catch (Exception e) {
            throw new IOException("增强签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建基础CMS签名
     */
    private CMSSignedData createBaseCMSSignature(byte[] contentBytes) throws Exception {
        List<X509Certificate> certList = new ArrayList<>();
        for (Certificate cert : certificateChain) {
            certList.add((X509Certificate) cert);
        }

        CMSSignedDataGenerator gen = new CMSSignedDataGenerator();

        ContentSigner sha256Signer = new JcaContentSignerBuilder("SHA256WithRSA")
                .setProvider(BouncyCastleProvider.PROVIDER_NAME).build(privateKey);

        gen.addSignerInfoGenerator(new JcaSignerInfoGeneratorBuilder(
                new JcaDigestCalculatorProviderBuilder()
                        .setProvider(BouncyCastleProvider.PROVIDER_NAME).build())
                .build(sha256Signer, (X509Certificate) certificateChain[0]));

        Store<X509Certificate> certs = new JcaCertStore(certList);
        gen.addCertificates(certs);

        CMSProcessableByteArray processable = new CMSProcessableByteArray(contentBytes);
        return gen.generate(processable, false);
    }

    /**
     * 为CMS签名添加时间戳
     */
    private CMSSignedData addTimestampToSignature(CMSSignedData originalSignature) throws Exception {
        // 获取原始签名的SignerInfo
        SignerInformationStore signerInfos = originalSignature.getSignerInfos();
        Collection<SignerInformation> signers = signerInfos.getSigners();

        if (signers.isEmpty()) {
            log.warn("没有找到签名者信息，无法添加时间戳");
            return originalSignature;
        }

        SignerInformation signer = signers.iterator().next();

        // 关键修正：使用签名值而不是整个CMS数据来计算消息摘要
        byte[] signatureBytes = signer.getSignature();
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] messageImprint = digest.digest(signatureBytes);

        // 获取时间戳
        TimeStampResponse tsResponse = TimestampService.getTrustedTimestamp(messageImprint);
        if (tsResponse.getTimeStampToken() == null) {
            log.warn("获取时间戳失败，使用无时间戳的签名");
            return originalSignature;
        }

        // 创建时间戳属性
        TimeStampToken tsToken = tsResponse.getTimeStampToken();
        ASN1ObjectIdentifier timestampOID = new ASN1ObjectIdentifier("1.2.840.113549.1.9.16.2.14");
        Attribute timestampAttribute = new Attribute(timestampOID, new DERSet(ASN1Primitive.fromByteArray(tsToken.getEncoded())));

        // 创建包含时间戳的UnsignedAttributes
        AttributeTable unsignedAttributes = signer.getUnsignedAttributes();
        Hashtable<ASN1ObjectIdentifier, Attribute> attributeMap = new Hashtable<>();

        if (unsignedAttributes != null) {
            attributeMap = unsignedAttributes.toHashtable();
        }
        attributeMap.put(timestampOID, timestampAttribute);

        AttributeTable newUnsignedAttributes = new AttributeTable(attributeMap);

        // 创建新的SignerInformation
        SignerInformation newSigner = SignerInformation.replaceUnsignedAttributes(signer, newUnsignedAttributes);

        // 重新构建SignerInformationStore
        List<SignerInformation> newSigners = new ArrayList<>();
        newSigners.add(newSigner);
        SignerInformationStore newSignerInfos = new SignerInformationStore(newSigners);

        // 创建新的CMSSignedData
        CMSSignedData newSignedData = CMSSignedData.replaceSigners(originalSignature, newSignerInfos);

        log.info("时间戳已成功添加到PDF签名中");
        return newSignedData;
    }

    private byte[] readAllBytes(InputStream input) throws IOException {
        byte[] buffer = new byte[8192];
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        int length;
        while ((length = input.read(buffer)) != -1) {
            output.write(buffer, 0, length);
        }
        return output.toByteArray();
    }
}
package tf.or.pdf.core.stream;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.core.PDFSignatureProcessor;
import tf.or.pdf.core.PDFSignatureValidator;
import tf.or.pdf.core.PDFWatermarkProcessor;
import tf.or.pdf.core.SignatureVerificationResult;

import java.io.*;
import java.util.function.Function;

/**
 * PDF流式处理器
 * 支持基于Stream的PDF处理操作，避免频繁的文件I/O
 * 支持连续处理：加水印 -> 签名 -> 验签
 */
@Component
public class PDFStreamProcessor {

    private static final Logger log = LoggerFactory.getLogger(PDFStreamProcessor.class);

    @Autowired
    private PDFWatermarkProcessor watermarkProcessor;

    @Autowired
    private PDFSignatureProcessor signatureProcessor;

    @Autowired
    private PDFSignatureValidator signatureValidator;

    /**
     * 基于InputStream添加文字水印
     *
     * @param inputStream       输入PDF流
     * @param outputStream      输出PDF流
     * @param watermarkText     水印文字
     * @param horizontalSpacing 横向间距
     * @param verticalSpacing   纵向间距
     * @param fontSize          字体大小
     * @param rotation          旋转角度
     * @param transparency      透明度
     * @throws Exception 处理失败时抛出异常
     */
    public void addTextWatermark(InputStream inputStream, OutputStream outputStream,
                                 String watermarkText, int horizontalSpacing, int verticalSpacing,
                                 float fontSize, float rotation, float transparency) throws Exception {

        log.info("开始基于流添加文字水印");

        try (PDDocument document = PDDocument.load(inputStream)) {
            // 复用现有的水印处理逻辑
            watermarkProcessor.addTextWatermarkToDocument(document, watermarkText, 
                horizontalSpacing, verticalSpacing, fontSize, rotation, transparency);
            
            document.save(outputStream);
            log.info("基于流的文字水印添加完成");
        } catch (Exception e) {
            log.error("基于流添加文字水印失败", e);
            throw new Exception("基于流添加文字水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于InputStream添加图片水印
     *
     * @param inputStream       输入PDF流
     * @param outputStream      输出PDF流
     * @param imagePath         水印图片路径
     * @param horizontalSpacing 横向间距
     * @param verticalSpacing   纵向间距
     * @param imageScale        图片缩放比例
     * @param rotation          旋转角度
     * @param transparency      透明度
     * @throws Exception 处理失败时抛出异常
     */
    public void addImageWatermark(InputStream inputStream, OutputStream outputStream,
                                  String imagePath, int horizontalSpacing, int verticalSpacing,
                                  float imageScale, float rotation, float transparency) throws Exception {

        log.info("开始基于流添加图片水印");

        try (PDDocument document = PDDocument.load(inputStream)) {
            // 复用现有的水印处理逻辑
            watermarkProcessor.addImageWatermarkToDocument(document, imagePath,
                horizontalSpacing, verticalSpacing, imageScale, rotation, transparency);
            
            document.save(outputStream);
            log.info("基于流的图片水印添加完成");
        } catch (Exception e) {
            log.error("基于流添加图片水印失败", e);
            throw new Exception("基于流添加图片水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于InputStream进行PDF签名
     *
     * @param inputStream      输入PDF流
     * @param outputStream     输出PDF流
     * @param signName         签署人姓名
     * @param signLocation     签署地点
     * @param signReason       签署原因
     * @param keystorePath     证书文件路径
     * @param keystorePassword 证书密码
     * @param keystoreAlias    证书别名
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDF(InputStream inputStream, OutputStream outputStream,
                       String signName, String signLocation, String signReason,
                       String keystorePath, String keystorePassword, String keystoreAlias) throws Exception {

        log.info("开始基于流进行PDF数字签名");

        try {
            // 调用签名处理器的流式方法
            signatureProcessor.signPDFStream(inputStream, outputStream, signName, signLocation, signReason,
                keystorePath, keystorePassword, keystoreAlias);
            log.info("基于流的PDF数字签名完成");
        } catch (Exception e) {
            log.error("基于流进行PDF签名失败", e);
            throw new Exception("基于流进行PDF签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用月度证书基于InputStream进行PDF签名
     *
     * @param inputStream  输入PDF流
     * @param outputStream 输出PDF流
     * @param signName     签署人姓名
     * @param signLocation 签署地点
     * @param signReason   签署原因
     * @throws Exception 签名失败时抛出异常
     */
    public void signPDFWithMonthlyKey(InputStream inputStream, OutputStream outputStream,
                                     String signName, String signLocation, String signReason) throws Exception {

        log.info("开始使用月度证书基于流进行PDF数字签名");

        try {
            // 调用签名处理器的月度证书流式方法
            signatureProcessor.signPDFStreamWithMonthlyKey(inputStream, outputStream, 
                signName, signLocation, signReason);
            log.info("基于流的月度证书PDF数字签名完成");
        } catch (Exception e) {
            log.error("使用月度证书基于流进行PDF签名失败", e);
            throw new Exception("使用月度证书基于流进行PDF签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于字节数组验证PDF签名
     *
     * @param pdfBytes PDF文件字节数组
     * @return 验证结果
     */
    public SignatureVerificationResult verifyPDFSignature(byte[] pdfBytes) {
        log.info("开始基于字节数组验证PDF数字签名");
        return signatureValidator.verifyPDFSignature(pdfBytes);
    }

    /**
     * 基于InputStream验证PDF签名
     *
     * @param inputStream 输入PDF流
     * @return 验证结果
     * @throws Exception 验证失败时抛出异常
     */
    public SignatureVerificationResult verifyPDFSignature(InputStream inputStream) throws Exception {
        log.info("开始基于流验证PDF数字签名");
        
        try {
            // 将流转换为字节数组进行验证
            byte[] pdfBytes = readAllBytes(inputStream);
            return signatureValidator.verifyPDFSignature(pdfBytes);
        } catch (Exception e) {
            log.error("基于流验证PDF签名失败", e);
            throw new Exception("基于流验证PDF签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 读取输入流的所有字节
     */
    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        return buffer.toByteArray();
    }
}

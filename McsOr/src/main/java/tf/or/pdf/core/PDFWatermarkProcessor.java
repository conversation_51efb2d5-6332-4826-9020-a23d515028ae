package tf.or.pdf.core;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tf.or.pdf.config.PDFConfig;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * PDF水印处理器
 * 专门负责PDF文档的水印添加功能
 * 支持文字水印和图片水印，线程安全
 */
@Component
public class PDFWatermarkProcessor {

    private static final Logger log = LoggerFactory.getLogger(PDFWatermarkProcessor.class);

    @Autowired
    private PDFConfig pdfConfig;

    /**
     * 添加文字水印
     *
     * @param inputPath         输入PDF文件路径
     * @param outputPath        输出PDF文件路径
     * @param watermarkText     水印文字内容
     * @param horizontalSpacing 水印横向间距
     * @param verticalSpacing   水印纵向间距
     * @param fontSize          字体大小
     * @param rotation          水印倾斜角度
     * @param transparency      透明度 0.0-1.0
     * @throws Exception 处理失败时抛出异常
     */
    public void addTextWatermark(String inputPath, String outputPath,
                                 String watermarkText, int horizontalSpacing, int verticalSpacing,
                                 float fontSize, float rotation, float transparency) throws Exception {

        log.info("开始添加文字水印：{} -> {}", inputPath, outputPath);

        // 参数验证
        validateWatermarkParameters(watermarkText, horizontalSpacing, verticalSpacing, fontSize, transparency);

        try (PDDocument document = PDDocument.load(new File(inputPath))) {

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(transparency);

            // 设置字体
            PDFont font = createFont(document);

            // 为每一页添加水印
            for (PDPage page : document.getPages()) {
                addTextWatermarkToPage(document, page, watermarkText, horizontalSpacing, verticalSpacing, fontSize, rotation, font, graphicsState);
            }

            // 保存文档
            document.save(outputPath);
            log.info("文字水印添加完成：{}", outputPath);

        } catch (Exception e) {
            log.error("添加文字水印失败：{} -> {}", inputPath, outputPath, e);
            throw new Exception("添加文字水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加图片水印
     *
     * @param inputPath         输入PDF文件路径
     * @param outputPath        输出PDF文件路径
     * @param imagePath         水印图片路径
     * @param horizontalSpacing 水印横向间距
     * @param verticalSpacing   水印纵向间距
     * @param imageScale        图片缩放比例
     * @param rotation          水印倾斜角度
     * @param transparency      透明度 0.0-1.0
     * @throws Exception 处理失败时抛出异常
     */
    public void addImageWatermark(String inputPath, String outputPath,
                                  String imagePath, int horizontalSpacing, int verticalSpacing,
                                  float imageScale, float rotation, float transparency) throws Exception {

        log.info("开始添加图片水印：{} -> {}，水印图片：{}", inputPath, outputPath, imagePath);

        // 参数验证
        validateImageWatermarkParameters(imagePath, horizontalSpacing, verticalSpacing, imageScale, transparency);

        try (PDDocument document = PDDocument.load(new File(inputPath))) {

            // 加载水印图片
            PDImageXObject watermarkImage = PDImageXObject.createFromFile(imagePath, document);

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(transparency);

            // 为每一页添加水印
            for (PDPage page : document.getPages()) {
                addImageWatermarkToPage(document, page, watermarkImage, horizontalSpacing, verticalSpacing, imageScale, rotation, graphicsState);
            }

            // 保存文档
            document.save(outputPath);
            log.info("图片水印添加完成：{}", outputPath);

        } catch (Exception e) {
            log.error("添加图片水印失败：{} -> {}", inputPath, outputPath, e);
            throw new Exception("添加图片水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为单页添加文字水印
     */
    private void addTextWatermarkToPage(PDDocument document, PDPage page, String watermarkText,
                                        int horizontalSpacing, int verticalSpacing, float fontSize,
                                        float rotation, PDFont font, PDExtendedGraphicsState graphicsState) throws IOException {

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page,
                PDPageContentStream.AppendMode.APPEND, true, true)) {

            contentStream.setGraphicsStateParameters(graphicsState);
            contentStream.setNonStrokingColor(Color.LIGHT_GRAY);
            contentStream.setFont(font, fontSize);

            // 获取页面尺寸
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 在页面上平铺水印
            for (float y = 0; y < pageHeight + verticalSpacing; y += verticalSpacing) {
                for (float x = 0; x < pageWidth + horizontalSpacing; x += horizontalSpacing) {
                    contentStream.beginText();

                    // 设置变换矩阵（位置和旋转）
                    Matrix matrix = Matrix.getRotateInstance(Math.toRadians(rotation), x, y);
                    contentStream.setTextMatrix(matrix);

                    contentStream.showText(watermarkText);
                    contentStream.endText();
                }
            }
        }
    }

    /**
     * 为单页添加图片水印
     */
    private void addImageWatermarkToPage(PDDocument document, PDPage page, PDImageXObject watermarkImage,
                                         int horizontalSpacing, int verticalSpacing, float imageScale,
                                         float rotation, PDExtendedGraphicsState graphicsState) throws IOException {

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page,
                PDPageContentStream.AppendMode.APPEND, true, true)) {

            contentStream.setGraphicsStateParameters(graphicsState);

            // 获取页面尺寸
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 在页面上平铺水印
            for (float y = 0; y < pageHeight + verticalSpacing; y += verticalSpacing) {
                for (float x = 0; x < pageWidth + horizontalSpacing; x += horizontalSpacing) {

                    // 保存当前图形状态
                    contentStream.saveGraphicsState();

                    // 设置变换矩阵（位置、旋转和缩放）
                    Matrix matrix = Matrix.getRotateInstance(Math.toRadians(rotation), x, y);
                    matrix.scale(imageScale, imageScale);
                    contentStream.transform(matrix);

                    // 绘制图片
                    contentStream.drawImage(watermarkImage, 0, 0);

                    // 恢复图形状态
                    contentStream.restoreGraphicsState();
                }
            }
        }
    }


    /**
     * 创建支持中文的字体
     */
    private PDFont createFont(PDDocument document) throws IOException {
        String fontPath = pdfConfig.getTextWatermark().getFontPath();
        if ((fontPath == null || !new File(fontPath).exists())) {
            throw new IllegalArgumentException("fontPath 未配置");
        }
        return PDType0Font.load(document, new File(fontPath));
    }

    /**
     * 验证文字水印参数
     */
    private void validateWatermarkParameters(String watermarkText, int horizontalSpacing, int verticalSpacing,
                                             float fontSize, float transparency) {
        if (watermarkText == null || watermarkText.trim().isEmpty()) {
            throw new IllegalArgumentException("水印文字不能为空");
        }
        if (horizontalSpacing <= 0) {
            throw new IllegalArgumentException("水印横向间距必须大于0");
        }
        if (verticalSpacing <= 0) {
            throw new IllegalArgumentException("水印纵向间距必须大于0");
        }
        if (fontSize <= 0) {
            throw new IllegalArgumentException("字体大小必须大于0");
        }
        if (transparency < 0.0f || transparency > 1.0f) {
            throw new IllegalArgumentException("透明度必须在0.0-1.0之间");
        }
    }

    /**
     * 验证图片水印参数
     */
    private void validateImageWatermarkParameters(String imagePath, int horizontalSpacing, int verticalSpacing,
                                                  float imageScale, float transparency) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            throw new IllegalArgumentException("水印图片路径不能为空");
        }
        if (!new File(imagePath).exists()) {
            throw new IllegalArgumentException("水印图片文件不存在：" + imagePath);
        }
        if (horizontalSpacing <= 0) {
            throw new IllegalArgumentException("水印横向间距必须大于0");
        }
        if (verticalSpacing <= 0) {
            throw new IllegalArgumentException("水印纵向间距必须大于0");
        }
        if (imageScale <= 0) {
            throw new IllegalArgumentException("图片缩放比例必须大于0");
        }
        if (transparency < 0.0f || transparency > 1.0f) {
            throw new IllegalArgumentException("透明度必须在0.0-1.0之间");
        }
    }

    /**
     * 使用配置文件中的设置添加文字水印
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @throws Exception 处理失败时抛出异常
     */
    public void addTextWatermark(String inputPath, String outputPath) throws Exception {
        PDFConfig.TextWatermarkConfig config = pdfConfig.getTextWatermark();
        addTextWatermark(inputPath, outputPath, config.getText(),
                config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getFontSize(), config.getRotation(), config.getTransparency());
    }

    /**
     * 使用配置文件中的设置添加文字水印（自定义文字）
     *
     * @param inputPath     输入PDF文件路径
     * @param outputPath    输出PDF文件路径
     * @param watermarkText 自定义水印文字
     * @throws Exception 处理失败时抛出异常
     */
    public void addTextWatermark(String inputPath, String outputPath, String watermarkText) throws Exception {
        PDFConfig.TextWatermarkConfig config = pdfConfig.getTextWatermark();
        addTextWatermark(inputPath, outputPath, watermarkText,
                config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getFontSize(), config.getRotation(), config.getTransparency());
    }

    /**
     * 使用配置文件中的设置添加图片水印
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @throws Exception 处理失败时抛出异常
     */
    public void addImageWatermark(String inputPath, String outputPath) throws Exception {
        PDFConfig.ImageWatermarkConfig config = pdfConfig.getImageWatermark();
        addImageWatermark(inputPath, outputPath, config.getImagePath(),
                config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getImageScale(), config.getRotation(), config.getTransparency());
    }

    /**
     * 使用配置文件中的设置添加图片水印（自定义水印图片）
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param imagePath  水印图片路径
     * @throws Exception 处理失败时抛出异常
     */
    public void addImageWatermark(String inputPath, String outputPath, String imagePath) throws Exception {
        PDFConfig.ImageWatermarkConfig config = pdfConfig.getImageWatermark();
        addImageWatermark(inputPath, outputPath, imagePath,
                config.getHorizontalSpacing(), config.getVerticalSpacing(),
                config.getImageScale(), config.getRotation(), config.getTransparency());
    }

    // ==================== 基于Stream的处理方法 ====================

    /**
     * 为PDDocument添加文字水印（内部方法，供流式处理使用）
     *
     * @param document          PDF文档对象
     * @param watermarkText     水印文字内容
     * @param horizontalSpacing 水印横向间距
     * @param verticalSpacing   水印纵向间距
     * @param fontSize          字体大小
     * @param rotation          水印倾斜角度
     * @param transparency      透明度 0.0-1.0
     * @throws Exception 处理失败时抛出异常
     */
    public void addTextWatermarkToDocument(PDDocument document, String watermarkText,
                                          int horizontalSpacing, int verticalSpacing,
                                          float fontSize, float rotation, float transparency) throws Exception {

        log.debug("为PDDocument添加文字水印");

        // 参数验证
        validateWatermarkParameters(watermarkText, horizontalSpacing, verticalSpacing, fontSize, transparency);

        try {
            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(transparency);

            // 设置字体
            PDFont font = createFont(document);

            // 为每一页添加水印
            for (PDPage page : document.getPages()) {
                addTextWatermarkToPage(document, page, watermarkText, horizontalSpacing, verticalSpacing, fontSize, rotation, font, graphicsState);
            }

            log.debug("PDDocument文字水印添加完成");

        } catch (Exception e) {
            log.error("为PDDocument添加文字水印失败", e);
            throw new Exception("为PDDocument添加文字水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为PDDocument添加图片水印（内部方法，供流式处理使用）
     *
     * @param document          PDF文档对象
     * @param imagePath         水印图片路径
     * @param horizontalSpacing 水印横向间距
     * @param verticalSpacing   水印纵向间距
     * @param imageScale        图片缩放比例
     * @param rotation          水印倾斜角度
     * @param transparency      透明度 0.0-1.0
     * @throws Exception 处理失败时抛出异常
     */
    public void addImageWatermarkToDocument(PDDocument document, String imagePath,
                                           int horizontalSpacing, int verticalSpacing,
                                           float imageScale, float rotation, float transparency) throws Exception {

        log.debug("为PDDocument添加图片水印");

        // 参数验证
        validateImageWatermarkParameters(imagePath, horizontalSpacing, verticalSpacing, imageScale, transparency);

        try {
            // 加载水印图片
            PDImageXObject watermarkImage = PDImageXObject.createFromFile(imagePath, document);

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(transparency);

            // 为每一页添加水印
            for (PDPage page : document.getPages()) {
                addImageWatermarkToPage(document, page, watermarkImage, horizontalSpacing, verticalSpacing, imageScale, rotation, graphicsState);
            }

            log.debug("PDDocument图片水印添加完成");

        } catch (Exception e) {
            log.error("为PDDocument添加图片水印失败", e);
            throw new Exception("为PDDocument添加图片水印失败: " + e.getMessage(), e);
        }
    }
}

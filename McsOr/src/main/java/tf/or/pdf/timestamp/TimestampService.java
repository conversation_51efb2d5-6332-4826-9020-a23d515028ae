package tf.or.pdf.timestamp;

import org.bouncycastle.asn1.cmp.PKIStatus;
import org.bouncycastle.tsp.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigInteger;
import java.security.SecureRandom;

/**
 * 简化的时间戳服务
 * 使用RestTemplate提供基本的时间戳获取功能
 * 支持多TSA服务的fallback机制
 */
public class TimestampService {

    private static final Logger log = LoggerFactory.getLogger(TimestampService.class);

    // 默认TSA服务列表
    private static final String[] DEFAULT_TSA_URLS = {
            "http://timestamp.digicert.com",      // DigiCert (主要)
            "http://timestamp.sectigo.com",       // Sectigo (备用1)
            "http://time.certum.pl",              // Certum (备用2)
            "http://ts.ssl.com",                  // SSL.com (备用3)
            "http://timestamp.apple.com/ts01",    // Apple (备用4)
            "http://timestamp.entrust.net/TSS/RFC3161sha2TS", // Entrust (备用5)
            "http://timestamp.globalsign.com/scripts/timstamp.dll",
            "http://timestamp.comodoca.com/rfc3161",
    };

    // RestTemplate实例
    private static RestTemplate restTemplate;

    // 配置参数
    private static final int CONNECTION_TIMEOUT = 1000; // 1秒
    private static final int READ_TIMEOUT = 3000;       // 3秒

    static {
        // 配置RestTemplate
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(CONNECTION_TIMEOUT);
        factory.setReadTimeout(READ_TIMEOUT);
        restTemplate = new RestTemplate(factory);
        log.info("时间戳服务已初始化");
    }

    /**
     * 获取可信时间戳（使用默认TSA列表）
     *
     * @param messageImprint 消息摘要
     * @return 时间戳响应
     * @throws TSPException 获取失败时抛出异常
     */
    public static TimeStampResponse getTrustedTimestamp(byte[] messageImprint) throws TSPException {
        return getTrustedTimestampWithFallback(messageImprint, DEFAULT_TSA_URLS);
    }

    /**
     * 获取可信时间戳（指定TSA列表）
     *
     * @param messageImprint 消息摘要
     * @param tsaUrls        TSA服务URL列表
     * @return 时间戳响应
     * @throws TSPException 获取失败时抛出异常
     */
    public static TimeStampResponse getTrustedTimestampWithFallback(byte[] messageImprint, String[] tsaUrls) throws TSPException {
        log.debug("开始获取时间戳，TSA服务数量: {}", tsaUrls.length);

        // 创建时间戳请求
        TimeStampRequest tsRequest = createTimestampRequest(messageImprint);

        // 依次尝试每个TSA服务（简化的fallback机制）
        TSPException lastException = null;
        for (String tsaUrl : tsaUrls) {
            try {
                log.debug("尝试TSA服务: {}", tsaUrl);
                TimeStampResponse response = requestTimestamp(tsRequest, tsaUrl);
                if (response != null && response.getStatus() == PKIStatus.GRANTED) {
                    log.debug("成功获取时间戳，TSA: {}", tsaUrl);
                    return response;
                }
            } catch (TSPException e) {
                log.warn("TSA服务请求失败: {}, 错误: {}", tsaUrl, e.getMessage());
                lastException = e;
            }
        }

        // 所有TSA服务都失败
        if (lastException != null) {
            throw lastException;
        } else {
            throw new TSPException("所有TSA服务都不可用");
        }
    }

    /**
     * 创建时间戳请求
     *
     * @param messageImprint 消息摘要
     * @return 时间戳请求
     */
    private static TimeStampRequest createTimestampRequest(byte[] messageImprint) {
        TimeStampRequestGenerator tsqGenerator = new TimeStampRequestGenerator();
        tsqGenerator.setCertReq(true); // 请求包含TSA证书

        // 生成随机nonce
        BigInteger nonce = new BigInteger(64, new SecureRandom());
        return tsqGenerator.generate(TSPAlgorithms.SHA256, messageImprint, nonce);
    }

    /**
     * 向指定TSA服务请求时间戳
     *
     * @param tsRequest 时间戳请求
     * @param tsaUrl    TSA服务URL
     * @return 时间戳响应
     * @throws TSPException 请求失败时抛出异常
     */
    private static TimeStampResponse requestTimestamp(TimeStampRequest tsRequest, String tsaUrl) throws TSPException {
        try {
            // 准备HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/timestamp-query"));
            headers.set("User-Agent", "PDF-Signature-Service/1.0");

            HttpEntity<byte[]> requestEntity = new HttpEntity<>(tsRequest.getEncoded(), headers);

            // 发送请求
            ResponseEntity<byte[]> response = restTemplate.postForEntity(tsaUrl, requestEntity, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 解析响应
                TimeStampResponse tsResponse = new TimeStampResponse(response.getBody());

                // 验证响应状态
                if (tsResponse.getStatus() == PKIStatus.GRANTED) {
                    log.debug("时间戳请求成功，TSA: {}", tsaUrl);
                    return tsResponse;
                } else {
                    throw new TSPException("TSA响应状态错误: " + tsResponse.getStatus());
                }
            } else {
                throw new TSPException("HTTP请求失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("时间戳请求失败，TSA: {}", tsaUrl, e);
            throw new TSPException("时间戳请求失败: " + e.getMessage(), e);
        }
    }

}

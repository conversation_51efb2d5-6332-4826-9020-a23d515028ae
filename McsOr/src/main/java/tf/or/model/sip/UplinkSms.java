package tf.or.model.sip;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

/**
 * 上行短信验证码
 * 
 * @version V1.0 20200529 miaoyunli
 */
public class UplinkSms implements Serializable {
	private static final long serialVersionUID = -2715721542290455843L;

	@ApiModelProperty(value = "用户ID", dataType = "String", name = "userId", required = true)
	private String userId;// 用户ID 开户时返回的用户ID

	@ApiModelProperty(value = "项目编号", dataType = "String", name = "projectCode", required = true)
	private String projectCode;// 项目编号 项目编号由客户自行定义，授权的编号不能重复，但一个编号可对应多个合同

	@ApiModelProperty(value = "短信模板ID", dataType = "String", name = "smsTemplateId", required = false)
	private String smsTemplateId;// 短信模板ID 客户平台客户将模板准备好，提交给安心签管理员后，管理员会反馈模板编号

	@ApiModelProperty(value = "短信备注	对模板里面的%smsRemark%字段进行替换", dataType = "String", name = "smsRemark", required = false)
	private String smsRemark;// 短信备注 对模板里面的%smsRemark%字段进行替换

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getSmsTemplateId() {
		return smsTemplateId;
	}

	public void setSmsTemplateId(String smsTemplateId) {
		this.smsTemplateId = smsTemplateId;
	}

	public String getSmsRemark() {
		return smsRemark;
	}

	public void setSmsRemark(String smsRemark) {
		this.smsRemark = smsRemark;
	}

	@Override
	public String toString() {
		return "UplinkSms [userId=" + userId + ", projectCode=" + projectCode + ", smsTemplateId=" + smsTemplateId
				+ ", smsRemark=" + smsRemark + "]";
	}

}

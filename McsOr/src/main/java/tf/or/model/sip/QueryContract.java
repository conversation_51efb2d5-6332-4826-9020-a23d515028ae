package tf.or.model.sip;

import io.swagger.annotations.ApiModelProperty;

public class QueryContract {

	@ApiModelProperty(value = "用户ID", dataType = "String", name = "userId", required = true)
	private String userId;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Override
	public String toString() {
		return "QueryContract [userId=" + userId + "]";
	}

}

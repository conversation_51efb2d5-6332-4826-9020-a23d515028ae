package tf.or.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "文件上传实体类", description = "亚马逊S3文件系统上传实体类")
public class AmazonUploadBean {
	@ApiModelProperty(value = "文件名称", dataType = "String", name = "filename", required = true)
	private String filename;

	@ApiModelProperty(value = "是否传输文件，若为1||2则base64不为空，否则filepath不为空", dataType = "String", name = "transportFileFlag", required = true)
	private String transportFileFlag;

	@ApiModelProperty(value = "base64文件流1、需解码两次；2、解码一次；", dataType = "String", name = "base64", required = false)
	private String base64;

	@ApiModelProperty(value = "文件路径", dataType = "String", name = "filepath", required = false)
	private String filepath;

	@ApiModelProperty(value = "桶名", dataType = "String", name = "bucketName", required = false)
	private String bucketName;

	@ApiModelProperty(value = "区域", dataType = "String", name = "region", required = false)
	private String region;

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	@Override
	public String toString() {
		return "AmazonUploadBean [filename=" + filename + ", transportFileFlag=" + transportFileFlag + ", base64="
				+ base64 + ", filepath=" + filepath + ", bucketName=" + bucketName + ", region=" + region + "]";
	}

	public String getBucketName() {
		return bucketName;
	}

	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getBase64() {
		return base64;
	}

	public void setBase64(String base64) {
		this.base64 = base64;
	}

	public String getFilepath() {
		return filepath;
	}

	public void setFilepath(String filepath) {
		this.filepath = filepath;
	}

	public String getTransportFileFlag() {
		return transportFileFlag;
	}

	public void setTransportFileFlag(String transportFileFlag) {
		this.transportFileFlag = transportFileFlag;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((base64 == null) ? 0 : base64.hashCode());
		result = prime * result + ((bucketName == null) ? 0 : bucketName.hashCode());
		result = prime * result + ((filename == null) ? 0 : filename.hashCode());
		result = prime * result + ((filepath == null) ? 0 : filepath.hashCode());
		result = prime * result + ((region == null) ? 0 : region.hashCode());
		result = prime * result + ((transportFileFlag == null) ? 0 : transportFileFlag.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AmazonUploadBean other = (AmazonUploadBean) obj;
		if (base64 == null) {
			if (other.base64 != null)
				return false;
		} else if (!base64.equals(other.base64))
			return false;
		if (bucketName == null) {
			if (other.bucketName != null)
				return false;
		} else if (!bucketName.equals(other.bucketName))
			return false;
		if (filename == null) {
			if (other.filename != null)
				return false;
		} else if (!filename.equals(other.filename))
			return false;
		if (filepath == null) {
			if (other.filepath != null)
				return false;
		} else if (!filepath.equals(other.filepath))
			return false;
		if (region == null) {
			if (other.region != null)
				return false;
		} else if (!region.equals(other.region))
			return false;
		if (transportFileFlag == null) {
			if (other.transportFileFlag != null)
				return false;
		} else if (!transportFileFlag.equals(other.transportFileFlag))
			return false;
		return true;
	}

}

# PDF安全配置示例
# 修复安全漏洞后的严格验证配置

pdf:
  security:
    # 签名配置
    signature:
      algorithm: "SHA256withRSA"
      provider: "BC"
    
    # 密钥配置
    key:
      size: 2048
      algorithm: "RSA"
    
    # 证书配置
    cert:
      validityDays: 365
      issuerName: "CN=microcredchina CA, O=MicroCredChina, C=CN"
      subjectTemplate: "CN=%s, O=MicroCredChina, C=CN"
    
    # 验证配置 - 关键安全设置
    validation:
      strict: true  # 启用严格验证模式
      checkRevocation: false  # 根据用户偏好禁用撤销检查
      checkTimestamp: true
      rootCaCertPath: "/etc/ssl/certs/microcredchina-root-ca.crt"  # 根CA证书路径
      trustedCAs:  # 受信任的CA列表
        - "CN=microcredchina CA"
        - "CN=DigiCert"
        - "CN=GlobalSign"
        - "CN=Entrust"
        - "CN=VeriSign"
        - "CN=Thawte"
        - "CN=Symantec"
    
    # 时间戳配置
    tsa:
      timeout: 30000
      retryCount: 3
      retryDelay: 1000
    
    # 缓存配置
    cache:
      ttl: 3600
      maxSize: 1000
      enabled: true
    
    # 审计配置
    audit:
      enabled: true
      level: "INFO"
      performanceLoggingEnabled: true
    
    # 安全配置
    security:
      maxFileSize: "100MB"
      allowedExtensions: "pdf"
      scanEnabled: false

# 日志配置
logging:
  level:
    tf.or.pdf.core.CertificateValidator: INFO
    tf.or.pdf.core.PDFSignatureValidator: INFO
    tf.or.pdf.security: INFO

knife4j:
  production: false
debug: true
cron.micronum: "0 0/1 * * * ?"
http:
  encoding:
    force: true
management:
  endpoint:
    health:
      show-details: "always"
  endpoints:
    web:
      base-path: "/actuator"
      exposure:
        include: "*"

# Dubbo
dubbo:
  registry:
    address: zookeeper://127.0.0.1:2048
    id: local
  host: 127.0.0.1
  port: 28813

myProps:
  snowflake:
    workerid: 1
    datacenterid: 1
  downlownPath: /tmp/McsOr
  whiteUrls: 127.0.0.1,*************
  newFilePath: /tmp/McsOr
  templetFilePath: /tmp/McsOr
  downloadUrl: http://localhost:8061/McsOr/download/
  smsProp:
    url: http://www.dh3t.com
    connectTimeout: 3000
    readTimeout: 10000
  sipProp:
    url: ************:9443
    platId: A08C2683D83E5CBBE05312016B0AA4E7
    jksPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/jks/anxinsign.jks
    jksPassword: 73628506
    alias: anxinsign
    channel: Test
    connectTimeout: 3000
    readTimeout: 10000
    userAgent: TrustSign FEP
    downlownPath: /tmp/McsOr
    face:
      url: https://dsptest.cpcn.com.cn/DSPLivenessWeb/InterfaceVI
      keystore: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/face/test.pfx
      password: cfca1234
      publicKey:  /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/face/dsptest.cer
      frontUrl: http://wechat.microcredchina.com/microweb/verifycode?tnsx={tnsx}&serid={serid}
      backUrl: http://**************:8061/McsOr/api/sipface/sip2319
      institutionID: 100513
  sipProp2:
    url: ************:9443
    platId: 97AA0CF31E5D0357E05312016B0A5BC0
    jksPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/jks/anxinsignSc.jks
    jksPassword: 8677526
    alias: anxinsign
    channel: Test
    connectTimeout: 3000
    readTimeout: 10000
    userAgent: TrustSign FEP
    downlownPath: /tmp/McsOr
    face:
      url: https://dsptest.cpcn.com.cn/DSPLivenessWeb/InterfaceVI
      keystore: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/face/test.pfx
      password: cfca1234
      publicKey:  /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/sip/face/dsptest.cer
      frontUrl: http://wechat.microcredchina.com/microweb/verifycode?tnsx={tnsx}&serid={serid}
      backUrl: http://**************:8061/McsOr/api/sipface2/sip2319
      institutionID: 100513
  aws:
    usekey: false
    access_key_id: ********************
    secret_access_key: KRS4wtISqdBK/ybWB1OSqF772Nf/J57C55vOVnO6
    s3:
      bucket: testmcs
      bucketSecurity: testmcs
      securityKeyName: MIS/Arrear/20250403/N10101080202203000051/20250403164217784_897263/sensitive_data_cipher.key
      region: cn-northwest-1
  kdProp:
    url: https://microcredchina.ik3cloud.com/K3Cloud/
    acctId: 20170607203727
    username: Tongfang
    password: ExY1V#fW
    lcid: 2052
  qccProp:
    url: http://api.qichacha.com/
    appkey: 9f083d7f9dbd44c090bfd7a6a0c0573d
    seckey: BCDAE6B77941B9CCF6008A82B515A399
  cpcnProp:
    configPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/cpcn/config/
    cpcnFilePath: /tmp/McsOr
  baiduProp:
    appId: 22736510
    apiKey: rytuFveP2RdpY29CIpvHl7PE
    secretKey: k3d0iz2qlAQprskSxLaVWSNOF8vF7Akf
spring:
  redis:
    database: 1
    host: *************
    port: 6666
    password: mxmcs
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
    timeout: 10000
  thymeleaf:
    prefix: classpath:/templates/
  aop:
    proxy-target-class: true
  datasource:
    url: ***************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: S4FK7EK52M73L2BK33H5EI6C
    password: S4FK7EK52M73L2BK33H5EI6C
    type: com.alibaba.druid.pool.DruidDataSource
    validationQuery: select 1
    maxWait: 10000
    maxActive: 20
    initialSize: 10
    multi:
  mail:
#    host: smtp.mxhichina.com,smtp.mxhichina.com
#    username: <EMAIL>,<EMAIL>
#    password: Mx@123456!,Mx@123456!
    alias: 美兴小贷
    accountName: <EMAIL>
    accessKeyId: LTAI5tQhuTPzyBWse7i24Cxe
    accessKeySecret: ******************************
  rabbitmq:
    host: *************
    port: 5672
    username: admin
    password: adminpwd
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: manual # 手动确认模式
        prefetch: 10
server:
  port: 8061
  servlet:
    contextPath: /McsOr
swagger:
  title: 外联站点 RESTful APIs
  description: 美兴信贷外联站点
  version: 1.0
  termsOfServiceUrl: http://www.tfrunning.com.cn
  contact:
    name: TF-developer
    url: http://www.tfrunning.com.cn
    mail: <EMAIL>

#Rabbitmq配置


#汇法接口配置
huifa:
    url: https://testapi.lawxp.com/s.aspx
    appkey: 60317924832cc863
    conid: 12026
    stype: 1
xwsc:
   configPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwsc/config/config.properties
   keyPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwsc/config/UATH3f6bac360ee64bc3af7696ae8858204a.sm2
   cerPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwsc/config/SM2oca31Test.cer

xwnc:
   configPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwnc/config/config.properties
   keyPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwnc/config/UATHa01ac6cf36e94341977ac08cc557db54.sm2
   cerPath: /Users/<USER>/IdeaProjects/meixing/mxmcs/config/mxmcs/McsOr/qa/xwnc/config/SM2oca31Test.cer


#朴道征信
pd:
  urlPath: https://api-test.pudaocredit.com.cn/v1/credit/at/prodservice

  signPriKeyPath1: pd/sc/20230801174852_SIGN_PRIVATE.pfx
  encryptPriPath1: pd/sc/private.data
  pdEncryptCertPath1: pd/sc/241c88ca2f9987a654ffeace40eb2123_20271205_pdencrypt.cer
  appKey1: 77805f3442404f20aa5b1daf82fd86a0
  orgnSgnSn1: 41ecce9dcd849af04db20bb3930d416c
  orgnEncrpSn1: 53528470a62925cf8492a6502a537646
  pdEncrpSn1: 241c88ca2f9987a654ffeace40eb2123
  scOccOrgn: 91510100564465217B


  signPriKeyPath2: pd/nc/20230801174852_SIGN_PRIVATE.pfx
  encryptPriPath2: pd/nc/private.data
  pdEncryptCertPath2: pd/nc/241c88ca2f9987a654ffeace40eb2123_20271205_pdencrypt.cer
  appKey2: 77805f3442404f20aa5b1daf82fd86a0
  orgnSgnSn2: 41ecce9dcd849af04db20bb3930d416c
  orgnEncrpSn2: 53528470a62925cf8492a6502a537646
  pdEncrpSn2: 241c88ca2f9987a654ffeace40eb2123
  ncOccOrgn: 91510100564465217B

#同盾接口配置
td:
  url: https://apitest.tongdun.cn/bodyguard/apply/v5.1
  partnerCode: ncmxxd
  partnerKey: ba44ea96484d4651917880971479355a
  appName: ncmxxd_web

chuanglan:
  sendUrl: https://smssh1.253.com/msg/variable/json
  reportUrl: https://smssh1.253.com/msg/pull/report

ALIBABA_CLOUD_ACCESS_KEY_ID: LTAI5tKw3doYCK3T2nDueAKB
ALIBABA_CLOUD_ACCESS_KEY_SECRET: ******************************

huatang:
  sendUrl: https://api.cqhuatang.com/SMSServer/smssend
  reportUrl: https://api.cqhuatang.com/SMSServer/smsstatus
  sendFullTextSms: https://api.cqhuatang.com/SMSServer/sendFullTextSms

# PDF数字签名项目配置
pdf:
  # PDF文件基础路径
  base-path: "/Users/<USER>/Downloads/pdf"

  # 根CA证书路径
  root-ca-cert-path: "${pdf.base-path}/microcredchina_ca.crt"

  # 签名相关配置
  signature:
    # 证书库路径（支持动态日期）
    keystore-path: "${pdf.base-path}/microcredchina_pdf_signer_20250723.p12"
    # 证书库密码
    keystore-password: "microcredchina_pdf_signer"
    # 签名者名称
    signer-name: "美兴中国"
    # 签名地点
    location: "中国四川"
    # 签名原因
    reason: "合同签署"
    # 证书别名
    alias: "pdf.signer"

  # 文字水印相关配置
  text-watermark:
    text: "美兴中国"
    # 中文字体文件路径
    font-path: "${pdf.base-path}/SourceHanSerifCN-Heavy.ttf"
    font-size: 36
    transparency: 0.2
    rotation: 45.0
    horizontal-spacing: 150
    vertical-spacing: 150

  # 图片水印相关配置
  image-watermark:
    image-path: "${pdf.base-path}/watermark.png"
    image-scale: 0.3
    transparency: 0.1
    rotation: 0.0
    # 水印横向间距
    horizontal-spacing: 225
    # 水印纵向间距
    vertical-spacing: 210

  # 证书管理配置
  certificate:
    # 默认密钥长度
    key-length: 2048
    # 签名算法
    signature-algorithm: "SHA256withRSA"
    # 密钥算法
    key-algorithm: "RSA"
    # 证书存储路径
    store-path: "${pdf.base-path}/certificates/"

    # CA证书配置
    ca:
      # 有效期（天）- 100年
      validity-days: 36500
      # 国家代码
      country: "CN"
      # 省份
      state: "SiChuan"
      # 城市
      locality: "ChengDu"
      # 组织名称
      organization: "microcredchina"
      # 组织单位
      organizational-unit: "microcredchina"
      # 通用名称
      common-name: "microcredchina CA"
      # 邮箱地址
      email-address: "<EMAIL>"
      # CA证书文件名
      cert-file-name: "microcredchina_ca.crt"
      # CA私钥文件名
      key-file-name: "microcredchina_ca.key"

    # 用户证书配置
    user:
      # 有效期（天）- 1年
      validity-days: 365
      # 国家代码
      country: "CN"
      # 省份
      state: "SiChuan"
      # 城市
      locality: "ChengDu"
      # 组织名称
      organization: "microcredchina"
      # 组织单位
      organizational-unit: "microcredchina"
      # 通用名称模板
      common-name-template: "microcredchina PDF Signer"
      # 邮箱地址
      email-address: "<EMAIL>"
      # 签名者邮箱
      signer-email: "<EMAIL>"
      # PKCS12密码
      pkcs12-password: "microcredchina_pdf_signer"
      # PKCS12别名
      pkcs12-alias: "pdf.signer"

  # PDF安全配置
  security:
    # 签名配置
    signature:
      algorithm: SHA256withRSA
      provider: BC

    # 密钥配置
    key:
      size: 2048
      algorithm: RSA

    # 证书配置
    cert:
      validity-days: 365
      issuer-name: "CN=PDF Security CA, O=microcredchina, C=CN"
      subject-template: "CN=%s, O=microcredchina, C=CN"

    # 时间戳配置
    tsa:
      timeout: 30000
      retry-count: 3
      retry-delay: 1000

    # 验证配置
    validation:
      strict: false
      check-revocation: true
      check-timestamp: true

    # 审计配置
    audit:
      enabled: true
      level: INFO
      performance-logging-enabled: true

    # 安全配置
    security:
      max-file-size: "100MB"
      allowed-extensions: "pdf"
      scan-enabled: false
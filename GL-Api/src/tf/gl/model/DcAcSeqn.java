/*
 * Powered By [ext]
 * Web Site: http://www.google.com
 * Since 2012 
 */

package tf.gl.model;

import java.util.*;
import java.io.Serializable;
import org.codehaus.xfire.aegis.type.java5.XmlElement;
import javax.persistence.*;
import tf.gl.model.key.*;

@Entity
@IdClass(DcAcSeqnPK.class)@Table
public class DcAcSeqn implements Serializable{
	
	private java.lang.String acCode;
	private java.lang.Long acSeqn;
	private java.lang.Long bankid;
	
	//---------------------------------Extra attributes below which don't belong to database table--------------------
	
	public void setAcCode(java.lang.String value) {
		this.acCode = value;
	}
	
	@Id
	@Column(length=32,desc="AC_CODE")
	public java.lang.String getAcCode() {
		return this.acCode;
	}
	
	public void setAcSeqn(java.lang.Long value) {
		this.acSeqn = value;
	}
	@XmlElement(nillable = true)
	
	@Column(length=10,desc="AC_SEQN")
	public java.lang.Long getAcSeqn() {
		return this.acSeqn;
	}
	
	public void setBankid(java.lang.Long value) {
		this.bankid = value;
	}
	@XmlElement(nillable = true)
	@Id
	@Column(length=6,desc="BANKID")
	public java.lang.Long getBankid() {
		return this.bankid;
	}
	
	
	//---------------------------------Extra attributes' getters and setters below which don't belong to database table--------------------
	

}


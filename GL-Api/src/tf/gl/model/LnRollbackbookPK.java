package tf.gl.model;

import javax.persistence.*;
import java.io.Serializable;

   /**
    * ln_rollbackbook PK
    * 2021-2-22 11:54:4 Administrator
    */ 

public class LnRollbackbookPK implements Serializable{
	private static final long serialVersionUID = 1363698548239245312L;
	private Long bankid;
	private Long acId;
	private String origtxdate;
	private Long origtraceno;

	public void setBankid(Long bankid){
		this.bankid=bankid;
	}

	@Column(length=19,name = "BANKID")
	public Long getBankid(){
		return bankid;
	}


	public void setAcId(Long acId){
		this.acId=acId;
	}

	@Column(length=19,name = "AC_ID")
	public Long getAcId(){
		return acId;
	}


	public void setOrigtxdate(String origtxdate){
		this.origtxdate=origtxdate;
	}

	@Column(length=8,name = "ORIGTXDATE")
	public String getOrigtxdate(){
		return origtxdate;
	}


	public void setOrigtraceno(Long origtraceno){
		this.origtraceno=origtraceno;
	}

	@Column(length=19,name = "ORIGTRACENO")
	public Long getOrigtraceno(){
		return origtraceno;
	}

}

package tf.gl.tx.ln;
import java.math.BigDecimal;
import java.util.ArrayList;
/**
 * 处理内容:手工计提上月所有贷款的损失准备
 * 更新gl_lnlossprovbook
 * 在手工计提之前的上个月末同时也是季度末已经更新了该表保留了计算需要的时点数据
 * <AUTHOR>
 * @file spL7000.java
 * @date 20210121
 * @history v1.0 20210121
 * @company:tfrunning
 */
import java.util.HashMap;

import tf.brc.gl.glException;
import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.gl.dao.GlLnLossprovbookDao;
import tf.gl.dao.LnMstDao;
import tf.gl.dao.LnParmDao;
import tf.gl.dao.MdmAcRelDao;
import tf.gl.dc.dcTools;
import tf.gl.dc.glDcTrance;
import tf.gl.impl.glSubTxHandler;
import tf.gl.main.glsitemain;
import tf.gl.model.GlLnLossprovbook;
import tf.gl.model.LnMst;
import tf.gl.model.LnParm;
import tf.gl.model.MdmAcRel;
import tf.gl.service.impl.GLServiceImpl;
import tf.gl.tx.tools.Arith;
import tf.gl.tx.tools.DateTools;
import tf.gl.tx.tools.PubLnUtil;
import tf.gl.tx.trance.AmtofMc;


public class SpL7000 implements glSubTxHandler {
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	//@Autowired
	@Override
	public ParmsofTrsDefinition subGLTxDealer(ParmsofTrsDefinition ptd) {
		//处理贷款业务账lnmst
		String ErrorMsg = "";
		try{
			DaoTemplate dtAR = new DaoTemplateImpl();
			long lbankid=ptd.getBankIDofTrs();
			dtAR.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			
			String provdate=ptd.getParmValuebyName("mprovdate").toString();//要进行计提的月份(或者重新计提)
			String swkDate=ptd.getParmValuebyName("workdate").toString();
			
			
			log.info("总账系统交易日志："+":手工计提上月损失准备-执行时间:["+ptd.getParmValuebyName("workdate").toString()+"]开始进行!");
			String sSql="select * from gl_ln_lossprovbook where  provdate='"+provdate+"' and bankid="+ptd.getBankIDofTrs()+" and provamt is null and provrate is null";
			ArrayList dealList = (ArrayList) dtAR.getMapListBySql(sSql);
			for (int i = 0; i < dealList.size(); i++) {
				HashMap hmAcid=(HashMap)dealList.get(i);
				long acid=Long.parseLong(hmAcid.get("acId").toString());
				double currbal=Double.parseDouble(hmAcid.get("bal").toString());
				int ioverday=Integer.parseInt(hmAcid.get("overdays").toString());
//				/上月计提损失准备
				//double lstprovamt=Double.parseDouble(hmAcid.get("lstprvamt").toString());
				//检索贷款主业务文件信息
				LnMst lm=new LnMst();
				LnMstDao lmd=new LnMstDao();
				lmd.setExternalSession(dtAR.getSession());
				lm.setAcId(acid);
				lm.setAcSeqn(0);
				lm.setBankid(lbankid);
				lm=lmd.getEntityByPK(lm);
				if(lm==null){
					log.error("贷款账号"+acid+"主文件检错错误没有该账户-贷款账户定义错误");
					log.error("总账系统交易日志：" + "还款账号"+acid+"主文件检错错误没有该账户");
					throw new glException("还款账号"+acid+"主文件检错错误没有该账户");				
				}
				/*************获取贷款产品参数***************/
				LnParm lnp = new LnParm();
				lnp.setBankid(lbankid);
				lnp.setPrdtNo(lm.getPrdtNo());
				LnParmDao lnpDao = new LnParmDao();
				lnpDao.setExternalSession(dtAR.getSession());
				LnParm lnpData=lnpDao.getEntityByPK(lnp);
				if(lnpData==null){
					ErrorMsg="获取ln_parm产品参数失败:prdtNo【"+lm.getPrdtNo()+"】";
					log.error(ErrorMsg);
					throw new Exception(ErrorMsg);
				}
				
				/*************获取账户介质对照表参数***************/
				MdmAcRel mar = new MdmAcRel();
				mar.setBankid(lbankid);
				mar.setAcId(acid);
				mar.setAcSeqn(0);
				MdmAcRelDao marDao = new MdmAcRelDao();
				marDao.setExternalSession(dtAR.getSession());
				MdmAcRel marData=marDao.getEntityByPK(mar);
				if(marData==null){
					ErrorMsg="获取mdm_ac_rel账户介质表参数失败:acId【"+acid+"】,acSeqn【"+0+"】,bankid【"+lbankid+"】";
					log.error(ErrorMsg);
					throw new Exception(ErrorMsg);
				}
				//判断业务发生类型1正常2重组3疫情期间重组
				String soccutype="";
				String sprdt=lm.getPrdtNo().toString();
				String opndate=lm.getOpnDate().toString();//重组放款日期
				double dbal=lm.getBal();//余额
				if(sprdt.startsWith("4")){
					ptd.setStringParm("sceno", "L901");
					if(DateTools.compareDate(opndate, "********")>0&&DateTools.compareDate("********", opndate)>0)
						soccutype="3";
					else soccutype="2";
				}else{
					ptd.setStringParm("sceno", "L900");
					soccutype="1";
				}
				double dratio=PubLnUtil.getLossProvRatio(soccutype, ioverday, dtAR);
				log.info("贷款acid:"+lm.getAcId()+"的计提比例是:"+dratio);
				
				
				GlLnLossprovbook gllpb=new GlLnLossprovbook();
				GlLnLossprovbookDao gllpbd=new GlLnLossprovbookDao();
				gllpbd.setExternalSession(dtAR.getSession());
				
				gllpb.setBankid(lbankid);
				gllpb.setAcId(lm.getAcId());
//				gllpb.setBal(new BigDecimal(lm.getBal().toString()));
//				gllpb.setCifid(lm.getCifid());
				gllpb.setProvdate(provdate);
				gllpb=gllpbd.getEntityByPK(gllpb);
				
				double dpoibal=gllpb.getBal().doubleValue();
				double dpoiintst=gllpb.getNmintst().doubleValue();
				double dpoiovintst=gllpb.getOvintst().doubleValue();
				double dlossprovamt=Arith.mul(Arith.adds(dpoibal,dpoiintst,dpoiovintst), dratio/100);
				
				gllpb.setProvamt(new BigDecimal(String.valueOf(dlossprovamt)));
				gllpb.setProvrate(new BigDecimal(String.valueOf(dratio)));
//				gllpb.setVchno(lm.getPactNo());
				gllpbd.update(gllpb);
				
				
				
				String sprovdate=gllpb.getProvdate();//当前计提日期
				
//				long oRTranceNo = ptd.getTraceNo();
				long oRTranceNo = GLServiceImpl.getPubTrsTranceNo(ptd);
				if (oRTranceNo == 0) {
					ErrorMsg="获取流水号失败!";
					log.error(ErrorMsg);
					throw new Exception(ErrorMsg);
				}
				
				ptd.setTransNo(oRTranceNo);

				AmtofMc aom = new AmtofMc();
				aom.setAmtValbyTypeCode("Q", dlossprovamt);
				aom.setAmtValbyTypeCode("R", 0d);
				
//				ptd.setStringParm("txdate",sysDate);
				ptd.setStringParm("tracests", "0");
//				ptd.setStringParm("workdate",sysDate);
				ptd.setStringParm("txopnbrno", lm.getOpnBrNo());
				ptd.setStringParm("txbrno", lm.getOpnBrNo());
				ptd.setStringParm("chopnbrno", lm.getOpnBrNo());
				ptd.setStringParm("txsts", "0");
				ptd.setStringParm("acwrkind3", "0");
				ptd.setObjectParm("traceamt", aom);
				ptd.setObjectParm("amt", dlossprovamt);
				ptd.setObjectParm("curr", "CNY");
				ptd.setStringParm("addind", "1");
				ptd.setStringParm("tracests", "0");
				ptd.setStringParm("noteno", "0");
				ptd.setStringParm("brf", "手工计提季末贷款减值损失准备计提");
				ptd.setStringParm("pactno", lm.getPactNo());
				ptd.setStringParm("ctind", "2");
				ptd.setStringParm("acwrkind2", "1");
				ptd.setStringParm("acwrkind3", "1");
				ptd.setStringParm("hstind", "1");
				ptd.setStringParm("dccode", lnpData.getDcCode());	
				ptd.setStringParm("acno", marData.getAcNo());
				ptd.setStringParm("prdtno", lm.getPrdtNo());
				ptd.setStringParm("acid", lm.getAcId().toString());
				ptd.setStringParm("acseqn", lm.getAcSeqn().toString());
				ptd.setTrsOpnInst(lm.getOpnBrNo());
				//直接登记会计账
				glDcTrance.wrtAccDcSOB(ptd);
				dcTools.genDcAccbyPendAcc(dtAR, lm.getBankid(), swkDate, String.valueOf(ptd.getTraceNo()));
				//上个月发生平移则不冲回
//				String sSql2="select count(*) from  ln_loss where ac_id='"+lm.getAcId().toString()+"' and bankid="+lbankid+" and is_back='0' and tx_type  in('3') and tx_date like '"+provdate.substring(0,6)+"%'";
//				int cnt=Integer.parseInt(String.valueOf(dtAR.getUniqueValueBySql(sSql2)));
//				if(cnt==0) {
//
//					//上个月发生了核销和销户则登记ln_loss流水号
//					String sSql3="select count(*) from  ln_loss where ac_id='"+lm.getAcId().toString()+"' and bankid="+lbankid+" and is_back='0' and tx_type   in('1','2') and tx_date like '"+provdate.substring(0,6)+"%'";
//					int cnt2=Integer.parseInt(String.valueOf(dtAR.getUniqueValueBySql(sSql3)));
//				//冲回上月计提金额
//				AmtofMc aomrb = new AmtofMc();
//				//如果计提日期和放款日期在同一个月并且为重组需要冲回上个月损失准备为正常贷款的
//				//add by huoxiujie ********
//				String getPrdtNo="select prdt_no from  ln_mst where pact_no in(select oldvchno from  Af_Loanrest_Info  where vchno='"+lm.getPactNo()+"' )";
//				String prdtNo=String.valueOf(dtAR.getUniqueValueBySql(getPrdtNo));
//				//end by huoxiujie
//				if(sprovdate.substring(0, 6).equals(opndate.substring(0, 6))&&prdtNo.startsWith("4")){
//					ptd.setStringParm("sceno", "L901");
//					aomrb.setAmtValbyTypeCode("Q", Arith.mul(-1, lstprovamt));//由R改为Q
//					aomrb.setAmtValbyTypeCode("R", 0);
//				}else{
//					aomrb.setAmtValbyTypeCode("Q", Arith.mul(-1, lstprovamt));
//					aomrb.setAmtValbyTypeCode("R", 0);
//					ptd.setStringParm("sceno", "L900");
//				}
//
//				oRTranceNo = (int) GLServiceImpl.getPubTrsTranceNo(ptd);
//
//				if (oRTranceNo == 0) {
//					ErrorMsg="获取流水号失败!";
//					log.error(ErrorMsg);
//					throw new Exception(ErrorMsg);
//				}
//
//				ptd.setTransNo(oRTranceNo);
//				ptd.setObjectParm("traceamt", aomrb);
//
//
//				ptd.setStringParm("brf", "冲销-上月月末贷款减值损失准备计提");
//				glDcTrance.wrtAccDcSOB(ptd);
//				dcTools.genDcAccbyPendAcc(dtAR, lm.getBankid(), swkDate, String.valueOf(oRTranceNo));
//				if(cnt2>0) {
//					//更新登记表
//					String updateSql="update ln_loss set is_back='1',back_date='"+swkDate+"', tx_trace_no='"+oRTranceNo+"'  where ac_id='"+acid+"' and bankid="+lm.getBankid()+"";
//					dtAR.executeSql(updateSql);
//				}
//				}
			}
			//处理平移冲销上月计提
//			String sSql2="select * from  ln_loss where bankid="+lbankid+" and is_back='0' and tx_type  in('3') and tx_date like '"+provdate.substring(0,6)+"%'";
//			ArrayList dealList2 = (ArrayList) dtAR.getMapListBySql(sSql2);
//			for (int i = 0; i < dealList2.size(); i++) {
//				HashMap hmAcid=(HashMap)dealList2.get(i);
//				long acid=Long.parseLong(hmAcid.get("acId").toString());
//                String txdate=String.valueOf(hmAcid.get("txDate"));
//                String opnBrNo=String.valueOf(hmAcid.get("opnBrNo"));
//				//检索贷款主业务文件信息
//				LnMst lm=new LnMst();
//				LnMstDao lmd=new LnMstDao();
//				lmd.setExternalSession(dtAR.getSession());
//				lm.setAcId(acid);
//				lm.setAcSeqn(0);
//				lm.setBankid(lbankid);
//				lm=lmd.getEntityByPK(lm);
//				if(lm==null){
//					log.error("贷款账号"+acid+"主文件检错错误没有该账户-贷款账户定义错误");
//					log.error("总账系统交易日志：" + "还款账号"+acid+"主文件检错错误没有该账户");
//					throw new glException("还款账号"+acid+"主文件检错错误没有该账户");
//				}
//				/*************获取贷款产品参数***************/
//				LnParm lnp = new LnParm();
//				lnp.setBankid(lbankid);
//				lnp.setPrdtNo(lm.getPrdtNo());
//				LnParmDao lnpDao = new LnParmDao();
//				lnpDao.setExternalSession(dtAR.getSession());
//				LnParm lnpData=lnpDao.getEntityByPK(lnp);
//				if(lnpData==null){
//					ErrorMsg="获取ln_parm产品参数失败:prdtNo【"+lm.getPrdtNo()+"】";
//					log.error(ErrorMsg);
//					throw new Exception(ErrorMsg);
//				}
//
//				/*************获取账户介质对照表参数***************/
//				MdmAcRel mar = new MdmAcRel();
//				mar.setBankid(lbankid);
//				mar.setAcId(acid);
//				mar.setAcSeqn(0);
//				MdmAcRelDao marDao = new MdmAcRelDao();
//				marDao.setExternalSession(dtAR.getSession());
//				MdmAcRel marData=marDao.getEntityByPK(mar);
//				if(marData==null){
//					ErrorMsg="获取mdm_ac_rel账户介质表参数失败:acId【"+acid+"】,acSeqn【"+0+"】,bankid【"+lbankid+"】";
//					log.error(ErrorMsg);
//					throw new Exception(ErrorMsg);
//				}
//
//				/*GlLnLossprovbook gllpb=new GlLnLossprovbook();
//				GlLnLossprovbookDao gllpbd=new GlLnLossprovbookDao();
//				gllpbd.setExternalSession(dtAR.getSession());
//
//				gllpb.setBankid(lbankid);
//				gllpb.setAcId(lm.getAcId());
////				gllpb.setBal(new BigDecimal(lm.getBal().toString()));
////				gllpb.setCifid(lm.getCifid());
//				gllpb.setProvdate(txdate);
//				gllpb=gllpbd.getEntityByPK(gllpb);
////				gllpb.setVchno(lm.getPactNo());
//				gllpbd.update(gllpb);
//
//
//
//				String sprovdate=gllpb.getProvdate();//当前计提日期*/
//
////				long oRTranceNo = ptd.getTraceNo();
//				long oRTranceNo = GLServiceImpl.getPubTrsTranceNo(ptd);
//				if (oRTranceNo == 0) {
//					ErrorMsg="获取流水号失败!";
//					log.error(ErrorMsg);
//					throw new Exception(ErrorMsg);
//				}
//
//				ptd.setTransNo(oRTranceNo);
//
//				AmtofMc aom = new AmtofMc();
//				//aom.setAmtValbyTypeCode("Q", dlossprovamt);
//				//aom.setAmtValbyTypeCode("R", 0d);
//
////				ptd.setStringParm("txdate",sysDate);
//				ptd.setStringParm("tracests", "0");
////				ptd.setStringParm("workdate",sysDate);
//				ptd.setStringParm("txopnbrno", opnBrNo);
//				ptd.setStringParm("txbrno", opnBrNo);
//				ptd.setStringParm("chopnbrno", opnBrNo);
//				ptd.setStringParm("txsts", "0");
//				ptd.setStringParm("acwrkind3", "0");
//				//ptd.setObjectParm("traceamt", aom);
//				//ptd.setObjectParm("amt", dlossprovamt);
//				ptd.setObjectParm("curr", "CNY");
//				ptd.setStringParm("addind", "1");
//				ptd.setStringParm("tracests", "0");
//				ptd.setStringParm("noteno", "0");
//				ptd.setStringParm("brf", "冲销-上月月末贷款减值损失准备计提");
//				ptd.setStringParm("pactno", lm.getPactNo());
//				ptd.setStringParm("ctind", "2");
//				ptd.setStringParm("acwrkind2", "1");
//				ptd.setStringParm("acwrkind3", "1");
//				ptd.setStringParm("hstind", "1");
//				ptd.setStringParm("dccode", lnpData.getDcCode());
//				ptd.setStringParm("acno", marData.getAcNo());
//				ptd.setStringParm("prdtno", lm.getPrdtNo());
//				ptd.setStringParm("acid", lm.getAcId().toString());
//				ptd.setStringParm("acseqn", lm.getAcSeqn().toString());
//				ptd.setTrsOpnInst(opnBrNo);
//
//				//冲回上月计提金额
//				String slstDate=DateTools.getLastDayOflstMonth(provdate);
//				log.info("上个月月末日期是:"+slstDate);
//				GlLnLossprovbook lmgllpb=new GlLnLossprovbook();
//				GlLnLossprovbookDao lmgllpbd=new GlLnLossprovbookDao();
//				lmgllpbd.setExternalSession(dtAR.getSession());
//				lmgllpb.setBankid(lbankid);
//				lmgllpb.setAcId(lm.getAcId());
//				lmgllpb.setProvdate(slstDate);
//				lmgllpb=lmgllpbd.getEntityByPK(lmgllpb);
//				//如果是第一次计提需要判断是否是重组过后的
//				//如果是第一次计提需要判断是否是重组过后的
//				double lstamt=0d;
//				if(lmgllpb==null){
//					log.info("当前业务重组判断:"+lm.getPrdtNo());
//
//					if(lm.getPrdtNo().startsWith("4")){//如果是重组第一个月,找到重组前历史业务acid
//						String origvch="";
//						//重新定义重组贷款原始业务借据好的取值方式
//						/*int ibidx=0;
//						if(lm.getOpnBrNo().startsWith("1010")){//南充
//							ibidx=lm.getPactNo().indexOf("N");
//							if(ibidx==-1)//如果是历史数据
//								ibidx=lm.getPactNo().indexOf("L");//历史数据借据以L开头
//							origvch=lm.getPactNo().substring(ibidx);
//
//						}else{
//							ibidx=lm.getPactNo().indexOf("S");
//							if(ibidx==-1)//如果是历史数据
//								ibidx=lm.getPactNo().indexOf("L");//历史数据借据以L开头
//							origvch=lm.getPactNo().substring(ibidx);
//						}
//						String selSql="select ac_id from ln_mst where pact_no in('"+origvch+"','"+origvch+"-1') and opn_br_no='"+lm.getOpnBrNo()+"' and cifid='"+lm.getCifid()+"'";
//						log.info("检索新发放重组贷款的历史业务sql:"+selSql);
//						String origacid=dtAR.getUniqueValueBySql(selSql).toString();
//						log.info("检索新发放重组贷款的历史业务得到的acid:"+origacid);
//						lmgllpb=new GlLnLossprovbook();
//						lmgllpb.setAcId(Long.parseLong(origacid));
//						lmgllpb.setBankid(lbankid);
//						lmgllpb.setProvdate(slstDate);
//						lmgllpb=lmgllpbd.getEntityByPK(lmgllpb);
//						if(lmgllpb==null){
//							log.info("重组贷款的原始业务未找到!");
////							continue;
//							//gllpb.setLstprvamt(new BigDecimal("0"));
//						}else{
//							double lstamt=lmgllpb.getProvamt().doubleValue();
//							//gllpb.setLstprvamt(lmgllpb.getProvamt());
//						}*/
//						String getcntSql="select count(*) from  ac_businessvch a,Gl_Ln_Lossprovbook b where contno in(select contno from ac_businessvch  where loanac_id='"+lm.getAcId()+"' "
//								+ " and bankid="+lbankid+") and a.bal_instcode='"+lm.getOpnBrNo()+"' and a.cifid='"+lm.getCifid()+"' and a.loanac_id=b.ac_id "
//								+ "and a.bankid=b.bankid and b.bankid="+lbankid+" and b.provdate='"+slstDate+"'";
//						int sqlcnt=Integer.parseInt(dtAR.getUniqueValueBySql(getcntSql).toString());
//						if(sqlcnt==0) {
//							lstamt=0d;
//						}else {
//						String selSql="select nvl(b.provamt,0) from  ac_businessvch a,Gl_Ln_Lossprovbook b where contno in(select contno from ac_businessvch  where loanac_id='"+lm.getAcId()+"' "
//								+ " and bankid="+lbankid+") and a.bal_instcode='"+lm.getOpnBrNo()+"' and a.cifid='"+lm.getCifid()+"' and a.loanac_id=b.ac_id "
//								+ "and a.bankid=b.bankid and b.bankid="+lbankid+" and b.provdate='"+slstDate+"'";
//					 double provamt=Double.parseDouble(dtAR.getUniqueValueBySql(selSql).toString());
//					  lstamt=provamt;}
//					}else{
//						 lstamt=0d;
//					}
//
//				}else{
//					 lstamt=lmgllpb.getProvamt().doubleValue();
//					//上月计提
//					//gllpb.setLstprvamt(lmgllpb.getProvamt());
//				}
//				/*double lstamt=0d;
//				if(lmgllpb!=null)
//					lstamt=lmgllpb.getProvamt().doubleValue();
//				else
//					lstamt=0d;*/
//				AmtofMc aomrb = new AmtofMc();
//				aomrb.setAmtValbyTypeCode("Q", Arith.mul(-1, lstamt));
//				//add by huoxiujie ********
//				String getPrdtNo="select prdt_no from  ln_mst where pact_no in(select oldvchno from  Af_Loanrest_Info  where vchno='"+lm.getPactNo()+"' )";
//				String prdtNo=String.valueOf(dtAR.getUniqueValueBySql(getPrdtNo));
//				//end by huoxiujie
//				if(txdate.substring(0, 6).equals(lm.getOpnDate().substring(0, 6))&&prdtNo.startsWith("4")){
//					ptd.setStringParm("sceno", "L901");
//					aomrb.setAmtValbyTypeCode("Q", Arith.mul(-1, lstamt));//由R改为Q
//					aomrb.setAmtValbyTypeCode("R", 0);
//				}else{
//					aomrb.setAmtValbyTypeCode("Q", Arith.mul(-1, lstamt));
//					aomrb.setAmtValbyTypeCode("R", 0);
//					ptd.setStringParm("sceno", "L900");
//				}
//
//				oRTranceNo = (int) GLServiceImpl.getPubTrsTranceNo(ptd);
//
//				if (oRTranceNo == 0) {
//					ErrorMsg="获取流水号失败!";
//					log.error(ErrorMsg);
//					throw new Exception(ErrorMsg);
//				}
//
//				ptd.setTransNo(oRTranceNo);
//				ptd.setObjectParm("traceamt", aomrb);
//				ptd.setObjectParm("amt", Arith.mul(-1, lstamt));
//
//				ptd.setStringParm("brf", "冲销-上月月末贷款减值损失准备计提");
//				glDcTrance.wrtAccDcSOB(ptd);
//				dcTools.genDcAccbyPendAcc(dtAR, lm.getBankid(), swkDate, String.valueOf(oRTranceNo));
//				//更新登记表
//				String updateSql="update ln_loss set is_back='1',back_date='"+swkDate+"', tx_trace_no='"+oRTranceNo+"'  where ac_id='"+acid+"' and bankid="+lm.getBankid()+"";
//				dtAR.executeSql(updateSql);
//			}

			ptd.setRespCode("0000","SpL7000");
			log.info("总账系统交易日志："+":手工计提:["+ptd.getParmValuebyName("mprovdate").toString()+"]贷款损失准备失败!");
			return ptd;
		}catch(Exception glException){
			log.error("总账系统交易日志："+":手工计提:["+ptd.getParmValuebyName("mprovdate").toString()+"]贷款损失准备失败");
			log.error(glException.getMessage(), glException);
		}
		return null;
	}
}
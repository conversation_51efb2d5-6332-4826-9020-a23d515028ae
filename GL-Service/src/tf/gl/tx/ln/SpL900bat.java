
package tf.gl.tx.ln;

import java.util.HashMap;

import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.core.dao.DaoTemplate;
import tf.gl.dc.glDcTrance;
import tf.gl.impl.glSubTxHandler;
import tf.gl.tx.tools.StringTools;

/**
 * 美兴日间批量处理
 * Copyright (C), 2020, dalian tfrunning 
 * <AUTHOR>
 * @version v1.0 【2020-07-20】
 * 
 * 客户充值
 * <br> @参数：ptd（bankid 法人机构号,workdate 会计日期,txdate 交易日期,entitytype 法人类型,optuser 操作员,chkuser 复核员,authuser 授权员,
 * splitFlg 还款方式（1 按总额还款，2按金额类型还款）,acId 贷款账户id, 
 * payMap<String, Double>(paySumAmt 还款总金额，payAmt 还款本金额，payIntst 还款息金额，payOverIntst 还款罚息金额)）
 * realRepayMap 总还款金额结构体(LnCaptAmt;LnInIntstAmt;LnInOverIntstAmt;LnOutIntstAmt;LnOutOverIntstAmt;);
 * realRepayTermList 获取每期应还金额结构体
 *
 */

public class SpL900bat implements glSubTxHandler {
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());

	@Override
	public ParmsofTrsDefinition subGLTxDealer(ParmsofTrsDefinition ptd) throws Exception {
		String ErrorMsg = "";	
		try {
			DaoTemplate  dtpl = ptd.getTrsSession();
			long acId = Long.parseLong(ptd.getParmValuebyName("acid").toString());
			log.info("SpL900bat补记业务机构会计账acId"+acId);
			long bankid = ptd.getBankIDofTrs();	
//			AmtofMc realRepayMap = (AmtofMc) ptd.getParmValuebyName("realRepayMap");
			/*********读取贷款户信息***********/			
			/*LnMst lm=new LnMst();
			lm.setAcId(acId);
			lm.setAcSeqn(0);
			lm.setBankid(bankid);;
			LnMstDao lmDao=new LnMstDao();
			lmDao.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			LnMst lmData = lmDao.getEntityByPK(lm, LockMode.UPGRADE);
			if(lmData==null){
				ErrorMsg="获取ln_mst主文件表数据出错:acId【" + acId+ "】,acSeqn【" + 0 + "】,bankid【"+ bankid + "】";
				log.error(ErrorMsg);
				throw new Exception(ErrorMsg);
			}*/
			/*************获取贷款产品参数***************/
			/*LnParm lnp = new LnParm();
			lnp.setBankid(bankid);
			lnp.setPrdtNo((String) lmData.getPrdtNo());
			LnParmDao lnpDao = new LnParmDao();
			lnpDao.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			LnParm lnpData=lnpDao.getEntityByPK(lnp);
			if(lnpData==null){
				ErrorMsg="获取ln_parm产品参数失败:prdtNo【"+lmData.getPrdtNo()+"】";
				log.error(ErrorMsg);
				throw new Exception(ErrorMsg);
			}*/
			/*************获取账户介质对照表参数***************/
			/*MdmAcRel mar = new MdmAcRel();
			mar.setBankid(bankid);
			mar.setAcId(acId);
			mar.setAcSeqn(0);
			MdmAcRelDao marDao = new MdmAcRelDao();
			marDao.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			MdmAcRel marData=marDao.getEntityByPK(mar);
			if(marData==null){
				ErrorMsg="获取mdm_ac_rel账户介质表参数失败:acId【"+acId+"】,acSeqn【"+0+"】,bankid【"+bankid+"】";
				log.error(ErrorMsg);
				throw new Exception(ErrorMsg);
			}*/
			//初始化ptd记账处理结构体参数
			/*ptd.setTrsOpnInst(lmData.getOpnBrNo());
			ptd.setTrsInst(lmData.getOpnBrNo());
			ptd.setStringParm("dccode", lnpData.getDcCode());	
			ptd.setStringParm("acno", marData.getAcNo());
			ptd.setStringParm("curr", lnpData.getCurNo());
			ptd.setStringParm("prdtno", lnpData.getPrdtNo());
			ptd.setStringParm("acid", lmData.getAcId().toString());
			ptd.setStringParm("acseqn", lmData.getAcSeqn().toString());
			ptd.setStringParm("pactno", lmData.getPactNo());
			ptd.setStringParm("ctind", "2");
			ptd.setStringParm("acwrkind2", "1");
			ptd.setStringParm("hstind", "1");
			ptd.setObjectParm("lmData", lmData);
			ptd.setObjectParm("lnpData", lnpData);*/
			//处理时间
			ptd.setObjectParm("txtime", StringTools.getTime6());
			ptd.setStringParm("addind", "1");
			ptd.setStringParm("tracests", "0");
			ptd.setStringParm("noteno", "0");
			//depsopnbr
			
			HashMap<String, Double>  payMap = (HashMap<String, Double>) ptd.getParmValuebyName("payMap");
			double paySumAmt = payMap.get("paySumAmt");//还款总金额
			double amt = paySumAmt;
			ptd.setStringParm("amt", String.valueOf(amt));
			
//			ptd = lnAcctTrance.dealTraceLog(ptd);
			glDcTrance.dealLnDcLogMc(ptd);
			ptd.setRespCode("0003", "spL900bat");
			return ptd;
		} catch (Exception errL203) {
			log.error(errL203.getMessage(), errL203);
		}
		return ptd;
	}
}

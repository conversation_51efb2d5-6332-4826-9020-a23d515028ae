package tf.gl.tx.ln;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.log4j.Logger;

import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.cbs.brc.maintance.BRC;
import tf.gl.dao.LnMstDao;
import tf.gl.impl.glSubTxHandler;
import tf.gl.main.glsitemain;
import tf.gl.model.LnMst;
import tf.gl.tx.tools.glTrsTools;


/**
 * 
 * Copyright: 同方软银 Copyright (c) 2009 All right reserved.
 * 
 * @作者: w_w
 * @文件名: Ctrl3203.java
 * @创建日期: 2014-9-28
 * @公司: tfrunning
 * @版本 1.0
 * @历史: 1.0
 * @功能：按揭还款交易跳转控制：
 */
public class Ctrl3203 implements glSubTxHandler {
	Logger log=Logger.getLogger(getClass());
	@Override
	public ParmsofTrsDefinition subGLTxDealer(ParmsofTrsDefinition ptd) throws Exception {
		BRC.getLog().info("执行Ctrl3203");
		BRC.getLog().info("总账系统交易日志：" + "进入按揭还款交易跳转控制Ctrl3203!");

		try {
			long bankid = ptd.getBankIDofTrs();

			// 将acno赋到ptd中
			if (ptd.getParmValuebyName("loanacno") == null || ptd.getParmValuebyName("loanacno").equals("")) {
				ptd.setStringParm("loanacno", ptd.getParmValuebyName("acno").toString());
			}
			ptd.setStringParm("acno", ptd.getParmValuebyName("loanacno").toString());

			// 查询是否有交易笔次，如果没有赋值0
			if (ptd.getParmValuebyName("tracecnt") == null || ptd.getParmValuebyName("tracecnt").equals("")) {
				ptd.setStringParm("tracecnt", "0");
			}

			// 将lnopnbrno赋到ptd中
			if (ptd.getParmValuebyName("lnopnbrno") == null || ptd.getParmValuebyName("lnopnbrno").equals("")) {
				ptd.setStringParm("lnopnbrno", ptd.getParmValuebyName("opnbrno").toString());
			}
			ptd.setStringParm("opnbrno", ptd.getParmValuebyName("lnopnbrno").toString());

			// 将lnopnbrno赋到ptd中

			ptd.setTrsOpnInst(ptd.getParmValuebyName("opnbrno").toString());

			ptd.setTrsInst(ptd.getParmValuebyName("txbrno").toString());

			// 取得acno
			String acno = ptd.getParmValuebyName("acno").toString();

			long acid = glTrsTools.getAcIDbyAcNo(ptd);
			ptd.setStringParm("acid", String.valueOf(acid));
			ptd.setStringParm("loanacid", String.valueOf(acid));

			String opnbrno = ptd.getParmValuebyName("opnbrno").toString();
			String txbrno = ptd.getParmValuebyName("txbrno").toString();
			// 校验机构是否为账务机构
			if (!glTrsTools.chkDcBrType(ptd.getTrsSession(), bankid, opnbrno)) {
				BRC.getLog().error("机构检查失败:" + opnbrno + "在com_branch中未定义，账号号为：" + acno);
				BRC.getLog().error("总账系统交易日志：" + "机构检查失败:" + opnbrno
						+ "在com_branch中未定义，账号号为：" + acno);
				throw new Exception("机构检查失败:" + opnbrno + "在com_branch中未定义，账号号为：" + acno);
			}
			if (!glTrsTools.chkDcBrType(ptd.getTrsSession(), bankid, txbrno)) {
				BRC.getLog().error("机构检查失败:" + txbrno + "在com_branch中未定义，账号号为：" + acno);
				BRC.getLog().error("总账系统交易日志：" + "机构检查失败:" + txbrno
						+ "在com_branch中未定义，账号号为：" + acno);
				throw new Exception("机构检查失败:" + txbrno + "在com_branch中未定义，账号号为：" + acno);
			}
			if (!glTrsTools.chkDcBrType(ptd.getTrsSession(), bankid, ptd.getTrsOpnInst())) {
				BRC.getLog().error("机构检查失败:" + ptd.getTrsOpnInst() + "在com_branch中未定义，账号号为：" + acno);
				BRC.getLog().error("总账系统交易日志：" + "机构检查失败:" + ptd.getTrsOpnInst()
						+ "在com_branch中未定义，账号号为：" + acno);
				throw new Exception("机构检查失败:" + ptd.getTrsOpnInst() + "在com_branch中未定义，账号号为：" + acno);
			}
			if (!glTrsTools.chkDcBrType(ptd.getTrsSession(), bankid, ptd.getTrsInst())) {
				BRC.getLog().error("机构检查失败:" + ptd.getTrsInst() + "在com_branch中未定义，账号号为：" + acno);
				BRC.getLog().error("总账系统交易日志：" + "机构检查失败:" + ptd.getTrsInst()
						+ "在com_branch中未定义，账号号为：" + acno);
				throw new Exception("机构检查失败:" + ptd.getTrsInst() + "在com_branch中未定义，账号号为：" + acno);
			}

			// 检查信息完整性
			ptd.setStringParm("acid", String.valueOf(acid));
			if (!lnTools.chkLnInfocomplete(ptd.getTrsDBFactory(), acid, opnbrno, 0, bankid, acno)) {
				BRC.getLog().error("贷款信息完整性检查失败!贷款账号标识为" + acid);
				BRC.getLog().error("总账系统交易日志：" + "贷款信息完整性检查失败!贷款账号标识为" + acid);
				throw new Exception("贷款信息完整性检查失败!贷款账号标识为" + acid);
			}
			// 检查账户状态
			if (!lnTools.chkLnAcSts(ptd)) {
				BRC.getLog().error("贷款状态检查!贷款账号标识为" + acid);
				BRC.getLog().error("总账系统交易日志：" + "贷款状态检查!贷款账号标识为" + acid);
				throw new Exception("贷款状态检查!贷款账号标识为" + acid);
			}

			// String repaystate =
			// ptd.getParmValuebyName("repaystate").toString();// 当期是否归还：0，否；1，是

			// ArrayList<LnLo> lnLoList = (ArrayList<LnLo>)
			// ptd.getParmValuebyName("lnLoList");
			// double earlypayamt =
			// Double.parseDouble(ptd.getParmValuebyName("earlypayamt").toString());//
			// 还款金额
			
			// 取得ln_mst中表内外状态
			LnMstDao lnMstDao = new LnMstDao();
			lnMstDao.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			LnMst lnMst = new LnMst();
			lnMst.setBankid(bankid);
			lnMst.setAcId(acid);
			lnMst.setAcSeqn(0);
			lnMst = lnMstDao.getEntityByPK(lnMst);
			if (lnMst == null) {
				BRC.getLog().error("查询分户主文件失败，账号标志为：" + acid);
				BRC.getLog().error("总账系统交易日志：" + "查询分户主文件失败，账号标志为：" + acid);
				throw new Exception("查询分户主文件失败，账号标志为：" + acid);
			}
			String inoutflg = lnMst.getInOutFlg();
			
			ArrayList arrayList = (ArrayList) ptd.getParmValuebyName("arrayList");

			if (arrayList.size() > 0) { // 归还欠款

				HashMap hashMap = (HashMap) arrayList.get(0);

				String calint = hashMap.get("calint").toString();
				ptd.setStringParm("calint", calint);
				if (calint == null || calint.equals("")) {
					BRC.getLog().error("还款账户标示为：" + acid + "是否需结息标志calint为空");
					BRC.getLog().error("总账系统交易日志：" + "还款账户标示为：" + acid
							+ "是否需结息标志calint为空");
					throw new Exception("还款账户标示为：" + acid + "是否需结息标志calint为空");
				} else if (calint.equals("0")) {// 无须结息，直接还款
					ptd.setStringParm("currcnt", hashMap.get("currcnt").toString());
					ptd.setStringParm("repayamt", hashMap.get("repayamt").toString());
					ptd.setStringParm("repayintst", hashMap.get("repayint").toString());
					ptd.setStringParm("intststs", hashMap.get("intststs").toString());
					ptd.setStringParm("inoutflg", inoutflg);
					ptd.setRespCode("0002", "LB03");
				} else if(calint.equals("1")) { // 先结息，再还款
					ptd.setStringParm("currcnt", hashMap.get("currcnt").toString());
					ptd.setStringParm("repayamt", hashMap.get("repayamt").toString());
					ptd.setStringParm("repayintst", hashMap.get("repayint").toString());
					ptd.setStringParm("intststs", hashMap.get("intststs").toString());
					ptd.setStringParm("provInt", hashMap.get("provInt").toString());
					ptd.setStringParm("inoutflg", inoutflg);
					ptd.setRespCode("0001", "LB03");
				}else {
					BRC.getLog().error("还款账户标示为：" + acid + "是否需结息标志calint未找到对应数据字典");
					BRC.getLog().error("总账系统交易日志：" + "还款账户标示为：" + acid
							+ "是否需结息标志calint未找到对应数据字典");
					throw new Exception("还款账户标示为：" + acid + "是否需结息标志calint未找到对应数据字典");
				}
				arrayList.remove(hashMap);
				ptd.setObjectParm("arrayList", arrayList);
				return ptd;
			}

//			ptd.setRespCode("0000", "LB03");
			ptd.setRespCode("0000", String.valueOf(ptd.getTraceNo()));

		} catch (Exception errL30C) {
			log.error("Ctrl3203子程序发生错误", errL30C);
			ptd.setRespCode("9999", errL30C.getMessage());
		}
		return ptd;

	}
}

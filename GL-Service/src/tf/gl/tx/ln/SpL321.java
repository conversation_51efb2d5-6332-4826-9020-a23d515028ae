
package tf.gl.tx.ln;

import java.util.Map;

import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.core.dao.DaoTemplate;
import tf.gl.dao.AcVerifiInfoDao;
import tf.gl.dc.glDcTrance;
import tf.gl.impl.glSubTxHandler;
import tf.gl.model.AcVerifiInfo;
import tf.gl.model.LnMst;
import tf.gl.model.LnParm;
import tf.gl.model.MdmAcRel;
import tf.gl.tx.tools.Arith;
import tf.gl.tx.tools.PubLnTools;
import tf.gl.tx.trance.AmtofTrance;

/**
 * Copyright (C), 2019, dalian tfrunning 
 * <AUTHOR>
 * @version v1.0 【2019-02-26】
 * @function:贷款利息减免，核销贷款利息减免处理
 * <br>1、核销贷款不处理贷款明细和贷款主文件。
 */

public class SpL321 implements glSubTxHandler{
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	private long acId;
	private String workDate;
	private long bankid;
	private String rate;
	private LnMst lmData;
	@Override
	public ParmsofTrsDefinition subGLTxDealer(ParmsofTrsDefinition ptd)
			throws Exception {
		log.info("SpL321贷款利息减免，核销贷款利息减免处理");
		String ErrorMsg = "";			
		try{
			acId = Long.parseLong(ptd.getParmValuebyName("acid").toString());
			DaoTemplate daoTemplate = ptd.getTrsSession();
			workDate = ptd.getParmValuebyName("workdate").toString();
			bankid = ptd.getBankIDofTrs();
			lmData = (LnMst) ptd.getParmValuebyName("lmData");
			LnParm lnpData = (LnParm) ptd.getParmValuebyName("lnpData"); 
			MdmAcRel marData = (MdmAcRel) ptd.getParmValuebyName("marData"); 
			rate = ptd.getParmValuebyName("rate").toString();
			/**************核销贷款登记簿**************/
			AcVerifiInfo acVer= new AcVerifiInfo();
			acVer.setBankid(bankid);
			acVer.setAcId(acId);
			AcVerifiInfoDao acVerDao= new AcVerifiInfoDao();
			acVerDao.setExternalSession(ptd.getTrsDBFactory().getTransSession());
			AcVerifiInfo acVerData = acVerDao.getEntityByPK(acVer);
			if(acVerData==null){
				ErrorMsg="获取核销贷款登记数据失败:acId【"+acId+"】,bankid【"+bankid+"】";
				log.error(ErrorMsg);
				ptd.setRespCode("9706", ErrorMsg);
				ptd.setStringResult("respdesc", ErrorMsg);
				return ptd;
			}
			//获取按调整后利率按比例计算需减免的利息金额
			String sql = "select (a.VERIFI_INTST-a.RECYCLE_INTST)-round((a.VERIFI_INTST-a.RECYCLE_INTST)*"+rate+"/b.natu_rate,2) as intst ," + 
					"(a.VERIFI_OVER_INTST-a.RECYCLE_OVER_INTST)-round((a.VERIFI_OVER_INTST-a.RECYCLE_OVER_INTST)*"+rate+"/b.natu_rate,2) as over_intst ," + 
					"(a.VERIFI_OVER_INTST-a.RECYCLE_OVER_INTST)-round((a.VERIFI_CMPD_INTST-a.RECYCLE_CMPD_INTST)*"+rate+"/b.natu_rate,2) as cmpd_intst " + 
					"from ac_verifi_info a,ln_mst b " + 
					"where a.ac_id=b.ac_id and a.bankid=a.bankid";
			Map hm = daoTemplate.getUniqueMapRowBySql(sql);
			if(hm==null) {
				ErrorMsg="未查询到借据【" + lmData.getPactNo()+ "】欠款信息无法进行利息减免处理。";
				log.error(ErrorMsg);
				ptd.setRespCode("9700", ErrorMsg);
				return ptd;
			}
			double intst = Double.parseDouble(hm.get("intst").toString());//减免利息
			double overIntst = Double.parseDouble(hm.get("overIntst").toString());//减免逾期利息
			double cmpdIntst = Double.parseDouble(hm.get("cmpdIntst").toString());//减免复利利息
			log.info("数据备份处理");
			PubLnTools pubLnTools = new PubLnTools(ptd);
			pubLnTools.backupRegisterBook(ptd);	
			//数据初始化化
			ptd.setStringParm("amt", String.valueOf(Arith.adds(intst,overIntst,cmpdIntst)));//发生额
			ptd.setStringParm("tracecnt","1");
			ptd.setStringParm("notetype", "299");
			ptd.setStringParm("ctind", "2");
			ptd.setStringParm("tracests", "0");
			ptd.setStringParm("noteno", "0");//其他介质
			ptd.setStringParm("brfcode", "30027");
			ptd.setStringParm("acid", String.valueOf(lmData.getAcId()));
			ptd.setStringParm("acseqn", String.valueOf(lmData.getAcSeqn()));
			ptd.setStringParm("currcnt", "0");
			ptd.setTrsOpnInst(lmData.getOpnBrNo());
			ptd.setStringParm("dccode", lnpData.getDcCode());	
			ptd.setStringParm("acno", marData.getAcNo());
			ptd.setStringParm("acwrkind2", "1");
			ptd.setStringParm("acwrkind3", "0");
			ptd.setStringParm("curr", lnpData.getCurNo());
			ptd.setStringParm("prdtno", lnpData.getPrdtNo());
			ptd.setStringParm("hstind", "1");
			ptd.setStringParm("otheramt", "0");
			ptd.setStringParm("pactno", lmData.getPactNo());
			/***登记trace_log表**/
			ptd.setStringParm("addind", "1");
			ptd.setStringParm("brf", "核销贷款利息减免");
			lnAcctTrance.dealTraceLog(ptd);		
			/***会计账记账处理**/			
			if("20".equals(acVerData.getVerifiFlag())) {
				//税后核销处理
				AmtofTrance aot = new AmtofTrance();
				//ptd.setTrsOpnInst("*********");//税后核销应收利息登记至清算中心
				aot.setOtherAmt(Arith.adds(intst,overIntst,cmpdIntst));//L
				ptd.setObjectParm("traceamt", aot);
				glDcTrance.dealLnDcLog(ptd);
			}else if("10".equals(acVerData.getVerifiFlag())){
				//税前核销处理 无需处理账务
			}else {
				ErrorMsg="获取核销贷款核销类型失败:"+acVerData.getVerifiFlag();
				log.error(ErrorMsg);
				ptd.setRespCode("9707", ErrorMsg);
				return ptd;
			}
			String updateSql = "update AC_VERIFI_INFO set RECYCLE_INTST=RECYCLE_INTST-"+intst+" ,RECYCLE_OVER_INTST=RECYCLE_OVER_INTST-"+overIntst+" "
					+ ",RECYCLE_CMPD_INTST=RECYCLE_CMPD_INTST-"+cmpdIntst+"  where ac_id="+acId+"  and bankid ="+bankid;
			daoTemplate.executeSql(updateSql);				
			ptd.setRespCode("0000","SpL320");
			return ptd;
		}catch(Exception errL306){
			log.error(errL306.getMessage(), errL306);
			ptd.setStringResult("respdesc", "贷款利息减免acId【"+acId+"】处理失败");
			ptd.setRespCode("9001", String.valueOf(ptd.getTraceNo()));
			return ptd;	
		}		
	}
}


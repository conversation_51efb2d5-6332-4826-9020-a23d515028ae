
package tf.gl.tx.in;

import java.util.HashMap;

import org.apache.log4j.Logger;

import tf.brc.gl.GLFactory;
import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.gl.dao.ComItemDao;
import tf.gl.dao.InMstDao;
import tf.gl.dao.InParmDao;
import tf.gl.dao.MdmAcRelDao;
import tf.gl.dc.dcTools;
import tf.gl.dc.glDcTrance;
import tf.gl.impl.glSubTxHandler;
import tf.gl.main.glsitemain;
import tf.gl.model.ComItem;
import tf.gl.model.InParm;
import tf.gl.tx.ln.lnAcctTrance;
import tf.gl.tx.tools.StringTools;
import tf.gl.tx.tools.glTrsTools;
import tf.gl.tx.trance.AmtofTrance;


/**  
 * 	功能描述：GL-抵质押品入库记账
 * 
 *  @file:spA099.java
 * 	@author: w_w
 * 	2014-7-2下午8:51:52
 *  @company: tfrunning
 */

public class SpA099 implements glSubTxHandler{
	Logger log=Logger.getLogger(getClass());

	/* (non-Javadoc)
	 * @see tf.gl.impl.glSubTxHandler#subGLTxDealer(tf.brc.gl.impl.ParmsofTrsDefinition)
	 */
	@Override
	public ParmsofTrsDefinition subGLTxDealer(ParmsofTrsDefinition ptd)
			throws Exception {
		
		BRC.getLog().info("执行spA099");
		BRC.getLog().info("总账系统交易日志："+":开始进行抵质押品记账交易spA099!");
		
				try{
			
			DaoTemplate daoTemplate = ptd.getTrsSession();
			
			ComItemDao comItemDao = new ComItemDao();
			comItemDao.setExternalSession(ptd.getTrsSession().getSession());
			
			MdmAcRelDao mdmAcRelDao = new MdmAcRelDao();
			mdmAcRelDao.setExternalSession(ptd.getTrsSession().getSession());

			InParmDao inParmDao = new InParmDao();
			inParmDao.setExternalSession(ptd.getTrsSession().getSession());
			
			InMstDao inMstDao = new InMstDao();
			inMstDao.setExternalSession(ptd.getTrsSession().getSession());
			
			long bankid = Long.valueOf(ptd.getParmValuebyName("bankid").toString());
			String guarType = ptd.getParmValuebyName("guarType").toString();
//			String opn_br_no = ptd.getParmValuebyName("opnbrno").toString();
			String opn_br_no = ptd.getTrsOpnInst();
//			String opn_br_no = lnTools.getclrhse();
			String entityType = glTrsTools.getEntityType(bankid, daoTemplate);
			
			ptd.setStringParm("entitytype", entityType);
			
//			String accno = glTrsTools.pubBaseGetParm("DZYLX", Integer.valueOf(guarType), bankid, daoTemplate);
			String inprdtno ="";
			if("1".equals(guarType)){//2是抵押
				inprdtno = glTrsTools.pubBaseGetParm("DZYLX", 2 , bankid, daoTemplate);	
			}else if("2".equals(guarType)){
				inprdtno = glTrsTools.pubBaseGetParm("DZYLX", 1 , bankid, daoTemplate);
			}
			ptd.setStringParm("prdtno", inprdtno);
			
			InParm inParm = new InParm();
			inParm.setPrdtNo(inprdtno);
			inParm.setBankid(bankid);
			inParm.setCurNo(ptd.getParmValuebyName("curr").toString());
			
			inParm = inParmDao.getEntityByPK(inParm);
			if (inParm == null) {
				BRC.getLog().error("根据产品号:"+inprdtno+"查找内部产品定义表失败,没有找到对应的内部帐产品(in_parm)");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1028", inprdtno));
				throw new Exception("根据产品号:"+inprdtno+"查找内部产品定义表失败,没有找到对应的内部帐产品(in_parm)");
			}
			
//			ptd.setStringParm("accno", accno);
			
			String acCode = inParm.getAcCode();//科目控制字
			String dccode = inParm.getDcCode();
			
			//检索科目号(根据科目控制字/科目号)
			ComItem comItem = new ComItem();
			comItem.setEntityType(entityType);
			comItem.setAccHrt(String.valueOf(acCode));
			
			comItem = comItemDao.getEntityByPK(comItem);
			if (comItem == null) {
				BRC.getLog().error("科目号:"+acCode+"在科目表中没有定义com_item");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1020", String.valueOf(acCode)));
				throw new Exception("科目号:"+acCode+"在科目表中没有定义com_item");
			}
			
			String inind = comItem.getInInd();
			String acchrt = comItem.getAccHrt();
			
//			// 由acc_hrt得到dc_code
//			String getDcCodeSql = "select dc_code from dc_acc_rel where entity_type = '"+entityType+"' and acc_hrt = '"+acchrt+"'";
//			String dccode = daoTemplate.getUniqueValueBySql(getDcCodeSql).toString();
			ptd.setStringParm("dccode", dccode);
			
			if(!inind.equals("I")){
				BRC.getLog().error("科目号:"+acCode+"非内部帐科目");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1023", String.valueOf(acCode)));
				throw new Exception("科目号:"+acCode+"非内部帐科目");
			}	
			
			//取得是否明细科目标识
			String subAccYn = comItem.getSubAccYn();
			if(subAccYn.equals("Y")){
				BRC.getLog().error("科目号:"+acCode+"不是明细科目不可进行记账");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1024", String.valueOf(acCode)));
				throw new Exception("科目号:"+acCode+"不是明细科目不可进行记账");
			}
			
			// 科目账户关联关系
			String accKnd = comItem.getAccKnd();
			if(!accKnd.equals("2")){
				BRC.getLog().error("科目号:"+accKnd+"不允许进行开户(com_item)");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1025", String.valueOf(acCode)));
				throw new Exception("科目号:"+accKnd+"不允许进行开户(com_item)");
			}
			
//			//检查inparmturn中的产品定义情况(内部帐科目与内部帐产品定义)
//			InParmTurn inParmTurn = new InParmTurn();
//			inParmTurn.setEntitytype(entityType);
//			inParmTurn.setInCode(String.valueOf(accno));
//			
//			inParmTurn = inParmTurnDao.getEntityByPK(inParmTurn);
//			if (inParmTurn == null) {
//				BRC.getLog().error("根据科目号:"+accno+"查找内部帐对应产品定义失败,没有找到对应的内部帐产品(in_parm_ture)");
//				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1026", String.valueOf(accno)));
//				throw new Exception("根据科目号:"+accno+"查找内部帐对应产品定义失败,没有找到对应的内部帐产品(in_parm_ture)");
//			}
			
//			String prdtNo = inParmTurn.getPrdtNo();
//			ptd.setStringParm("prdtno", inprdtno);
			
			//标准账户:开立前检查是否开立过
/*			String sSql="select * from in_mst where opn_br_no='"+opn_br_no+"' and ac_type='1' and prdt_no='"+inprdtno+"' and bankid ="+bankid;
			
			HashMap hmItem=(HashMap)daoTemplate.getUniqueMapRowBySql(sSql);
			// 如果为空，则开立内部帐
			if(hmItem == null){
				ptd.setStringParm("incode", acCode);
				ptd.setStringParm("odind", inParm.getOdInd());// 透支
				ptd.setStringParm("stddind", "1");
				ptd.setStringParm("enddate", "0");
				ptd.setStringParm("intsttype", inParm.getIntstKnd());
				
				BRC.getLog().error("科目号:"+acCode+"尚未开立标准账户");
				ptd.setRespCode("0001", "A009");
				return ptd;
			}*/
			
			// 得到标准账户账号
			/*long acid = Long.parseLong(hmItem.get("acId").toString());
			int acseqn = Integer.parseInt(hmItem.get("acSeqn").toString());
			
			InMst inMst = new InMst();
			inMst.setBankid(bankid);
			inMst.setAcId(acid);
			inMst.setAcSeqn(acseqn);
			
			inMst = inMstDao.getEntityByPK(inMst);
			
			ptd.setObjectParm("inmstinstance", inMst);
			
			MdmAcRel mdmAcRel = new MdmAcRel();
			mdmAcRel.setBankid(bankid);
			mdmAcRel.setAcId(acid);
			mdmAcRel.setAcSeqn(Integer.valueOf(String.valueOf(acseqn)));
			mdmAcRel = mdmAcRelDao.getEntityByPK(mdmAcRel);
			
			if (mdmAcRel == null) {
				BRC.getLog().error("账号ID:"+acid+"在介质账户对照表(mdm_ac_rel)中无记录");
				BRC.getLog().error("总账系统交易日志："+GLFactory.getFactory().getRtnMessBD("1030", String.valueOf(acCode)));
				throw new Exception("账号ID:"+acid+"在介质账户对照表(mdm_ac_rel)中无记录");
			}
			String acno = mdmAcRel.getAcNo();*/
			//获取会计账
			String sSql="select * from dc_mst where br_no='"+opn_br_no+"' and acc_hrt='"+acCode+"' and bankid ="+bankid;			
			BRC.getLog().info("取得会计账号SQL:"+sSql);
			Object hmo=daoTemplate.getUniqueMapRowBySql(sSql);
			String sacno="";
			if(hmo==null){
				sacno=dcTools.opnACAcc(bankid, opn_br_no, acCode, comItem.getAccName(),daoTemplate);
			}else{
				HashMap hmItem=(HashMap)daoTemplate.getUniqueMapRowBySql(sSql);
				sacno=hmItem.get("acNo").toString();
			}
			
			

			ptd.setStringParm("acid", "0");
			ptd.setStringParm("acseqn", "0");
			ptd.setStringParm("acno", sacno);						
			ptd.setStringParm("acwrkind2", "0");
			ptd.setStringParm("acwrkind3", "0");
//			ptd.setStringParm("brf", "抵质押品记账");
			ptd.setStringParm("ctind", "2");
			ptd.setStringParm("acwrkindq", "1");
			ptd.setStringParm("acidtype", "9");
//			ptd.setStringParm("acidtype", "9");
			ptd.setStringParm("svcind", "901");
			ptd.setStringParm("tracecnt", "0");//当前笔次
			ptd.setStringParm("hstind", "1");//是否要入明细账
			ptd.setStringParm("svcind", "9011");
			ptd.setStringParm("txtime", StringTools.getNow1());//交易时间
			ptd.setStringParm("tracests", "0");//交易类型0正常1撤销
			ptd.setStringParm("otheramt", "0");//文档说:登记流水附表
			ptd.setStringParm("pactno", "");
			ptd.setStringParm("txdate", ptd.getParmValuebyName("workdate").toString());//交易日期
			ptd.setStringParm("txtime", StringTools.getNow1());//账户序号
			
			// 根据增减标志确定借贷标志
			String addind = ptd.getParmValuebyName("addind").toString();
			String dcind = "";
			String brf = "";
			AmtofTrance aot = new AmtofTrance();
			if (addind.equals("1")) {
				dcind = "1";
				brf = "抵质押品记账(入库)";
				aot.setLnCaptAmt(Double.parseDouble(ptd.getParmValuebyName("amt").toString()));
			}else {
				dcind = "2";
				brf = "抵质押品记账(出库)";
				aot.setOtherAmt(Double.parseDouble(ptd.getParmValuebyName("amt").toString()));
			}
			ptd.setStringParm("dcind", dcind);
			ptd.setStringParm("brf", brf);			
			ptd.setStringParm("inhsttype", "1");//是否记录内部帐明细表
			// 发生额结构类型赋值， AmtofTrance  traceamt		
			ptd.setObjectParm("traceamt", aot);
			
			ptd = lnAcctTrance.dealTraceLog(ptd);

			ptd = glDcTrance.dealLnDcLog(ptd);			
			//ptd = inAcctTrance.dealAcctIn(ptd);					
			ptd.setStringResult("acno", ptd.getParmValuebyName("acno").toString());
			ptd.setStringResult("acid", ptd.getParmValuebyName("acid").toString());
			
			ptd.setRespCode("0000","spA099");
			return ptd;
		}catch(Exception errspA099){
			log.error("spA099子程序发生错误", errspA099);
		}
		return ptd;
	
	}
	
	
}


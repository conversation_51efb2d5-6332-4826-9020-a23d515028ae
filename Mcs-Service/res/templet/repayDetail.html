<html >
<head>
    <style type="text/css">
	   div.header-left {  
            display: none  
        }  
  
        div.header-right {  
            display: none  
        }  
  
        div.footer-left {  
            display: none  
        }  
  
        div.footer-right {  
            display: none  
        }  
        @media print {  
            div.header-left {  
                display: block;  
                position: running(header-left);
				height: 500px; 
            }  
  
            div.header-right {  
                display: block;  
                position: running(header-right);  
            }  
  
            div.footer-left {  
                display: block;  
                position: running(footer-left);  
            }  
  
            div.footer-right {  
                display: block;  
                position: running(footer-right);  
            }  
        }
	   @page {
	   size:210mm 297mm;
	  /* border-bottom: 0.25pt solid #666;*/ /*ҳ�Ŵ��ĺ���*/
      /* border-top: 0.25pt solid #666;*//*ҳü���ĺ���*/ 
       @top-left { content: element(header-left) };  
       @top-right { content: element(header-right) };  
       @bottom-left { content: element(footer-left) };  
       @bottom-right { content: element(footer-right) }; 
	   }
        *{
            font-size: 14px;
            font-family: SimSun;
        }
        body{
		   font: 9pt sans-serif;
           line-height: 1.3;

           margin-top: 100px;
           margin-bottom: 50px;
           
        }
        h1{
            font-size: 20px;
        }
        h2{
            font-size: 16px;
            font-weight: normal;
            margin: 15px auto;
        }
        p{
            margin: 0 auto;
            line-height: 24px;
            text-indent: 28px;
        }
        .center {
            text-align: center;
        }
		 .right {
            text-align: right;
        }
       #pagenumber:before {  
            content: counter(page);  
        }  
  
        #pagecount:before {  
            content: counter(pages);  
        }
    </style>
</head>
<body style="font-family:'SimSun'">
     <div id="header-left" class="header-left" align="left"><img src="logo.png"  width="60" height="60"/></div>
	 <div id="footer-right" class="footer-right" align="center"> �� <span id="pagenumber"/> ҳ  �� <span id="pagecount"/> ҳ</div> 
<tr>
	<td>
		<table width="725" align="center" border="0" cellspacing="0px" style="table-layout:fixed;word-break:break-all;">
			<tr>
				<td  align="center" >
					<div style="font-weight:bold" ><h1>instname</h1>
					  <h1>�������ϸ��</h1>
					</div>
			  </td>
			</tr>
	  </table>
	</td>
</tr>
<br/>
<br/>
<table border="0" align="center" style="table-layout:fixed;border-collapse:separate;width:95%; margin-left:0%;">
 <tr>
  <td width="15%" align="left" >�����������</td>
  <td width="20%" align="left" >cliname</td>
  <td width="15%" align="left" >֤�����ͣ�</td>
  <td width="25%" align="left" >certtype </td>
  <td width="15%" align="left" >֤�����룺</td>
  <td width="25%" align="left" >certno </td>
 </tr>
  <tr>
  <td width="15%" align="left" >����˱�ţ�</td>
  <td width="20%" align="left" >cifid</td>
  <td width="15%" align="left" >��ͬ��ţ�</td>
  <td width="25%" align="left" >contno</td>
  <td width="15%" align="left" >���� </td>
  <td width="25%" align="left" >busSum </td>
 </tr>
  <tr>
  <td width="15%" align="left" >�������ڣ�</td>
  <td width="15%" align="left" >begindate</td>
  <td width="15%" align="left" >�˻���</td>
  <td width="15%" align="left" >csbal</td>
  
  <!--showFlagbegin--><!--showBusbal0-->
    <td width="15%" align="left" >����� </td>
    <td width="25%" align="left" >busbal </td>
  <!--showFlagend-->
  <!--showFlagbegin--><!--showBusbal1-->
    <td width="15%" align="left" ></td>
    <td width="25%" align="left" > </td>
  <!--showFlagend-->
  
 </tr>
 <tr>
  <td width="90%" align="left" colspan="6"></td>
 </tr>
 <tr>
  <td width="90%" align="left" colspan="6">ע�����ͻ�������ļ�-����"Ϊ�ÿͻ��Ĵ��룬�����ڸý���ͬ�Ļ�����루��ͻ�ͬʱ���ڶ�ʴ���ģ��������ڹ黹�����µ�������ͬ���</td>
 </tr>
  <tr>
  <td width="15%" align="left"> </td>
  <td width="15%" align="left" ></td>
  <td width="15%" align="right"></td>
  <td width="15%" align="left"> </td>
  <td width="15%" align="left" ></td>
  <td width="15%" align="right">��λ��Ԫ</td>
 </tr>
</table>
<table width="99%" border="1"  align="center"  cellpadding="0" cellspacing="0"  style="table-layout:fixed;border-collapse:separate;width:100%;">
  <tr>
    <td width="13%" align ="center" >��������</td>
    <td width="26%" align="center" >ժҪ </td>
	<td width="26%" align="center" >�ۿ�����</td>
	<td width="26%" align="center" >���ױ��</td>
	<td width="13%" align="center" >����</td>
    <td width="13%" align="center" >֧��</td>
	<td width="13%" align="center" >�ڴ�</td>
  </tr>
   <!--showFlagbegin--><!--show0-->
   <!--loopbegin--><!--array0-->
  <tr>
    <td width="13%" align="center" >txdate</td>
    <td width="26%" align="center" >brf</td>
    <td width="26%" align="center" >repaychanl</td>
	<td width="26%" align="center" >traceNo</td>
	<td width="13%" align="center" >addamt</td>
    <td width="13%" align="center" >expamt</td>
	<td width="13%" align="center" >currcnt</td>
  </tr>
  <!--loopend-->
  <!--showFlagend-->
</table>
</body>
</html>
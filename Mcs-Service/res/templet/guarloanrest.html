<html>
<head>
    <style type="text/css">
  div.header-left {  
            display: none  
        }  
  
        div.header-right {  
            display: none  
        }  
  
        div.footer-left {  
            display: none  
        }  
  
        div.footer-right {  
            display: none
        }  
        @media print {  
            div.header-left {  
                display: block;  
                position: running(header-left);
				height: 500px; 
            }  
  
            div.header-right {  
                display: block;  
                position: running(header-right);  
            }  
  
            div.footer-left {  
                display: block;  
                position: running(footer-left);  
            }  
  
            div.footer-right {  
                display: block;  
                position: running(footer-right);  
            }  
        }
	   @page {
	   size:210mm 297mm;
	  /* border-bottom: 0.25pt solid #666;*/ /*ҳ�Ŵ��ĺ���*/
      /* border-top: 0.25pt solid #666;*//*ҳü���ĺ���*/ 
	   margin-top:110px;
	   margin-bottom:90px;
       @top-left { content: element(header-left) };  
       @top-right { content: element(header-right) };  
       @bottom-left { content: element(footer-left) };  
       @bottom-right { content: element(footer-right) }; 
	   }
        *{
            font-size: 14px;
            font-family: SimSun;
        }
        body{
		   font: 9pt sans-serif;
           line-height: 1.2;
           margin-top: 0px;
           margin-bottom: 50px;
           
        }
        h1{
            font-size: 20px;
        }
        h2{
            font-size: 16px;
            font-weight: normal;
            margin: 15px auto;
        }
        p{
            margin: 0 auto;
            line-height: 22px;
            text-indent: 28px;
        }
        .center {
            text-align: center;
        }
		 .right {
            text-align: right;
        }
       #pagenumber:before {  
            content: counter(page);  
        }  
  
        #pagecount:before {  
            content: counter(pages);  
        }
		#changedirection {
			width:100%;
			height:0px;
			transform:rotate(90deg);
			-ms-transform:rotate(90deg); 	/* IE 9 */
			-moz-transform:rotate(90deg); 	/* Firefox */
			-webkit-transform:rotate(90deg); /* Safari �� Chrome */
			-o-transform:rotate(90deg); 	/* Opera */
		}
    </style>
</head>
<body>
	<div id="header-left" class="header-left" align="left" style="height:50px;width:100%;"><img src="logo.png"/></div>
	<div id="footer-right" class="footer-right" align="center">vchno �� <span id="pagenumber"/> ҳ  �� <span id="pagecount"/> ҳ</div> 
	<div id="content" style="color:white; position:relative; left:0px;top:0px;">
	</div>
	<div id="content1">
		<p style="text-align:center;font-weight:bold;font-size:20px;">��������Э��</p>
		<p style="text-align:right; margin-top:40px;"><span style="margin-right:10px;">Э����:byear ��</span><span>��:vchno ��</span></p>
		<p style="text-indent:0px;margin-top:5px;">
			<span>�׷�(�����)��cliname</span>&#160;&#160;&#160;&#160;<span>֤�����ͣ�clicerttype</span>
			<br />
			<span>֤���ţ�clicertno</span>&#160;&#160;&#160;&#160;<span>�־�ס�أ�famaddr</span>
		</p>
		<!--showFlagbegin--><!--show0-->
		<!--loopbegin--><!--array0-->
		<p style="text-indent:0px;margin-top:5px;">
			<span>�׷�(�����rowno)��doecliname</span>&#160;&#160;&#160;&#160;<span>֤�����ͣ�doecerttype</span>
			<br />
			<span>֤���ţ�doecertno </span>&#160;&#160;&#160;&#160;<span>��ҵ��ַ��doeaddr</span>
		</p>
		<!--loopend-->
		<!--showFlagend-->
		<!--showFlagbegin--><!--show11-->
		<!--loopbegin--><!--array11-->
		<p style="text-indent:0px;margin-top:5px;">
			<span>�׷�(��ͬ�����rowno)��borcliname</span>&#160;&#160;&#160;&#160;<span>֤�����ͣ�borcerttype</span>
			<br />
			<span>֤���ţ�borcertno</span>
		</p>
		<!--loopend-->
		<!--showFlagend-->
		<p style="text-indent:0px;margin-top:5px;">
			<span>�ҷ�(������)��managename</span><br />
			<span>ס���أ�instcodeaddr</span><br />
			<span>�����ˣ�manager</span><br />
		</p>
		<!--showFlagbegin--><!--show22-->
		<!--loopbegin--><!--array2-->
		<p style="text-indent:0px;margin-top:5px;">
			<span>����(������ rowno)��guarname</span> &#160;&#160;&#160;&#160;<span>֤�����ͣ�guarcerttype</span>
			<br />
			<span>֤���ţ�guarcertno</span>
		</p>
		<!--loopend-->
		<!--showFlagend-->
		<!--showFlagbegin--><!--show44-->
		<!--loopbegin--><!--array4-->
		<p style="text-indent:0px;margin-top:5px;">
			<span>����(��Ѻ�� rowno)��guarname</span> &#160;&#160;&#160;&#160;<span>֤�����ͣ�guarcerttype</span>
			<br />
			<span>֤���ţ�guarcertno</span>
		</p>
		<!--loopend-->
		<!--showFlagend-->

		<p style="text-indent:24px; margin-top:5px;">		���ڼ׷��ֽ������㣬������ʱ���ܰ�ԭ����ͬ���ڣ�contno�ţ�(���³ơ�ԭ����ͬ��)��ʱ��������ҷ���������ҷ������ԭ����ͬ�Ļ�������/���ʽ/���������뻹�ʽ�����������ҷ�����飬ͬ����ܼ׷������룬���ݹ�����ط��ɡ�����Ĺ涨����ƽ�ȡ���Ը��������õĻ����ϣ���ǩ����Э�飬��ͬ���ء�
		</p><br/>
		<p style="line-height:22px; text-indent:0px;">��һ���������ı����busSum Ԫ ������ʲ��䡣</p><br/>
		<p style="line-height:22px; text-indent:0px;">�ڶ����������Ļ������ޡ����ʽ��&#160;&#160;<br/>1���׷�������޹�<u>term,��bdate��ʼ��edate</u>��ֹ��&#160;&#160;<br/>2���׷��밴�ա�������ڻ���ʱ������飩��Լ���Ļ������ޡ���������ʽ��ʱ�����</p><br/>
		<p style="line-height:22px; text-indent:0px;">��������<u>��֤��</u>��ԸΪ�׷���ԭ����ͬ����Э���γɵ�ծ�����ҷ��ṩ�������α�֤����֤��ΧΪ��Э�����µĽ�����Ϣ��ΥԼ���Լ�ʵ��ծȨ�ķ��ã������������ڹ��桢�ʹ�����ѡ���ʦ�ѡ����Ϸѡ����÷ѡ������ѡ������ѡ��Ʋ���ȫ��֤�ݱ�ȫ�ѡ�ǿ��ִ�зѵȣ���ǰ��ʵ��ծȨ�ķ�����ʵ�ʷ�����Ƹ�����֤�ڼ�Ϊ��ծ�������ڽ���֮�������ꡣ</p><br/>
		<p style="line-height:20px; text-indent:0px;">����������ҵ�ļ�������������ʹ��ַ���ʹ﷽ʽԼ��&#160;&#160;<br/>
			1���׷�������ȷ����ҵ�ļ������������ʹ��ַ���ʹ﷽ʽ���£�&#160;&#160;<br/>
		<table border="1" cellspacing="0" cellpadding="0" bordercolor="#000000" style="table-layout:fixed;word-break:break-strict;width:100%; margin-left:0%;">
		<!--showFlagbegin--><!--show122-->
		<!--loopbegin--><!--array122-->
			<tr>
				<td width="13%" style="text-align:center;">��addInd������/���ƣ�</td>
				<td width="13%" style="text-align:center;">addname</td>
				<td width="15%" style="text-align:center;">�ʹ��ַ��</td>
				<td width="26%" style="text-align:center;">address</td>
				<td width="17%" style="text-align:center;">�ֻ�����</td>
				<td width="15%" style="text-align:center;">addtel</td>
				<td width="17%" style="text-align:center;">�������䣺</td>
				<td width="15%" style="text-align:center;">addemail</td>
			</tr>
		<!--loopend-->
		<!--showFlagend-->
		</table>
			2���׷�������ȷ�ϣ� ������һ������ʹ��ַ���ֻ����롢��������Ϊ����ͬ�漰�ĸ���֪ͨ����ҵ�ļ��ź��Լ��ͱ���ͬ��������ʱ˾�����أ���Ժ���ٲû���������ļ������ϡ��ٲ�������ʹ��ַ�������ʹ��������ʹ����ͬ�ȷ���Ч�������׷���������д��������ģ��׷�������ͬ�Ⲣ��Ȩ�ɼ׷����������ֻ�������Ӫ�̸��ݼ׷����������ֻ������Զ����ɵĵ������䲢��Ϊ�ʹ��ַ��&#160;&#160;<br/>
			3���ʹ��ַ�����ڼ䡣�����ʹ��ַ���ֻ����롢������������������ͬ������ϻ����龭��һ�󡢶���������ִ���ս�ʱֹ�����Ǹ������¿��֪�����&#160;&#160;<br/>
			4���ʹ��ַ���ֻ�����͵�������ı�����κ�һ���ʹ��ַ���ֻ����롢����������Ҫ����ģ�Ӧ��ǰ������������ͬ��������˾�������ͽ���������֪��(�������Ѿ�����˾��������)��&#160;&#160;<br/>
			5���׷���������ŵ������ȷ�ϵ��ʹ��ַ���ֻ����롢����������ʵ��Ч�����д��󣬵��µ���ҵ�ź������������ʹﲻ�ܵķ��ɺ���ɼ׷����������ге���&#160;&#160;<br/>
			6��������ʾ���׷�����������֪��������ṩ����ȷ�ϵ��ʹ��ַ���ֻ����롢�������䲻׼ȷ�� �����ʹ��ַ�����δ��ʱ�������֪�ҷ���˾�����ء����ߵ����˺�ָ�������˾ܾ�ǩ�յ�ԭ�򣬵�����������δ�ܱ�������ʵ�ʽ��գ��ʼ��ʹ�ģ��������˻�֮����Ϊ�ʹ�֮�գ�ֱ���ʹ�ģ��ʹ��˵������ʹ��֤�ϼ������֮����Ϊ�ʹ�֮�գ����ӷ�ʽ�ʹ�ģ��ʹ�������˾�����أ���Ժ���ٲû�������Ӧ�����ʹ﷽ʽ��ʾ���ͳɹ�������Ϊ�ʹ�֮�ա�&#160;&#160;<br/>
		</p><br/>
		<p style="line-height:22px; text-indent:0px;">���������ر�Լ��</p><br/>
		<p style="line-height:22px; text-indent:0px;">&#160;&#160;�׷�������֪Ϥ�ҷ���ֹ������Ա������ȡ�ͻ��Ļ����ֽ����׷������������������ͨ���ֽ�ʽ����ģ�����ʱ�������ҷ��������Ϲ�����Ա�ڳ���Ӧ��������׷�����������ҵ��Ŀͻ�������ͬʱ���׷�������Ӧ������ǰ���տ���Ա��ȡ�Ӹ��ҷ�����ǩ�µ��ֽ��վݣ����µ�400-0840-168�����ҷ�����ȷ�����յ��黹�Ŀ����׷�������δ���б���Լ�������µľ�����ʧ���ҷ��޹أ��ɼ׷����������ге���</p><br/>
		<p style="line-height:22px; text-indent:0px;">����������Э�鼰��������������ڻ���ʱ������飩������ԭ����ͬ����Ҫ��ɲ��֣���ԭ����ͬ��һ�µģ��Ա�Э��Լ����Ϊ׼��</p><br/>
		<p style="line-height:22px; text-indent:0px;">����������Э���Ը���ǩ��/����֮������Ч��</p><br/>
		<p style="line-height:22px; text-indent:0px;">�ڰ�������Э������һʽ <span id="pagecount"/>ҳ������/���ݣ��׷������������У���Ҽ�ݡ��ҷ����ݣ�����ͬ�ȷ���Ч����
������������ڻ���ʱ������飩����������£�
</p><br/>
	
		<p style="text-align:center;">
		<span style="margin-left:5%;"><strong>������������ڻ���ʱ������飩��</strong></span>
		<hr style="margin-left:10px;width:98%"/>
		</p>
		<table border="1" cellspacing="0" cellpadding="0" bordercolor="#000000" style="table-layout:fixed;word-break:break-strict;width:100%; margin-left:0%;">
		<tr>
			<td width="13%" style="text-align:center;">��ݱ��</td>
			<td width="26%" style="text-align:center;">vchno</td>
			<td width="15%" style="text-align:center;">�ͻ�����</td>
			<td width="14%" style="text-align:center;">cliname</td>
			<td width="17%" style="text-align:center;">����󱾽�</td>
			<td width="15%" style="text-align:center;">busSum</td>
		</tr>
		<tr>
			<td width="13%" style="text-align:center;">���ʽ</td>
			<td width="26%" style="text-align:center;">prdtName</td>
			<td width="15%" style="text-align:center;">����󻹿����</td>
			<td width="14%" style="text-align:center;">cnt</td>
			<td width="17%" style="text-align:center;">���ʽ</td>
			<td width="15%" style="text-align:center;">repayType</td>
		</tr>
		<tr>
		 <td   width="100%" colspan="6" style="text-align:center;">&#160;
		 </td>
		</tr>
		<tr>
			<td width="13%" style="text-align:center;">���������</td>
			<td width="26%" style="text-align:center;">��������</td>
			<td width="15%" style="text-align:center;">�����</td>
			<td width="14%" style="text-align:center;">������Ϣ</td>
			<td width="17%" style="text-align:center;">�����ܶ�</td>
			<td width="15%" style="text-align:center;">����󱾽����</td>
		</tr>
		<!--showFlagbegin--><!--show3-->
		<!--loopbegin--><!--array3-->
		<tr>
			<td width="13%" style="text-align:center;">&#160;��currcnt��</td>
			<td width="26%" style="text-align:center;">&#160;reyear��remonth��reday��</td>
			<td width="15%" style="text-align:center;">&#160;curramt</td>
			<td width="14%" style="text-align:center;">&#160;currint</td>
			<td width="17%" style="text-align:center;">&#160;currsum</td>
			<td width="15%" style="text-align:center;">&#160;repaybal</td>
		</tr>
		<!--loopend-->
		<!--showFlagend-->
		</table>
		<p style="margin-left:5%;">�ϼ�&#160;&#160;&#160;&#160;����amtotal&#160;&#160;&#160;��Ϣ��inttotal&#160;&#160;&#160;&#160;�����ܶamtSumtotal&#160;&#160;
		</p>
</div>
	<div id="content3" style="width:100%; position:absolute;left:1px;top:2950px;">
		<table border="1" cellspacing="0" cellpadding="0" bordercolor="#000000" style="table-layout:fixed;word-break:break-strict;width:100%; vertical-align:text-top ">
          <tr>
			<td>
				<!--showFlagbegin--><!--show-->
				<p style="height:100px; text-indent:0px">�����(�׷�)��cliname<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				</p>
				 <p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
				 <!--showFlagend-->
				 
				<!--showFlagbegin--><!--show00-->
				<!--loopbegin--><!--array00-->
				<p style="height:100px; text-indent:0px">�����rowno(�׷�): doename<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				</p>
				 <p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
				 <!--loopend-->
                 <!--showFlagend-->
				 
				
				 
				 <!--showFlagbegin--><!--show1-->
				<!--loopbegin--><!--array1-->
				<p style="height:100px; text-indent:0px">��ͬ�����rowno(�׷�)��borcliname<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				</p>
				<p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
				<!--loopend-->
                 <!--showFlagend-->
				 
				 
			</td>
			<td>
				<p style="height:100px; text-indent:0px;">������(�ҷ�)��<br/>
				<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				</p>
				<p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
				<p style="text-align:right;">&#160;</p>
				<p style="text-align:right;">&#160;</p>
			</td>
			<td>
			    <!--showFlagbegin--><!--show2-->
				<!--loopbegin--><!--array2-->
				<p style="height:100px; text-indent:0px;">������rowno(����)��guarname<br/>
				<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				&#160;&#160;<br/>
				</p>
				<p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
			    <!--loopend-->
				  <!--showFlagend-->
				<!--showFlagbegin--><!--show4-->
				<!--loopbegin--><!--array4-->
				<p style="height:100px; text-indent:0px;">��Ѻ��rowno(����)��guarname<br/>
					<br/>
					&#160;&#160;<br/>
					&#160;&#160;<br/>
					&#160;&#160;<br/>
					&#160;&#160;<br/>
				</p>
				<p style="text-align:right;"><u>byear </u> ��<u>bmonth</u>��<u>bday</u>��&#160;&#160;</p>
				<!--loopend-->
				<!--showFlagend-->
			</td>
		</tr>
	</table>
</div>
	
	
</body>
</html>






















package tf.tools;

/**
 * <p>BRC加解密处理包-3DES处理方法</p>
 * <p>Description:
 *    功能包括：
 *    1:DES的加解密
 *    2:
 *    3:
 * </p>
 *
 * <p>Copyright: 同方软银 刘宇 Copyright (c) 2013</p>
 * <p>Company: tfrunning </p>
 * @author: 刘宇
 * @date:2013-05-28
 * @version 1.0
 * @history: 1.0
 *
 */
import java.util.Random;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;

import tf.tools.SnowFlakeFactory;

@Component
public class DESEncrypt {
	private static Log logger = LogFactory.getLog(DESEncrypt.class);

	public byte[] encrypt(byte[] b_src, byte[] salt) {
		return encrypt(b_src, 1, salt);
	}

	public byte[] decrypt(byte[] b_src, byte[] salt) {
		return encrypt(b_src, 2, salt);
	}

	public String encrypt(String s, byte[] salt) {
		StringBuffer sb = new StringBuffer(1024);
		byte[] b = encrypt(s.getBytes(), 1, salt);
		if ((b == null) || (b.length < 1))
			return null;
		Random r = new Random(s.length());

		for (int i = 0; i < b.length; ++i) {
			char c = (char) (r.nextInt(10) + 71);
			sb.append(c);
			if (b[i] < 0) {
				c = (char) (r.nextInt(10) + 81);
				b[i] = (byte) (-b[i]);
				sb.append(c);
			}
			sb.append(Integer.toString(b[i], 16).toUpperCase());
		}
		sb.deleteCharAt(0);
		return sb.toString();
	}

	public String decrypt(String s, byte[] salt) {
		if (s.length() < 1) {
			return null;
		}

		String[] sByte = s.split("[G-Pg-p]");
		byte[] b = new byte[sByte.length];

		for (int i = 0; i < b.length; ++i) {
			char c = sByte[i].charAt(0);
			if (((c >= 'Q') && (c <= 'Z')) || ((c >= 'q') && (c <= 'z')))
				b[i] = (byte) (-Byte.parseByte(sByte[i].substring(1), 16));
			else
				b[i] = Byte.parseByte(sByte[i], 16);
		}

		byte[] ch = encrypt(b, 2, salt);
		if ((ch == null) || (ch.length < 1))
			return null;

		return new String(ch);
	}

	private byte[] encrypt(byte[] s, int mode, byte[] salt) {
		byte[] ciphertext;
		// byte[] salt = { -57, 115, 33, -116, 126, -56, -18, -103 };
		try {
			SecretKeyFactory keyFac = SecretKeyFactory.getInstance("DES");
			DESKeySpec desKeySpec = new DESKeySpec(salt);
			SecretKey desKey = keyFac.generateSecret(desKeySpec);

			Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

			cipher.init(mode, desKey);

			ciphertext = cipher.doFinal(s);
		} catch (Exception e) {
			logger.error("\n DES Ecrypt error", e);
			ciphertext = null;
		}
		return ciphertext;
	}
   
	public static String encrypt(String s) {
		StringBuffer sb = new StringBuffer(1024);
		byte[] b = encrypt(s.getBytes(), 1);
		if ((b == null) || (b.length < 1))
			return null;
		Random r = new Random(s.length());

		for (int i = 0; i < b.length; ++i) {
			char c = (char) (r.nextInt(10) + 71);
			sb.append(c);
			if (b[i] < 0) {
				c = (char) (r.nextInt(10) + 81);
				b[i] = (byte) (-b[i]);
				sb.append(c);
			}
			sb.append(Integer.toString(b[i], 16).toUpperCase());
		}
		sb.deleteCharAt(0);
		return sb.toString();
	}
	
	public static String decrypt(String s) {
		if (s.length() < 1) {
			return null;
		}

		String[] sByte = s.split("[G-Pg-p]");
		byte[] b = new byte[sByte.length];

		for (int i = 0; i < b.length; ++i) {
			char c = sByte[i].charAt(0);
			if (((c >= 'Q') && (c <= 'Z')) || ((c >= 'q') && (c <= 'z')))
				b[i] = (byte) (-Byte.parseByte(sByte[i].substring(1), 16));
			else
				b[i] = Byte.parseByte(sByte[i], 16);
		}

		byte[] ch = encrypt(b, 2);
		if ((ch == null) || (ch.length < 1))
			return null;

		return new String(ch);
	}

	private static byte[] encrypt(byte[] s, int mode) {
		byte[] ciphertext;
		byte[] salt = { -57, 115, 33, -116, 126, -56, -18, -103 };
		try {
			SecretKeyFactory keyFac = SecretKeyFactory.getInstance("DES");
			DESKeySpec desKeySpec = new DESKeySpec(salt);
			SecretKey desKey = keyFac.generateSecret(desKeySpec);

			Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

			cipher.init(mode, desKey);

			ciphertext = cipher.doFinal(s);
		} catch (Exception e) {
			// ARE.getLog().debug("DES Ecrypt error", e);
			System.out.print("\n DES Ecrypt error");
			ciphertext = null;
		}
		return ciphertext;
	}
	
	public static void main(String[] args) throws Exception {
		DESEncrypt desEncrypt = new DESEncrypt();
		String encryptString = "666888";
//		String salt = new Random().nextInt(99999999) + "_" + SnowFlakeFactory.getInstance().nextId();
//		System.out.println("salt is:" + salt);
//		String after = desEncrypt.encrypt(encryptString, salt.getBytes());
//		System.out.println(encryptString + "加密后：" + after);
//		System.out.println(after + "解密后：" + desEncrypt.decrypt(after, salt.getBytes()));
		System.out.println(DESEncrypt.encrypt("00000"));
		System.out.println(DESEncrypt.decrypt("1AM74OV5FP3CNT3EM20J1J58MW69M1FOV20OR2AOZ32K59HW70M51"));
		System.out.println(DESEncrypt.decrypt("W3FM49OR68J27NU65G5DH31O35"));
	}
}

package tf.ifms.zyfdd.handler;

import java.util.Map;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.utils.hb.model.Pager;

/**
 * <AUTHOR>
 * @describe 获取房抵贷进件房产列表
 */
public class GetMorgloanHouseList implements CommonHandlerStub{
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		
		BRC.getLog().info("----------进入GetMorgloanHouseList参数为" + arg0);
		RespDataBusData rdbd = new RespDataBusData();
		// 获取前台参数
		long bankid = arg0.getCommDC().getiBanks();
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 获取数据库会话
		DaoTemplate daoTemplate = arg0.getReqDBPoolservice().getDaoTemplate();
		try {
			String sql = "select t1.* from morgloan_fs_house t1 where t1.bankid="+bankid+"";
			System.out.println(sql);
			daoTemplate.getMapListByLikeSql(sql, whereMap, pager);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取GetMorgloanHouseList列表失败!");
			return rdbd;
		}
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取GetMorgloanHouseList列表成功!");
		return rdbd;
	}

}

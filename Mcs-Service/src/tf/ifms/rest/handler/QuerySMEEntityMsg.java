package tf.ifms.rest.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;

import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.InvestigationUtil;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

/**
 * @description 查询美商贷实调阶段客户实体信息(企业做主借人) 1465
 * <AUTHOR>
 * @date 2020年6月19日
 * update 20200917 根据美兴给出PAD页面原型修改接口与之适配
 */
public class QuerySMEEntityMsg {
    private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());

    public RespTfData querySMEEntityMsg(ReqTfData reqTfData, Session session) throws Exception {
        RespTfData respTfData = new RespTfData();
        try {
            log.info("参数信息：" + reqTfData);

            // 获取session
            DaoTemplate daoTemplate = new DaoTemplateImpl();
            daoTemplate.setExternalSession(session);

            // 获取前台参数
            String applyno = (String) reqTfData.getReqEntity("applyno");// 申请编号
            String cifid = (String) reqTfData.getReqEntity("cifid");// 客户编号
            String operid = (String) reqTfData.getReqEntity("operid");// 客户编号
            long bankid =  Long.parseLong(String.valueOf(reqTfData.getReqEntity("bankid")));// 法人号

            String contno = InvestigationUtil.getStringValueBySql(daoTemplate,"select contno from af_main_info where applyno='"+applyno+"'");

            // 获取客户所属机构信息
            Map map = new HashMap();

            String mainSql ="select cifid as doecifid,cliname as doecliname,certtype,legalperson,shareholder_rate,reg_sum,paid_sum,"
                    + "doeaddr,scope,doetype,industrytype,industryname,off_type,legaltel,legalcerttype,legalcertno,"
                    + "sex,legalbirth,legalmarray,legaledu,legaladdr,oprate_type,oper_date,continue_date,"
                    + "bus_speterm,emp_num,compressure,main_bus,relcliname,busenvironment,endamage,maplocation_address,"
                    + "manability,financebility,transtype,is_co_oper,bus_area,oper_speterm,oper_addr_speterm,"
                    + "is_hasbuslic,buslicno,bus_pla,mon_sale,mon_profit_type,mon_profit,accounts,stock,"
                    + "total_asset,total_liability,regioncode,regionname,equily_situation,if_effect_env,operaterent,"
                    + "other_bus,other_bus_brf,businvolved,brf,busmaindesc,bussupplierdesc,busdowndesc,"
                    + "pjinout,ownfunds,appsum,othfschannel,loanpurpose,saledate1,salefunds1,saledate2,"
                    + "salefunds2,saledate3,salefunds3,profitdate1,busprofit1,profitdate2,busprofit2,profitdate3,"
                    + "busprofit3,highmonth,lowmonth,normalmonth,salevolume1,salevolume2,salevolume3,seareasons,"
                    + "off_contact,off_tel,off_addr,basbank,paybank,chk_account,crdcardno,off_email,credit_code,registration_num,"
                    + "approval_date,business_start_date,business_end_date,registrar,registration_status,business_address,"
                    + "business_scope,now_bus_region_name,now_bus_region_code,labor_wage,bus_fee_pay,aff_desc,is_borsign,environmentallevel"
                    + " from bus_doe_base"
                    + " where applyno='"+applyno+"'"
                    + " and bankid='"+bankid+"' and exists (select 1 from ac_businesscont where contno='"+contno+"' and is_borsign='1')  ";
            Map uniqueMapRowBySql = daoTemplate.getUniqueMapRowBySql(mainSql);
            //modif by wlong 新增 is_holder_change,holder_change_reason 字段
            if(uniqueMapRowBySql !=null ){
                String doecifid = uniqueMapRowBySql.get("doecifid").toString();
                mainSql = "select is_holder_change,holder_change_reason from rest_relBus where cifid='"+doecifid+"' and applyno='"+applyno+"' ";
                Map restMap = InvestigationUtil.getMapValueBySql(daoTemplate,mainSql);
                if (restMap != null) {
                    uniqueMapRowBySql.putAll(restMap);
                }
                map.putAll(uniqueMapRowBySql);
            }

            // 获取股东列表信息
            ArrayList<Map> busShareholderList = new ArrayList<Map>();
            mainSql = "select cliname as shareholder_name,cstype,share_ratio,subscribed_capital,subscription_date"
                    + " from bus_doe_owner"
                    + " where applyno = '"+applyno+"' and ispreloan = '0'";
            List<Map> mapListBySql = daoTemplate.getMapListBySql(mainSql);
            if (mapListBySql != null) {
                busShareholderList.addAll(mapListBySql);
                map.put("busShareholderList", busShareholderList);
            }
            // 获取案件列表信息
            ArrayList<Map> busCaseList = new ArrayList<Map>();
            mainSql = "select case_type,case_status,case_cause,subject_amount,judgment_date,litigation_duration"
                    + " from bus_litigation_case"
                    + " where applyno = '"+applyno+"'";
            mapListBySql = daoTemplate.getMapListBySql(mainSql);
            if (mapListBySql != null) {
                busCaseList.addAll(mapListBySql);
                map.put("busCaseList", busCaseList);
            }
            // 获取关联企业信息
            ArrayList<Map> associatedEnterpriseList = new ArrayList<Map>();
            mainSql = "select reltype,business_name,legal_person,owner_ratio,establishment_time,serid"
                    + " from bus_doe_relation"
                    + " where applyno = '"+applyno+"'";
            mapListBySql = daoTemplate.getMapListBySql(mainSql);
            if (mapListBySql != null) {
                associatedEnterpriseList.addAll(mapListBySql);
                map.put("associatedEnterpriseList", associatedEnterpriseList);
            }

            respTfData.setRespEntity("map", map);
            respTfData.setRespDesc("SME客户关联实体信息获取成功");
            respTfData.setRespCode("0000");
            return respTfData;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            respTfData.setRespDesc("服务器异常");
            respTfData.setRespCode("7777");
            return respTfData;
        }
    }
}

package tf.ifms.schedule;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;

import tf.cbs.brc.io.FilesTools;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.IfmsTool;
import tf.mcs.dao.CnBasicinfoDao;
import tf.mcs.dao.CnCoreuserRelDao;
import tf.mcs.dao.CnCredenceDao;
import tf.mcs.dao.CnInstLevelDao;
import tf.mcs.dao.CnPostDao;
import tf.mcs.dao.CnPrdtApplyinstcodeDao;
import tf.mcs.dao.CnPrdtGuartypeDao;
import tf.mcs.dao.CnPrdtRepaytypeDao;
import tf.mcs.dao.CnRptPostDao;
import tf.mcs.dao.ComBranchDao;
import tf.mcs.model.CnBasicinfo;
import tf.mcs.model.CnCoreuserRel;
import tf.mcs.model.CnCredence;
import tf.mcs.model.CnInstLevel;
import tf.mcs.model.CnPost;
import tf.mcs.model.CnPrdtApplyinstcode;
import tf.mcs.model.CnPrdtGuartype;
import tf.mcs.model.CnPrdtRepaytype;
import tf.mcs.model.CnRptPost;
import tf.mcs.model.ComBranch;
import tf.tax.util.McsTaskAspect;
import tf.tools.HrTool;
import tf.tools.StringTools;

/**
 * 同步美兴机构信息
 *
 * @version V1.0 wyq@NC2021年9月3日
 */
@Component
public class SendReqdepHrTask {
	private static Logger logger = LoggerFactory.getLogger(SendReqdepHrTask.class);
	@Autowired
	SessionFactory sessionFactory;

	/**
	 * 每天同步两次(中午11点、晚11点)
	 * 
	 * @version v1.0 wyq ********
	 */
	public void sendReqdepHr() {
		String desc = "同步HR机构";
		logger.info(desc + "开始执行");
		Session session = null;
		try {
			session = sessionFactory.openSession();
			session.beginTransaction();
			boolean isSystemEod = IfmsTool.isSystemEod(session, 100000L);
//			if (isSystemEod) {
//				logger.info("系统当前正在日终，停止查询逾期状态");
//				session.getTransaction().commit();
//				return;
//			}
			Long bankid =100000L;
			String workdate = IfmsTool.getWorkdate(session, bankid);
			DaoTemplate daoTemplate = new DaoTemplateImpl();
			daoTemplate.setExternalSession(session);
			HrTool hrTool=new HrTool();
			String address=String.valueOf(daoTemplate.getUniqueValueBySql("SELECT val FROM com_parm WHERE parm_code='HR' and parm_seqn='1'"));
			String appKey=String.valueOf(daoTemplate.getUniqueValueBySql("SELECT val FROM com_parm WHERE parm_code='HR' and parm_seqn='2'"));
			String appSecret=String.valueOf(daoTemplate.getUniqueValueBySql("SELECT val FROM com_parm WHERE parm_code='HR' and parm_seqn='3'"));
			String tokenUrl="/api/user/token";
			String empurl="/api/class/org_department";
			String httpTokenUrl=address+tokenUrl+"?appKey="+appKey+"&appSecret="+appSecret;
			String httpEmpUrl=address+empurl+"?all=1&fovs=1&pageSize=3000";
			String depInfo=hrTool.sendRequestHr(httpTokenUrl, httpEmpUrl);
			List<HashMap> arylist = new ArrayList<HashMap>();
			//获取返回数控
			JSONArray jsonarray=JSONArray.parseArray(depInfo);
			arylist=JSONArray.parseArray(jsonarray.toJSONString(),HashMap.class);
			for(int i=0;i<arylist.size();i++) {
				HashMap hMap=arylist.get(i);
				String hrcode=String.valueOf(hMap.get("id"));
				String instcode=String.valueOf(daoTemplate.getUniqueValueBySql("SELECT a.new_instcode from cn_instmapping_info a WHERE a.hr_instcode='"+hrcode+"'"));
				if(!"null".equals(instcode) && !"null".equals(instcode) && null!=instcode) {
					String instname=String.valueOf(hMap.get("name"));
					String shortname=String.valueOf(hMap.get("fullName"));
					String level=String.valueOf(hMap.get("level"));//一级部门就是1、二级部门就是2，依次类推
					String lvls="";
					String sinstcode="";
					String sinstname="";
					if("1".equals(level)) {
						lvls="10";
					}else if("2".equals(level)) {
						lvls="20";
						sinstname=String.valueOf(hMap.get("orgIdText"));
					}else if("3".equals(level)) {
						lvls="30";
						sinstname=String.valueOf(hMap.get("secondIdText"));
					}else {
						lvls="40";
						sinstname=String.valueOf(hMap.get("thirdIdText"));
					}
					String inststate=String.valueOf(hMap.get("deleted"));
					String jgkysj=String.valueOf(hMap.get("jgkysj"));//登记日期
					//20230423 日期格式化
					String date = new SimpleDateFormat("yyyyMMdd").format(
							new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(jgkysj));

					logger.info("instcode:"+instcode+" jgkysj:"+jgkysj+" date:"+date);

					//SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
					CnBasicinfo cnBasicinfo=new CnBasicinfo();
					CnBasicinfoDao cnBasicinfoDao=new CnBasicinfoDao();
					cnBasicinfoDao.setExternalSession(session);
					CnInstLevel cnInstLevel = new CnInstLevel();
					CnInstLevelDao cnInstLevelDao = new CnInstLevelDao();
					cnInstLevelDao.setExternalSession(session);
					cnBasicinfo.setBankid(bankid);
					cnBasicinfo.setInstcode(instcode);
					CnBasicinfo oldcnBasicinfo=cnBasicinfoDao.getEntityByPK(cnBasicinfo);
					if(oldcnBasicinfo!=null) {
						oldcnBasicinfo.setPostcode(date);
						oldcnBasicinfo.setInstname(instname);
						oldcnBasicinfo.setShortname(shortname);
						oldcnBasicinfo.setLvl(lvls);//级别10总部 20分中心 30分公司 40 网点
						//oldcnBasicinfo.setSinstcode(sinstcode);//上级机构号
						//oldcnBasicinfo.setSinstname(sinstname);//上级机构名称
						cnBasicinfoDao.update(oldcnBasicinfo);
					}else {
						cnBasicinfo.setPostcode(date);
						//处理cnbasicinfo机构信息
						cnBasicinfo.setInstname(instname);
						cnBasicinfo.setShortname(shortname);
						cnBasicinfo.setLvl(lvls);//级别10总部 20分中心 30分公司 40 网点
						cnBasicinfo.setGroupflg("0");
						cnBasicinfo.setPutoutflg("0");
						cnBasicinfo.setInststate(inststate);
						//处理cnInstLevel机构信息
						String contMySQL = "select max(cast( substr(sortno,2,length(sortno)-1) as signed)) + 1 cont from cn_basicinfo where bankid='"
								+ bankid
								+ "' and instcode<>'"
								+ cnBasicinfo.getInstcode()
								+ "'";
						int cont = Integer.valueOf(String.valueOf(daoTemplate
								.getUniqueValueBySql(contMySQL)));
						String sortno = "";
						if (cont > 0 && cont < 10) {
							sortno = "A" + "0" + cont;
						} else {
							sortno = "A" + cont;
						}
						cnBasicinfo.setSortno(sortno);
						// added by jinran ********
						// 新增机构同时更新cn_inst_level 从属机构关系信息表
						//String instcode = cnBasicinfo.getInstcode();// 当前新增机构编号
						//String sinstcode = cnBasicinfo.getSinstcode();// 当前新增机构上级机构编号
						if ("root".equals(sinstcode)) {
							throw new Exception("同一法人机构下只允许存在一个总行!");
						} else {
							
							cnInstLevel.setBankid(bankid);
							cnInstLevel.setInstcode(instcode);
							cnInstLevel.setInstname(cnBasicinfo.getInstname());
							cnInstLevel.setShortname(cnBasicinfo.getShortname());
							cnInstLevel.setLvl(cnBasicinfo.getLvl());
							cnInstLevel.setSortno(sortno);
							cnInstLevel.setInststate(cnBasicinfo.getInststate());
							cnInstLevel.setInstmgr(cnBasicinfo.getInstmgr());
							cnInstLevel.setTelno(cnBasicinfo.getTelno());
							cnInstLevel.setAddress(cnBasicinfo.getAddress());
							cnInstLevel.setPostcode(cnBasicinfo.getPostcode());
					}
						cnBasicinfoDao.insert(cnBasicinfo);
						cnInstLevelDao.insert(cnInstLevel);
					}
				
				}
		   }
			session.getTransaction().commit();
			logger.info(desc + "执行成功");
		} catch (Exception e) {
			try{
				McsTaskAspect mcsTaskAspect = new McsTaskAspect();
				mcsTaskAspect.doAfterThrowing("SendReqdepHrTask");
			}catch (Exception a){
				logger.error("推送钉钉消息异常", a);
			}
			logger.error("执行异常-------------------------------------------", e);
			if (session != null){session.getTransaction().rollback();}
		} finally {
			if (session != null)
				session.close();
		}
	}
	
	 public boolean isJsonArray(String content) {
		    try {
		    	JSONArray jsonStr = JSONArray.parseArray(content);
		        return true;
		    } catch (Exception e) {
		        return false;
		    }
		}
	 
	 public static void main(String[] args) throws ParseException {


		 // 2023-04-14T10:26:30.000Z

		 String date = new SimpleDateFormat("yyyyMMdd").format(
		 		new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse("2023-04-14T10:26:30.000Z"));

		 System.out.println(date);


			ClassPathXmlApplicationContext applicationContext = new ClassPathXmlApplicationContext("applicationContext.xml");
			applicationContext.start();
			BRC.setBrcContext(applicationContext);
			BRC.setRunMode(1);
			// 初始化操作
			BRC.getLog().debug("开始日终");
			String sAppHome = System.getProperty("user.dir");
			System.out.println("\n 当前路径是:" + sAppHome);
			
			BRC.setProperty("APPHOME", sAppHome);
			java.io.File cf = FilesTools.findFile("brc.xml");
			if (cf != null) {
				BRC.init(cf);
			} else {
			}
			// 日终检查和初始化初始化
			SessionFactory sessionFactory = (SessionFactory) BRC.getBrcContext().getBean("sf");
			Session psessCheck = sessionFactory.openSession();
			HashMap hm = new HashMap();
			DaoTemplateImpl daoTemplate = new DaoTemplateImpl();
			daoTemplate.setExternalSession(psessCheck);
			psessCheck.beginTransaction();
			hm.put("daoTemplate", daoTemplate);
			//获取日期
			long bankid = 100000;
			String sql = "select sys_date from com_sys_parm where bankid = " + bankid ;
			String workdate = (String) daoTemplate.getUniqueValueBySql(sql);
			hm.put("txdate", workdate);
			hm.put("bankid", bankid);
			hm.put("externalSession", psessCheck);
			HrTool hrTool =new HrTool();
			SendReqdepHrTask srot=new SendReqdepHrTask();
			try {
				String url="https://microcredchina.darensoft.com/api/user/token?appKey=10f006b98b9444dc8c808dca59fe326e&appSecret=35451d9eb5474fdcb7501fd51c665eee";
				String token=hrTool.sendRequestToken(url);
				String httpUrl="https://microcredchina.darensoft.com/api/class/view_pers_employee_biz?all=1&fovs=1&pageSize=3000";//员工
				String httpUrl1="https://microcredchina.darensoft.com/api/class/org_department?all=1&fovs=1&pageSize=3000";//部门
				//hrTool.sendRequestHr(url,httpUrl);
				//srot.sendReqdepHr(psessCheck);
				//System.out.println("---------------------"+token);
				psessCheck.getTransaction().commit();
				psessCheck.close();
			} catch (Exception e) {
				psessCheck.getTransaction().rollback();
				psessCheck.close();
				StringTools.stackLog(e);
			}
		}
}

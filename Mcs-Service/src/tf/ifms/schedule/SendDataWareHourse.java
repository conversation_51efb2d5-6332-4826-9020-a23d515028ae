package tf.ifms.schedule;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import tf.brc.gl.GLFactory;
import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.brc.prt.PrintMgrFactory;
import tf.cbs.brc.io.FilesTools;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.DateTool;
import tf.ifms.rpc.handler.IfmsTool;
import tf.ifms.wechat.handler.WechatPushUtil;
import tf.mcs.PublicMethods;
import tf.mcs.dao.AcBusinesscontDao;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.CpcnpayRecordLogDao;
import tf.mcs.dao.CustCouponRelDao;
import tf.mcs.dao.CustMsgInfoDao;
import tf.mcs.dao.DataWarehouseRecordDao;
import tf.mcs.dao.DeFlowrecordDao;
import tf.mcs.dao.IndBaseDao;
import tf.mcs.eod.handler.EodSendDataWareHouse;
import tf.mcs.model.AcBusinesscont;
import tf.mcs.model.AcBusinessvch;
import tf.mcs.model.CpcnpayRecordLog;
import tf.mcs.model.CustCouponRel;
import tf.mcs.model.CustMsgInfo;
import tf.mcs.model.DataWarehouseRecord;
import tf.mcs.model.DeFlowrecord;
import tf.mcs.model.FileSystemResp;
import tf.mcs.model.IndBase;
import tf.mcs.service.GlobalInit;
import tf.tax.util.McsTaskAspect;
import tf.tools.Arith;
import tf.tools.SendMsgTools;
import tf.tools.SnowFlakeFactory;
import tf.tools.UniversalObject;
import tf.tools.flatRate;

/**
 * 生产数仓数据文件
 *
 * @version V1.0 wyq@NC2020年8月6日
 */
@Component
public class SendDataWareHourse {
	private static Logger logger = LoggerFactory.getLogger(SendDataWareHourse.class);
	@Autowired
	SessionFactory sessionFactory;

	public void sendDataWareHourse() {
		String desc = "生成数仓文件并发送";
		logger.info(desc + "开始执行");
		Session session = null;
		try {
			session = sessionFactory.openSession();
			session.beginTransaction();
			boolean isSystemEod = IfmsTool.isSystemEod(session, 100000L);
//			if (isSystemEod) {
//				logger.info("系统当前正在日终，停止查询");
//				session.getTransaction().commit();
//				return;
//			}
			Long bankid =100000L;
			String workdate = IfmsTool.getWorkdate(session, bankid);
			DaoTemplate daoTemplate = new DaoTemplateImpl();
			daoTemplate.setExternalSession(session);
			HashMap hm=new HashMap();
			hm.put("daoTemplate", daoTemplate);
			hm.put("session", session);
			hm.put("txdate",workdate);
			hm.put("bankid", 100000);
			doOperation(hm);
			session.getTransaction().commit();
			logger.info(desc + "执行成功");
		} catch (Exception e) {
			try{
				McsTaskAspect mcsTaskAspect = new McsTaskAspect();
				mcsTaskAspect.doAfterThrowing("SendDataWareHourse");
			}catch (Exception a){
				logger.error("推送钉钉消息异常", a);
			}
			logger.error("执行异常-------------------------------------------", e);
			if (session != null){session.getTransaction().rollback();}
		} finally {
			if (session != null)
				session.close();
		}
	}
	
	public void doOperation(HashMap hm) throws Exception {
		try {

			String message = "进入EodSendDataWareHouse类处理-------------------" + hm.get("txdate") + "日-" + "生成发送数仓文件";
			BRC.getLog().info(message);
			// 获取前台传入参数
			DaoTemplate daoTemplate = (DaoTemplate) hm.get("daoTemplate");
//			String workdate = hm.get("txdate").toString();
			String workdate= (String)daoTemplate.getUniqueValueBySql("select lst_date from com_sys_parm");
			long bankid = Long.valueOf(hm.get("bankid").toString()).longValue();
			
			//获取提供数仓文件及对应路径
			ArrayList resultList=  (ArrayList)daoTemplate.getMapListBySql("select * from data_warehouse_def where bankid="+bankid+ " and isvalid='1'");
			Map hmap = new HashMap();
			//循环获取
			for(int i=0;i<resultList.size();i++){
				HashMap resultHm = (HashMap) resultList.get(i);
				String dwhName=String.valueOf(resultHm.get("dwhname"));//数仓文件
				String fileName=String.valueOf(resultHm.get("filename"));//文件名称
				String bucketname=String.valueOf(resultHm.get("bucketname"));//文件上传S3桶名
				fileName = fileName.replace("#YYYYMMDD#", workdate);
				String localPath=String.valueOf(resultHm.get("localpath"));//本地路径
				String uploadPath=String.valueOf(resultHm.get("uploadpath"));//上传路径
				BRC.getLog().info("dwhName"+dwhName);
				BRC.getLog().info("fileName"+fileName);
				BRC.getLog().info("localPath"+localPath);
				BRC.getLog().info("uploadPath"+uploadPath);
				//根据不同数仓文件传入不同取数sql
				String Sql="";
				if("MCCLoanReport".equals(dwhName))
				{
					 Sql="select 'LoanId','Category','CategoryDesc','ClientName','ClientFirstName','RepayAccount','LoanCycle','InterestRate','ClientID','ClientAge','ClientSector','SectorDesc','LoanPurpose','BranchCode','CurrentDaoId','CurrentDaoName','ClientGender','ClientRating','LoanApplyDate','CommercialProdCode','CommercialProdName','EarlyRepayDate',"
							+ "'TerminationFee','DisbursementDate','CurrentMaturityDate','LoanTerm','DisbursementAmount','AllDisbursementCharges','RepaymentType','OutstandingPrincipal','PdIndicator','PdDueDate','LastRepaymentDate','IsRestructured','CurrentOverdueDays','MaxOverdueDays','TotalOverdueDays','TotalOverdueAmountPR','TotalOverdueAmountIN','TotalOverdueAmountCH',"
							+ "'TotalOverdueAmountOthers','TotalOverdueAmount','PrincipalOverdueAmount1_30','PrincipalOverdueAmount31_60','PrincipalOverdueAmount61_90','PrincipalOverdueAmount91_180','PrincipalOverdueAmount181_360','PrincipalOverdueAmount361','TotalAmountPE','TotalAmountPS','LoanStatus','AccruedInterests','NextScheduleDate','NextScheduledAmount','FirstScheduleDate',"
							+ "'LoanOverdueStatus','UpdateDate','LoanProcess','LoanCreditRisk','CompanyCode','TruckCompanyCode','TruckCompanyName','ChannelType','ChannelID','InvestigatorCode','InvestigatorName','ClientParentCompanyName','BusinessLicense','AmountIncreaseDateTime','PrincipalRepayDate','PrincipalRepayAmount','ExceptionReasson','RemainingTerm','CurrentOverdueTerm',"
							+ "'AmountIncrease','DisbursementDaoId','DisbursementDaoName','BeforeChangeDaoId','BeforeChangeDaoName','AfterChangeDaoId','AfterChangeDaoName','CollateralExecutionValue','ClientDebtRatio','TotalAssets','TotalLiability','AnnualTurnover','BusinessOpeningDate','CompanyGuarantorName','RelatedClientID','RelatedClientName','RelationCode','LoanApplStageCode1',"
							+ "'LoanApplStageCode2','LoanApplStageCode3','LoanApplStageCode4','LoanApplStageCode5','LoanApplStageInputterCode1','LoanApplStageInputterCode2','LoanApplStageInputterCode3','LoanApplStageInputterCode4','LoanApplStageInputterCode5','LoanApplStageInputterName1','LoanApplStageInputterName2','LoanApplStageInputterName3','LoanApplStageInputterName4',"
							+ "'LoanApplStageInputterName5','LoanApplStageAuthoriserCode1','LoanApplStageAuthoriserCode2','LoanApplStageAuthoriserCode3','LoanApplStageAuthoriserCode4','LoanApplStageAuthoriserCode5','LoanApplStageAuthoriserName1','LoanApplStageAuthoriserName2','LoanApplStageAuthoriserName3','LoanApplStageAuthoriserName4','LoanApplStageAuthoriserName5','LoanApplStageDate1',"
							+ "'LoanApplStageDate2','LoanApplStageDate3','LoanApplStageDate4','LoanApplStageDate5','LoanInputterCode','LoanInputterName','TaxPolicy','LoanDisbursementCommission','CollateralIDList','CollateralTypeList','RestructureDate','TerminationType','HouseholdRegisterType','RequestedAmount','RequestedTerm','ApprovedAmount','ApprovedTerm','OriginalMaturityDate'," 
							+ "'LastDaoUpdateDate','RelatedClientID1','RelatedClientName1','RelationCode1','RelatedClientID2','RelatedClientName2','RelationCode2','RelatedClientID3','RelatedClientName3','RelationCode3','RelatedClientID4','RelatedClientName4','RelationCode4','RelatedClientID5','RelatedClientName5','RelationCode5','CollateralID1','CollateralType1','CollateralID2','CollateralType2',"
							+ "'CollateralID3','CollateralType3','CollateralID4','CollateralType4','LoanApplCode','SpouseSigniture','FamilyTotalAsset','FamliyTotalLiability','FamilyIncome','FamliyExpense','CompanyTotalAsset','CompanyLiquidAsset','CompanyTotalLiability','CompanyLiquidLiability','CorporateIncome','AbilityToRepayRate','SpouseID','SpouseName','SpouseRelationCode','CollateralExecutionValue1',"
							+ "'CollateralExecutionValue2','CollateralExecutionValue3','ClientDisbursementBank','ClientDisbursementBankAccount','ExceptionType','AdjustAssetLiabilityRatio','TotalAdjustAssetLiabilityRatio','SMECustRiskRating','SMETransRiskRating','LoanApproveAmtBeforeChange','LoanApproveAmtAfterChange','LoanApproveAmtInputterID','LoanApproveAmtAuthoriserID','LoanApplicationCurrNo',"
							+ "'LoanApproveAmtInputterName','LoanApproveAmtAuthoriserName' " 
							+ " union all "  
							+ "select a.contno LoanId ,(case when u.oprdtno is not null then u.oprdtno else (select foreign_itemcode from cn_data_dictionary where itemno='dt0052' and a.prdt_no=itemcode and bankid=a.bankid) end ) Category," +
							"(case when u.oprdtno is not null then (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode not like 'F%' and foreign_itemcode=u.oprdtno ) else " + 
							" (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode=a.prdt_no and bankid=a.bankid ) end )  CategoryDesc,'' ClientName,upper(to_pinyin(a.cliname)) ClientFirstName, " + 
							"nvl(i.noteno,i.cifid) as RepayAccount,c.lncnt LoanCycle,a.arate InterestRate,a.cifid ClientID,c.age ClientAge,j.industrytype ClientSector,j.indname SectorDesc, " + 
							"a.purpose LoanPurpose,case when p.foreign_itemcode is null then a.manage_instcode else p.foreign_itemcode end BranchCode,case when d.dao is null then a.manage_operid else d.dao end CurrentDaoId, case when d.daoname is null then y.opername else d.daoname end CurrentDaoName, " + 
							"case when c.sex='1' then 'MALE' when c.sex='2' then 'FEMALE' end ClientGender,'' ClientRating,e.app_date LoanApplyDate,w.foreign_itemcode CommercialProdCode, " + 
							"w.foreign_itemname CommercialProdName,'' EarlyRepayDate, " + 
							"(select nvl(sum(breach_fee),0) from af_main_info t1 where t1.vchno=a.vchno and t1.bankid=a.bankid and t1.breach_fee_sts='2' and t1.isbreach_fee='0' and t1.changer_type='05') TerminationFee, " + 
							"a.begindate DisbursementDate,a.enddate CurrentMaturityDate,a.term LoanTerm,a.bus_sum DisbursementAmount,a.loanfee AllDisbursementCharges,l.foreign_itemcode RepaymentType, " + 
							"a.norm_bal OutstandingPrincipal,case when a.curr_overdays> 0 THEN 'Y' ELSE 'N' END INDICATOR,a.lo_date PdDueDate,g.lastrepaydate LastRepaymentDate,  " + 
							"(CASE WHEN a.core_prdt_no like '4%' or a.prdt_no like 'F%' THEN 'Y' ELSE '' END) IsRestructured,a.curr_overdays CurrentOverdueDays,a.overdue_day MaxOverdueDays, " + 
							"a.total_over_days TotalOverdueDays,k.lo_amt TotalOverdueAmountPR,k.lo_intst TotalOverdueAmountIN,'0' TotalOverdueAmountCH,'0' TotalOverdueAmountOthers,(k.lo_amt+k.lo_intst+k.lo_over_intst)TotalOverdueAmount, " + 
							"m.over30 PrincipalOverdueAmount1_30,m.over60 PrincipalOverdueAmount31_60,m.over90 PrincipalOverdueAmount61_90,m.over180 PrincipalOverdueAmount91_180,m.over360 PrincipalOverdueAmount181_360,m.over361 PrincipalOverdueAmount361,k.lo_over_intst TotalAmountPE, " + 
							"'0' TotalAmountPS,(CASE WHEN a.vch_sts = '30'  THEN 'LIQ'  ELSE 'CUR' END) as LoanStatus, " + 
							"-(b.in_normal_intst + b.in_over_intst + b.out_normal_intst + b.out_over_intst) AccruedInterests, a.next_pay_int_date NextScheduleDate , " + 
							" nvl((a.next_pay_amt+a.next_pay_int),0) NextScheduledAmount,a.first_due_date FirstScheduleDate,h.loansts LoanOverdueStatus,'"+workdate+"' UpdateDate, " + 
							"(CASE WHEN a.occurtype = '1' THEN 'NEW.LOAN' WHEN a.occurtype = '3' THEN 'RENEW' WHEN a.occurtype = '5' THEN 'WCSP.RENEW' ELSE '' END) as LoanProcess,'' LoanCreditRisk, " + 
							"x.foreign_itemcode AS CompanyCode,'' TruckCompanyCode,'' TruckCompanyName, " + 
							"(CASE WHEN c.channeltype = '01' THEN '老推新' WHEN c.channeltype = '02' THEN '政府/协会渠道' WHEN c.channeltype = '03' THEN '代理点' WHEN c.channeltype = '04' THEN '产业链' ELSE c.channeltype END) as ChannelType, " + 
							"(CASE WHEN c.recom_type = '2' THEN c.recom_code ELSE '' END) AS ChannelID,case when z.dao is null  then e.pre_loan_operid else z.dao end InvestigatorCode, case when z.daoname is null then (select opername from cn_user t9 where e.pre_loan_operid=t9.operid) else z.daoname end InvestigatorName, " + 
							"c.workcorp ClientParentCompanyName,'' BusinessLicense,'' AmountIncreaseDateTime,'' PrincipalRepayDate,'' PrincipalRepayAmount,'' ExceptionReasson	, " + 
							"cast((select count(*) from ac_lnrepayschd t4 where t4.acid=a.loanac_id and t4.bankid=a.bankid and state='1' and rependdate>'"+workdate+"') as CHAR) RemainingTerm, " + 
							"cast((select count(*) from ln_lo t5 where t5.ac_id =a.loanac_id and t5.bankid=a.bankid and pay_type<>'1') as CHAR) CurrentOverdueTerm, " + 
							"'' AmountIncrease	,case when d.dao is null then a.manage_operid else d.dao end	DisbursementDaoId	,case when d.daoname is null then y.opername else d.daoname end DisbursementDaoName	, " + 
							"''	BeforeChangeDaoId	,''	BeforeChangeDaoName,''	AfterChangeDaoId,''	AfterChangeDaoName,''	CollateralExecutionValue,''	ClientDebtRatio,''	TotalAssets,''	TotalLiability, " + 
							"''	AnnualTurnover,''	BusinessOpeningDate,''	CompanyGuarantorName,''	RelatedClientID,''	RelatedClientName,''	RelationCode,''	LoanApplStageCode1,''	LoanApplStageCode2,''	LoanApplStageCode3,''	LoanApplStageCode4,''	LoanApplStageCode5, " + 
							"''	LoanApplStageInputterCode1,''	LoanApplStageInputterCode2,''	LoanApplStageInputterCode3,''	LoanApplStageInputterCode4,''	LoanApplStageInputterCode5,''	LoanApplStageInputterName1,''	LoanApplStageInputterName2,''	LoanApplStageInputterName3,''	LoanApplStageInputterName4,''	LoanApplStageInputterName5, " + 
							"''	LoanApplStageAuthoriserCode1,''	LoanApplStageAuthoriserCode2,''	LoanApplStageAuthoriserCode3,''	LoanApplStageAuthoriserCode4,''	LoanApplStageAuthoriserCode5,''	LoanApplStageAuthoriserName1,''	LoanApplStageAuthoriserName2,''	LoanApplStageAuthoriserName3,''	LoanApplStageAuthoriserName4,''	LoanApplStageAuthoriserName5, " + 
							"''	LoanApplStageDate1,''	LoanApplStageDate2,''	LoanApplStageDate3,''	LoanApplStageDate4,''	LoanApplStageDate5, " + 
							"case when d.dao is null then a.manage_operid else d.dao end	LoanInputterCode,case when d.daoname is null then y.opername else d.daoname end	LoanInputterName,''	TaxPolicy,a.loanfee	LoanDisbursementCommission,''	CollateralIDList,''	CollateralTypeList, " + 
							"(select max(resstartdate) from  af_loanrest_info t6 where t6.vchno=a.vchno and t6.bankid=a.bankid) RestructureDate, " + 
							"(case when b.ac_sts='6' then 'W' when a.core_prdt_no like '4%' or a.prdt_no like 'F%' then 'R' else 'D' end )	TerminationType	,case when c.is_farm='0' then 'Urban' when c.is_farm='1' then 'Rural' end 	HouseholdRegisterType	, " + 
							"e.app_sum	RequestedAmount,e.app_term	RequestedTerm,a.bus_sum	ApprovedAmount,a.term	ApprovedTerm,a.enddate	OriginalMaturityDate, " + 
							"''	LastDaoUpdateDate,''	RelatedClientID1,''	RelatedClientName1,''	RelationCode1,''	RelatedClientID2,''	RelatedClientName2,''	RelationCode2,''	RelatedClientID3,''	RelatedClientName3,''	RelationCode3,''	RelatedClientID4,''	RelatedClientName4,''	RelationCode4,''	RelatedClientID5,''	RelatedClientName5,''	RelationCode5, " + 
							"''	CollateralID1,''	CollateralType1,''	CollateralID2,''	CollateralType2,''	CollateralID3,''	CollateralType3,''	CollateralID4,''	CollateralType4, " + 
							"a.applyno	LoanApplCode,case when v.sps_will_sign='0' then 'NO' when v.sps_will_sign='1' then 'YES' end SpouseSigniture,o.fam_assets	FamilyTotalAsset,o.allbal	FamliyTotalLiability,c.family_income	FamilyIncome,''	FamliyExpense,q.busassetstotal	CompanyTotalAsset,''	CompanyLiquidAsset, " + 
							"''	CompanyTotalLiability,r.sdebttotal	CompanyLiquidLiability,s.incometotal	CorporateIncome,''	AbilityToRepayRate,n.sps_cifid	SpouseID,n.cliname 	SpouseName,'' SpouseRelationCode,''	CollateralExecutionValue1,''	CollateralExecutionValue2,''	CollateralExecutionValue3, " + 
							"(select foreign_itemcode from cn_data_dictionary where itemno ='dt0016' and itemcode=a.depaccbk_no and bankid=a.bankid)	ClientDisbursementBank,a.depacc_no	ClientDisbursementBankAccount,''	ExceptionType,q.aftbusdebtrate	AdjustAssetLiabilityRatio,q.aftalldebtrate	TotalAdjustAssetLiabilityRatio, " + 
							"(select foreign_itemname from cn_data_dictionary where itemno='dt0030' and itemcode=t.custriskrating and bankid=t.bankid)	SMECustRiskRating	, " + 
							"(select foreign_itemname from cn_data_dictionary where itemno='dt0030' and itemcode=t.adjustrating and bankid=t.bankid)	SMETransRiskRating	, " + 
							"''	LoanApproveAmtBeforeChange,''	LoanApproveAmtAfterChange,''	LoanApproveAmtInputterID,''	LoanApproveAmtAuthoriserID,''	LoanApplicationCurrNo,''	LoanApproveAmtInputterName,''  LoanApproveAmtAuthoriserName	 " + 
							" from   " + 
							"ac_businessvch a inner join ln_mst b on a.loanac_id=b.ac_id and a.bankid=b.bankid  " + 
							"left join ind_base c on a.cifid=c.cifid and a.bankid=c.bankid  " + 
							"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) d on a.manage_operid=d.itemcode and b.bankid=d.bankid " + 
							"left join app_main_info e on a.applyno=e.applyno and a.bankid=e.bankid  " + 
							"left join (select max(updcurrrepdate)lastrepaydate,acid,bankid from ac_lnrepayschd where state='1' and rependdate<'"+workdate+"' group by acid,bankid) g on a.loanac_id=g.acid and  a.bankid=g.bankid   " +
							"left join (select case when count(case when lo_ind='1' and repaystate='0' and DATEDIFF('"+workdate+"',updcurrrepdate)>90 then '1' end )>0 then 'NAB' " +
							"when  count(case when lo_ind='1' and repaystate='0' and DATEDIFF('"+workdate+"',updcurrrepdate)<=90 then '1' end )>0 then 'PDO' " +
							"when  count(case when lo_ind='1' and repaystate='2' then '1' end )>0 then 'CUR' " +
							"when sum(lo_ind)='0' then '' end loansts,acid,bankid from ac_lnrepayschd group by acid,bankid) h on a.loanac_id=h.acid and a.bankid=h.bankid " + 
							"left join (select * from cs_mst where ac_id='1' and bankid='"+bankid+"') i on a.cifid=i.cifid and a.manage_instcode=i.opn_br_no and a.bankid=i.bankid " + 
							"left join (select itemcode,bankid,foreign_itemcode industrytype,foreign_itemname indname from cn_data_dictionary where itemno='dt0054') j on a.industrytype=j.itemcode and a.bankid=j.bankid    " + 
							"left join (select ac_id,(sum(lo_amt) - sum(pay_lo_amt)) as lo_amt,(sum(lo_intst) - sum(pay_lo_intst)) as lo_intst,(sum(lo_over_intst) - sum(pay_over_intst)) as lo_over_intst from ln_lo group by ac_id) k on a.loanac_id = k.ac_id  " + 
							"left join (select * from cn_data_dictionary where itemno='dt0001') l on a.repay_type=l.itemcode and a.bankid=l.bankid   " + 
							"left join ( select nvl(sum(case when DATEDIFF('"+workdate+"',over_date)<=30 then lo_amt - pay_lo_amt else 0 end),0) over30, " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>31 and DATEDIFF('"+workdate+"',over_date)<=60 then lo_amt - pay_lo_amt else 0 end),0) over60, " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>61 and DATEDIFF('"+workdate+"',over_date)<=90 then lo_amt - pay_lo_amt else 0 end),0) over90, " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>91 and DATEDIFF('"+workdate+"',over_date)<=180 then lo_amt - pay_lo_amt else 0 end),0) over180, " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>181 and DATEDIFF('"+workdate+"',over_date)<=360 then lo_amt - pay_lo_amt else 0 end),0) over360, " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>361 then lo_amt - pay_lo_amt else 0 end),0) over361,ac_id,bankid from ln_lo   " + 
							"	where pay_type<>'1' group by ac_id,bankid) m on a.loanac_id=m.ac_id and a.bankid=m.bankid  " + 
							"left join ind_spouse_info n on a.cifid =n.cifid and a.bankid=n.bankid " + 
							"left join bus_survey_famassets o on a.applyno=o.applyno  and a.bankid=o.bankid  " + 
							"left join (select * from  cn_data_dictionary where  itemno='dt0004' and bankid ='"+bankid+"' and foreign_itemcode not in ('23','45','42')) p on a.manage_instcode=p.itemcode and a.bankid=p.bankid " + 
							"left join bus_survey_fsindex q on a.applyno=q.applyno and a.bankid=q.bankid  " + 
							"left join bus_survey_busdebt r on a.applyno = r.applyno and a.bankid=r.bankid  " + 
							"left join bus_survey_busprofit s on a.applyno=s.applyno and a.bankid=s.bankid " + 
							"left join bus_survey_result t on a.applyno=t.applyno and a.bankid=t.bankid " +
							"left join hd_loan u on a.contno=u.id " +
							"left join app_bus_info v on a.applyno=v.applyno and a.bankid=v.bankid  " +
							"left join (select * from  cn_data_dictionary where  itemno='dt0053' and bankid ='"+bankid+"') w on CONCAT(a.prdt_no,a.guar_type)=w.itemcode and a.bankid=w.bankid " +
							"left join (select * from  cn_data_dictionary where  itemno='dt0051' and bankid ='"+bankid+"') x on a.manage_instcode=x.itemcode and a.bankid=x.bankid " +
							"left join cn_user y on a.manage_operid=y.operid and a.bankid=y.bankid " +
							"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) z on e.pre_loan_operid=z.itemcode and e.bankid=z.bankid " +
							"where b.ac_sts not in ('9') and a.vch_sts<>'40' and (a.vch_sts not in ('20','30') or DATEDIFF('"+workdate+"',a.finishdate)<=7)";
						BRC.getLog().info("MCCLoanReport-SQL:" + Sql);
						
						String[] str = {"loanid","category","categorydesc","clientname","clientfirstname","repayaccount","loancycle","interestrate","clientid","clientage","clientsector","sectordesc","loanpurpose","branchcode","currentdaoid","currentdaoname","clientgender","clientrating","loanapplydate","commercialprodcode","commercialprodname","earlyrepaydate",
								"terminationfee","disbursementdate","currentmaturitydate","loanterm","disbursementamount","alldisbursementcharges","repaymenttype","outstandingprincipal","pdindicator","pdduedate","lastrepaymentdate","isrestructured","currentoverduedays","maxoverduedays","totaloverduedays","totaloverdueamountpr","totaloverdueamountin","totaloverdueamountch","totaloverdueamountothers","totaloverdueamount","principaloverdueamount130","principaloverdueamount3160","principaloverdueamount6190","principaloverdueamount91180","principaloverdueamount181360","principaloverdueamount361","totalamountpe",
								"totalamountps","loanstatus","accruedinterests","nextscheduledate","nextscheduledamount","firstscheduledate","loanoverduestatus","updatedate","loanprocess","loancreditrisk","companycode","truckcompanycode","truckcompanyname","channeltype","channelid","investigatorcode","investigatorname","clientparentcompanyname","businesslicense","amountincreasedatetime","principalrepaydate","principalrepayamount","exceptionreasson","remainingterm","currentoverdueterm","amountincrease","disbursementdaoid","disbursementdaoname","beforechangedaoid","beforechangedaoname","afterchangedaoid",
								"afterchangedaoname","collateralexecutionvalue","clientdebtratio","totalassets","totalliability","annualturnover","businessopeningdate","companyguarantorname","relatedclientid","relatedclientname","relationcode","loanapplstagecode1","loanapplstagecode2","loanapplstagecode3","loanapplstagecode4","loanapplstagecode5","loanapplstageinputtercode1","loanapplstageinputtercode2","loanapplstageinputtercode3","loanapplstageinputtercode4","loanapplstageinputtercode5","loanapplstageinputtername1","loanapplstageinputtername2","loanapplstageinputtername3","loanapplstageinputtername4",
								"loanapplstageinputtername5","loanapplstageauthorisercode1","loanapplstageauthorisercode2","loanapplstageauthorisercode3","loanapplstageauthorisercode4","loanapplstageauthorisercode5","loanapplstageauthorisername1","loanapplstageauthorisername2","loanapplstageauthorisername3","loanapplstageauthorisername4","loanapplstageauthorisername5","loanapplstagedate1","loanapplstagedate2","loanapplstagedate3","loanapplstagedate4","loanapplstagedate5","loaninputtercode","loaninputtername","taxpolicy","loandisbursementcommission","collateralidlist","collateraltypelist","restructuredate","terminationtype","householdregistertype","requestedamount","requestedterm","approvedamount","approvedterm","originalmaturitydate",
								"lastdaoupdatedate","relatedclientid1","relatedclientname1","relationcode1","relatedclientid2","relatedclientname2","relationcode2","relatedclientid3","relatedclientname3","relationcode3","relatedclientid4","relatedclientname4","relationcode4","relatedclientid5","relatedclientname5","relationcode5","collateralid1","collateraltype1","collateralid2","collateraltype2","collateralid3","collateraltype3","collateralid4","collateraltype4","loanapplcode","spousesigniture","familytotalasset","famliytotalliability","familyincome","famliyexpense","companytotalasset","companyliquidasset",
								"companytotalliability","companyliquidliability","corporateincome","abilitytorepayrate","spouseid","spousename","spouserelationcode","collateralexecutionvalue1","collateralexecutionvalue2","collateralexecutionvalue3","clientdisbursementbank","clientdisbursementbankaccount","exceptiontype","adjustassetliabilityratio","totaladjustassetliabilityratio","smecustriskrating","smetransriskrating","loanapproveamtbeforechange","loanapproveamtafterchange","loanapproveamtinputterid","loanapproveamtauthoriserid","loanapplicationcurrno","loanapproveamtinputtername","loanapproveamtauthorisername","tmp" };
						
						//生成文件及上传
						sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				} else if ("MCR.LOAN".equals(dwhName)) 
				{
					 Sql="select  'CONTRACT NO','CATEGORY','CATEGORY DESC','CLIENT NAME','CLIENT FIRST NAME','REPAY ACCT','LOAN CYCLE','INTEREST RATE','CLIENT NO','CLIENT AGE','CLIENT No CNI','DAO DESC','AUTHORISER','INPUTTER','CLIENT SECTOR','SECTOR DESC','LOAN PURPOSE','BRANCH','DAO','CUST DAO', " + 
					 		"'DAO NAME','PARENT DAO','CLIENT GENDER','CLIENT RATING','LOAN APPL DATE','RECRUITMENT','COMMERCIAL PROD CODE','COMMERCIAL PROD NAME','TERMINATED EARLY','TERMINATION DATE','TERMINATION FEE','DISBURSEMENT DATE','MATURITY DATE','LOAN DURATION','DISBURSEMENT AMOUNT','DISBURSEMENT COMMISSION','REPAYMENT TYPE','PRINCIPAL OUTSTANDING', " + 
					 		"'PD INDICATOR','PD DUE.DATE','LAST REPAYMENT DATE','RESTRUCTURATION INDICATOR','TOTAL CUR NO OF DAYS OVERDUE','MAX OVERDUE DAYS','TOTAL OVERDUE DAYS','TOTAL PRINCIPAL DUE AMT','TOTAL INTEREST DUE AMT','TOTAL CHARGES DUE AMT','TOTAL OTHERS DUE AMT','TOTAL DUE AMT','PRIN. DUE AMT (0-30)','PRIN. DUE AMT (31-60)','PRIN. DUE AMT (61-90)','PRIN. DUE AMT (91-180)', " + 
					 		"'PRIN. DUE AMT (181-360)','PRIN. DUE AMT (>361)','TOTAL PENALTY AMT','TOTAL PENALTY SPREAD AMT','LOAN STATUS','ACCRUED INTERESTS','NEXT SCHEDULE DATE','TOTAL SCHEDULED AMOUNT','FIRST SCHEDULE DATE','LOAN OVERDUE STATUS','SOURCES FOUNDS','HOME LAT','HOME LNG', " + 
					 		"'BUS LAT','BUS LNG','REPAY TYPE','LOAN CONSENT','LOAN PROCESS','LOAN EXCEPTION REASONS','LOAN CREDIT RISK','COMPANY CODE','TRUCK COMPANY ID','TRUCK COMPANY NAME','CHANNEL TYPE','CHANNEL ID','PRE DAO NAME','CURRENCY','COVID IMPACT','COVID IMPACT DATE'" +
							"union all " + 
							"select a.contno CONTRACTNO ,(case when p.oprdtno is not null then p.oprdtno else (select foreign_itemcode from cn_data_dictionary where itemno='dt0052' and a.prdt_no=itemcode and bankid=a.bankid) end ) CATEGORY, " +
							"(case when p.oprdtno is not null then (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode not like 'F%' and foreign_itemcode=p.oprdtno ) else " + 
							" (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode=a.prdt_no and bankid=a.bankid ) end )  CATEGORYDESC,c.cliname CLIENTNAME,upper(to_pinyin(a.cliname)) CLIENTFIRSTNAME,nvl(i.noteno,i.cifid) as REPAYACCT,c.lncnt LOANCYCLE,a.arate INTERESTRATE,a.cifid CLIENTNO,c.age CLIENTAGE,'*' CLIENTNoCNI,'' DAODESC " + 
							",(select opername from de_flowrecord where applyno = a.applyno and phaseno in ('2400','2410','2540') and bankid = '"+bankid+"' order by begindate desc limit 1) AUTHORISER " + 
							",case when t.daoname is null then d.opername else t.daoname end INPUTTER,j.industrytype CLIENTSECTOR,j.indname SECTORDESC,a.purpose LOANPURPOSE,case when o.foreign_itemcode is null then a.manage_instcode else o.foreign_itemcode end BRANCH,case when t.dao is null then a.manage_operid else t.dao end DAO,case when t.dao is null then a.manage_operid else t.dao end CUSTDAO, case when t.daoname is null then d.opername else t.daoname end DAONAME,'' PARENTDAO " + 
							",case when c.sex='1' then 'MALE' when c.sex='2' then 'FEMALE' end CLIENTGENDER,'' CLIENTRATING,e.app_date LOANAPPLDATE,'' RECRUITMENT,q.foreign_itemcode  COMMERCIALPRODCODE " + 
							",q.foreign_itemname COMMERCIALPRODNAME " + 
							",(CASE WHEN n.changer_type = '05' or n.changer_type = '55' THEN 'Y' else 'N' END) TERMINATEDEARLY,n.trace_date TERMINATIONDATE,n.breach_fee - n.redbreach_fee TERMINATIONFEE,a.begindate DISBURSEMENTDATE,a.enddate MATURITYDATE,a.term LOANDURATION,a.bus_sum DISBURSEMENTAMOUNT " + 
							",a.loanfee DISBURSEMENTCOMMISSION,l.foreign_itemcode REPAYMENTTYPE,a.norm_bal PRINCIPALOUTSTANDING,case when a.curr_overdays> 0 THEN 'Y' ELSE 'N' END PDINDICATOR,a.lo_date PDDUEDATE,g.lastrepaydate LASTREPAYMENTDATE " + 
							",(CASE WHEN a.core_prdt_no like '4%' or a.prdt_no like 'F%' THEN 'Y' ELSE '' END) RESTRUCTURATIONINDICATOR,a.curr_overdays TOTALCURNOOFDAYSOVERDUE,a.overdue_day MAXOVERDUEDAYS,a.total_over_days TOTALOVERDUEDAYS,k.lo_amt TOTALPRINCIPALDUEAMT,k.lo_intst TOTALINTERESTDUEAMT,'0' TOTALCHARGESDUEAMT,'0' TOTALOTHERSDUEAMT " + 
							",(k.lo_amt+k.lo_intst+k.lo_over_intst) TOTALDUEAMT,m.over30 PrincipalOverdueAmount1_30,m.over60 PrincipalOverdueAmount31_60,m.over90 PrincipalOverdueAmount61_90,m.over180 PrincipalOverdueAmount91_180,m.over360 PrincipalOverdueAmount181_360,m.over361 PrincipalOverdueAmount361 " + 
							",k.lo_over_intst TOTALPENALTYAMT,'0' TOTALPENALTYSPREADAMT " + 
							",(CASE WHEN a.vch_sts = '30' THEN 'LIQ'  ELSE 'CUR' END) as LOANSTATUS " + 
							",-(b.in_normal_intst + b.in_over_intst + b.out_normal_intst + b.out_over_intst) ACCRUEDINTERESTS, a.next_pay_int_date NEXTSCHEDULEDATE  " + 
							", nvl((a.next_pay_amt+a.next_pay_int),0) TOTALSCHEDULEDAMOUNT,a.first_due_date FIRSTSCHEDULEDATE,h.loansts LOANOVERDUESTATUS,a.invest_name SOURCESFOUNDS " + 
							",'' HOMELAT,'' HOMELNG,'' BUSLAT,'' BUSLNG,l.foreign_itemcode REPAYTYPE,'' LOANCONSENT " + 
							",(CASE WHEN a.occurtype = '1' THEN 'NEW.LOAN' WHEN a.occurtype = '3' THEN 'RENEW' WHEN a.occurtype = '5' THEN 'WCSP.RENEW' ELSE '' END) as LOANPROCESS " + 
							",'' LOANEXCEPTIONREASONS,'' LOAN_CREDIT_RISK,r.foreign_itemcode AS COMPANYCODE,'' TRUCKCOMPANYID,'' TRUCKCOMPANYNAME " + 
							",(CASE WHEN c.channeltype = '01' THEN '老推新' WHEN c.channeltype = '02' THEN '政府/协会渠道' WHEN c.channeltype = '03' THEN '代理点' WHEN c.channeltype = '04' THEN '产业链' ELSE c.channeltype END) as CHANNELTYPE " + 
							",(CASE WHEN c.channeltype = '01' THEN (select a.cifid from (select id.cifid,id.mtel from ind_base id where id.mtel not in (select a.mtel from (select count(*) count,mtel from ind_base GROUP BY mtel) a where a.count>1 and a.mtel is not null and a.mtel != '') and bankid = '"+bankid+"') a where a.mtel = c.recom_code limit 1) else c.recom_code  END) AS CHANNELID " + 
							", case when u.daoname is null then (select opername from cn_user t9 where e.pre_loan_operid=t9.operid) else u.daoname end PREDAONAME,'CNY' CURRENCY,s.foreign_itemname COVIDIMPACT,a.epiddate COVIDIMPACTDATE " + 
							" from     " + 
							"ac_businessvch a inner join ln_mst b on a.loanac_id=b.ac_id and a.bankid=b.bankid    " + 
							"left join ind_base c on a.cifid=c.cifid and a.bankid=c.bankid    " + 
							"left join cn_user d on a.manage_operid=d.operid and a.bankid=d.bankid    " + 
							"left join app_main_info e on a.applyno=e.applyno and a.bankid=e.bankid    " + 
							"left join (select max(updcurrrepdate)lastrepaydate,acid,bankid from ac_lnrepayschd where state='1' and rependdate<'"+workdate+"' group by acid,bankid) g on a.loanac_id=g.acid and  a.bankid=g.bankid    " + 
							"left join (select case when count(case when lo_ind='1' and repaystate='0' and DATEDIFF('"+workdate+"',updcurrrepdate)>90 then '1' end )>0 then 'NAB'  " + 
							"when  count(case when lo_ind='1' and repaystate='0' and DATEDIFF('"+workdate+"',updcurrrepdate)<=90 then '1' end )>0 then 'PDO'  " + 
							"when  count(case when lo_ind='1' and repaystate='2' then '1' end )>0 then 'CUR'  " + 
							"when sum(lo_ind)='0' then '' end loansts,acid,bankid from ac_lnrepayschd group by acid,bankid) h on a.loanac_id=h.acid and a.bankid=h.bankid   " + 
							"left join (select * from cs_mst where ac_id='1' and bankid='"+bankid+"') i on a.cifid=i.cifid and a.manage_instcode=i.opn_br_no and a.bankid=i.bankid   " + 
							"left join (select itemcode,bankid,foreign_itemcode industrytype,foreign_itemname indname from cn_data_dictionary where itemno='dt0054') j on a.industrytype=j.itemcode and a.bankid=j.bankid    " + 
							"left join (select ac_id,(sum(lo_amt) - sum(pay_lo_amt)) as lo_amt,(sum(lo_intst) - sum(pay_lo_intst)) as lo_intst,(sum(lo_over_intst) - sum(pay_over_intst)) as lo_over_intst from ln_lo group by ac_id) k on a.loanac_id = k.ac_id    " + 
							"left join (select * from cn_data_dictionary where itemno='dt0001') l on a.repay_type=l.itemcode and a.bankid=l.bankid     " + 
							"left join ( select nvl(sum(case when DATEDIFF('"+workdate+"',over_date)<=30 then lo_amt - pay_lo_amt else 0 end),0) over30,   " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>31 and DATEDIFF('"+workdate+"',over_date)<=60 then lo_amt - pay_lo_amt else 0 end),0) over60,   " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>61 and DATEDIFF('"+workdate+"',over_date)<=90 then lo_amt - pay_lo_amt else 0 end),0) over90,   " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>91 and DATEDIFF('"+workdate+"',over_date)<=180 then lo_amt - pay_lo_amt else 0 end),0) over180,   " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>181 and DATEDIFF('"+workdate+"',over_date)<=360 then lo_amt - pay_lo_amt else 0 end),0) over360,   " + 
							" nvl(sum(case when DATEDIFF('"+workdate+"',over_date)>361 then lo_amt - pay_lo_amt else 0 end),0) over361,ac_id,bankid from ln_lo     " + 
							"	where pay_type<>'1' group by ac_id,bankid) m on a.loanac_id=m.ac_id and a.bankid=m.bankid    " + 
							"left join (select  a.bankid,a.vchno,a.changer_type,a.trace_date,a.breach_fee,a.redbreach_fee from ( " + 
							"select a.bankid,a.vchno,a.changer_type,a.trace_date,a.breach_fee,a.redbreach_fee, " + 
							"case when @mid = vchno then @rowno\\:=@rowno+1 else @rowno\\:=1 end rn, @mid\\:=vchno   " + 
							"from af_main_info a  where bankid = '"+bankid+"' and changer_type in (05,55) and wb_sts = '2' order by a.vchno,a.trace_date desc ) a " +
							"where rn =1) n on a.vchno = n.vchno and n.bankid=a.bankid " + 
							"left join (select * from  cn_data_dictionary where  itemno='dt0004' and bankid ='"+bankid+"' and foreign_itemcode not in ('23','45','42')) o on a.manage_instcode=o.itemcode and a.bankid=o.bankid   " + 
							"left join hd_loan p on a.contno=p.id  " +
							"left join (select * from  cn_data_dictionary where  itemno='dt0053' and bankid ='"+bankid+"') q on CONCAT(a.prdt_no,a.guar_type)=q.itemcode and a.bankid=q.bankid   " +
							"left join (select * from  cn_data_dictionary where  itemno='dt0051' and bankid ='"+bankid+"') r on a.manage_instcode=r.itemcode and a.bankid=r.bankid " +
							"left join (select * from  cn_data_dictionary where  itemno='dt0034' and bankid ='"+bankid+"') s on a.epidlevel=s.itemcode and a.bankid=s.bankid " +
							"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) t on a.manage_operid=t.itemcode and a.bankid=t.bankid " +
							"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) u on e.pre_loan_operid=u.itemcode and e.bankid=u.bankid " +
							"where b.ac_sts not in ('9') and a.vch_sts not in ('20','30') and a.vch_sts<>'40' and a.contno not like 'PDPD%' " +
							"and a.contno not like 'HT%' and a.contno not like '%LC'";
							BRC.getLog().info("MCR.LOAN-SQL:" + Sql);
							
							String[] str = {"contract no","category","category desc","client name","client first name","repay acct","loan cycle","interest rate","client no","client age","client no cni","dao desc","authoriser","inputter","client sector","sector desc","loan purpose","branch","dao","cust dao", 
									"dao name","parent dao","client gender","client rating","loan appl date","recruitment","commercial prod code","commercial prod name","terminated early","termination date","termination fee","disbursement date","maturity date","loan duration","disbursement amount","disbursement commission","repayment type","principal outstanding", 
									"pd indicator","pd due.date","last repayment date","restructuration indicator","total cur no of days overdue","max overdue days","total overdue days","total principal due amt","total interest due amt","total charges due amt","total others due amt","total due amt","prin. due amt (0-30)","prin. due amt (31-60)","prin. due amt (61-90)","prin. due amt (91-180)", 
									"prin. due amt (181-360)","prin. due amt (>361)","total penalty amt","total penalty spread amt","loan status","accrued interests","next schedule date","total scheduled amount","first schedule date","loan overdue status","sources founds","home lat","home lng", 
									"bus lat","bus lng","repay type","loan consent","loan process","loan exception reasons","loan credit risk","company code","truck company id","truck company name","channel type","channel id","pre dao name","currency","covid impact","covid impact date","tmp" };
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("CUSTOMER".equals(dwhName)) 
				{
					 Sql="select t1.cifid	ID, " + 
					 		"t1.cliname	SHORTNAME, " + 
					 		"UPPER(to_pinyin(t1.cliname))	NAME1, " + 
					 		"t1.famaddr1	STREET, " + 
					 		"t1.workcorp	SECTOR, " + 
					 		"case when t9.dao is null then t1.ipt_usr else t9.dao end ACCOUNTOFFICER, " + 
					 		"t1.job_type	INDUSTRY, " + 
					 		"'CN'	NATIONALITY, " + 
					 		"'CN'	RESIDENCE, " + 
					 		"t1.birthday	BIRTHINCORPDATE, " + 
					 		"''	TITLE, " + 
					 		"case when t1.sex='1' then 'MALE' when t1.sex='2' then 'FEMALE' end	GENDER, " + 
					 		"t5.foreign_itemcode	MARITALSTATUS, " + 
					 		"t1.email	EMAIL1, " + 
					 		"t1.occupation	OCCUPATION, " + 
					 		"''	CURRNO, " + 
					 		"t1.upd_date	DATETIME, " + 
					 		"t6.foreign_itemcode	COCODE, " + 
					 		"''	DEPTCODE, " + 
					 		"pinyin(t1.cliname)	INITIALS, " + 
					 		"t1.live_postal_code	POSTALCODE,  " + 
					 		"t1.house	POBOXNO, " + 
					 		"t1.postal_code POPOSTCODE, " + 
					 		"t1.job_adr	EMPLOYBUSADDR, " + 
					 		"t1.work_postal_code	EMPLPOSTCODE, " + 
					 		"t1.workcorptel	TELWORK, " + 
					 		"CASE WHEN t1.is_local='0' then 'N' when t1.is_local='1' then 'Y' end	RESIDEYN, " + 
					 		"t1.workcorp	EMPLOYNAME, " + 
					 		"t1.ipt_date	OPENINGDATE, " + 
					 		"'I'	CUSTTYPE, " + 
					 		" ''	CORRESPONDNAME, " + 
					 		" t7.foreign_itemcode	IDTYPES, " + 
					 		" t1.edu	PROFESSION, " + 
					 		"''	SALARYRANGE, " + 
					 		"t1.workcorptype	EMPLOYSTATUS, " + 
					 		"''	MAININCOME, " + 
					 		"t1.age	AGE, " + 
					 		"t1.job_edate	EMPLOYPERIOD, " + 
					 		" t1.famstat	ACCOMTYPE, " + 
					 		" t1.famaddr	PRVPHYADD, " + 
					 		"'' 	PRVPOSTCODE, " + 
					 		"t2.cliname	SPNAME, " + 
					 		" t2.workcorp	SPEMPNAME, " + 
					 		"t2.job_adr	SPEMPADD, " + 
					 		"''	CRDTCHK, " + 
					 		"''	PRVACCTYPE, " + 
					 		"t2.sps_cifid 	SPMEMNO, " + 
					 		"t1.cust_source	SOURCETYPE, " + 
					 		"t2.certno	SPOUSEID, " + 
					 		" t2.birthday	SPOUSEDOB, " + 
					 		" case when t2.sex='1' then 'MALE' when t2.sex='2' then 'FEMALE' end	SpouseGender, " + 
					 		" '' SPOUSELFLT, " + 
					 		" t2.mtel	SPOTHERCONTAC, " + 
					 		" t1.certno	CLIENTIDNUM, " + 
					 		" t1.degree	ACADEMICDEGREE, " + 
					 		" t1.position	JOB, " + 
					 		" t2.tel_addr	SpouseResAdd, " + 
					 		" t3.depaccbk_name	DISBURSEBANK, " + 
					 		" t3.depacc_no	DISBURSEACCT, " + 
					 		" t1.mtel	MOBILEPHONE, " + 
					 		" t1.house	BOPHYADD, " + 
					 		" t1.live_postal_code BOPOSTCODE, " + 
					 		" case when t1.is_farm='0' then 'Urban' when t1.is_farm='1' then 'Rural' else 'Resident' end BOPROPERTY, " + 
					 		" t8.foreign_itemcode	SPCREDTYPE, " + 
					 		" ''	IDADDRESS, " + 
					 		" t2.house	SPIDADD, " + 
					 		"  t1.headship	JOBPOSITION, " + 
					 		" '86'	PHONECODE1, " + 
					 		" CONCAT(t1.crditlvl,1)	CUSTOMERLEVEL, " + 
					 		" ''	POLEXPOPERSON, " + 
					 		"''	SANCTIONLIST, " + 
					 		" ''	AMTRISKLEVEL, " + 
					 		" t1.job_adr	ENTERPRISEADDR, " + 
					 		" t1.workcorptel	ENTERPRISEPHON, " + 
					 		"t4.covimp	COVIDIMPACT, " + 
					 		"t4.covdate	COVIDIMPACTDA, " + 
					 		"t4.covimp1	COVIDIMPACT1, " + 
					 		"t4.covimp3	COVIDIMPACT3, " + 
					 		"t4.covdate3	COVIDIMPACTD3, " + 
					 		"t4.covimp4	COVIDIMPACT4, " + 
					 		"t4.covdate4	COVIDIMPACTD4, " + 
					 		"t4.covimp5	COVIDIMPACT5, " + 
					 		"t4.covdate5	COVIDIMPACTD5, " + 
					 		"t4.covimp6	COVIDIMPACT6, " + 
					 		"t4.covdate6	COVIDIMPACTD6, " + 
					 		"t4.covimp7	COVIDIMPACT7, " + 
					 		"t4.covdate7 	COVIDIMPACTD7 " + 
					 		"  from ind_base t1   " + 
					 		"LEFT JOIN ind_spouse_info t2 on t1.cifid = t2.cifid and t1.bankid = t2.bankid   " + 
					 		"LEFT JOIN (SELECT cifid, bankid, GROUP_CONCAT(DISTINCT depaccbk_name) depaccbk_name, GROUP_CONCAT(DISTINCT depacc_no) depacc_no,   " + 
					 		"GROUP_CONCAT(DISTINCT epidlevel) epidlevel, GROUP_CONCAT(DISTINCT epiddate) epiddate FROM ac_businessvch GROUP BY bankid, cifid) t3   " + 
					 		"on t1.bankid = t3.bankid and t1.cifid = t3.cifid    " + 
					 		"left join  (select max(case when rowno='1' then epidlevel end) covimp, max(case when rowno='1' then epiddate end)covdate,  " + 
					 		"max(case when rowno='2' then epidlevel end)covimp1,  " + 
					 		"max(case when rowno='3' then epidlevel end)covimp3, max(case when rowno='3' then epiddate end)covdate3,  " + 
					 		"max(case when rowno='4' then epidlevel end)covimp4, max(case when rowno='4' then epiddate end)covdate4,  " + 
					 		"max(case when rowno='5' then epidlevel end)covimp5, max(case when rowno='5' then epiddate end)covdate5,  " + 
					 		"max(case when rowno='6' then epidlevel end)covimp6, max(case when rowno='6' then epiddate end)covdate6,  " + 
					 		"max(case when rowno='7' then epidlevel end)covimp7, max(case when rowno='7' then epiddate end)covdate7,  " + 
					 		" cifid,bankid  " + 
					 		" from (SELECT @rowno\\:=@rowno+1 as rowno,r.* from ac_businessvch r ,(select @rowno\\:=0) t order by applyno) a group by cifid,bankid) t4 on t1.cifid=t4.cifid and t1.bankid=t4.bankid   " + 
					 		"left join (select * from cn_data_dictionary where itemno='dt0009' and bankid ='"+bankid+"' and foreign_itemname<>'296' and foreign_itemcode regexp '[a-z0-9A-Z]') t5 on t1.is_mar=t5.itemcode and t1.bankid=t5.bankid    " + 
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0051' and bankid ='"+bankid+"') t6 on t1.ipt_brno=t6.itemcode and t1.bankid=t6.bankid  " + 
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0007' and bankid ='"+bankid+"') t7 on t1.certtype=t7.itemcode and t1.bankid=t7.bankid  " + 
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0007' and bankid ='"+bankid+"') t8 on t2.certtype=t8.itemcode and t2.bankid=t8.bankid  " +
					 		"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) t9 on t1.ipt_usr=t9.itemcode and t1.bankid=t9.bankid " +
					 		"where t1.ipt_date='"+workdate+"' or t1.upd_date='"+workdate+"'";
							BRC.getLog().info("CUSTOMER:" + Sql);
							
							//最后一个字段带分隔符,加空值tmp
							String[] str = {"id","mnemonic","shortname","name1","name2","street","address","towncountry","postcode","country","relationcode","relcustomer","reversrelcode","reldelivopt","role","rolemoreinfo","rolenotes","relreserv6","relreserv5","relreserv4","relreserv3","relreserv2","relreserv1","sector","accountofficer","otherofficer","industry","target","nationality","customerstatus","residence","contactdate","introducer","text","legalid","legaldocname","legalholdername","legalissauth","legalissdate","legalexpdate","offphone","reviewfrequency","birthincorpdate","globalcustomer","customerliability","language","postingrestrict","dispoofficer","companybook","confidtxt","dispoexempt","issuecheques","clscparty","fxcommgroupid","residenceregion","assetclass","customerrating","crprofiletype","crprofile","noupdatecrm","title","givennames","familyname","gender","dateofbirth","maritalstatus","noofdependents","phone1","sms1","email1","addrlocation","employmentstatus","occupation","jobtitle","employersname","employersadd","employersbuss","employmentstart","customercurrency","salary","annualbonus","salarydatefreq","netmonthlyin","netmonthlyout","residencestatus","residencetype","residencesince","residencevalue","mortgageamt","otherfinrel","otherfininst","commtype","prefchannel","allowbulkprocess","legaliddocname","interests","fax1","previousname","changedate","changereason","customersince","customertype","datelastverified","spokenlanguage","pastimes","furtherdetails","domicile","othernationality","calcriskclass","manualriskclass","overridereason","taxid","vistype","viscomment","visinternalreview","formervistype","formerviscomment","riskassettype","risklevel","risktolerance","riskfromdate","lastkycreviewdate","autonextkycreviewdate","manualnextkycreviewdate","lastsuitreviewdate","autonextsuitreviewdate","manualnextsuitreviewdate","kycrelationship","mandateappl","mandatereg","mandaterecord","securemessage","amlcheck","amlresult","lastamlresultdate","kyccomplete","internetbankingservice","mobilebankingservice","reporttemplate","holdingspivot","mergedto","mergedstatus","altcusid","externsysid","externcusid","override","recordstatus","currno","inputter","datetime","authoriser","cocode","deptcode","auditorcode","auditdatetime","initials","suburbtown","citymunicipal","provincestate","postalcode","poboxno","posuburbtown","pocitymunicip","poprovstate","popostcode","employbusaddr","employsubtown","emplctymunic","emplprovstate","emplpostcode","telhome","telwork","telmobile","resideyn","emailaddress","employname","taxregno","taxinvoice","mailinglist","blocked","staffofficial","noofdepend","employeeno","dateofemploy","faxno","otheraccts","bankbranch","sortcode","dateacopnd","openingdate","custtype","correspondname","datecurraddr","formername","idtypes","profession","salaryrange","classification","custstatus","employstatus","entitytype","mainincome","secondincome","busstartdate","locreserved1","unallocated2","unallocated3","unallocated4","memoofass","artofass","certofincorp","foundingstate","certtocommenc","trustdeed","partagreement","constitution","namechangecer","auditorname","auditortel","busprincipals","assentities","productservice","positconname","curraddress","age","expirydate","signname","signidpassp","typeidpassp","dateexpiry","membersince","memberyears","savingssince","savingsyears","employperiod","dummycust","legalidno","partners","xdateofbirth","gendergr","partnertext","businesstype","custlanguage","relatedcust","nonindtext","signatorycode","signatoryid","signtpprtno","signtpprtexp","signttelno","yrsatcuraddr","sameasresadd","businessstdt","contactname","contactpos","contactworktel","contacthomtel","contactmobtel","contactemail","contactfaxno","alteridtype","alteridno","accomtype","prvphyadd","prvsuburb","prvcity","prvstate","prvpostcode","prvyrsocp","emplyrscode","spname","spoccup","spempname","spempadd","spempsub","spempcity","spempstate","spemppost","mailstmt","mailletters","maillabels","minbal55","minbal60","minbal65","minbal70","crdtchk","crdtind","consdisclose","dateofsig","placestudy","durcourse","fieldstudy","nominalform","insmemno","inssurname","insaddress","inssuburb","inscity","insstate","insposcode","insphoneno","insrelcode","insamtlegacy","busname","busnature","bustype","busplan","busrole","busadd","bussuburb","buccity","busstate","c2held","c2expdate","areacode","nonqual","student","prvacctype","offrtype","copyresadd","spmemno","signinstruc","deceaseddate","loanswof","noofemp","group","cycle","externalrating","sourcetype","advisorname","spouseid","spouseage","spousedob","spousegender","marriagestatus","spousela","spouseel","spousewp","spousewflt","spouselflt","spousemp","spothercontac","spouseca","sppostcode","spouseecbr","clientability","entsln","enterpriseexp","enterpriseregi","enterpriseowne","bpsl","competitionpre","operationexpen","ftr","envdamage","ece","clientidnum","guarantorid","custactive","clientrating","artificialpers","academicdegree","job","busphone","busotherphone","industryexp","buslicenceno","indusexpdate","yrent","dateent","spouseresadd","spouseno","refname","reftelnumber","refrelation","refereidentity","refcomment","refcomment2","custbranchco","nextfee","disbursebank","disburseacct","repaymentbank","repaymentacct","countrycode","mobilephone","copyresadd","bophyadd","bosuburbtown","bocitymunicip","boprovstate","bopostcode","boproperty","spcredtype","locarea","idaddress","spidadd","envloc","migflag","maritalstatus1","livingcond","custprof","jobposition","salbankacct","salacct","legacyrating","migflag","prevdao","daochgdate","daochgrsn","loccity","loclga","loctown","loczone","rseexph","rseexpedu","rsegarbage","rsehygiene","rseaccidentnb","rsewatercons","rsewateradd","rseenergycons","rseenergyadd","rserisk","rsecomment","rserisktype","ageinhose","businessname","busnactiviy","legalstatus","legalact","incorpnumber","ageinlocation","businessaddres","buzzone","buzcity","buslga","buzstate","legalrepname","legalrepfunct","signatoryname","sigfinction","empcity","emplga","empstate","phonecode1","mciemployee","customerlevel","mrksurvey","inforknow","dculegalstatu","dculegalact","locbirth","housestatus","state","lgrepnam","lgrepfunct","loancycle","referrerno","referrername","referrerphone","homelat","homelng","buslat","buslng","fraudflagind","fraudlabid","fraudloanid","flagdate","fraudinfoff","fraudtype","blflagind","blflagdate","blflaginp","blflagaut","coname","regnum","corpidnum","withholdinfo","conftelno","bioenrolstat","bioenrollflag","bioenrolldate","bioupdchk","bioupdchkdte","initiatorid","messagetypes","nofield","proccode","stanid","retrrefno","terminalid","terminalloc","agentacctno","agentid","operator","preferredagent","interstcusac","interstcusld","interstlnamt","interstlnterm","lnpurpose","comment","prospectcity","custothimf","mortgagedprope","banksignedyn","occcredit","oldcuid","emergencyname","emergencyconta","emergencyphone","indcredit","polexpoperson","sanctionlist","amtrisklevel","noofkids","phonetype","incllevel","incomefreq","incomegrp","resideproof","paymentdate","actorid","enterguarantor","archivesstatus","litigationprog","proceedingsdat","litigationpro1","proceedingsda1","channelmodel","branchapprover","agent","otherfinancial","channeldistrib","distributorpos","investigatstaf","optouttaka","optoutind","channelcusyn","enterpriseaddr","enterprisephon","covidimpact","covidimpactda","covidimpact1","covidimpact3","covidimpactd3","covidimpact4","covidimpactd4","covidimpact5","covidimpactd5","covidimpact6","covidimpactd6","covidimpact7","covidimpactd7","tmp"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("COLLATERAL".equals(dwhName)) 
				{
					String date=workdate.substring(2, 8);
					 Sql="select '@ID','COLLATERAL.TYPE','DESCRIPTION','COMPANY','APPLICATION.ID','CURRENCY','COUNTRY','NOMINAL.VALUE','MAXIMUM.VALUE','EXECUTION.VALUE','THIRD.PARTY.VALUE','GEN.LEDGER.VALUE','CENTRAL.BANK.VALUE','VALUE.DATE', " + 
					 		"'REVIEW.DATE.FQU','EXPIRY.DATE','ADDRESS','COLLATERAL.CODE','NOTES','APPLICATION','STATUS','REVALUED.DATE','REVALUED.AMOUNT','OVERRIDE','RECORD.STATUS','CURR.NO','INPUTTER','DATE.TIME','AUTHORISER', " + 
					 		"'CO.CODE','DEPT.CODE','AUDITOR.CODE','AUDIT.DATE.TIME','PROP.CERT.NO','OWNER.OF.PROP','REAL.EST.CRED','INV.UNIT.PRICE','OWN.CLT.PARTY','COLL.OWN.NAME','OWN.NATIONAL.ID','OWN.PH.NUM','COLL.ACREAGE','COLL.STRUCT', " + 
					 		"'COLL.HOUSE.TYPE','COLL.FLOOR','COLL.CONST.TYPE','COLL.D.OF.COMP','COLL.LIFE','BRAND','MODEL','PUR.DATE','SERIAL','HORSE.POWER','ENERGY.SOURCE','MOTOR.TYPE','MOT.LIC.PLATE','CUST.NUM','NAME.OF.BANK', " + 
					 		"'CON.NUM.WD.BANK','TERM','QUANTITY','NAME.OF.COLL','OWNER.OF.ESTATE','MORTGAGED.PROPE','REGISTR.INSTITU','HOUSE.PROPERTY','RENT.SITUATION','NUM.REAL.ESTATE','OWNERSHIP.CERTI','MANUFACTURER','VEHICLE.TYPE'  " + 
					 		"union all  " + 
					 		"select a.guar_no,a.col_two_no,'','','',a.fin_curr,'CN',a.fst_value,a.guar_sum_max,a.fst_value*0.8,a.fst_value*0.8,a.buy_sum,'',c.begindate,'',c.enddate,a.house_addr,a.col_two_no,case when a.description is null then a.brf else a.description end, " + 
					 		"'','','','','','','',case when d.dao is null then a.ipt_usr else d.dao end,a.ipt_date,'','',a.ipt_brno,'','',case when a.house_certno is null then a.receipt_no else a.house_certno end,a.cliname,a.cons_land_certno,'','NO',a.cliname,a.certno,a.mtel,a.house_area,a.house_strut, " + 
					 		"a.roomtype,a.floor,'','','',a.trvl_liseceno,a.trvl_model,case when a.trvl_buydate is null then  a.buy_date else a.trvl_buydate end ,a.trvl_motorno,a.trvl_liseceno,'','','','','','','','','','','',a.fst_brno,'','','','','','' " + 
					 		"from guar_base a inner join bus_affrim_collateral b on a.guar_no=b.guar_no and a.bankid=b.bankid " +
					 		"inner join ac_guarcont c on b.gccontno=c.contno  and b.bankid=c.bankid  " + 
					 		"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) d on a.ipt_usr=d.itemcode and a.bankid=d.bankid " +
					 		"where substr(a.ipt_date,1,6)='"+date+"' or a.upd_date='"+workdate+"'";
							BRC.getLog().info("COLLATERAL-SQL:" + Sql);
							
							//最后一个字段带分隔符,加空值tmp
							String[] str = {"@id","collateral.type","description","company","application.id","currency","country","nominal.value","maximum.value","execution.value","third.party.value","gen.ledger.value","central.bank.value","value.date", 
									"review.date.fqu","expiry.date","address","collateral.code","notes","application","status","revalued.date","revalued.amount","override","record.status","curr.no","inputter","date.time","authoriser", 
									"co.code","dept.code","auditor.code","audit.date.time","prop.cert.no","owner.of.prop","real.est.cred","inv.unit.price","own.clt.party","coll.own.name","own.national.id","own.ph.num","coll.acreage","coll.struct",  
									"coll.house.type","coll.floor","coll.const.type","coll.d.of.comp","coll.life","brand","model","pur.date","serial","horse.power","energy.source","motor.type","mot.lic.plate","cust.num","name.of.bank", 
									"con.num.wd.bank","term","quantity","name.of.coll","owner.of.estate","mortgaged.prope","registr.institu","house.property","rent.situation","num.real.estate","ownership.certi","manufacturer","vehicle.type","tmp"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("LoanInstallments".equals(dwhName)) 
				{
					//替换日期
					
				       String fdate=(String)daoTemplate.getUniqueValueBySql("select sys_date from com_sys_parm");
				       
				       fileName = fileName.replace("#FYYYYMMDD#", fdate);
					
					 Sql="select 'CustomerId','CustomerName','LoanId','T24Account','ScheduleDate','OverdueDays','ScheduledPRAmount'," + 
					 		"'OutstandingPRAmount','ScheduledINAmount','OutstandingINAmount','ScheduledPEAmount','OutstandingPEAmount'," + 
					 		"'LastRepaymentDate','ZdInRepay','CompanyCode','WorkingBalance','DisbursementAmount' " + 
					 		"union all " + 
					 		"(select a.cifid CustomerId,a.cliname CustomerName,a.contno LoanId,nvl(c.noteno,c.cifid) T24Account,b.updcurrrepdate ScheduleDate,   " + 
					 		"case when DATEDIFF('"+workdate+"',b.updcurrrepdate)>0 and b.lo_ind='1' and b.repaystate='0' then DATEDIFF('"+workdate+"',b.updcurrrepdate) else '0' end OverdueDays,    " + 
					 		"b.updcurramt ScheduledPRAmount,nvl(case when repaystate='0' then d.lo_amt-d.pay_lo_amt else '0' end,0) OutstandingPRAmount,b.updcurrint ScheduledINAmount, " + 
					 		"nvl(case when repaystate='0' then d.lo_intst-d.pay_lo_intst else '0' end,0)  OutstandingINAmount,   " + 
					 		"nvl(d.lo_over_intst,0) ScheduledPEAmount,    " + 
					 		"nvl(case when repaystate='0' then d.lo_over_intst-d.pay_over_intst else '0' end,0)  OutstandingPEAmount,   " + 
					 		"case when now_period=b.currcnt then (select updcurrrepdate from ac_lnrepayschd where acid=a.loanac_id and a.bankid=bankid and currcnt=now_period-1 ) else '' end LastRepaymentDate," + 
					 		"case when now_period=b.currcnt then b.prov_bal-b.updcurrint else '0' end ZdInRepay,e.foreign_itemcode CompanyCode,c.bal WorkingBalance,a.bus_sum  DisbursementAmount    " + 
					 		"from ac_businessvch a   " + 
					 		"left join ac_lnrepayschd b  on a.loanac_id =b.acid and a.bankid=b.bankid    " + 
					 		"left join (select * from cs_mst where ac_id='1' and bankid='"+bankid+"') c on a.cifid=c.cifid and a.manage_instcode=c.opn_br_no and a.bankid=c.bankid    " + 
					 		"left join ln_lo d on b.acid=d.ac_id and b.currcnt=d.lo_term and b.bankid=d.bankid " + 
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0051' and bankid ='"+bankid+"') e on a.manage_instcode=e.itemcode and a.bankid=e.bankid " + 
					 		"where a.vch_sts<>'40' and (a.vch_sts not in ('20','30') or DATEDIFF('"+workdate+"',a.finishdate)<=7 )   " + 
					 		"order by b.acid ,b.updcurrrepdate " + 
					 		")";
							BRC.getLog().info("LoanInstallments-SQL:" + Sql);
							
							//最后一个字段带分隔符,加空值tmp
							String[] str = {"customerid","customername","loanid","t24account","scheduledate","overduedays","scheduledpramount",
									"outstandingpramount","scheduledinamount","outstandinginamount","scheduledpeamount","outstandingpeamount",
									"lastrepaymentdate","zdinrepay","companycode","workingbalance","disbursementamount","tmp"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("contract".equals(dwhName)) 
				{
					
					 Sql="select a.ipt_brno,a.serid,a.phasename,a.filetype,a.phaseno,'' contract_value,a.ipt_date,a.cifid,b.cliname,case when c.dao is null then a.ipt_usr else c.dao end,a.fileurl,'' openid,a.fileurl as fileurl2,a.ipt_date as ipt_date2,'' signers,'' state,'' status,b.contno,a.ipt_date as ipt_date3 " + 
					 		"from file_attached a " +
					 		"inner join (select applyno,contno,bankid,cliname from app_main_info union select applyno,contno,bankid,cliname from af_main_info ) b on a.relserid=b.applyno and a.bankid=b.bankid " +
					 		"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) c on a.ipt_usr=c.itemcode and a.bankid=c.bankid ";
							BRC.getLog().info("contract:" + Sql);
					//字段名称
					String[] orderHeader =  { "iptBrno","serid","phasename","filetype","phaseno","contractValue","iptDate","cifid",
									"cliname","fileurl","openid","fileurl2","iptDate2","signers","state","status","contno","iptDate3"};
					//csv文件标题
					CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("company_code","contract_id","contract_name","contract_template_id",
							"contract_type","contract_value","created_at","customer_id","customer_name","dao","image","openid",
							"pdf","sign_end_at","signers","state","status","t24_contract_record_id","updated_at");		
							
					//生成csv文件及上传
					//sendDataHouseCsv(Sql,orderHeader,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,csvFormat,bucketname);
				}else if ("MCR.LOAN.APPLICATION.BOX".equals(dwhName)) 
				{
					//插入dwh_flow表
					String delSql="";
					String insertSql="";
					delSql="delete from dwh_flow";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_flow select applyno,bankid,(case when phaseno ='2100' then '1' when phaseno ='2300' then '2' when phaseno ='2310' then '3' when phaseno ='2400' then '4'  when phaseno ='6100' then '5' else phaseno end),substr(begindate,1,8),substr(replace(replace(begindate,':',''),' ',''),3,10)  from de_flowrecord where (serid,applyno) in (select max(serid),applyno from  de_flowrecord group by applyno)";
					daoTemplate.executeSql(insertSql);
					
					//插入dwh_flow_ph表
					delSql="delete from dwh_flow_ph";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_flow_ph select applyno,bankid, GROUP_CONCAT((case when phaseno ='2100' then '1' when phaseno ='2300' then '2' when phaseno ='2310' then '3' when phaseno ='2400' then '4'  when phaseno ='6100' then '5' else phaseno end) SEPARATOR '|') flowphaseno,GROUP_CONCAT(substr(begindate,1,8) SEPARATOR '|')flowbeg,GROUP_CONCAT(operid SEPARATOR '|')flowoperid  from de_flowrecord group by applyno,bankid";
					daoTemplate.executeSql(insertSql);

					//插入dwh_afm_res 表
					delSql="delete from dwh_afm_res";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_afm_res select applyno,bankid,bus_sum,term,(select foreign_itemcode from cn_data_dictionary b where itemno='dt0001' and itemcode=repay_type and a.bankid=b.bankid),arate  from afm_res_info a where (applyno,aud_num) in (select applyno,max(aud_num) from afm_res_info group by applyno) ";
					daoTemplate.executeSql(insertSql);

					//插入dwh_collateral表 
					delSql="delete from dwh_collateral";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_collateral select applyno,bankid,GROUP_CONCAT(guar_no)guar_no,sum(guarsum)guarsum,GROUP_CONCAT(col_no)col_no,GROUP_CONCAT(guar_ltv)guar_ltv,count(guar_no)collcnt  from bus_affrim_collateral group by applyno,bankid ";
					daoTemplate.executeSql(insertSql);

					//插入dwh_guarantor表
					delSql="delete from dwh_guarantor";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_guarantor select applyno,bankid,GROUP_CONCAT(cifid)gccifid,sum(relation)relation,count(cifid)totguarcnt from bus_affrim_guarantor group by applyno,bankid";
					daoTemplate.executeSql(insertSql);
					
					//插入dwh_guarantyco表 
					delSql="delete from dwh_guarantyco";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_guarantyco select applyno,bankid,count(gccontno)gcocnt from bus_affrim_guarantyco group by applyno,bankid";
					daoTemplate.executeSql(insertSql);

					//插入dwh_coborrower表 
					delSql="delete from dwh_coborrower";
					daoTemplate.executeSql(delSql);
					insertSql="insert into dwh_coborrower select applyno,bankid,GROUP_CONCAT(cifid)cocifid,count(cifid)totcocnt from bus_affrim_coborrower where state='1' group by applyno,bankid";
					daoTemplate.executeSql(insertSql);
					
					 Sql="select  b.applyno ID, " + 
					 		" b.cifid CUSTOMERID, " + 
					 		"(CASE WHEN b.occurtype = '1' THEN 'NEW.LOAN' WHEN b.occurtype = '3' THEN 'RENEW' WHEN b.occurtype = '5' THEN 'WCSP.RENEW' ELSE '*' END) LOANPROCESS, " + 
					 		"c.lncnt CURRCUSTCYCLE, " + 
					 		"'CNY' LOANAMOUNTCCY, " +
					 		"b.app_sum AMTREQUESTED, " + 
					 		"b.app_term TERMREQUESTED, " + 
					 		"v.bus_sum APPROVEDAMT, " + 
					 		"v.term APPROVEDTERM, " + 
					 		"a.purpose LOANPURPOSE, " + 
					 		"b.cliname CUSTOMERNAME, " + 
					 		"CONCAT(c.crditlvl,1) CUSTRATING, " + 
					 		"p.foreign_itemcode REPAYMENTTYPE, " + 
					 		"b.app_usr DEPTACCTOFFR, " + 
					 		"d.guar_no COLATERALID, " + 
					 		"d.guarsum COLLTOTVALUE, " + 
					 		"e.gccifid GUARANTOR, " + 
					 		"e.relation GUARRELATN, " + 
					 		"a.loanfee BOXCOMM, " + 
					 		"v.arate INTERESTRATE, " + 
					 		"f.foreign_itemcode CATEGORY, " + 
					 		"h.foreign_itemcode COMMPRODUCT, " + 
					 		"nvl(g.totcocnt,0)+nvl(e.totguarcnt,0)+nvl(i.gcocnt,0) NUMBEROFGUAR, " + 
					 		"g.totcocnt GUARCOV1, " + 
					 		"e.totguarcnt GUARCOV2, " + 
					 		"i.gcocnt GUARCOV3, " + 
					 		"d.col_no COLLTYPE, " + 
					 		"d.guar_ltv COLLCOVRATIO, " + 
					 		"d.collcnt NUMBERCOLL, " + 
					 		"f.foreign_itemcode EXISTCATEG, " + 
					 		"f.foreign_itemname EXISTCOMMPROD, " + 
					 		"a.arate EXISTINTEREST, " + 
					 		"j.phaseno LOANAPPLSTAGE, " + 
					 		"j.begindate STAGEDATE, " + 
					 		"REVERSE(SUBSTR(REVERSE(k.phaseno) FROM INSTR(REVERSE(k.phaseno),'|')+1)) PREVAPPLSTAGE, " + 
					 		"REVERSE(SUBSTR(REVERSE(k.begindate) FROM INSTR(REVERSE(k.begindate),'|')+1)) PREVSTAGEDATE, " + 
					 		"REVERSE(SUBSTR(REVERSE(k.operid) FROM INSTR(REVERSE(k.operid),'|')+1)) PREVSTAGEINPUTTER, " + 
					 		"REVERSE(SUBSTR(REVERSE(k.operid) FROM INSTR(REVERSE(k.operid),'|')+1)) PREVSTAGEAUTHORISER, " + 
					 		"REVERSE(left(REVERSE(k.operid) ,LOCATE('|',REVERSE(k.operid))-1)) INPUTTER, " + 
					 		"case when j.time is null then substr(replace(replace(replace(b.last_modify_time,':',''),' ',''),'-',''),3,10) else j.time end DATETIME, " + 
					 		"REVERSE(left(REVERSE(k.operid) ,LOCATE('|',REVERSE(k.operid))-1)) AUTHORISER, " + 
					 		"w.foreign_itemcode COCODE, " + 
					 		"l.laonmrepay REPAMOUTMON, " + 
					 		"l.repincomerate RAPYAMTBUSIN, " + 
					 		"a.bus_sum LOANLIMITAMT, " + 
					 		"b.app_date EVALUATINGDATE, " + 
					 		"l.repayabilityrate REPAYABILITYR, " + 
					 		"b.app_sum LOANAPPROVED, " + 
					 		"l.aftalldebtrate TADJASSETLIAB, " + 
					 		"case when a1.dao is null then b.pre_loan_operid else a1.dao end PREEVALDAO, " + 
					 		"z.foreign_itemcode CUSTSECTOR, " + 
					 		"c.age CUSTAGE, " + 
					 		"case when c.is_local='0' then 'N' when  c.is_local='1' then 'Y' end CUSTHOUSE, " + 
					 		"q.foreign_itemcode CUSTMARITAL, " + 
					 		"c.famaddr CUSTLOCAL, " + 
					 		"n.main_bus CUSTBUSNAME, " + 
					 		"b.app_date OPENINGDATE, " + 
					 		"n.oper_date BUSINESSOPENIN, " + 
					 		"l.repayabilityrate REPABILIRATIO, " + 
					 		"l.aftbusdebtrate ADJASSETRATIO, " + 
					 		"l.aftalldebtrate TADJASSETRATI, " + 
					 		"n.cliname CUSTCONAME1, " + 
					 		"n.certno CUSTREGNUM1, " + 
					 		"case when s.sps_will_sign='0' then 'NO' when s.sps_will_sign='1' then 'YES' end SPSIGNINFO, " + 
					 		"o.sps_cifid SPOUSEINFO, " + 
					 		"x.foreign_itemname CUSTRISKRATIN, " + 
					 		"y.foreign_itemname TRANSRISKRATI, " + 
					 		"v.bus_sum PREAPPROVDAMT, " + 
					 		"v.term PREAPRVTERM, " + 
					 		"v.repay_type  PREREPAYTYPE " + 
					 		"from app_main_info b  " + 
					 		"left join ac_businessvch a on a.applyno=b.applyno and a.bankid=b.bankid     " + 
					 		"left join ind_base c on b.cifid=c.cifid and b.bankid=c.bankid     " + 
					 		"left join dwh_collateral d on d.applyno=b.applyno and b.bankid=d.bankid     " + 
					 		"left join dwh_guarantor e on e.applyno=b.applyno and b.bankid=e.bankid     " + 
					 		"left join (select * from cn_data_dictionary where itemno='dt0052') f on b.prdt_no=f.itemcode and b.bankid=f.bankid      " + 
					 		"left join dwh_coborrower g on b.applyno=g.applyno and b.bankid=g.bankid     " +
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0053' and bankid ='"+bankid+"') h on CONCAT(b.prdt_no,b.guar_type)=h.itemcode and b.bankid=h.bankid " +
					 		"left join dwh_guarantyco i on i.applyno=b.applyno and b.bankid=i.bankid     " + 
					 		"left join dwh_flow j on b.applyno=j.applyno and b.bankid=j.bankid     " + 
					 		"left join dwh_flow_ph k on b.applyno=k.applyno and b.bankid=k.bankid     " + 
					 		"left join bus_survey_fsindex l on b.applyno=l.applyno and b.bankid=l.bankid     " + 
					 		"left join bus_doe_base n on b.applyno = n.applyno and b.bankid=n.bankid     " + 
					 		"left join ind_spouse_info o on b.cifid=o.cifid and b.bankid=o.bankid     " + 
					 		"left join (select * from cn_data_dictionary where itemno='dt0001') p on b.repay_type=p.itemcode and b.bankid=p.bankid   " + 
					 		"left join (select * from cn_data_dictionary where itemno='dt0009' and foreign_itemname<>'296' and foreign_itemcode regexp '[a-z0-9A-Z]') q on c.is_mar=q.itemcode and c.bankid=q.bankid   " + 
					 		"left join app_bus_info s on b.applyno=s.applyno and b.bankid=s.bankid    " + 
					 		"left join bus_survey_result t on b.applyno=t.applyno and b.bankid=t.bankid    " + 
					 		"left join dwh_afm_res v on b.applyno=v.applyno and b.bankid=v.bankid   " + 
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0051' ) w on b.app_brno=w.itemcode and b.bankid=w.bankid " +
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0030' ) x on t.custriskrating=x.itemcode and t.bankid=x.bankid " +
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0030' ) y on t.adjustrating=y.itemcode and t.bankid=y.bankid " +
					 		"left join (select * from  cn_data_dictionary where  itemno='dt0054' ) z on n.industrytype=z.itemcode and n.bankid=z.bankid " +
							"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) a1 on b.pre_loan_operid=a1.itemcode and b.bankid=a1.bankid " +
					 		"where b.applyno not regexp '[a-zA-Z]' or b.applyno like 'APL%'  ";
							BRC.getLog().info("MCR.LOAN.APPLICATION.BOX-SQL:" + Sql);
							
							//最后一个字段带分隔符,加空值tmp
							String[] str = {"id","customerid","loanprocess","currcustcycle","loanamountccy","amtrequested","termrequested","approvedamt","approvedterm","loanpurpose","customername","custrating","repaymenttype","deptacctoffr",
									"colateralid","colltotvalue","guarantor","guarrelatn","int.discount","add.new.comm","add.new.chrg","boxcomm","box.charges","waive.comm.chg","interestrate","category","commproduct","cc.level","existing.loan.id","numberofguar",
									"guarcov1","guarcov2","guarcov3","guar.cov.4","high.guar.rel","colltype","collcovratio","numbercoll","categ.req.box","int.req.box","com.prd.req.box","comm.req.box","chrg.req.box","cc.lev.req.box","existcateg",
									"existcommprod","existinterest","internal.score","external.score","committee.level","comite.lev.req.box","comite.price.id","control.flag","term.frequency","box.price.id","box.price.id.value","loanapplstage","stagedate","stage.reason",
									"prevapplstage","prevstagedate","prevstageinputter","prevstageauthoriser","price.control","mcbox.status","cc.price.id","catg.price.id","int.price.id","prod.price.id","comm.price.id","chrg.price.id","disb.loan.id",
									"prev.stage.1.reason","prev.stage.2.reason","exception.reasons","credit.risk","int.max.discount","int.min.discount","tot.current.outs.amt","tot.future.outs.amt","override","record.status","curr.no","inputter","datetime","authoriser",
									"cocode","dept.code","auditor.code","audit.date.time","source.funds","fm.income","no.income.famil","business.income","business.cost","gross.profit","expenses","bus.net.income","fm.net.income","total.net.incom","cash","deposit","artd",
									"stock.in.trade","total.current","fixed.assets","other.assets","ent.tot.assets","tot.run.debt","debt-owner","return.to.capit","bi.profit.margi","flow.ratio","quick.ratio","stock.trade.tur","acct.rec.turnov","acct.payable.tu","assets.liabilit",
									"adj.asset.liab.","repamoutmon","rapyamtbusin","loanlimitamt","evaluatingdate","repayabilityr","loanapproved","familly.income","family.expenses","en.debt.in.sho","en.debt.in.long","fm.cash","family.deposit","fm.artd","fm.current",
									"fm.fixed.assets","fm.other.asset","fm.tot.assets","fm.debt.short","fm.debt.long","fm.tot.debt","total.cash","total.deposit","total.artd","total.cur.ass","total.fixed.ass","t.other.assets","t.total.assets","t.debt.short","t.debt.in.long",
									"t.total.debit","tadjassetliab","preevaldao","fm.debt.owner","cust.oth.imf","empl.no","rse.risk.categ","rep.willingness","health.status","bus.sustainabil","car.t.amt","kiva.customer","prepaid.account","bank.name","prince.amount","end.date",
									"custsector","custage","custhouse","custmarital","custlocal","custbusname","co.cust.no1","co.cust.no2","co.cust.no3","co.cust.no4","risk.level","credit.committe","note","special.approva","openingdate","businessopenin","credit.record",
									"repabiliratio","adjassetratio","tadjassetrati","business.change","bus.add.changes","bus.asset.chang","bus.liab.change","fam.asset.chang","fam.liab.change","cre.report.chan","fam.add.changes","marital.changes","borrow.changes","custconame1",
									"cust.co.name2","custregnum1","cust.reg.num2","prev.stage.4","tax.break","ca.risk.tip","document.integr","lack.of.documen","document.name","spsigninfo","spouseinfo","refere.comment1","refere.comment2","credit.limit","credit.expendit","acct.payables",
									"credit.risk.inf","exception.info","exception","exception.type","exception.comme","channel.model","branch.approver","agent","other.financial","channel.distrib","distributor.pos","investigat.staf","channel.cust.nu","custriskratin","transriskrati","credit.comm.ris",
									"proof.repayment","credit.approval","preapprovdamt","preaprvterm","prerepaytype","st3.approvd.amt","stg3.aprv.term","st3.repay.type","tmp"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("tfe_mcr.loan.wof_mcnc_ups".equals(dwhName)) 
				{
					
					 Sql="select CONVERT(jbresult,CHAR) result from (select JSON_OBJECT('ID',IFNULL(e.contno,''),'DATE_TIME','','CUSTOMER_ID',IFNULL(e.cifid,''),'CATEGORY',IFNULL(f.prdt_no,''),'LOAN_OFFICER',IFNULL(case when h.dao is null then e.manage_operid else h.dao end,''),'LIQ_ACCOUNT',IFNULL(e.reppriac_no,''),'DATE',JSON_OBJECT('$date',IFNULL(DATE_FORMAT(a.occur_date,'%Y-%m-%dT%H:%i:%s.000-0000'),'')),'AMOUNT',IFNULL(a.verifi_amt+a.verifi_intst+ a.verifi_over_intst+a.verifi_cmpd_intst,''),'TOT_AMT_OUTS',IFNULL(a.verifi_amt+a.verifi_intst+ a.verifi_over_intst+a.verifi_cmpd_intst-a.recycle_amt-a.recycle_intst-a.recycle_over_intst-a.recycle_cmpd_intst,''),'STATUS',IFNULL(a.recycle_sts,''),'NO_DAYS_OVERDUE',IFNULL(DATEDIFF(a.occur_date,g.over_date),''),'DATA_EXTRACT_DATE','"+workdate+"','LAST_OVDUE_DATE',JSON_OBJECT('$date',IFNULL(DATE_FORMAT(g.over_date,'%Y-%m-%dT%H:%i:%s.000-0000'),'')),  " + 
					 		"'TYPE',JSON_ARRAY(  " + 
					 		"case when b.vchno is not null then JSON_OBJECT('TYPE','PR','OVERDUE_AMT',IFNULL(a.verifi_amt,''),'OUTS_AMT',IFNULL(a.verifi_amt-a.recycle_amt,''),'PAID_AMT',IFNULL(a.recycle_amt,''),'TYPE_PAY_DATE', PRJSON ) else   " + 
					 		"JSON_OBJECT('TYPE','PR','OVERDUE_AMT',IFNULL(a.verifi_amt,''),'OUTS_AMT',IFNULL(a.verifi_amt-a.recycle_amt,''),'PAID_AMT',IFNULL(a.recycle_amt,'') ) end,  " + 
					 		"case when c.vchno is not null  then JSON_OBJECT('TYPE','IN','OVERDUE_AMT',IFNULL(a.verifi_intst,''),'OUTS_AMT',IFNULL(a.verifi_intst-a.recycle_intst,''),'PAID_AMT',IFNULL(a.recycle_intst,''),'TYPE_PAY_DATE',INJSON) else   " + 
					 		"JSON_OBJECT('TYPE','IN','OVERDUE_AMT',IFNULL(a.verifi_intst,''),'OUTS_AMT',IFNULL(a.verifi_intst-a.recycle_intst,''),'PAID_AMT',IFNULL(a.recycle_intst,'')) end ,  " + 
					 		"case when d.vchno is not null  then JSON_OBJECT('TYPE','PE','OVERDUE_AMT',IFNULL(a.verifi_over_intst+a.verifi_cmpd_intst,''),'OUTS_AMT',IFNULL(a.verifi_over_intst+a.verifi_cmpd_intst-a.recycle_over_intst-a.recycle_cmpd_intst,''),'PAID_AMT',IFNULL(a.recycle_over_intst+a.recycle_cmpd_intst,''),'TYPE_PAY_DATE',PEJSON) else JSON_OBJECT('TYPE','PE','OVERDUE_AMT',IFNULL(a.verifi_over_intst+a.verifi_cmpd_intst,''),'OUTS_AMT',IFNULL(a.verifi_over_intst+a.verifi_cmpd_intst-a.recycle_over_intst-a.recycle_cmpd_intst,''),'PAID_AMT',IFNULL(a.recycle_over_intst+a.recycle_cmpd_intst,'')) end  )) jbresult  " + 
					 		" from ac_verifi_info a   " + 
					 		" left join (select bankid,vchno, JSON_ARRAYAGG(JSON_OBJECT('TYPE_PAY_DATE',JSON_OBJECT('$date',DATE_FORMAT(ipt_date,'%Y-%m-%dT%H:%i:%s.000-0000')),'TYPE_PAY_FT',trace_no,'TYPE_PAY_AMT',recycle_amt)) PRJSON from ac_verify_recycle_info where recycle_amt>0 group by bankid,vchno)b on a.vchno=b.vchno and a.bankid=b.bankid   " + 
					 		" left join (select bankid,vchno,JSON_ARRAYAGG(JSON_OBJECT('TYPE_PAY_DATE',JSON_OBJECT('$date',DATE_FORMAT(ipt_date,'%Y-%m-%dT%H:%i:%s.000-0000')),'TYPE_PAY_FT',trace_no,'TYPE_PAY_AMT',recycle_intst)) INJSON from ac_verify_recycle_info where recycle_intst>0 group by bankid,vchno)c on a.vchno=c.vchno and a.bankid=c.bankid   " + 
					 		" left join (select bankid,vchno,JSON_ARRAYAGG(JSON_OBJECT('TYPE_PAY_DATE',JSON_OBJECT('$date',DATE_FORMAT(ipt_date,'%Y-%m-%dT%H:%i:%s.000-0000')),'TYPE_PAY_FT',trace_no,'TYPE_PAY_AMT',recycle_fee)) PEJSON from   " + 
					 		" ac_verify_recycle_info where recycle_fee>0 group by bankid,vchno)d on a.vchno=d.vchno and a.bankid=d.bankid   " + 
					 		" left join ac_businessvch e on a.vchno=e.vchno and a.bankid=e.bankid  " + 
					 		" left join ln_mst f on e.loanac_id=f.ac_id and e.bankid=f.bankid  " + 
					 		" left join (select * from ln_lo where (ac_id,lo_term) in (select ac_id,max(lo_term) from ln_lo group by ac_id))g on f.ac_id=g.ac_id and f.bankid =g.bankid " +
					 		" left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) h on e.manage_operid=h.itemcode and e.bankid=h.bankid ) aa";
							BRC.getLog().info("tfe_mcr.loan.wof_mcnc_ups-SQL:" + Sql);
							
							String[] str = {"result"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
						
				}else if ("MCR.SAVINGS".equals(dwhName)) 
				{
					//插入dwh_cs_hst表 
					String delSql="delete from dwh_cs_hst";
					daoTemplate.executeSql(delSql);
					String insertSql="insert into dwh_cs_hst select cifid,bankid,opn_br_no,sum( case when add_ind='0' then -tx_amt when add_ind='1' then tx_amt end) txamt from cs_mst_hst where (tx_date,cifid,opn_br_no) in (select max(tx_date),cifid,opn_br_no from cs_mst_hst  group by cifid,opn_br_no) group by cifid,opn_br_no,bankid";
					BRC.getLog().info("MCR.SAVINGS-SQL-insert:" + insertSql);
					daoTemplate.executeSql(insertSql);
					
					 Sql="select 'Account/contract Number','Last movement','Category','Client number','Client Name','Client Gender','Client Age','Sector','DAO','DAO Description','Parent DAO','Parent DAO Description','USER','DEPT.LEVEL','DELIVERY.POINT','Account creation date','Term deposit maturity date','Outstanding principal','Average outstanding  amount','Cumulative Debit mvt since beginning of the month','Cumulative Credit mvt since beginning of the month','Cumulative mvt since beginning of the month','Interests accrued','Term Deposit Status','Customer Type','Currency','SP Maturity Date','Saving Rate' " + 
					 		"union all " + 
					 		"select a.noteno Account,case when nvl(d.txamt,0)=0 then k.movement else nvl(d.txamt,0) end LastMovement, " + 
					 		"'7500' Category, a.cifid ClientNumber,b.cliname ClientName,case when b.sex='1' then 'MALE'  when b.sex='2' then 'FEMALE' end ClientGender, " + 
					 		"b.age ClientAge,h.sector,g.dao,e.staname,''ParentDAO,''ParentDAODescription,''USER,''DEPTLEVEL,''DELIVERYPOINT,b.ipt_date ,'',c.busBal,'0','','','','0','','I','CNY','','0' " + 
					 		"from (select * from cs_mst where ac_id='1' and bankid='"+bankid+"')  a  " + 
					 		"left join ind_base b on a.cifid=b.cifid " + 
					 	 	"left join (select sum(bus_bal) busBal,t1.cifid,t1.bankid,t1.instcode  " + 
					 		" from ac_businessvch t1 group by t1.cifid,t1.bankid,t1.instcode ) c on a.cifid=c.cifid and a.bankid=c.bankid and c.instcode=a.opn_br_no " + 
					 		" left join dwh_cs_hst d on d.cifid = a.cifid and d.bankid=a.bankid and d.opn_br_no=a.opn_br_no " +
					 		"left join cn_user e on b.ipt_usr=e.operid and b.bankid=e.bankid " +
					 		"left join (select max(industrytype) industrytype,cifid,bankid,instcode from ac_businessvch where (applyno,cifid) in (select max(applyno),cifid from ac_businessvch group by cifid) group by cifid,bankid,instcode ) f on f.cifid=a.cifid and a.bankid=f.bankid and f.instcode=a.opn_br_no " +
					 		"left join (select b.bankid,b.itemcode,b.foreign_itemcode sector from  cn_data_dictionary b where b.itemno='dt0054' ) h on f.industrytype=h.itemcode and f.bankid=h.bankid " +
					 		"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) g on b.ipt_usr=g.itemcode and b.bankid=g.bankid " +
					 		"left join hd_saving k on a.noteno=k.acno " +
					 		"where a.noteno is not null";
							BRC.getLog().info("MCR.SAVINGS-SQL:" + Sql);
							
							//最后一个字段带分隔符,加空值tmp
							String[] str = {"account/contract number","last movement","category","client number","client name",
									"client gender","client age","sector","dao","dao description","parent dao","parent dao description",
									"user","dept.level","delivery.point","account creation date","term deposit maturity date","outstanding principal",
									"average outstanding  amount","cumulative debit mvt since beginning of the month",
									"cumulative credit mvt since beginning of the month","cumulative mvt since beginning of the month",
									"interests accrued","term deposit status","customer type","currency","sp maturity date","saving rate","tmp"};
							
							//生成文件及上传
							sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
				}else if ("MCNC_MCR.WOF.REPORT".equals(dwhName)) 
				{
					
					 Sql="select 'Number Contract / PD','Product','Product description','Customer Number','Name','Surename(s)','Home Address','Business Address','Cell phone','Account recovery','Commercial Agent','Com  Agent Name','Agent recovery','Agent recovery Name','Disbursement Date','Loan Amount','Loan Cycle','Number of days in arrear at WO date','WO date','Total Amount WO','Outst. balance to be recovery','Status','Element Type 1','PR Element Amount WO','PR Element Amount paid','PR Element Amount remaining','PR Last repayment date','PR FT code of last repayment','PR Amount of last repayment','Element type 2','IN Element Amount WO','IN Element Amount paid','IN Element Amount remaining','IN Last repayment date','IN FT code of last repayment','IN Amount of last repayment','Element type 3','PE Element Amount WO','PE Element Amount paid','PE Element Amount remaining','PE Last repayment date','PE FT code of last repayment','PE Amount of last repayment','Element type 4','PS Element Amount WO','PS Element Amount paid','PS Element Amount remaining','PS Last repayment date','PS FT code of last repayment','PS Amount of last repayment','Currency','PR Element Amount paid per month','IN Element Amount paid per month','PS Element Amount paid per month','PE Element Amount paid per month','Guarantor Number(s)','Guarantor Full Name(s)','Guarantor Business Address','Guarantor Phone(s)','Industry Type','Business sector','Sex','Branch'  " + 
					 		"union all "+
					 		"select a.contno,(case when o.oprdtno is not null then o.oprdtno else (select foreign_itemcode from cn_data_dictionary where itemno='dt0052' and a.prdt_no=itemcode and bankid=a.bankid) end ) ," +
					 		"(case when o.oprdtno is not null then (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode not like 'F%' and foreign_itemcode=o.oprdtno ) else " + 
					 		" (select foreign_itemname from cn_data_dictionary where itemno='dt0052' and itemcode=a.prdt_no and bankid=a.bankid) end ) ,h.cifid,h.cliname,upper(to_pinyin(h.cliname)) pinyin,'' famaddr,h.famzip,'' famtel,k.noteno, case when r.dao is null then a.manage_operid else r.dao end ,case when r.daoname is null then j.opername else r.daoname end,nvl(b.recycle_amt,0)+nvl(b.recycle_intst,0)+nvl(b.recycle_over_intst,0)+nvl(b.recycle_cmpd_intst,0)+nvl(b.recycle_fee,0) agentrecovery,q.AgentrecoveryName,  " + 
					 		"a.occurdate,ab.bus_sum,h.lncnt termcnt,case when p.overdays is not null then p.overdays else a.curr_overdays end lodays,b.occur_date,(b.verifi_amt+b.verifi_intst+b.verifi_over_intst+b.verifi_cmpd_intst+b.verifi_fee) totalamt,  " +
					 		"(b.verifi_amt+b.verifi_intst+b.verifi_over_intst+b.verifi_cmpd_intst+b.verifi_fee-b.recycle_amt-b.recycle_intst-b.recycle_over_intst-b.recycle_cmpd_intst-b.recycle_fee)recoveryamt," +
					 		"case when (b.verifi_amt+b.verifi_intst+b.verifi_over_intst+b.verifi_cmpd_intst+b.verifi_fee-b.recycle_amt-b.recycle_intst-b.recycle_over_intst-b.recycle_cmpd_intst-b.recycle_fee)=0 then 'REIMBURSED'  when (b.verifi_amt+b.verifi_intst+b.verifi_over_intst+b.verifi_cmpd_intst+b.verifi_fee-b.recycle_amt-b.recycle_intst-b.recycle_over_intst-b.recycle_cmpd_intst-b.recycle_fee)>0 then 'OUSTANDING' end status,'PR' pr,b.verifi_amt,b.recycle_amt,(b.verifi_amt-b.recycle_amt) prunrecovery,  " + 
					 		"c.radate,c.ratrace,c.ramt,'IN' elementin ,b.verifi_intst,b.recycle_intst,(b.verifi_intst-b.recycle_intst) inunrecovery,d.rindate,d.rintrace,d.rint,'PE' pe ,(b.verifi_over_intst+b.verifi_cmpd_intst)cmp,(b.recycle_over_intst+b.recycle_cmpd_intst)reccmp,  " + 
					 		"(b.verifi_over_intst+b.verifi_cmpd_intst-b.recycle_over_intst-b.recycle_cmpd_intst)peunrecovery,e.rcdate,e.rctrace,e.rcmp,'PS' ps,b.verifi_fee,b.recycle_fee,(b.verifi_fee-b.recycle_fee) psunrecovery,f.rodate,f.rotrace,f.roth,'CNY'curr,'0'prmonth,'0'inmonth,'0'psmonth,'0'pemonth,  " + 
					 		"CONCAT_WS('|',g.guarcifid,m.commcifid),CONCAT_WS('|',CONCAT(upper(to_pinyin(g.guarcliname)),g.guarcliname),CONCAT(upper(to_pinyin(m.commcliname)),m.commcliname)) guarcliname,''guaraddr,'' guartel,n.IndustryType,p.Businesssector,case when h.sex='1' then 'MALE'  when h.sex='2' then 'FEMALE' end sex ,a.manage_instcode  as branch " + 
					 		" from ac_businessvch a inner join ac_businesscont ab on a.contno =  ab.contno inner join ac_verifi_info b on a.bankid=b.bankid and a.vchno=b.vchno   " +
					 		"inner join  ind_base h on a.cifid= h.cifid and a.bankid=h.bankid   " + 
					 		"inner join ln_mst l on a.loanac_id=l.ac_id and a.bankid=l.bankid   " + 
					 		"left join (select sum(recycle_amt)ramt,max(case when serid not like 'FT%' then trace_no else serid end)ratrace,max(ipt_date)radate,vchno,bankid from ac_verify_recycle_info where recycle_amt>0 and state='1' group by  vchno,bankid ) c on b.vchno=c.vchno and b.bankid=c.bankid   " + 
					 		"left join (select sum(recycle_intst)rint,max(case when serid not like 'FT%' then trace_no else serid end)rintrace,max(ipt_date)rindate,vchno,bankid from ac_verify_recycle_info where recycle_amt>0 and state='1' group by  vchno,bankid ) d on b.vchno=d.vchno and b.bankid=d.bankid   " + 
					 		"left join (select sum(recycle_oth)rcmp,max(case when serid not like 'FT%' then trace_no else serid end)rctrace,max(ipt_date)rcdate,vchno,bankid from ac_verify_recycle_info where recycle_amt>0 and state='1' group by  vchno,bankid ) e on b.vchno=e.vchno and b.bankid=e.bankid   " + 
					 		"left join (select sum(recycle_fee)roth,max(case when serid not like 'FT%' then trace_no else serid end)rotrace,max(ipt_date)rodate,vchno,bankid from ac_verify_recycle_info where recycle_amt>0 and state='1' group by  vchno,bankid ) f on b.vchno=f.vchno and b.bankid=f.bankid   " + 
					 		"left join (select group_concat(cifid)guarcifid,group_concat(cliname)guarcliname,t1.contno,t1.bankid from bus_gcc_relative t1,ac_guarcont t2 where  t1.gccontno= t2.contno group by t1.contno,t1.bankid) g on a.contno=g.contno and a.bankid=g.bankid   " + 
					 		"left join cn_product i on a.prdt_no=i.prdt_no and a.bankid=i.bankid    " + 
					 		"left join cn_user j on a.manage_operid=j.operid and a.bankid=j.bankid  " + 
					 		"left join (select * from cs_mst where ac_id='1') k on a.cifid=k.cifid and a.manage_instcode=k.opn_br_no and a.bankid=k.bankid " + 
					 		"left join (select group_concat(cifid)commcifid,group_concat(cliname)commcliname,applyno,bankid from bus_affrim_coborrower group by applyno,bankid) m on m.applyno=a.applyno and a.bankid=m.bankid   " + 
					 		"left join (select b.bankid,b.itemcode,b.foreign_itemname IndustryType from cn_data_dictionary b where b.itemno='dt0055' ) n on substr(a.industrytype,1,3)=n.itemcode  and a.bankid=n.bankid " +
					 		"left join (select b.bankid,b.itemcode,b.foreign_itemname Businesssector from  cn_data_dictionary b where b.itemno='dt0054' ) p on a.industrytype=p.itemcode and a.bankid=p.bankid " +
					 		"left join (select b.bankid,b.itemcode,b.foreign_itemname AgentrecoveryName from  cn_data_dictionary b where b.itemno='dt0051' ) q on a.manage_instcode=q.itemcode and a.bankid=q.bankid " +
					 		"left join hd_wof p on a.contno=p.contno " +
					 		"left join hd_loan o on a.contno=o.id " +
					 		"left join (select bankid,itemcode,foreign_itemcode dao,foreign_itemname daoname from  cn_data_dictionary b where b.itemno='dt0056' ) r on a.manage_operid=r.itemcode and b.bankid=r.bankid " +
					 		"where a.contno not like 'PDPD%' and a.contno not like 'HT%' and a.contno not like '%LC'";
								BRC.getLog().info("MCNC_MCR.WOF.REPORT:" + Sql);
								
								
						//字段名称
//						String[] orderHeader =  { "vchno","prdtNo","prdtName","cifid","cliname","pinyin","famaddr","famzip","famtel","reppriacNo",
//										"manageOperid","opername","agentrecovery","agentoperid","begindate","busSum","termcnt","lodays","occurDate","totalamt",
//										"recoveryamt","status","pr","verifiAmt","recycleAmt","prunrecovery","radate","ratrace","ramt","elementin",
//										"verifiIntst","recycleIntst","inunrecovery","rindate","rintrace","rint","pe","cmp","reccmp","peunrecovery",
//										"rcdate","rctrace","rcmp","ps","verifiFee","recycleFee","psunrecovery","rodate","rotrace","roth","curr","prmonth",
//										"inmonth","psmonth","pemonth","guarcifid","guarcliname","guaraddr","guartel","ind","bus","sex"};
						//csv文件标题
//						CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("Number Contract / PD","Product","Product description",
//								"Customer Number","Name","Surename(s)","Home Address","Business Address","Cell phone","Account recovery","Commercial Agent","Com  Agent Name",
//								"Agent recovery","Agent recovery Name","Disbursement Date","Loan Amount","Loan Cycle","Number of days in arrear at WO date","WO date","Total Amount WO",
//								"Outst. balance to be recovery","Status","Element Type 1","PR Element Amount WO","PR Element Amount paid","PR Element Amount remaining","PR Last repayment date","PR FT code of last repayment",
//								"PR Amount of last repayment","Element type 2","IN Element Amount WO","IN Element Amount paid","IN Element Amount remaining","IN Last repayment date","IN FT code of last repayment","IN Amount of last repayment",
//								"Element type 3","PE Element Amount WO","PE Element Amount paid","PE Element Amount remaining","PE Last repayment date","PE FT code of last repayment","PE Amount of last repayment","Element type 4","PS Element Amount WO",
//								"PS Element Amount paid","PS Element Amount remaining","PS Last repayment date","PS FT code of last repayment","PS Amount of last repayment","Currency","PR Element Amount paid per month","IN Element Amount paid per month",
//								"PS Element Amount paid per month","PE Element Amount paid per month","Guarantor Number(s)","Guarantor Full Name(s)","Guarantor Business Address","Guarantor Phone(s)","Industry Type","Business sector","Sex");
						
						//生成csv文件及上传
//						sendDataHouseCsv(Sql,orderHeader,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,csvFormat);
						String[] str = {"number contract / pd","product","product description","customer number","name","surename(s)",
								"home address","business address","cell phone","account recovery","commercial agent","com  agent name",
								"agent recovery","agent recovery name","disbursement date","loan amount","loan cycle","number of days in arrear at wo date",
								"wo date","total amount wo","outst. balance to be recovery","status","element type 1","pr element amount wo","pr element amount paid",
								"pr element amount remaining","pr last repayment date","pr ft code of last repayment","pr amount of last repayment","element type 2",
								"in element amount wo","in element amount paid","in element amount remaining","in last repayment date","in ft code of last repayment",
								"in amount of last repayment","element type 3","pe element amount wo","pe element amount paid","pe element amount remaining","pe last repayment date",
								"pe ft code of last repayment","pe amount of last repayment","element type 4","ps element amount wo","ps element amount paid","ps element amount remaining",
								"ps last repayment date","ps ft code of last repayment","ps amount of last repayment","currency","pr element amount paid per month","in element amount paid per month",
								"ps element amount paid per month","pe element amount paid per month","guarantor number(s)","guarantor full name(s)","guarantor business address","guarantor phone(s)","industry type","business sector","sex"};
						
						SimpleDateFormat sdf =new SimpleDateFormat("yyyyMMdd");
						Date date=sdf.parse(workdate);
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(date);
						calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
//						if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
//						sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);
//						}
						sendDataHouse(Sql,str,dwhName,fileName,localPath,uploadPath,workdate,daoTemplate,bankid,bucketname);

				}

			}

			BRC.getLog().info("日终MCCLoanReport上传完毕");
		} catch (Exception e) {
			logger.error("日终生成数仓文件，异常", e);
			throw e;
		}
	}
	
	/**
	 * 生成txt文件并上传
	 * 
	 * @param Sql
	 * @param str
	 * @param dwhName
	 * @param fileName
	 * @param localPath
	 * @param uploadPath
	 * @param workdate
	 * @param daoTemplate
	 * @param bankid
	 * @param bucketname
	 * @version v1.0 yyp
	 */
	private void sendDataHouse(String Sql, String[] str,String dwhName, String fileName, String localPath, String uploadPath, String workdate,DaoTemplate daoTemplate,long bankid,String bucketname) throws Exception 
	{

		//生成本地文件
		try {

		List<Map> arrayList= daoTemplate.getMapListBySql(Sql);
		if("MCR.LOAN.APPLICATION.BOX".equals(dwhName))
		{
			String title="@ID^CUSTOMER.ID^LOAN.PROCESS^CURR.CUST.CYCLE^LOAN.AMOUNT.CCY^AMT.REQUESTED^TERM.REQUESTED^APPROVED.AMT^APPROVED.TERM^LOAN.PURPOSE^CUSTOMER.NAME^CUST.RATING^REPAYMENT.TYPE^DEPT.ACCT.OFFR^COLATERAL.ID^COLL.TOT.VALUE^GUARANTOR^GUAR.RELATN^INT.DISCOUNT^ADD.NEW.COMM^ADD.NEW.CHRG^BOX.COMM^BOX.CHARGES^WAIVE.COMM.CHG^INTEREST.RATE^CATEGORY^COMM.PRODUCT^CC.LEVEL^EXISTING.LOAN.ID^NUMBER.OF.GUAR^GUAR.COV.1^GUAR.COV.2^GUAR.COV.3^GUAR.COV.4^HIGH.GUAR.REL^COLL.TYPE^COLL.COV.RATIO^NUMBER.COLL^CATEG.REQ.BOX^INT.REQ.BOX^COM.PRD.REQ.BOX^COMM.REQ.BOX^CHRG.REQ.BOX^CC.LEV.REQ.BOX^EXIST.CATEG^EXIST.COMM.PROD^EXIST.INTEREST^INTERNAL.SCORE^EXTERNAL.SCORE^COMMITTEE.LEVEL^COMITE.LEV.REQ.BOX^COMITE.PRICE.ID^CONTROL.FLAG^TERM.FREQUENCY^BOX.PRICE.ID^BOX.PRICE.ID.VALUE^LOAN.APPL.STAGE^STAGE.DATE^STAGE.REASON^PREV.APPL.STAGE^PREV.STAGE.DATE^PREV.STAGE.INPUTTER^PREV.STAGE.AUTHORISER^PRICE.CONTROL^MCBOX.STATUS^CC.PRICE.ID^CATG.PRICE.ID^INT.PRICE.ID^PROD.PRICE.ID^COMM.PRICE.ID^CHRG.PRICE.ID^DISB.LOAN.ID^PREV.STAGE.1.REASON^PREV.STAGE.2.REASON^EXCEPTION.REASONS^CREDIT.RISK^INT.MAX.DISCOUNT^INT.MIN.DISCOUNT^TOT.CURRENT.OUTS.AMT^TOT.FUTURE.OUTS.AMT^OVERRIDE^RECORD.STATUS^CURR.NO^INPUTTER^DATE.TIME^AUTHORISER^CO.CODE^DEPT.CODE^AUDITOR.CODE^AUDIT.DATE.TIME^SOURCE.FUNDS^FM.INCOME^NO.INCOME.FAMIL^BUSINESS.INCOME^BUSINESS.COST^GROSS.PROFIT^EXPENSES^BUS.NET.INCOME^FM.NET.INCOME^TOTAL.NET.INCOM^CASH^DEPOSIT^ARTD^STOCK.IN.TRADE^TOTAL.CURRENT^FIXED.ASSETS^OTHER.ASSETS^ENT.TOT.ASSETS^TOT.RUN.DEBT^DEBT-OWNER^RETURN.TO.CAPIT^BI.PROFIT.MARGI^FLOW.RATIO^QUICK.RATIO^STOCK.TRADE.TUR^ACCT.REC.TURNOV^ACCT.PAYABLE.TU^ASSETS.LIABILIT^ADJ.ASSET.LIAB.^REP.AMOUT.MON^RAPY.AMT.BUS.IN^LOAN.LIMIT.AMT^EVALUATING.DATE^REPAY.ABILITY.R^LOAN.APPROVED^FAMILLY.INCOME^FAMILY.EXPENSES^EN.DEBT.IN.SHO^EN.DEBT.IN.LONG^FM.CASH^FAMILY.DEPOSIT^FM.ARTD^FM.CURRENT^FM.FIXED.ASSETS^FM.OTHER.ASSET^FM.TOT.ASSETS^FM.DEBT.SHORT^FM.DEBT.LONG^FM.TOT.DEBT^TOTAL.CASH^TOTAL.DEPOSIT^TOTAL.ARTD^TOTAL.CUR.ASS^TOTAL.FIXED.ASS^T.OTHER.ASSETS^T.TOTAL.ASSETS^T.DEBT.SHORT^T.DEBT.IN.LONG^T.TOTAL.DEBIT^TADJ.ASSET.LIAB^PRE.EVAL.DAO^FM.DEBT.OWNER^CUST.OTH.IMF^EMPL.NO^RSE.RISK.CATEG^REP.WILLINGNESS^HEALTH.STATUS^BUS.SUSTAINABIL^CAR.T.AMT^KIVA.CUSTOMER^Prepaid.Account^BANK.NAME^PRINCE.AMOUNT^END.Date^CUST.SECTOR^CUST.AGE^CUST.HOUSE^CUST.MARITAL^CUST.LOCAL^CUST.BUS.NAME^CO.CUST.NO1^CO.CUST.NO2^CO.CUST.NO3^CO.CUST.NO4^RISK.LEVEL^CREDIT.COMMITTE^NOTE^SPECIAL.APPROVA^OPENING.DATE^BUSINESS.OPENIN^CREDIT.RECORD^REP.ABILI.RATIO^ADJ.ASSET.RATIO^TADJ.ASSET.RATI^BUSINESS.CHANGE^BUS.ADD.CHANGES^BUS.ASSET.CHANG^BUS.LIAB.CHANGE^FAM.ASSET.CHANG^FAM.LIAB.CHANGE^CRE.REPORT.CHAN^FAM.ADD.CHANGES^MARITAL.CHANGES^BORROW.CHANGES^CUST.CO.NAME1^CUST.CO.NAME2^CUST.REG.NUM1^CUST.REG.NUM2^PREV.STAGE.4^TAX.BREAK^CA.RISK.TIP^DOCUMENT.INTEGR^LACK.OF.DOCUMEN^DOCUMENT.NAME^SP.SIGN.INFO^SPOUSE.INFO^REFERE.COMMENT1^REFERE.COMMENT2^CREDIT.LIMIT^CREDIT.EXPENDIT^ACCT.PAYABLES^CREDIT.RISK.INF^EXCEPTION.INFO^EXCEPTION^EXCEPTION.TYPE^EXCEPTION.COMME^CHANNEL.MODEL^BRANCH.APPROVER^AGENT^OTHER.FINANCIAL^CHANNEL.DISTRIB^DISTRIBUTOR.POS^INVESTIGAT.STAF^CHANNEL.CUST.NU^CUST.RISK.RATIN^TRANS.RISK.RATI^CREDIT.COMM.RIS^PROOF.REPAYMENT^CREDIT.APPROVAL^PRE.APPROVD.AMT^PRE.APRV.TERM^PRE.REPAY.TYPE^ST3.APPROVD.AMT^STG3.APRV.TERM^ST3.REPAY.TYPE^";
			writeToFile(localPath+fileName,(ArrayList) arrayList, "^", str, bankid,title);
			
		}else if("CUSTOMER".equals(dwhName))
		{
			String title="@ID^MNEMONIC^SHORT.NAME^NAME.1^NAME.2^STREET^ADDRESS^TOWN.COUNTRY^POST.CODE^COUNTRY^RELATION.CODE^REL.CUSTOMER^REVERS.REL.CODE^REL.DELIV.OPT^ROLE^ROLE.MORE.INFO^ROLE.NOTES^REL.RESERV6^REL.RESERV5^REL.RESERV4^REL.RESERV3^REL.RESERV2^REL.RESERV1^SECTOR^ACCOUNT.OFFICER^OTHER.OFFICER^INDUSTRY^TARGET^NATIONALITY^CUSTOMER.STATUS^RESIDENCE^CONTACT.DATE^INTRODUCER^TEXT^LEGAL.ID^LEGAL.DOC.NAME^LEGAL.HOLDER.NAME^LEGAL.ISS.AUTH^LEGAL.ISS.DATE^LEGAL.EXP.DATE^OFF.PHONE^REVIEW.FREQUENCY^BIRTH.INCORP.DATE^GLOBAL.CUSTOMER^CUSTOMER.LIABILITY^LANGUAGE^POSTING.RESTRICT^DISPO.OFFICER^COMPANY.BOOK^CONFID.TXT^DISPO.EXEMPT^ISSUE.CHEQUES^CLS.CPARTY^FX.COMM.GROUP.ID^RESIDENCE.REGION^ASSET.CLASS^CUSTOMER.RATING^CR.PROFILE.TYPE^CR.PROFILE^NO.UPDATE.CRM^TITLE^GIVEN.NAMES^FAMILY.NAME^GENDER^DATE.OF.BIRTH^MARITAL.STATUS^NO.OF.DEPENDENTS^PHONE.1^SMS.1^EMAIL.1^ADDR.LOCATION^EMPLOYMENT.STATUS^OCCUPATION^JOB.TITLE^EMPLOYERS.NAME^EMPLOYERS.ADD^EMPLOYERS.BUSS^EMPLOYMENT.START^CUSTOMER.CURRENCY^SALARY^ANNUAL.BONUS^SALARY.DATE.FREQ^NET.MONTHLY.IN^NET.MONTHLY.OUT^RESIDENCE.STATUS^RESIDENCE.TYPE^RESIDENCE.SINCE^RESIDENCE.VALUE^MORTGAGE.AMT^OTHER.FIN.REL^OTHER.FIN.INST^COMM.TYPE^PREF.CHANNEL^ALLOW.BULK.PROCESS^LEGAL.ID.DOC.NAME^INTERESTS^FAX.1^PREVIOUS.NAME^CHANGE.DATE^CHANGE.REASON^CUSTOMER.SINCE^CUSTOMER.TYPE^DATE.LAST.VERIFIED^SPOKEN.LANGUAGE^PASTIMES^FURTHER.DETAILS^DOMICILE^OTHER.NATIONALITY^CALC.RISK.CLASS^MANUAL.RISK.CLASS^OVERRIDE.REASON^TAX.ID^VIS.TYPE^VIS.COMMENT^VIS.INTERNAL.REVIEW^FORMER.VIS.TYPE^FORMER.VIS.COMMENT^RISK.ASSET.TYPE^RISK.LEVEL^RISK.TOLERANCE^RISK.FROM.DATE^LAST.KYC.REVIEW.DATE^AUTO.NEXT.KYC.REVIEW.DATE^MANUAL.NEXT.KYC.REVIEW.DATE^LAST.SUIT.REVIEW.DATE^AUTO.NEXT.SUIT.REVIEW.DATE^MANUAL.NEXT.SUIT.REVIEW.DATE^KYC.RELATIONSHIP^MANDATE.APPL^MANDATE.REG^MANDATE.RECORD^SECURE.MESSAGE^AML.CHECK^AML.RESULT^LAST.AML.RESULT.DATE^KYC.COMPLETE^INTERNET.BANKING.SERVICE^MOBILE.BANKING.SERVICE^REPORT.TEMPLATE^HOLDINGS.PIVOT^MERGED.TO^MERGED.STATUS^ALT.CUS.ID^EXTERN.SYS.ID^EXTERN.CUS.ID^OVERRIDE^RECORD.STATUS^CURR.NO^INPUTTER^DATE.TIME^AUTHORISER^CO.CODE^DEPT.CODE^AUDITOR.CODE^AUDIT.DATE.TIME^INITIALS^SUBURB.TOWN^CITY.MUNICIPAL^PROVINCE.STATE^POSTAL.CODE^PO.BOX.NO^PO.SUBURB.TOWN^PO.CITY.MUNICIP^PO.PROV.STATE^PO.POST.CODE^EMPLOY.BUS.ADDR^EMPLOY.SUB.TOWN^EMPL.CTY.MUNIC^EMPL.PROV.STATE^EMPL.POST.CODE^TEL.HOME^TEL.WORK^TEL.MOBILE^RESIDE.Y.N^E.MAIL.ADDRESS^EMPLOY.NAME^TAX.REG.NO^TAX.INVOICE^MAILING.LIST^BLOCKED^STAFF.OFFICIAL^NO.OF.DEPEND^EMPLOYEE.NO^DATE.OF.EMPLOY^FAX.NO^OTHER.ACCTS^BANK.BRANCH^SORT.CODE^DATE.AC.OPND^OPENING.DATE^CUST.TYPE^CORRESPOND.NAME^DATE.CURR.ADDR^FORMER.NAME^ID.TYPES^PROFESSION^SALARY.RANGE^CLASSIFICATION^CUST.STATUS^EMPLOY.STATUS^ENTITY.TYPE^MAIN.INCOME^SECOND.INCOME^BUS.START.DATE^LOC.RESERVED.1^UNALLOCATED.2^UNALLOCATED.3^UNALLOCATED.4^MEMO.OF.ASS^ART.OF.ASS^CERT.OF.INCORP^FOUNDING.STATE^CERT.TO.COMMENC^TRUST.DEED^PART.AGREEMENT^CONSTITUTION^NAME.CHANGE.CER^AUDITOR.NAME^AUDITOR.TEL^BUS.PRINCIPALS^ASS.ENTITIES^PRODUCT.SERVICE^POSIT.CON.NAME^CURR.ADDRESS^AGE^EXPIRY.DATE^SIGN.NAME^SIGN.ID.PASSP^TYPE.ID.PASSP^DATE.EXPIRY^MEMBER.SINCE^MEMBER.YEARS^SAVINGS.SINCE^SAVINGS.YEARS^EMPLOY.PERIOD^DUMMY.CUST^LEGAL.ID.NO^PARTNERS^X.DATE.OF.BIRTH^GENDER.GR^PARTNER.TEXT^BUSINESS.TYPE^CUST.LANGUAGE^RELATED.CUST^NON.IND.TEXT^SIGNATORY.CODE^SIGNATORY.ID^SIGNT.PPRT.NO.^SIGNT.PPRT.EXP^SIGNT.TEL.NO.^YRS.AT.CUR.ADDR^SAME.AS.RESADD^BUSINESS.ST.DT^CONTACT.NAME^CONTACT.POS^CONTACT.WORKTEL^CONTACT.HOMTEL^CONTACT.MOBTEL^CONTACT.EMAIL^CONTACT.FAX.NO^ALTER.ID.TYPE^ALTER.ID.NO^ACCOM.TYPE^PRV.PHY.ADD^PRV.SUBURB^PRV.CITY^PRV.STATE^PRV.POST.CODE^PRV.YRS.OCP^EMPLYRS.CODE^SP.NAME^SP.OCCUP^SP.EMP.NAME^SP.EMP.ADD^SP.EMP.SUB^SP.EMP.CITY^SP.EMP.STATE^SP.EMP.POST^MAIL.STMT^MAIL.LETTERS^MAIL.LABELS^MIN.BAL.55^MIN.BAL.60^MIN.BAL.65^MIN.BAL.70^CRDT.CHK^CRDT.IND^CONS.DISCLOSE^DATE.OF.SIG^PLACE.STUDY^DUR.COURSE^FIELD.STUDY^NOMINAL.FORM^INS.MEM.NO^INS.SURNAME^INS.ADDRESS^INS.SUBURB^INS.CITY^INS.STATE^INS.POS.CODE^INS.PHONE.NO^INS.REL.CODE^INS.AMT.LEGACY^BUS.NAME^BUS.NATURE^BUS.TYPE^BUS.PLAN^BUS.ROLE^BUS.ADD^BUS.SUBURB^BUC.CITY^BUS.STATE^C2.HELD^C2.EXP.DATE^AREA.CODE^NON.QUAL^STUDENT^PRV.ACC.TYPE^OFFR.TYPE^COPY.RESADD^SP.MEM.NO^SIGN.INSTRUC^DECEASED.DATE^LOANS.WOF^NO.OF.EMP^GROUP^CYCLE^External.Rating^SOURCE.TYPE^Advisor.Name^SPOUSE.ID^Spouse.Age^SPOUSE.DOB^Spouse.Gender^Marriage.Status^SPOUSE.LA^SPOUSE.EL^SPOUSE.WP^SPOUSE.WFLT^SPOUSE.LFLT^SPOUSE.MP^SP.OTHER.CONTAC^Spouse.CA^SP.POST.CODE^Spouse.ECBR^CLIENTABILITY^ENTSLN^Enterprise.Exp^Enterprise.Regi^Enterprise.Owne^BPSL^Competition.Pre^Operation.Expen^FTR^Env.Damage^ECE^CLIENT.ID.NUM^GUARANTOR.ID^CUST.ACTIVE^CLIENT.RATING^ARTIFICIAL.PERS^ACADEMIC.DEGREE^JOB^BUS.PHONE^BUS.OTHER.PHONE^INDUSTRY.EXP^Bus.Licence.No^INDUS.EXP.DATE^YRENT^DATE.ENT^Spouse.Res.Add^SPOUSE.NO^Ref.Name^Ref.Tel.Number^Ref.Relation^Refere.Identity^Ref.Comment^Ref.Comment.2^CUST.BRANCH.CO^NEXT.FEE^DISBURSE.BANK^DISBURSE.ACCT^REPAYMENT.BANK^REPAYMENT.ACCT^COUNTRY.CODE^MOBILE.PHONE^COPY.RES.ADD^BO.PHY.ADD^BO.SUBURB.TOWN^BO.CITY.MUNICIP^BO.PROV.STATE^BO.POST.CODE^BO.PROPERTY^SP.CRED.TYPE^LOC.AREA^ID.ADDRESS^SP.ID.ADD^ENV.LOC^MIGFLAG^MARITAL.STATUS1^LIVING.COND^CUST.PROF^JOB.POSITION^SAL.BANK.ACCT^SAL.ACCT^LEGACY.RATING^MIG.FLAG^PREV.DAO^DAO.CHG.DATE^DAO.CHG.RSN^LOC.CITY^LOC.LGA^LOC.TOWN^LOC.ZONE^RSE.EXP.H^RSE.EXP.EDU^RSE.GARBAGE^RSE.HYGIENE^RSE.ACCIDENT.NB^RSE.WATER.CONS^RSE.WATER.ADD^RSE.ENERGY.CONS^RSE.ENERGY.ADD^RSE.RISK^RSE.COMMENT^RSE.RISK.TYPE^AGE.IN.HOSE^BUSINESS.NAME^BUSN.ACTIVIY^LEGAL.STATUS^LEGAL.ACT^INCORP.NUMBER^AGE.IN.LOCATION^BUSINESS.ADDRES^BUZ.ZONE^BUZ.CITY^BUS.LGA^BUZ.STATE^LEGAL.REP.NAME^LEGAL.REP.FUNCT^SIGNATORYNAME^SIG.FINCTION^EMP.CITY^EMP.LGA^EMP.STATE^PHONECODE.1^MCI.EMPLOYEE^CUSTOMER.LEVEL^MRK.SURVEY^INFOR.KNOW^DCU.LEGAL.STATU^DCU.LEGAL.ACT^LOC.BIRTH^HOUSE.STATUS^STATE^LG.REP.NAM^LG.REP.FUNCT^LOAN.CYCLE^REFERRER.NO^REFERRER.NAME^REFERRER.PHONE^HOME.LAT^HOME.LNG^BUS.LAT^BUS.LNG^FRAUD.FLAG.IND^FRAUD.LAB.ID^FRAUD.LOAN.ID^FLAG.DATE^FRAUD.INF.OFF^FRAUD.TYPE^BL.FLAG.IND^BL.FLAG.DATE^BL.FLAG.INP^BL.FLAG.AUT^CO.NAME^REG.NUM^CORP.ID.NUM^WITHHOLD.INFO^CONF.TEL.NO^BIO.ENROL.STAT^BIO.ENROLL.FLAG^BIO.ENROLL.DATE^BIO.UPD.CHK^BIO.UPD.CHK.DTE^INITIATOR.ID^MESSAGE.TYPES^NOFIELD^PROC.CODE^STAN.ID^RETR.REF.NO^TERMINAL.ID^TERMINAL.LOC^AGENT.ACCT.NO^AGENT.ID^OPERATOR^PREFERRED.AGENT^INTERST.CUS.AC^INTERST.CUS.LD^INTERST.LN.AMT^INTERST.LN.TERM^LN.PURPOSE^COMMENT^PROSPECT.CITY^CUST.OTH.IMF^MORTGAGED.PROPE^BANK.SIGNED.Y.N^OCC.CREDIT^OLD.CU.ID^EMERGENCY.NAME^EMERGENCY.CONTA^EMERGENCY.PHONE^IND.CREDIT^POL.EXPO.PERSON^SANCTION.LIST^AMT.RISK.LEVEL^NO.OF.KIDS^PHONE.TYPE^INCL.LEVEL^INCOME.FREQ^INCOME.GRP^RESIDE.PROOF^PAYMENT.DATE^ACTOR.ID^ENTER.GUARANTOR^ARCHIVES.STATUS^LITIGATION.PROG^PROCEEDINGS.DAT^LITIGATION.PRO1^PROCEEDINGS.DA1^CHANNEL.MODEL^BRANCH.APPROVER^AGENT^OTHER.FINANCIAL^CHANNEL.DISTRIB^DISTRIBUTOR.POS^INVESTIGAT.STAF^OPT.OUT.TAKA^OPT.OUT.IND^Channel.Cus.Y.N^ENTERPRISE.ADDR^ENTERPRISE.PHON^COVID.IMPACT^COVID.IMPACT.DA^COVID.IMPACT1^COVID.IMPACT3^COVID.IMPACT.D3^COVID.IMPACT4^COVID.IMPACT.D4^COVID.IMPACT5^COVID.IMPACT.D5^COVID.IMPACT6^COVID.IMPACT.D6^COVID.IMPACT7^COVID.IMPACT.D7^";
			writeToFile(localPath+fileName,(ArrayList) arrayList, "^", str, bankid,title);
			
		}else 
		{
//			PrintMgrFactory pmf = PrintMgrFactory.getFactory();
//			System.out.println("开始写入");
			writeToFileByOrder((ArrayList) arrayList, "^", str, localPath+fileName);
		}
		
		} catch (Exception e) {
			BRC.getLog().info("生成本地文件失败"+fileName);
			BRC.getLog().error(e);
			throw e;
		}
		String ftpName=localPath+fileName;
		if("tfe_mcr.loan.wof_mcnc_ups".equals(dwhName)) 
		{
			BRC.getLog().info("压缩文件开始");
			String zipName= fileName.substring(0, fileName.length() - 5)+".zip";
			zip(localPath+fileName,localPath+zipName);
			ftpName=localPath+zipName;
			BRC.getLog().info("压缩文件结束");
		}
		String fileUrl="";
		//文件上传
		FileSystemResp resp = new FileSystemResp();
		String repsCode="";
		String repsDesc="";
//		try {
//			resp = IfmsTool.uploadFileOtherBucket(ftpName, uploadPath + workdate,bucketname,"cn-north-1");
//			repsCode=resp.getRespcode();
//			repsDesc=resp.getRespdesc();
//			if(!"0000".equals(repsCode)){
//				throw new Exception("文件上传失败,原因：" +repsDesc);
//			}
//			fileUrl=resp.getKey();
//			BRC.getLog().info("文件上传成功！ url："+fileUrl);
//		} catch (Exception e) {
//			BRC.getLog().info("文件上传失败，原因："+repsDesc);
//			BRC.getLog().error(e);
//			throw e;
//		}
		//记录上传流水
		Session session= daoTemplate.getSession();
		insertLog(session, bankid, workdate,dwhName, fileName, fileUrl);

	}
	
	/**
	 * 生成csv文件并上传
	 * 
	 * @param Sql
	 * @param orderHeader
	 * @param dwhName
	 * @param fileName
	 * @param localPath
	 * @param uploadPath
	 * @param workdate
	 * @param daoTemplate
	 * @param bankid
	 * @param csvFormat
	 * @param bucketname
	 * @version v1.0 yyp
	 */
	private void sendDataHouseCsv(String Sql, String[] orderHeader,String dwhName, String fileName, String localPath, String uploadPath, String workdate,DaoTemplate daoTemplate,long bankid,CSVFormat csvFormat,String bucketname) throws Exception 
	{
		CSVPrinter csvPrinter = null;
		//生成本地文件
		try {
			
			 FileOutputStream fos = new FileOutputStream(localPath+fileName);
		     OutputStreamWriter osw = new OutputStreamWriter(fos, "GBK");					
		        BRC.getLog().info("filePath is:" +localPath+fileName);
					
				csvPrinter = new CSVPrinter(osw, csvFormat);					
				List<Map> listContent =(List)daoTemplate.getMapListBySql(Sql);
				for (Map map1 : listContent) {						
					Collection finalStr = null;
					LinkedHashMap<String, Object> printMap = new LinkedHashMap<String, Object>();
					if (orderHeader != null && orderHeader.length > 0) {
						for (String orderStr : orderHeader) {
							printMap.put(orderStr, map1.get(orderStr));
						}
						finalStr = printMap.values();
					} else {
						finalStr = map1.values();
					}
					csvPrinter.printRecord(finalStr);
				}
				BRC.getLog().info("CSV文件生成完毕");
			} catch (Exception e) {
				BRC.getLog().info("CSV文件生成失败!"+localPath+fileName);
				BRC.getLog().error(e);
				throw e;

			} finally {
				try {// 清空并关闭csvPrinter
					if (csvPrinter != null) {
						csvPrinter.flush();
						csvPrinter.close();
					}
				} catch (IOException e) {
					BRC.getLog().error("关闭csvPrinter异常", e);
					throw new Exception("关闭csvPrinter异常");
				}
			}
		
		String fileUrl="";
		//文件上传
		FileSystemResp resp = new FileSystemResp();
		String repsCode="";
		String repsDesc="";
//		try {
//
//			resp = IfmsTool.uploadFileOtherBucket(localPath+fileName, uploadPath + workdate,bucketname,"cn-north-1");
//			repsCode=resp.getRespcode();
//			repsDesc=resp.getRespdesc();
//			if(!"0000".equals(repsCode)){
//				throw new Exception("文件上传失败,原因：" +repsDesc);
//			}
//			fileUrl=resp.getKey();
//			BRC.getLog().info("文件上传成功！ url："+fileUrl);
//		} catch (Exception e) {
//			BRC.getLog().info("文件上传失败，原因："+repsDesc);
//			BRC.getLog().error(e);
//			throw e;
//		}
		//记录上传流水
		Session session= daoTemplate.getSession();
		insertLog(session, bankid, workdate,dwhName, fileName, fileUrl);

	}
	
	/**
	 * 生成文件成功后记录日志
	 * 
	 * @param session
	 * @param bankid
	 * @param workdate
	 * @param dwhName
	 * @param fileName
	 * @param fileUrl
	 * @version v1.0 yyp
	 * 	 */
	private void insertLog(Session session, Long bankid, String workdate, String dwhName,String fileName, 
			String fileUrl) {
		BRC.getLog().info("开始记录流水");
		String serid = SnowFlakeFactory.getInstance().nextId() + "";
		DataWarehouseRecordDao dao = new DataWarehouseRecordDao();
		dao.setExternalSession(session);
		DataWarehouseRecord record = new DataWarehouseRecord();
		record.setSerid(serid);
		record.setBankid(bankid);
		record.setDwhname(dwhName);
		record.setFilename(fileName);
		record.setTxdate(workdate);
		record.setFileurl(fileUrl);
		dao.insert(record);
		BRC.getLog().info(dwhName+"文件记录完毕");
	}
	
	public void zip(String filePath, String zipPath) throws IOException{

		File file = null;
		FileInputStream fis = null;
		BufferedInputStream bin = null;
		DataInputStream dis = null;

		File zipfile = null;
		FileOutputStream fos = null;
		BufferedOutputStream bos = null;
		ZipOutputStream zos = null;
		ZipEntry ze = null;
		try {
			file = new File(filePath);
			fis = new FileInputStream(file);
			bin = new BufferedInputStream(fis);
			dis = new DataInputStream(bin); 

			zipfile = new File(zipPath);
			BRC.getLog().info(zipPath);
			//存在压缩文件，则删除，重新压缩
			
			if(zipfile.exists()) 
			{
				zipfile.delete();
			}
			
			fos = new FileOutputStream(zipfile); 
			bos = new BufferedOutputStream(fos, 1024); //the buffer size
			zos = new ZipOutputStream(bos); //压缩输出流
			ze = new ZipEntry(file.getName()); //实体ZipEntry保存
			zos.putNextEntry(ze);
			int len = 0;//临时文件
			byte[] bts = new byte[1024]; //读取缓冲
			while((len=dis.read(bts)) != -1){ //每次读取1024个字节
				zos.write(bts, 0, len); 
			}
			BRC.getLog().info("压缩成功");
			zos.closeEntry();
			zos.close();
			bos.close();
			fos.close();
			dis.close();
			bin.close();
			fis.close();
			//压缩后删除原文件
//			file.delete();
		} catch (Exception e) {
			logger.error("", e);
			zos.closeEntry();
			zos.close();
			bos.close();
			fos.close();
			dis.close();
			bin.close();
			fis.close();
		} 
	}

	  public void writeToFileByOrder(ArrayList alMsg,  String delimiter, String[] order,String fileName)
			    throws Exception
			  {
			    
			    File file = new File(fileName);
			    OutputStreamWriter ots = null;
			    BufferedWriter bw = null;
			    alMsg = changeToRequireByOrder(alMsg, delimiter, order);
			    
			    try {
			      ots = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
			      bw = new BufferedWriter(ots);
			      for (int i = 0; i < alMsg.size(); i++) {
			        bw.write((String)alMsg.get(i));
			        bw.write("\r");
//			        bw.newLine();
			      }
			    }
			    catch (IOException e) {
			      logger.error("", e);
			      throw new Exception(e);
			    } finally {
			      try {
			        bw.flush();
			        bw.close();
			        ots.close();
			      } catch (Exception e) {
			        logger.error("", e);
			      }
			    }

			    
			  }
	  
	  public void writeToFile(String fileName, ArrayList alMsg, String delimiter, String[] order, long bankid,String titel)
			    throws Exception
			  {
			    
			    File file = new File(fileName);
			    OutputStreamWriter ots = null;
			    BufferedWriter bw = null;
			    OutputStreamWriter fots = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
			    BufferedWriter fbw = new BufferedWriter(fots);
			    fbw.write(titel);
			    fbw.newLine();
			    fbw.flush();
			    fbw.close();
			    fots.close();
			    alMsg = changeToRequireByOrder(alMsg, delimiter, order);
			    
			    try {
			      ots = new OutputStreamWriter(new FileOutputStream(file,true), "UTF-8");
			      bw = new BufferedWriter(ots);
			      for (int i = 0; i < alMsg.size(); i++) {
			        bw.write((String)alMsg.get(i));
			        bw.newLine();
			      }
			    }
			    catch (IOException e) {
			      logger.error("", e);
			      throw new Exception(e);
			    } finally {
			      try {
			        bw.flush();
			        bw.close();
			        ots.close();
			      } catch (Exception e) {
			        logger.error("", e);
			      }
			    }

			    
			  }
	  
	  public ArrayList<String> changeToRequireByOrder(ArrayList arrList, String delimiter, String[] order)
	  {
	    ArrayList returnArr = new ArrayList();
	    for (int i = 0; i < arrList.size(); i++) {
	      String returnStr = "";
	      HashMap hm = (HashMap)arrList.get(i);
	      for (int h = 0; h < order.length; h++) {
	        String keyName = order[h];
	        Object obj = hm.get(keyName);
	        String value = obj == null ? "" : String.valueOf(obj);
	        returnStr = returnStr + value + delimiter;
	      }
	      returnStr = returnStr.substring(0, returnStr.length() - 1);
	      returnArr.add(returnStr);
	    }
	    return returnArr;
	  }
	
	
	
}

package tf.ifms.rpc.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.hibernate.Session;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;
import tf.tools.StringTools;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Describe：H5  H5消息中心相关接口
 */
public class MsgCenterForH5 {
    private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());

    /**
     * 获取消息列表
     * @param reqTfData
     * @param session
     * @return
     * @throws Exception
     */
    public RespTfData getList(ReqTfData reqTfData, Session session) throws Exception {
        RespTfData respTfData = new RespTfData();
        try {
            log.info("参数信息：" + reqTfData);

            DaoTemplate daoTemplate = new DaoTemplateImpl();
            daoTemplate.setExternalSession(session);

            // 获取前台参数
            String cifid = (String) reqTfData.getReqEntity("cifid");// 纠纷类型编号
            Pager pager = (Pager) reqTfData.getReqEntity("pager");// 分页信息
            String custId =String.valueOf(daoTemplate.getUniqueValueBySql("select cust_id from cust_info where cifid='"+cifid+"' limit 1 "));
            Map<String, Object> whereMap = new HashMap();
            whereMap.put("#SQL#", " 1=1 order by sendtime desc ");
            // 声明返回集
            List<Map> list = daoTemplate.getMapListBySql("select * from cust_msg_info where template_id=(select template from cn_wechat_push where msgno ='40') and phone='" + custId + "' ",whereMap,pager);
            if(null!=list&&list.size()>0){
                for (int i = 0; i < list.size(); i++) {
                    Map map = list.get(i);
                    String templateId=String.valueOf(map.get("templateId"));
                    String querysql="select msgname from cn_wechat_push where template ='"+templateId+"' ";
                    /*if("AfterContSendWechatMsg".equals(templateId)){
                        querysql="select msgname from cn_wechat_push where msgno ='4' ";
                    }else if("SimplifyLoadByOrganizationManager".equals(templateId)){
                        querysql="select msgname from cn_wechat_push where msgno ='10'";
                    }else if("sendCreditTips".equals(templateId)){
                        querysql="select msgname from cn_wechat_push where msgno ='40'";
                    }*/
                    String msgInfoDesc=String.valueOf(daoTemplate.getUniqueValueBySql(querysql));
                    String time=String.valueOf(map.get("sendtime"));
                    map.put("msgInfoDesc",StringTools.isNotNull(msgInfoDesc)?msgInfoDesc:"");
                    if(StringTools.isNotNull(time)){
                        map.put("sendtime",time.substring(0,4)+"年"+time.substring(4,6)+"月"+time.substring(6,8)+"日"+time.substring(8,10)+"时"+time.substring(10,12)+"分"+time.substring(12,14)+"秒");
                    }
                }
            }

            respTfData.setRespEntity("pager", pager);
            respTfData.setRespCode("0000");
            respTfData.setRespDesc("查询站内信列表成功！");
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("查询站内信列表失败！");
        }
        return respTfData;
    }
    /**
     * 修改消息状态
     */
    public RespTfData updateSts(ReqTfData reqTfData, Session session) throws Exception {
        RespTfData respTfData = new RespTfData();
        try {
            log.info("参数信息：" + reqTfData);

            DaoTemplate daoTemplate = new DaoTemplateImpl();
            daoTemplate.setExternalSession(session);

            // 获取前台参数
            JSONArray seridList= (JSONArray)reqTfData.getReqEntity("seridList");
            String workDate = (String) reqTfData.getReqEntity("workdate");
            String reqtime = (String) reqTfData.getReqEntity("reqtime");
            String readtime=workDate+" "+reqtime;
            if(null!=seridList&&seridList.size()>0){
                for (int i = 0; i < seridList.size(); i++) {
                    Map<String, String> map = (Map<String, String>)seridList.get(i);
                    log.info("------------站内信阅读-----------数据获取"+map);
                    String serid = map.get("serid");// 消息序号
                    String cnt=String.valueOf(daoTemplate.getUniqueValueBySql("select count(*) from cust_msg_info where serid = '"+serid+"' and ifnull(readtime,'')='' "));
                    if(Integer.parseInt(cnt)==0){
                        log.info("------------站内信阅读-----------不存在的数据"+serid);
                    }
                    daoTemplate.executeSql("update cust_msg_info set readtime='"+readtime+"' where serid ='"+serid+"' ");
                }
            }
            respTfData.setRespCode("0000");
            respTfData.setRespDesc("站内信阅读状态修改成功！");
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("站内信阅读状态修改失败！");
        }
        return respTfData;
    }
}

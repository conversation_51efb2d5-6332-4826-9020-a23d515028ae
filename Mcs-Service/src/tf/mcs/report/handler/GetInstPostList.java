package tf.mcs.report.handler;

import java.util.ArrayList;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;

public class GetInstPostList extends HibernateDaoSupport implements
		CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		BRC.getLog().info("#############进入GetInstPostList类#############");
		//session
		DaoTemplateImpl daoTemplate=(DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		// 公共域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String workDate = cdbt.getWorkDate();
		String workTime = cdbt.getReqTime();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		// 业务处理
		String mainSql="select * from " +
				"(" +
				"select t1.instcode,t1.instname,case t1.sinstcode when 'root' then 'root' else t1.sinstcode end as high_lvl,t1.bankid,'BAK' as bps from cn_basicinfo t1 where t1.bankid='"+bankid+"'" +
				"union all " +
				"select concat(t2.instcode,t2.postid) as instcode,t2.postname as instname,t2.instcode as high_lvl,t2.bankid,'POS' bps from cn_post t2 where t2.bankid='"+bankid+"'" +
				") a " +
				"where 1=1 order by a.instcode";
		//BRC.getLog().info(mainSql);
		ArrayList list=(ArrayList) daoTemplate.getMapListBySql(mainSql);
		for(int i=0;i<list.size();i++){
			System.out.println(list.get(i));
		}
		RespDataBusData rdbd=new RespDataBusData();
		rdbd.setRespEntity("list", list);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取机构岗位GetInstPostList执行成功！");
		return rdbd;
	}
}
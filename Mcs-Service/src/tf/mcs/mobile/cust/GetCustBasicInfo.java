package tf.mcs.mobile.cust;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.IndBaseDao;
import tf.mcs.model.IndBase;
/**
 * description:
 * <p>
 * 客户信息详情：基本概况
 * </p>
 * <AUTHOR>
 * @version v1.0
 */
public class GetCustBasicInfo extends HibernateDaoSupport implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0,RespDataBusData arg1) throws Exception {

		BRC.getLog().info(arg0);
		//session
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		
		IndBaseDao indBaseDao=new IndBaseDao();
		indBaseDao.setExternalSession(externalSession);
		//daoTemplate
		DaoTemplateImpl daoTemplate=(DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Map<String,String> templateMap = new HashMap<String,String>();
		//dataBus
		RespDataBusData rdbd=new RespDataBusData();
		//公共参数获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String workDate = cdbt.getWorkDate();
		String workTime = cdbt.getReqTime();
		String brno = cdbt.getOpBr();
		String operid=cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		//获取前台参数
		HashMap reqMap=(HashMap) arg0.get("hashMap");
		String cifid=tf.mcs.Tools.AppendSql(String.valueOf(reqMap.get("cifid")));
		//业务处理
		String mainSql="SELECT t1.main_right,t1.apply_right,t1.see_right " +
				"FROM cust_permission t1 " +
				"WHERE t1.cifid='"+tf.mcs.Tools.AppendSql(cifid)+"' AND t1.operid='"+tf.mcs.Tools.AppendSql(operid)+"' " +
						"AND t1.brno='"+tf.mcs.Tools.AppendSql(brno)+"' AND t1.bankid='"+tf.mcs.Tools.AppendSql(String.valueOf(bankid))+"'";
		HashMap sqlMap=(HashMap) daoTemplate.getUniqueValueBySql(mainSql);
		
		if(sqlMap!=null&&(!"0".equals(sqlMap.get("seeRight")))){
			IndBase indBase=new IndBase();
			indBase.setCifid(cifid);
			indBase.setBankid(bankid);
			indBase=indBaseDao.getEntityByPK(indBase);
			
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("获取客户基本概况信息执行成功！");
			rdbd.setRespEntity("indBase", indBase);
			return rdbd;
		}else{
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("对不起，您的权限不足！");
			return rdbd;
		}
	}
}
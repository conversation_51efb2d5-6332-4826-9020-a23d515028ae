package tf.mcs.assetsafe.handler;

import java.util.HashMap;
import java.util.Map;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.dao.AcBusinesscontDao;
import tf.mcs.model.AcBusinesscont;
import tf.mcs.model.CnProductdefine;
import tf.mcs.model.CnUser;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * @Describe：获取落实资产保全任务信息列表
 * @ClassName: GetAssetsafeMissionFixList.java
 * <AUTHOR>
 * @version V1.0
 * @Date 2015-8-28 下午2:23:30
 * @Company tfrunning
 */

public class GetAssetsafeMissionFixList implements CommonHandlerStub {
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		// 初始化
		UniversalObject uo = new UniversalObject(arg0);
		BRC.getLog().info(arg0);
		BRC.getLog().info("################进入GetAssetsafeMissionFixList类#################传入参数为：" + arg0);
		RespDataBusData rdbd = new RespDataBusData();	
		//获取session
		AcBusinesscontDao acBusinesscontDao = new AcBusinesscontDao();
		acBusinesscontDao.setExternalSession(uo.getSession());
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		try {
			// 接收前台参数
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();
			String brno = cdbt.getOpBr();
			String operid = cdbt.getOpTel();
			String workDate = cdbt.getWorkDate();
			Pager pager = (Pager) arg0.get("pager");
			@SuppressWarnings("unchecked")
			Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
			whereMap.put("#SQL#", "1=1 ORDER BY t1.contno,t1.cifid,t1.cliname DESC");
			String getSql = "select t1.*  from assetsafe_mission t1 ,de_flowrecord t2 where t1.bankid='" + bankid
					+ "' and t1.bankid=t2.bankid and t1.applyno=t2.applyno and t2.operid='" + operid + "' and t1.handle_result!='20' "
					+ "and (t2.phaseno like '61%' ) and t2.isrtn='0'   and nvl(t2.enddate,null) is null";
			daoTemplate.getMapListByLikeSql(getSql, whereMap, pager);		
			//获取台账链接页面所需要的相关参数
			for (int i = 0; i < pager.getList().size(); i++) {
				HashMap<String,String> hMap = (HashMap<String,String>) pager.getList().get(i);
				hMap.put("iptInstcode", "["+brno+"]"+tf.mcs.Tools.getSessionInstname(bankid,brno));//登记机构名称
				hMap.put("iptUsr", "["+operid+"]"+tf.mcs.Tools.getSessionOpername(bankid,operid));//登记人名称
				hMap.put("iptTime",workDate);//获取当前Web中时间
				String contno = hMap.get("contno");
				CnProductdefine cnProductdefine = tf.mcs.Tools.getProductdefine(bankid,(String)hMap.get("prdtNo"));
				AcBusinesscont acBusinesscont = new AcBusinesscont();
				acBusinesscont.setBankid(bankid);
				acBusinesscont.setContno(contno);
				acBusinesscont = acBusinesscontDao.getEntityByPK(acBusinesscont);
				if(cnProductdefine!=null){
					hMap.put("afbcTreeId", cnProductdefine.getAfbcTreeId());
					hMap.put("aplyTreeId", cnProductdefine.getAplyTreeId());
					hMap.put("surveyTreeId", cnProductdefine.getSurveyTreeId());
					hMap.put("afmTreeId", cnProductdefine.getAfmTreeId());
					hMap.put("afTreeId", cnProductdefine.getAfTreeId());
					hMap.put("prdtNo", cnProductdefine.getPrdtNo());
					hMap.put("loanApplyno", acBusinesscont.getApplyno());
					// added by 邓聪 2015-10-23  增加返回业务类型字段，用于前台页面识别最高额用信
					hMap.put("bustype", acBusinesscont.getBustype());
					// added end by 邓聪 2015-10-23
				}
			}
			rdbd.setRespEntity("pager", pager);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("获取资产保全任务信息列表信息GetAssetsafeMissionList执行成功!");
			return rdbd;
		} catch (Exception e) {
			StringTools.stackLog(e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取资产保全任务信息列表信息GetAssetsafeMissionList执行失败!");
			return rdbd;
		}
	}
}

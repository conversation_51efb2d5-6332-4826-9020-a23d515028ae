package tf.mcs.assetsafe.handler;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.mcs.dao.AssetsafeMissionDao;
import tf.mcs.model.AssetsafeMission;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * 描述 更新处置有效期维护信息
 * 
 * <AUTHOR>
 * @company tfrunning
 * @date ********
 * @version v1.0
 */
public class UpdataAccountInfo implements CommonHandlerStub{
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info("--------------进入UpdataAccountInfo类--------------传入参数为："+arg0);
		//获取session
	    UniversalObject uo = new UniversalObject(arg0);
		Session session = uo.getSession();
		AssetsafeMissionDao assetsafeMissionDao = new AssetsafeMissionDao();
		assetsafeMissionDao.setExternalSession(session);
		RespDataBusData rdbd = new RespDataBusData();
		// 公共域获取
		long bankid = uo.getBankid();//法人机构id
		// 前台参数获取
		try {
			AssetsafeMission assetsafeMission = (AssetsafeMission) arg0.get("assetsafeMission");//前台传入实体
			AssetsafeMission oldAssetsafeMission = new AssetsafeMission();
			assetsafeMission.setBankid(bankid);	
			oldAssetsafeMission = assetsafeMissionDao.getEntityByPK(assetsafeMission);		
			tf.core.utils.func.DomainHelper.copyProperty(assetsafeMission,oldAssetsafeMission);
			assetsafeMissionDao.update(oldAssetsafeMission);
			arg1.setRespEntity("assetsafeMission", oldAssetsafeMission);							
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("修改保全台账表信息UpdataAccountInfo执行成功!");
			return rdbd;
			} catch (Exception e) {
					StringTools.stackLog(e);
					rdbd.setRespCode("9999");
					rdbd.setRespDesc("修改保全台账表信息UpdataAccountInfo执行失败!");
					return rdbd;
				}
			}
}

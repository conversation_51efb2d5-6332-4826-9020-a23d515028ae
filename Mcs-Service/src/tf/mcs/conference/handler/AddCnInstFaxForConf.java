package tf.mcs.conference.handler;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.mcs.dao.CnBasicinfoDao;
import tf.mcs.dao.CnInstFaxDao;
import tf.mcs.dao.PcLnvatratioDao;
import tf.mcs.model.CnBasicinfo;
import tf.mcs.model.CnInstFax;
import tf.mcs.model.PcLnvatratio;
/**
 * 描述  机构税率配置维护增加
 * <AUTHOR>
 * @date 20200901
 */
public class AddCnInstFaxForConf  extends HibernateDaoSupport implements CommonHandlerStub{
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		
		try {
			//获取公共数据连接相关信息
			BRC.getLog().info(arg0);
			DBPoolFactory dbpf = arg0.getReqDBPoolservice();
			Session externalSession = dbpf.getTransSession();
			CnBasicinfoDao cnBasicinfoDao = new CnBasicinfoDao();
			cnBasicinfoDao.setExternalSession(externalSession);
			CnInstFaxDao cifDao = new CnInstFaxDao();
			cifDao.setExternalSession(externalSession);
			PcLnvatratioDao pcDao = new PcLnvatratioDao(); 
			pcDao.setExternalSession(externalSession);
			//获取请求参数
			CnInstFax cnInstFax = (CnInstFax)arg0.get("cnInstFax");
			HashMap hmap = (HashMap) arg0.get("hashMap");
			String relflg = (String) hmap.get("relflg");//获取前天是否有免税政策
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();
			String workdate = cdbt.getWorkDate();
			//传入信息校验
			if(null == cnInstFax.getInstcode() || "".equals(cnInstFax.getInstcode()) || "null".equals(cnInstFax.getInstcode())) {
				arg1.setRespCode("9999");
				arg1.setRespDesc("请指定对应机构");
				return arg1;
			}
			if(null == cnInstFax.getFarmFax() || "".equals(cnInstFax.getFarmFax()) || "null".equals(cnInstFax.getFarmFax())) {
				arg1.setRespCode("9999");
				arg1.setRespDesc("农户贷款税率不可为空");
				return arg1;
			}
			if(null == cnInstFax.getUnfarmFax() || "".equals(cnInstFax.getUnfarmFax()) || "null".equals(cnInstFax.getUnfarmFax())) {
				arg1.setRespCode("9999");
				arg1.setRespDesc("非农户贷款税率不可为空");
				return arg1;
			}
			String s[] = cnInstFax.getInstcode().split("\\[");
			if(s.length>1) {
				String k[] = s[1].split("]");
				cnInstFax.setInstcode(k[0]);
			}
			
			CnBasicinfo cnBasicinfo = new CnBasicinfo();
			cnBasicinfo.setBankid(bankid);
			cnBasicinfo.setInstcode(cnInstFax.getInstcode());
			cnBasicinfo = cnBasicinfoDao.getEntityByPK(cnBasicinfo);
			
			CnInstFax cnInstFaxOld = cifDao.getEntityByPK(cnInstFax);
			cnInstFax.setInstname(cnBasicinfo.getInstname());
			if (cnInstFaxOld != null) {
//				cifDao.update(cnInstFax);
				arg1.setRespCode("9999");
				arg1.setRespDesc("该机构已存在增值税率信息，不可新增！");
				return arg1;
			}else {
				cifDao.insert(cnInstFax);
			}
			
	
			PcLnvatratio pc = new PcLnvatratio();
			pc.setBankid(bankid);
			pc.setInstcode(cnInstFax.getInstcode());
			pc.setBdate(workdate);
			PcLnvatratio pcOld = pcDao.getEntityByPK(pc);
			pc.setVatratio(cnInstFax.getUnfarmFax());
			pc.setActflg("1");
			pc.setEdate("********");
			pc.setRelflg(relflg);//添加是否有免税政策  yanghaocheng ********
			if (pcOld != null) {
				pcDao.update(pc);
			}else {
				pcDao.insert(pc);
			}
			
			arg1.setRespCode("0000");
			arg1.setRespDesc("上传成功");
			return arg1;
		}catch(Exception e) {
			log.error(e.getCause(),e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("添加失败");
			return arg1;
		}
	}
	
}

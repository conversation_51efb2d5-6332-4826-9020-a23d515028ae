package tf.mcs.agre.apply.handle;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;

/**
 * @file:GetCancelAgreApp.java
 * @author: sunziao
 * @date:2014-11-28
 * @company: tfrunning
 * @version v1.0
 */

public class GetCancelAgreApp extends HibernateDaoSupport implements
		CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		// 获取session
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0
				.getReqDBPoolservice().getDaoTemplate();
		// 公共域获取
		RespDataBusData rdbd = new RespDataBusData();
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 业务处理
		whereMap.put("#SQL#", " 1=1 order by t1.applyno desc");
		try {
			String mainSql = "select t1.*,t3.agre_highsum from app_main_info t1,de_flowrecord t2,app_bus_info t3 where t1.bankid='"
					+ bankid
					+ "' and t1.bankid=t2.bankid and t1.applyno=t2.applyno and t1.bankid=t3.bankid and t1.applyno=t3.applyno  and t1.app_usr='"
					+ operid
					+ "' and nvl(t2.enddate,null) is not null and t2.phaseno like '0%'  and t2.phasechoice='deny' and t2.isrtn='0' and t1.bustype='30'  and t1.prdt_no like 'C2%'";
			daoTemplate.getMapListByLikeSql(mainSql, whereMap, pager);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取批量贷协议主动取消申请信息GetCancelAgreApp执行失败!");
			return rdbd;
		}
		for (int i = 0; i < pager.getList().size(); i++) {
			HashMap hMap = (HashMap) pager.getList().get(i);
			hMap.put("prdtNa", "[" + (String) hMap.get("prdtNo") + "]"
					+ tf.mcs.Tools.getProductName(bankid,(String) hMap.get("prdtNo")));
		}
		
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取批量贷协议主动取消申请信息GetCancelAgreApp执行成功!");
		return rdbd;
	}
}

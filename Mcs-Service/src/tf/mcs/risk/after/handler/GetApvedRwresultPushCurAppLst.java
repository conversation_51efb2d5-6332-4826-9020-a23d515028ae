package tf.mcs.risk.after.handler;

import java.util.Map;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
/**
 * 获取已审批的风险预警信息列表
 * <AUTHOR>
 * @date 2017-5-11
 * @ClassName GetApvedRwresultPushCurAppLst.java
 * @Company tfrunning
 *
 */
public class GetApvedRwresultPushCurAppLst implements
CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		// 获取前台参数
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		// 公用域获取
		CommonDBT cdbt = arg0.getCommDC();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		whereMap.put("#SQL#", "1=1 order by t1.misn_no");
		RespDataBusData rdbd = new RespDataBusData();
		try {
			
			String getSql="select t1.* from rwresult_push_cur_app t1  where  exists (select 1 from de_flowrecord t4 where  t1.misn_no=t4.applyno and  t1.bankid=t4.bankid  and t4.operid='"
									+ operid+ "' and t4.enddate is not null and t4.isrtn='0' and t4.phaseno not like '0%')  ";
			daoTemplate.getMapListByLikeSql(getSql, whereMap, pager);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取风险预警检查执行已完成列表GetApvedRwresultPushCurAppLst执行失败!");
			return rdbd;
		}
		
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取风险预警检查执行已完成列表GetApvedRwresultPushCurAppLst执行成功!");
		return rdbd;
	}

}

package tf.mcs.posting;

import java.util.HashMap;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import tf.cbs.brc.io.FilesTools;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;

import tf.mcs.eod.handler.EodBusinesscont;
import tf.mcs.eod.handler.EodBusinessvch;
import tf.tools.StringTools;


/**
 * <AUTHOR>
 * @function 铁岭过旧账
 */
public class TLposting {
	public static final String oriDBName = "tlmcs";
	public static final String filename = "D:\\TL过数\\initAccount.sql";
	public static final long bankid = 100000;
	public static final String txdate = "********";
	
	public void deal(HashMap hm) throws Exception{
		
		// 生成流水号
		Long bankid = Long.valueOf(hm.get("bankid").toString());
		String txdate = hm.get("txdate").toString();
		Session externalSession= (Session) hm.get("externalSession");

		DaoTemplate daoTemplate = new DaoTemplateImpl();
		daoTemplate.setExternalSession(externalSession);
		
		// 插入明细完成
		PostingSql postingSql = new PostingSql();
		postingSql.importDatabase(bankid, txdate, daoTemplate, oriDBName,filename);
		postingSql.fillInMstHstForLnlo(bankid,txdate, daoTemplate);
		postingSql.fillInMstHstForRepayday(bankid,txdate, daoTemplate);
		
		

		BRC.getLog().info("更新分户ln_mst中的in_lo_intst");
		String updateLnInLoIntstSql = "update ln_mst set in_lo_intst = in_normal_intst + in_over_intst+in_cmpd_intst";
		daoTemplate.executeSql(updateLnInLoIntstSql);
		

		BRC.getLog().info("-----------------插入明细结束-----------------pass-----------------");
	}
	
	public static void main(String[] args) {
		ClassPathXmlApplicationContext applicationContext= new ClassPathXmlApplicationContext("applicationContext.xml");
		applicationContext.start();
		BRC.setBrcContext(applicationContext);
		BRC.setRunMode(1);
		// 初始化操作
		BRC.getLog().debug("开始日终");
		String sAppHome = System.getProperty("user.dir");
		System.out.println("\n 当前路径是:" + sAppHome);
		
		BRC.setProperty("APPHOME", sAppHome);
		java.io.File cf = FilesTools.findFile("brc.xml");
		if (cf != null) {
			BRC.init(cf);
		} else {
		}

		SessionFactory sessionFactory1 = (SessionFactory) BRC.getBrcContext().getBean("sf");
		Session psessCheck1=sessionFactory1.openSession();
		
		psessCheck1.beginTransaction();
		DaoTemplate daoTemplate = new DaoTemplateImpl();
		daoTemplate.setExternalSession(psessCheck1);
		try {
			PostingSql postingSql = new PostingSql();
			postingSql.importDatabase(bankid, txdate, daoTemplate, oriDBName,filename);
			
			BRC.getLog().info("-----------------导入旧账数据完成，事务提交-----------------");
			psessCheck1.getTransaction().commit();
			psessCheck1.close();
		} catch (Exception e) {
			psessCheck1.getTransaction().rollback();
			psessCheck1.close();
			StringTools.stackLog(e);
		}
		
		SessionFactory sessionFactory2 = (SessionFactory) BRC.getBrcContext().getBean("sf");
		Session psessCheck2=sessionFactory2.openSession();
		
		HashMap hm = new HashMap();
		psessCheck2.beginTransaction();
		daoTemplate = new DaoTemplateImpl();
		daoTemplate.setExternalSession(psessCheck2);
		hm.put("txdate", txdate);
		hm.put("bankid", bankid);
		hm.put("externalSession", psessCheck2);
		hm.put("daoTemplate", daoTemplate);

		try {
			PostingSql postingSql = new PostingSql();
			postingSql.fillInMstHstForLnlo(bankid,txdate, daoTemplate);
			postingSql.fillInMstHstForRepayday(bankid,txdate, daoTemplate);
			
			BRC.getLog().info("-----------------补流水完成-----------------");
			
			EodBusinessvch eodBusinessvch = new EodBusinessvch();
			eodBusinessvch.doOperation(hm);
			
			EodBusinesscont eodBusinesscont = new EodBusinesscont();
			eodBusinesscont.doOperation(hm);
			
			BRC.getLog().info("更新分户ln_mst中的in_lo_intst");
			String updateLnInLoIntstSql = "update ln_mst set in_lo_intst = in_normal_intst + in_over_intst+in_cmpd_intst";
			daoTemplate.executeSql(updateLnInLoIntstSql);
			
			BRC.getLog().info("-----------------更新借据合同表完成，事务提交-----------------");
			psessCheck2.getTransaction().commit();
			psessCheck2.close();
		} catch (Exception e) {
			psessCheck2.getTransaction().rollback();
			psessCheck2.close();
			StringTools.stackLog(e);
		}
		
	}
	
}

package tf.mcs.dc.handle;

import java.util.ArrayList;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.tools.UniversalObject;
/**
 * 描述 数据快查维护，进入DCLB0002页面,获取新增快查列表用户
 * <AUTHOR>
 * @date ********
 * @version v1.0 create
 *
 */
public class AddGetDcDataAuthList extends HibernateDaoSupport implements
		CommonHandlerStub {
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		BRC.getLog().info("【进入AddGetDcDataAuthList类】");
		BRC.getLog().info("后台接收参数为【"+arg0+"】");
		RespDataBusData rdbd = new RespDataBusData();
		UniversalObject uo = new UniversalObject(arg0);
		DaoTemplate daoTemplate = uo.Dao();
		long bankid = uo.getBankid();
		// 前台参数获取
		String itemNo = (String) arg0.get("itemNo");
		String mainSql = "select instcode as operid,shortname as opername," +
				"(case when up_instcode is null then 'root' else up_instcode end) as high_lvl from cn_inst_level where bankid="+bankid+
				" start with sortno='A' connect by prior instcode=up_instcode union " +
				"select t2.operid as operid,'['||t2.operid||']'||t2.opername,t2.instcode as high_lvl from cn_user t2 " +
				"where t2.status='1' and t2.bankid="
				+bankid+" and t2.operid not in (select operid from dc_data_auth where item_no='"+itemNo+"')";
		BRC.getLog().info(mainSql);
		ArrayList list=(ArrayList) daoTemplate.getMapListBySql(mainSql);
		rdbd.setRespEntity("list", list);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("【AddGetDcDataAuthList类】执行成功，【新增快查列表用户】！");
		return rdbd;
	}
}
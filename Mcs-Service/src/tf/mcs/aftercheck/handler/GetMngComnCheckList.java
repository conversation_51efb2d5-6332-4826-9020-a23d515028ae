package tf.mcs.aftercheck.handler;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.Tools;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;

@Transactional
public class GetMngComnCheckList extends HibernateDaoSupport implements
		CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		//获取管理常规检查列表

		BRC.getLog().info(arg0);

		// 获取前台参数
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0
				.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 公用域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		whereMap.put("#SQL#", "1=1 order by t1.misn_no");
		// String cstype = "A01";
		// whereMap.put("#EQUAL#t1.cstype", cstype);
		// whereMap.put("#EQUAL#t3.brno", brno);
		// whereMap.put("#EQUAL#t3.operid", operid);
		// whereMap.put("#EQUAL#t1.bankid", bankid);
		try {
			// credtprdtno关联授信额度的产品编号
			daoTemplate
					.getMapListByLikeSql(
							"select T1.*,T2.cstype,T3.brno from AF_CHECKBASE T1,CUST_INFO T2,CUST_PERMISSION T3 where   "
									+ " T1.CHK_TYPE='2'  and T1.cifid=T2.cifid and T1.cifid=T3.cifid and T3.main_right='1'", whereMap, pager);
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e.toString());
		}
		
		for(int i=0;i<pager.getList().size();i++){
			HashMap hMap = (HashMap) pager.getList().get(i);
			hMap.put("iptBrnoName", Tools.getSessionInstname(bankid,(String)hMap.get("iptBrno")));
			hMap.put("iptUsrName", Tools.getSessionOpername(bankid,(String)hMap.get("iptUsr")));
			hMap.put("launchUsrName", Tools.getSessionOpername(bankid,(String)hMap.get("launchUsr")));
			hMap.put("launchBrnoName", Tools.getSessionInstname(bankid,(String)hMap.get("launchBrno")));
			hMap.put("brnoName", Tools.getSessionInstname(bankid,(String)hMap.get("brno")));
		}
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取管理常规检查列表GetMngComnCheckList执行成功!");
		return rdbd;
	}
}

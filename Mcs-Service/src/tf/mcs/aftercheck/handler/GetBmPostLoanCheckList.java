package tf.mcs.aftercheck.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.ifms.rpc.handler.DateTool;
import tf.ifms.rpc.handler.IfmsTool;
import tf.ifms.rpc.handler.InvestigationUtil;
import tf.tools.DateTools;

/**
 * @Description 获取机构下所有客户经理贷后检查信息<br/>
 *  	 1.接收前台传参,指定机构、贷后月份<br/>
 *  	 2.如果未指定获取公共域中当前机构上月贷后检查信息
 * <AUTHOR> 
 * @date 2021年2月3日
 */
public class GetBmPostLoanCheckList implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		//公共域获取
		CommonDBT cdbt=arg0.getCommDC();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		log.info("进入----------------------GetBmPostLoanCheckList:参数"+arg0);
		long bankid=cdbt.getiBanks();
		String workDate = cdbt.getWorkDate();
		String brno = cdbt.getOpBr();
		Session externalSession=arg0.getReqDBPoolservice().getTransSession();
		
		try {
			Pager pager = (Pager) arg0.get("pager");
			
			HashMap hashMap = (HashMap) arg0.get("hashMap");
			String relInstcode = (String) hashMap.get("instcode");
			String monthafterloan = (String) hashMap.get("monthafterloan");
			String operid = (String) hashMap.get("operid");
			String opername = (String) hashMap.get("opername");
			String coFinished = (String) hashMap.get("coFinished");
			
			if (IfmsTool.isNull(relInstcode)) {
				relInstcode = brno;
			}
			if (IfmsTool.isNull(monthafterloan)) {
				monthafterloan =  String.valueOf(DateTools.CountDate(workDate, "month", -1)).substring(0,6);
			}
			String mainSql = "select t1.operid,t1.opername"
					+ " from cn_user_post t2,cn_user t1"
					+ " where t1.instcode = '"+ relInstcode +"' and t1.operid = t2.operid and t2.postid = 'C00030'";
			if (IfmsTool.isNotNull(operid)) {
				mainSql += " and t1.operid like '%"+ operid +"%'";
			}
			if (IfmsTool.isNotNull(opername)) {
				mainSql += " and t1.opername like '%"+ opername +"%'";
			}
			
			daoTemplate.getMapListBySql( mainSql , pager );
			List<HashMap> queryPageList = pager.getList();
			List<HashMap> resultList = new ArrayList<>();

			for (int i = 0; i < queryPageList.size(); i++) {
				HashMap hMap = queryPageList.get(i);
//				String confirmPhase = String.valueOf(hMap.get("confirmPhase"));
				operid =  String.valueOf(hMap.get("operid"));
//				if (IfmsTool.isNull(confirmPhase)||"00".equals(confirmPhase)) {// 客户经理未做贷后检查
				// 贷后月份
				hMap.put("monthafterloan",monthafterloan);

				// 客户经理提交情况
				String iptDate = String.valueOf(daoTemplate.getUniqueValueBySql("select max(ipt_date) from af_chkconclusion where monthafterloan = '"+monthafterloan+"' and ipt_usr = '"+operid+"' and confirm_phase = '01'"));
				if (IfmsTool.isNull(iptDate)) {
					hMap.put("coFinished", "0");
				}else {
					hMap.put("coFinished", "1");
					hMap.put("iptDate", iptDate);
				}

				if(IfmsTool.isNotNull(coFinished)){

					if(StringUtils.equals("0", coFinished)
							&& IfmsTool.isNotNull(iptDate)){
						continue;
					}else if(StringUtils.equals("1", coFinished)
							&& IfmsTool.isNull(iptDate)){
						continue;
					}
				}

				// 上月新增户数
				int newHouseholds = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						/*"select ifnull(count(t1.bankid),0) from ac_businessvch t1"
						+ " where t1.manage_operid = '"+operid+"' and t1.manage_instcode = '"+relInstcode+"' and t1.occurdate< '"+workDate.substring(0,6)+"01'"
						+ " and t1.occurdate >= '"+monthafterloan+"01' and t1.occurtype = '1'"
						+ " AND NOT EXISTS (SELECT 1 FROM ac_businessvch_his WHERE cifid = t1.cifid  AND vch_sts != '20')")));*/
						//随机选取
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%1%'")));
				hMap.put("newHouseholds",newHouseholds);
					
				// 金额30万以上户数
				int morethansum = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						/*"select nvl(count(*),0) from ac_businessvch where manage_operid = '"+operid+"' and manage_instcode = '"+ relInstcode +"' and occurdate< '"+workDate.substring(0,6)+"01' and bus_bal >= 300000 and vch_sts ='10'")));*/
						//10万以上
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%2%'")));
				hMap.put("morethansum",morethansum);
					
				// 拖欠10天以上户数
				int lomorethan = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%3%'")));
				hMap.put("lomorethan",lomorethan);
					
				// 外地人本地无住房户数
				int outnohouse = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						//"select nvl(count(bankid),0) from ac_businessvch where manage_operid = '"+operid+"'  and manage_instcode = '"+ relInstcode +"'  and occurdate< '"+workDate.substring(0,6)+"01' and vch_sts = '10' and cifid in (select cifid from app_ind_info where is_out_reg='1')")));
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%4%'")));
				hMap.put("outnohouse",outnohouse);
					
				// 重组贷款户数
				int restloan = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						//"select nvl(count(t1.bankid),0) from ac_businessvch t1,ln_mst t2 where t1.manage_operid = '"+operid+"'  and manage_instcode = '"+ relInstcode +"'  and t1.occurdate< '"+workDate.substring(0,6)+"01' and t1.vch_sts = '10' and t1.loanac_id = t2.ac_id and t2.prdt_no like '4%'")));
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%5%'")));
				hMap.put("restloan",restloan);
					
				// 整贷整还户数
				int wholeloan =Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						//"select nvl(count(bankid),0) from ac_businessvch where manage_operid = '"+operid+"' and manage_instcode = '"+ relInstcode +"' and occurdate< '"+workDate.substring(0,6)+"01' and vch_sts = '10' and repay_type = '9'")));
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%6%'")));
				hMap.put("wholeloan",wholeloan);
					
				// 合计户数
				int custtotal = InvestigationUtil.doTypeConversionInt(String.valueOf(daoTemplate.getUniqueValueBySql("select count(*) from af_checkbase where manageoperid = '"+operid+"' and launch_brno = '"+ relInstcode +"' and monthafterloan = '"+monthafterloan+"' and inspection_reason NOT LIKE '%7%'")));
				hMap.put("custtotal",custtotal);
//					
				// 截止上月末有效户数
				String checkMonth = String.valueOf(DateTools.CountDate(monthafterloan+"01", "month", 1)).substring(0,6);

				int lastmoneffnum = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						"select nvl(count(distinct t1.cifid),0) from ac_businessvch t1 where t1.manage_operid = '"+operid+"' and manage_instcode = '"+ relInstcode +"' and t1.occurdate< '"+checkMonth.substring(0,6)+"01' and t1.vch_sts = '10'")));
				log.info("这是客户经理的sql"+"select nvl(count(distinct t1.cifid),0) from ac_businessvch t1 where t1.manage_operid = '"+operid+"' and manage_instcode = '"+ relInstcode +"' and t1.occurdate< '"+checkMonth.substring(0,6)+"01' and t1.vch_sts = '10'");
				hMap.put("lastmoneffnum",lastmoneffnum);

				int handovers = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql(
						"SELECT COUNT(bankid) from af_checkbase where launch_usr = '"+operid+"' and monthafterloan = '"+monthafterloan+"' and inspection_reason like '%8%'")));
				hMap.put("handovers",handovers);
				resultList.add(hMap);
			}
			pager.setList(resultList);
			arg1.setRespEntity("pager", pager);
			arg1.setRespCode("0000");
			arg1.setRespDesc("获取机构经理贷后检查页面客户列表信息成功！");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("获取机构经理贷后检查页面客户列表信息失败!");
		}
		return arg1;
	}
}

package tf.mcs.aftercheck.handler;

import java.util.HashMap;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.Tools;
import tf.mcs.model.CnProductdefine;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
/**
 * @version v1.1 edited by wuzhujun 20160517 调整代码格式,删除无用代码
 */
//获取检查成员名单
public class GetSpeCheckList implements CommonHandlerStub{
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		BRC.getLog().info(arg0); 
		// 获取前台参数
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");   
		HashMap hashmap = (HashMap) arg0.get("hashMap");
		// 公用域获取
		CommonDBT cdbt = arg0.getCommDC();
		long bankid = cdbt.getiBanks(); 
		String misnNo=(String)hashmap.get("misnNo");
		String chkObj=(String)hashmap.get("chkObj");
		String sts=(String)hashmap.get("sts");
		String appendSql="";
		if(sts!=null&&!sts.isEmpty()){
			appendSql="and t1.sts='"+sts+"'";
		}
		if(chkObj.equals("1")){
			try {
				daoTemplate.getMapListBySql("select t1.* from af_specialchecklist t1,cust_info t2 where "+
						"t1.misn_no='"+misnNo+"' and t1.relserid=t2.cifid "+appendSql,pager);
			} catch (Exception e) {
				e.printStackTrace();
				throw new Exception(e.toString());
			}
		}else if(chkObj.equals("2")){
			try {
				daoTemplate.getMapListBySql("select t1.*,t2.prdt_no from af_specialchecklist t1,ac_businesscont t2 where " +
						"t1.misn_no='"+misnNo+"' and t1.relserid=t2.contno "+appendSql,pager);
			} catch (Exception e) {
				e.printStackTrace();
				throw new Exception(e.toString());
			}
			for (int i = 0; i < pager.getList().size(); i++) {
				HashMap hMap = (HashMap) pager.getList().get(i);
				CnProductdefine cnProductdefine = (CnProductdefine) tf.mcs.Tools.getProductdefine(bankid,(String)hMap.get("prdtNo"));
				if (cnProductdefine != null) {
					hMap.put("prdtNa", cnProductdefine.getPrdtNa());
				}
			}
		}
		for (int i = 0; i < pager.getList().size(); i++) {
			HashMap hMap = (HashMap) pager.getList().get(i);
			hMap.put("iptUsrName", Tools.getSessionOpername(bankid,(String)hMap.get("iptUsr")));
			hMap.put("launchUsrName", Tools.getSessionOpername(bankid,(String)hMap.get("launchUsr")));
			hMap.put("launchBrnoName", Tools.getSessionInstname(bankid,(String)hMap.get("launchBrno")));
		}	
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取检查成员名单GetSpeCheckList执行成功!");
		return rdbd;
	}
}
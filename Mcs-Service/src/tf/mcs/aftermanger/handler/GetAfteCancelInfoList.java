package tf.mcs.aftermanger.handler;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.ifms.rpc.handler.IfmsTool;

/**  
 *  @file:GetAfteCancelInfoList.java
 * 	@author: w_w
 * 	2014-5-26下午4:48:17
 *  @company: tfrunning
 *  @version V1.1 edit by wuzhujun 20160505 梳理代码，删除无用代码
 */
public class GetAfteCancelInfoList extends HibernateDaoSupport implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info(arg0);
		BRC.getLog().info("#############进入GetAfteCancelInfoList类###############");
		// 获取session
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		// 公共域获取
		CommonDBT cdbt = arg0.getCommDC();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		RespDataBusData rdbd = new RespDataBusData();
		// 业务处理
		whereMap.put("#SQL#", " 1=1 order by t1.applyno desc");
		try {
			String mainSql = "select t1.*,t2.REFUSEREASON,t2.phasename,t2.opername,t2.phaseno as recphaseno from af_main_info t1,de_flowrecord t2 where t1.bankid="+ bankid+ "" +
					" and t1.bankid=t2.bankid and t1.applyno=t2.applyno and t1.ipt_user='"+ operid+ "' and t2.phasechoice ='deny' and t2.phaseno like '0%'";
			daoTemplate.getMapListByLikeSql(mainSql, whereMap, pager);	
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取GetAfteCancelInfoList失败!");
			return rdbd;
		}

		for (int i = 0; i < pager.getList().size(); i++) {
			HashMap hMap = (HashMap) pager.getList().get(i);
			String prdtNo = (String)hMap.get("prdtNo");
			if(IfmsTool.isNotNull(prdtNo)){
				hMap.put("prdtNa", "[" + (String)hMap.get("prdtNo") + "]" + tf.mcs.Tools.getProductName(bankid,(String)hMap.get("prdtNo")));
			}
		}
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取贷后主动取消申请列表GetAfteCancelInfoList执行成功!");
		rdbd.setRespEntity("pager", pager);
		return rdbd;
	}
}

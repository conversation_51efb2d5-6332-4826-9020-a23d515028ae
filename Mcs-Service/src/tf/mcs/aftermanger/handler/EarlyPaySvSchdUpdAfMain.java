package tf.mcs.aftermanger.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.AfMainInfoDao;
import tf.mcs.dao.BusHandmaderepayDao;
import tf.mcs.model.AcBusinessvch;
import tf.mcs.model.AfMainInfo;
import tf.mcs.model.BusHandmaderepay;
import tf.tools.Arith;
import tf.tools.ScheduleBuildbk;

/**
 * @file:EarlyPaySvSchdUpdAfMain.java
 * @author: w_w 2014-4-18下午4:35:29
 * @funtion 贷后提前还款保存还款计划表并更新贷后信息
 */
public class EarlyPaySvSchdUpdAfMain implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		BRC.getLog().info("传入参数为：" + arg0);
		// 获取session
		RespDataBusData rdbd = new RespDataBusData();
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		// 前台参数获取
		AfMainInfo afMainInfo = (AfMainInfo) arg0.get("afMainInfo");
		String changerRepayway = afMainInfo.getChangerRepayway();
		String applyno = afMainInfo.getApplyno();
		String vchno = afMainInfo.getVchno();
		String resetEnddate = afMainInfo.getResetEnddate();
		String applyDate = afMainInfo.getApplyDate();
		String repayday = afMainInfo.getRepayday();
		double repaySum = afMainInfo.getRepaySum();
		// 公共域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String workDate = cdbt.getWorkDate();
		String workTime = cdbt.getReqTime();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 业务处理
		AfMainInfoDao afMainInfoDao = new AfMainInfoDao();
		afMainInfoDao.setExternalSession(externalSession);
		try {
			// 如果还款方法不是手工编制则删除bus_handmaderepay中的数据
			if (changerRepayway != null && !changerRepayway.equals("6")) {
				String sql = "delete from bus_handmaderepay where bankid ='" + bankid + "' and applyno = '" + applyno + "'";
				daoTemplate.executeSql(sql);
				// 获取借据信息
				AcBusinessvch acBusinessvch = new AcBusinessvch();
				acBusinessvch.setBankid(bankid);
				acBusinessvch.setVchno(vchno);
				AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
				acBusinessvchDao.setExternalSession(externalSession);
				acBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
				String vchbegindate = acBusinessvch.getBegindate();
				double busBal = acBusinessvch.getBusBal();
				double arate = acBusinessvch.getArate();
				String enddate = acBusinessvch.getEnddate();
				double repayBal = Arith.sub(busBal, repaySum);
				if (repayBal != 0) {
					// 判断appdate是否在借据期限内
					if (Double.valueOf(applyDate) < Double.valueOf(vchbegindate) || Double.valueOf(applyDate) >= Double.valueOf(enddate)) {
						rdbd.setRespCode("9150");
						rdbd.setRespDesc("申请日期不在原借据期限内，请检查申请信息");
						return rdbd;
					}
					// 校验重新设定到期日是否超过借据期限
					if (Double.valueOf(resetEnddate) < Double.valueOf(workDate) || Double.valueOf(resetEnddate) >= Double.valueOf(enddate)) {
						rdbd.setRespCode("9150");
						rdbd.setRespDesc("重设到期日超出期限范围，请检查申请信息");
						return rdbd;
					}
					// 获取还款计划表信息
					HashMap hMap =
							(HashMap) daoTemplate.getUniqueMapRowBySql("select repbegindate,currcnt from ac_lnrepayschd where vchno ='"
									+ vchno + "' and bankid ='" + bankid + "' and rependdate >= '" + applyDate + "' and repbegindate <'"
									+ applyDate + "'");
					// 新还款计划表的起始日应该是申请日所在期限下一期的起始日
					String begindate = hMap.get("rependdate").toString();
					int currcnt = Integer.valueOf(hMap.get("currcnt").toString());
					// 生成自动还款计划表
					if (repayday != null && repayday.length() == 1) {
						repayday = "0" + repayday;
					}
					ScheduleBuildbk scheduleBuild = new ScheduleBuildbk();
					ArrayList arrayList = scheduleBuild.build(repayday, repayBal, arate, begindate, resetEnddate, changerRepayway);
					ArrayList rtnList = new ArrayList();
					Iterator iterator = arrayList.iterator();
					BusHandmaderepayDao busHandmaderepayDao = new BusHandmaderepayDao();
					busHandmaderepayDao.setExternalSession(externalSession);
					// 将数据插入贷后中间表bus_handmaderepay
					while (iterator.hasNext()) {
						HashMap hashMap = (HashMap) iterator.next();
						BusHandmaderepay busHandmaderepay = new BusHandmaderepay();
						busHandmaderepay.setBankid(bankid);
						busHandmaderepay.setApplyno(applyno);
						busHandmaderepay.setCurramt(Double.valueOf(hashMap.get("repaypri").toString()));
						busHandmaderepay.setCurrcnt(Long.valueOf(hashMap.get("term").toString()) + currcnt - 1);
						busHandmaderepay.setCurrsum(Double.valueOf(hashMap.get("repaysum").toString()));
						busHandmaderepay.setCurrint(Double.valueOf(hashMap.get("repayins").toString()));
						busHandmaderepay.setRepbegindate(hashMap.get("bdate").toString());
						busHandmaderepay.setRependdate(hashMap.get("edate").toString());
						busHandmaderepay.setRepaybal(Double.valueOf(hashMap.get("repaybal").toString()));
						busHandmaderepay.setIptUsr(operid);
						busHandmaderepay.setIptBrno(brno);
						busHandmaderepay.setIptDate(workDate);
						busHandmaderepayDao.insert(busHandmaderepay);
					}
				}
			} else {
				// 如果是手编，则需校验最后最后一期的到期日应与原到期日相等
				// 取得原还款计划表的最后一期到期日
				AcBusinessvch acBusinessvch = new AcBusinessvch();
				acBusinessvch.setBankid(bankid);
				acBusinessvch.setVchno(vchno);
				AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
				acBusinessvchDao.setExternalSession(externalSession);
				acBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
				double busBal = acBusinessvch.getBusBal();
				double repayBal = Arith.sub(busBal, repaySum);
				if (repayBal != 0) {
					HashMap hmp3 =
							(HashMap) daoTemplate
									.getUniqueMapRowBySql("select max(rependdate) as rependdate from bus_handmaderepay where applyno ='"
											+ applyno + "' and bankid ='" + bankid + "'");
					String newLastEndDate = hmp3.get("rependdate").toString();
					if (!resetEnddate.equals(newLastEndDate)) {
						rdbd.setRespCode("9999");
						rdbd.setRespDesc("变更后的还款计划表的最后一期到期日应与重设到期日相符！");
						return rdbd;
					}
				}
			}
			// 更新贷后主表
			String rtnString = "";
			afMainInfo.setBankid(bankid);
			AfMainInfo oldAfMainInfo = new AfMainInfo();
			oldAfMainInfo = afMainInfoDao.getEntityByPK(afMainInfo);
			if (oldAfMainInfo == null) {
				rtnString += "变更记录不存在";
				rdbd.setRespCode("9999");
				rdbd.setRespDesc(rtnString);
				return rdbd;
			} else {
				tf.core.utils.func.DomainHelper.copyProperty(afMainInfo, oldAfMainInfo);
				afMainInfoDao.update(oldAfMainInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("贷后提前还款保存还款计划表并更新贷后信息EarlyPaySvSchdUpdAfMain执行失败！");
			return rdbd;
		}
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("贷后提前还款保存还款计划表并更新贷后信息EarlyPaySvSchdUpdAfMain执行成功！");
		return rdbd;
	}
}
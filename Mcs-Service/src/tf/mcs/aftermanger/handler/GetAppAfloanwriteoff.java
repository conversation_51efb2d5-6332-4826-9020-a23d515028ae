package tf.mcs.aftermanger.handler;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;

public class GetAppAfloanwriteoff extends HibernateDaoSupport implements
CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		// 获取session
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0
				.getReqDBPoolservice().getDaoTemplate();
		// 公共域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		int bankid = cdbt.getiBanks();
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 业务处理
		whereMap.put("#SQL#", " 1=1 order by t1.applyno desc");
		
		RespDataBusData rdbd = new RespDataBusData();
		
		try {
			daoTemplate
					.getMapListByLikeSql(
							"select t1.* from af_main_info t1,de_flowrecord t2 "
									+ "where  t2.operid='"
									+ operid
									+ "' and ifnull(t1.enddate,'')='' and t2.phaseno='6100' and  t1.applyno =t2.applyno and t2.isrtn!='1' and t1.bankid="
									+ bankid + " and t1.bankid =t2.bankid ",
							whereMap, pager);
			for (int i = 0; i < pager.getList().size(); i++) {
				HashMap hMap = (HashMap) pager.getList().get(i);
				hMap.put("prdtNa", "[" + (String)hMap.get("prdtNo") + "]" + tf.mcs.Tools.getProductName(bankid,(String)hMap.get("prdtNo")));
				hMap.put("iptBrno", tf.mcs.Tools.getSessionInstname(bankid,(String)hMap.get("iptBrno")));
				hMap.put("iptUsrName", tf.mcs.Tools.getSessionOpername(bankid,(String)hMap.get("iptUser")));
				hMap.put("detBrno", tf.mcs.Tools.getSessionInstname(bankid,(String)hMap.get("detBrno")));
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取业务申请信息表GetAfterAppinfoList执行失败!");
			return rdbd;
		}


		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取业务申请信息表GetAfterAppinfoList执行成功!");
		return rdbd;
	}
}

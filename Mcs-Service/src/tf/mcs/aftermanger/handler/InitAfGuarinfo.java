package tf.mcs.aftermanger.handler;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AfGuarinfoDao;
import tf.mcs.model.AfGuarinfo;

/**
 * 功能描述： 云微贷担保变更类申请统一初始化贷后备份表程序
 * 
 * <AUTHOR>
 * @file InitAfGuarInfo.java
 * @date 2014-04-18
 * @version v1.0
 * @company tfrunning
 * @version V1.1 edit by wuz<PERSON>jun 20160505 梳理代码，删除无用代码
 */

public class InitAfGuarinfo implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info(arg0);
		RespDataBusData rdbd = new RespDataBusData();
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) dbpf.getDaoTemplate();
		Session externalSession = dbpf.getTransSession();
		CommonDBT cdbt = arg0.getCommDC();
		// 公用参数定义
		int bankid = cdbt.getiBanks();
		// 获取前台传入值
		HashMap hm = new HashMap();
		hm = (HashMap) arg0.get("hashMap");
		// 贷后变更申请号
		String afapplyno = (String) hm.get("afapplyno");
		// 业务合同号
		String contno = (String) hm.get("contno");
		// 贷后变更类型
		String guartype = (String) hm.get("guartype");
		// 数据校验
		BRC.getLog().info("----------InitAfGuarInfo前台传入数据校验开始----------");
		if (null == afapplyno || afapplyno.equals("")) {
			rdbd.setRespCode("9900");
			rdbd.setRespDesc("业务变更获取变更申请号为空");
			return rdbd;
		}
		if (null == contno || contno.equals("")) {
			rdbd.setRespCode("9800");
			rdbd.setRespDesc("业务变更获取合同号为空");
			return rdbd;
		}
		if (null == guartype || guartype.equals("")) {
			rdbd.setRespCode("9700");
			rdbd.setRespDesc("业务变更获取担保类型为空");
			return rdbd;
		}
		BRC.getLog().info("----------InitAfGuarInfo前台传入数据校验成功----------");
		// 检查DB中是否存在当前贷后申请初始化信息，若存在则返回成功
		Integer cnt = null;
		try {	
			cnt=Integer.valueOf(String.valueOf(daoTemplate.getUniqueValueBySql("select count(*) from af_guarinfo where afapplyno='"+ afapplyno + "' and bankid=" + bankid)));
		} catch (Exception e1) {
			e1.printStackTrace();
			log.error(e1.getMessage(), e1);
			rdbd.setRespCode("9600");
			rdbd.setRespDesc("贷后担保变更申请校验是否存在初始化信息时发生错误");
			return rdbd;
		}
		if (!cnt.toString().equals("0")) {
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("担保变更类申请初始化时已存在初始化信息，不需要再次初始化");
			return rdbd;
		}
		// 定义贷后担保信息afGuarinfo表HQL实体
		AfGuarinfoDao afGuarinfoDao = new AfGuarinfoDao();
		afGuarinfoDao.setExternalSession(externalSession);
		AfGuarinfo afGuarinfo = new AfGuarinfo();
		// 定义担保详情信息容器
		HashMap guarHm = new HashMap();
		// 抽取当前贷后变更合同相应担保信息
		ArrayList guarList = new ArrayList();
		try {
			if (guartype.equals("30")) {// 担保人变更
				guarList = (ArrayList) daoTemplate.getMapListBySql("select a.*,b.workcorp,b.workaddr,b.headship,b.workcorptel,b.income,b.address,b.tel,b.state,b.assets,b.brf"
							+" from ac_guar_relative a,bus_affrim_guarantor b where a.contno='"+ contno+ "' and a.bankid="+ bankid+ " and a.reltype='30' and a.is_invalid='1'"
							+" and a.bankid=b.bankid and a.cifid=b.cifid and a.applyno=b.applyno");
			} else if (guartype.equals("10")) {// 共同借款人变更
				guarList = (ArrayList) daoTemplate.getMapListBySql("select a.*,b.workcorp,b.workaddr,b.headship,b.workcorptel,b.income,b.address,b.tel,b.state,b.assets,b.brf "
							+"from ac_guar_relative a,bus_affrim_coborrower b where a.contno='"+ contno+ "' and a.bankid="+ bankid+ " and a.reltype='10' and a.is_invalid='1' " 
							+"and a.bankid=b.bankid and a.cifid=b.cifid and a.applyno=b.applyno");
			} else if (guartype.equals("20")) {// 押品变更
				guarList = (ArrayList) daoTemplate.getMapListBySql("select * from ac_guar_relative where contno='"+ contno+ "' and reltype='20' and is_invalid='1' and bankid="+ bankid);
			} else {
				rdbd.setRespCode("9999");
				rdbd.setRespDesc("申请的担保类型匹配失败");
				return rdbd;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取原担保信息列表时发生错误");
			return rdbd;
		}
		// 进行数据抽取并初始化至af_guarinfo
		try {
			if (guartype.equals("20")) {
				// 押品变更,仅涉及ac_guar_relative所含数据
				for (int i = 1; i <= guarList.size(); i++) {
					guarHm = (HashMap) guarList.get(i - 1);
					// 重置HQL实体
					afGuarinfo = null;
					// 将数据列压入HQL实体
					afGuarinfo.setBankid((long) bankid);
					afGuarinfo.setAfapplyno(afapplyno);
					afGuarinfo.setAfSeq((long) i);
					afGuarinfo.setSerid((String) guarHm.get("serid"));
					afGuarinfo.setApplyno((String) guarHm.get("applyno"));
					afGuarinfo.setGccontno((String) guarHm.get("gccontno"));
					afGuarinfo.setReltype((String) guarHm.get("reltype"));
					afGuarinfo.setRelcolNo((String) guarHm.get("relcolNo"));
					afGuarinfo.setRelserid((String) guarHm.get("relserid"));
					afGuarinfo.setCifid((String) guarHm.get("cifid"));
					afGuarinfo.setCliname((String) guarHm.get("cliname"));
					afGuarinfo.setCerttype((String) guarHm.get("certtype"));
					afGuarinfo.setCertno((String) guarHm.get("certno"));
					afGuarinfo.setGuarform((String) guarHm.get("guarform"));
					afGuarinfo.setGuarsum((Double) guarHm.get("guarsum"));
					afGuarinfo.setGuarLtv((Double) guarHm.get("guarLtv"));
					afGuarinfo.setGuarcurr((String) guarHm.get("guarcurr"));
					afGuarinfo.setStatus((String) guarHm.get("status"));
					afGuarinfo.setRelation((String) guarHm.get("relation"));
					afGuarinfo.setGuarRel((String) guarHm.get("guarRel"));
					afGuarinfo.setIsInvalid((String) guarHm.get("isInvalid"));
					afGuarinfo.setIsNew("0");
					// 插入中间表
					afGuarinfoDao.insert(afGuarinfo);
				}
			} else {
				// 共同借款人、担保人变更
				for (int i = 1; i <= guarList.size(); i++) {
					guarHm = (HashMap) guarList.get(i - 1);
					// 重置HQL实体
					afGuarinfo = null;
					// 将数据列压入HQL实体
					afGuarinfo.setBankid((long) bankid);
					afGuarinfo.setAfapplyno(afapplyno);
					afGuarinfo.setAfSeq((long) i);
					afGuarinfo.setSerid((String) guarHm.get("serid"));
					afGuarinfo.setApplyno((String) guarHm.get("applyno"));
					afGuarinfo.setGccontno((String) guarHm.get("gccontno"));
					afGuarinfo.setReltype((String) guarHm.get("reltype"));
					afGuarinfo.setRelcolNo((String) guarHm.get("relcolNo"));
					afGuarinfo.setRelserid((String) guarHm.get("relserid"));
					afGuarinfo.setCifid((String) guarHm.get("cifid"));
					afGuarinfo.setCliname((String) guarHm.get("cliname"));
					afGuarinfo.setCerttype((String) guarHm.get("certtype"));
					afGuarinfo.setCertno((String) guarHm.get("certno"));
					afGuarinfo.setGuarform((String) guarHm.get("guarform"));
					afGuarinfo.setGuarsum((Double) guarHm.get("guarsum"));
					afGuarinfo.setGuarLtv((Double) guarHm.get("guarLtv"));
					afGuarinfo.setGuarcurr((String) guarHm.get("guarcurr"));
					afGuarinfo.setStatus((String) guarHm.get("status"));
					afGuarinfo.setRelation((String) guarHm.get("relation"));
					afGuarinfo.setGuarRel((String) guarHm.get("guarRel"));
					afGuarinfo.setIsInvalid((String) guarHm.get("isInvalid"));
					afGuarinfo.setWorkcorp((String) guarHm.get("workcorp"));
					afGuarinfo.setWorkaddr((String) guarHm.get("workaddr"));
					afGuarinfo.setHeadship((String) guarHm.get("headship"));
					afGuarinfo.setWorkcorptel((String) guarHm.get("workcorptel"));
					afGuarinfo.setIncome((Double) guarHm.get("income"));
					afGuarinfo.setAddress((String) guarHm.get("address"));
					afGuarinfo.setTel((String) guarHm.get("tel"));
					afGuarinfo.setState((String) guarHm.get("state"));
					afGuarinfo.setAssets((String) guarHm.get("assets"));
					afGuarinfo.setBrf((String) guarHm.get("brf"));
					afGuarinfo.setIsNew("0");
					// 插入中间表
					afGuarinfoDao.insert(afGuarinfo);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9500");
			rdbd.setRespDesc("贷后变更担保信息初始化时发生错误");
			return rdbd;
		}
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("担保变更类申请初始化执行成功");
		return rdbd;
	}
}
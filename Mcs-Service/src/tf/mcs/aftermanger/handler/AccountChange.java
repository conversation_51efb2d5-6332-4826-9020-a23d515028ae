package tf.mcs.aftermanger.handler;

import java.util.HashMap;

import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplate;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.model.AcBusinessvch;
import tf.mcs.model.AfMainInfo;

/**
 * @used 自动还款账号维护
 * @file AccountChange.java
 * <AUTHOR>
 * @company tfrunning
 * @date ********
 * @version v1.0
 * @Version v1.1 editor Dav 由于赣州记录账号户名，故还款账号变更后需要将户名更新到借据表中
 * @version v1.2 edited by wuz<PERSON>jun ******** 调整代码格式
 */
public class AccountChange implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@SuppressWarnings("unchecked")
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info("页面传入参数为----------" + arg0);
		BRC.getLog().info("--------------进入自动还款账号维护AccountChange交易--------------");
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		// 获取session
		DaoTemplate daoTemplate = (DaoTemplate) arg0.getReqDBPoolservice().getDaoTemplate();
		AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
		acBusinessvchDao.setExternalSession(externalSession);
		// 公用域获取
		CommonDBT cdbt = arg0.getCommDC();
		long bankid = cdbt.getiBanks();
		String workDate = cdbt.getWorkDate();
		String workTime = cdbt.getReqTime();
		String operid = cdbt.getOpTel();
		try {
			// 获取传入参数
			AfMainInfo afMainInfo = (AfMainInfo) arg0.get("afMainInfo");
			HashMap<String, Object> hm = (HashMap<String, Object>) arg0.get("hashMap");
			String vchno = (String) hm.get("vchno");
			String applyno = afMainInfo.getApplyno();
			// 判断流程是否结束，如果结束，则无法落实变更
			String checkFlowSql = "select count(t1.bankid) from de_flowrecord t1 where t1.bankid='"+ bankid
					+ "' and t1.applyno ='"+ applyno+ "' and ifnull(t1.enddate,'') = ''";
			try {
				int checkFlowCount = Integer.parseInt(daoTemplate.getUniqueValueBySql(checkFlowSql).toString());
				if (checkFlowCount == 0) {
					arg1.setRespCode("9100");
					arg1.setRespDesc("本次业务变更流程已结束，不可落实变更!");
					return arg1;
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				arg1.setRespCode("8200");
				arg1.setRespDesc("判断流程是否结束时发生错误！");
				return arg1;
			}
			AcBusinessvch acBusinessvch = new AcBusinessvch();
			acBusinessvch.setBankid(bankid);
			acBusinessvch.setVchno(vchno);
			AcBusinessvch oldAcBusinessvch = new AcBusinessvch();
			oldAcBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
			if (oldAcBusinessvch == null) {
				arg1.setRespCode("9999");
				arg1.setRespDesc("变更记录不存在");
				return arg1;
			}
			oldAcBusinessvch.setReppriacNo(afMainInfo.getNewreppriacNo());
			oldAcBusinessvch.setReppriacna(afMainInfo.getNewreppriacNa());//新还款账号户名
			oldAcBusinessvch.setSecreppriacNo(afMainInfo.getSecreppriacNo());
			tf.core.utils.func.DomainHelper.copyProperty(acBusinessvch,oldAcBusinessvch);
			acBusinessvchDao.update(oldAcBusinessvch);
			// 完成变更后更新流程表让流程表结束流程
			// 获得需要更新的流程记录
			String enddate = workDate + ' ' + workTime;
			String updateSql = "update de_flowrecord set enddate='" + enddate+ "',phasechoice='agree',phaseoptions='同意',phaseaction = '"
					+ operid + "' where bankid=" + bankid + " and applyno='"+ applyno + "' and ifnull(enddate,'') = ''";
			try {
				daoTemplate.executeSql(updateSql);
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				arg1.setRespCode("8300");
				arg1.setRespDesc("更新流程记录时发生错误！");
				return arg1;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("自动还款账号更新时发生错误！");
			return arg1;
		}
		arg1.setRespCode("0000");
		arg1.setRespDesc("自动还款账号更新执行成功");
		return arg1;
	}
}
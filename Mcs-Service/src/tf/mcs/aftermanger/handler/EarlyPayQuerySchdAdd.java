package tf.mcs.aftermanger.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.AcLnrepayschdDao;
import tf.mcs.dao.AfMainInfoDao;
import tf.mcs.dao.BusHandmaderepayDao;
import tf.mcs.model.AcBusinessvch;
import tf.mcs.model.AcLnrepayschd;
import tf.mcs.model.AfMainInfo;
import tf.mcs.model.BusHandmaderepay;
import tf.tools.Arith;
import tf.tools.ScheduleBuild;
import tf.tools.ScheduleRecord;

/**
 * @des 提前还款生成默认自定义还款方式
 * @author:王忠钰 
 * @company: tfrunning
 */
public class EarlyPayQuerySchdAdd implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info(arg0);	
		// 获取session
		RespDataBusData rdbd = new RespDataBusData();
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		// 前台参数获取
		// 获取前台参数
		Pager pager = (Pager) arg0.get("pager");
		AfMainInfo afMainInfoNew = (AfMainInfo) arg0.get("afMainInfo");
		String applyno = afMainInfoNew.getApplyno().toString();
		String vchno = afMainInfoNew.getVchno().toString();
		String readflag = "1";//首月控制位 ，0为首月
		String repaystate = ""; // 贷款还清标志，0未还清，1已还清
		double bal = 0 ;//本金
		HashMap hMap = new HashMap();
		// 公共域获取	
		CommonDBT cdbt = arg0.getCommDC();
		String workDate = cdbt.getWorkDate();
		long bankid = cdbt.getiBanks();
		
		BusHandmaderepayDao busHandmaderepayDao = new BusHandmaderepayDao();
		busHandmaderepayDao.setExternalSession(externalSession);
		BusHandmaderepay busHandmaderepay = new BusHandmaderepay();
		// 业务处理
		try {	
			// 取得原借据信息
			AcBusinessvch acBusinessvch = new AcBusinessvch();
			acBusinessvch.setBankid(bankid);
			acBusinessvch.setVchno(vchno);
			AcLnrepayschdDao acLnrepayschdDao = new AcLnrepayschdDao();
			acLnrepayschdDao.setExternalSession(externalSession);
			AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
			acBusinessvchDao.setExternalSession(externalSession);
			acBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);	
			AfMainInfo afMainInfo = new AfMainInfo();
			afMainInfo.setBankid(bankid);
			afMainInfo.setApplyno(applyno);
			AfMainInfoDao afMainInfoDao = new AfMainInfoDao();
			afMainInfoDao.setExternalSession(externalSession);
			afMainInfo = afMainInfoDao.getEntityByPK(afMainInfo);	
			
			String applyDate = afMainInfo.getApplyDate();
			double repaySum = afMainInfo.getRepaySum();
			String resetEnddate = afMainInfo.getResetEnddate();
			double repayBal = afMainInfo.getRepayBal();
			String vchbegindate = acBusinessvch.getBegindate();				
			// 判断是否全部还清
			if (Arith.sub(repayBal, 0) == 0) {
				repaystate = "1";
				hMap.put("repaystate", repaystate);	
				rdbd.setRespCode("0000");
				rdbd.setRespMap("hMap", hMap);
				rdbd.setRespDesc("贷款已经全部还清，无须生成新的还款计划表");
				return rdbd;
			}
			if(Double.valueOf(applyDate)>Double.valueOf(resetEnddate)||Double.valueOf(applyDate)<Double.valueOf(vchbegindate)){
				rdbd.setRespCode("9100");
				rdbd.setRespDesc("申请日期超出期限范围");
				return rdbd;
			}
			// 查询当期期次
			String getCurrcntSql = "select nvl(min(currcnt),0) from ac_lnrepayschd where bankid = " + bankid + " and vchno ='" + vchno
					+ "' and updcurrrepdate >= '" + workDate + "' and repaystate='0' and state = '1'";
			long currcnt = Long.valueOf(daoTemplate.getUniqueValueBySql(getCurrcntSql).toString());
			BigDecimal arate = new BigDecimal(acBusinessvch.getArate()).divide(new BigDecimal("100"),6,BigDecimal.ROUND_HALF_UP);//执行年利率
			HashMap<String,Object>  inputMap = new HashMap<String,Object>();
			inputMap.put("repayType", "1");// 还款方式
			inputMap.put("amt", afMainInfo.getRepayBal());// 还款本金
			inputMap.put("begDate", afMainInfo.getAppRepaydate());
			inputMap.put("endDate", resetEnddate);
			inputMap.put("repayday", afMainInfo.getRepayday());//还款日，若为空，则贷款发放日为还款日
			inputMap.put("repayFreq", "1");
			inputMap.put("arate", arate);
			inputMap.put("calType", afMainInfo.getCalType());
			inputMap.put("beginAbsFlag", afMainInfo.getBeginAbsFlag());
			inputMap.put("endAbsFlag", afMainInfo.getEndAbsFlag());
			inputMap.put("daoTemplate", daoTemplate);
			inputMap.put("currterm", "0");
		    ArrayList rtnList = new ArrayList();
			ScheduleBuild scheduleBuild = new ScheduleBuild();
			List<ScheduleRecord> rtnArrayList = scheduleBuild.bulid(inputMap);
			Iterator iterator = rtnArrayList.iterator();
			// 删除原还款计划表中的数据
			String sql = "delete from bus_handmaderepay where bankid ='" + bankid + "' and applyno = '" + afMainInfo.getApplyno() + "'";
			daoTemplate.executeSql(sql);
			while (iterator.hasNext()) {
				ScheduleRecord scheduleRecord = (ScheduleRecord) iterator.next();
				HashMap map2 = new HashMap();
				map2.put("currcnt", Integer.valueOf(scheduleRecord.getCurrcnt()));
				map2.put("repbegindate", scheduleRecord.getRepbegindate());
				map2.put("rependdate", scheduleRecord.getRependdate());
				map2.put("curramt", scheduleRecord.getCurramt());
				map2.put("currint", scheduleRecord.getCurrint());
				map2.put("currsum", scheduleRecord.getCurrsum());
				map2.put("repaybal", scheduleRecord.getRepaybal());
				map2.put("updcurrrepdate",scheduleRecord.getRependdate());
				rtnList.add(map2);
				//将还款计划表插入大bus_handmaderepay手工编织过渡表
				busHandmaderepay.setApplyno(afMainInfo.getApplyno());
				busHandmaderepay.setBankid(bankid);
				busHandmaderepay.setCurramt(Double.parseDouble(scheduleRecord.getCurramt().toString()));
				busHandmaderepay.setCurrcnt(Long.valueOf(scheduleRecord.getCurrcnt()));
				busHandmaderepay.setCurrint(Double.parseDouble(scheduleRecord.getCurrint().toString()));
				busHandmaderepay.setCurrsum(Double.parseDouble(scheduleRecord.getCurrsum().toString()));
				busHandmaderepay.setIptBrno(cdbt.getOpBr());
				busHandmaderepay.setIptDate(cdbt.getWorkDate());
				busHandmaderepay.setIptUsr(cdbt.getOpTel());
				busHandmaderepay.setRepaybal(Double.parseDouble(scheduleRecord.getRepaybal().toString()));
				busHandmaderepay.setRepbegindate(scheduleRecord.getRepbegindate());
				busHandmaderepay.setRependdate(scheduleRecord.getRependdate());
				busHandmaderepay.setUpdcurrrepdate(scheduleRecord.getRependdate());
				busHandmaderepay.setFlg("0");
				busHandmaderepayDao.insert(busHandmaderepay);
			}
			pager.setList(rtnList);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("提前还款生成默认自定义还款方式失败！"+ e.getMessage());
			return rdbd;
		}
		rdbd.setRespCode("0000");
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespDesc("提前还款生成默认自定义还款方式EarlyPayQuerySchdAdd执行失败！");
		return rdbd;
	}
}
package tf.mcs.def.handle.overdue;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.comm.OverdueInfoCommon;
import tf.tools.UniversalObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 求和借据历史逾期记录金额
 * <AUTHOR>
 * @date 2022-11-14
 */
public class SumBusinessHisOverdueAmt implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(
			ReqDataBusData reqDataBusData, RespDataBusData respDataBusData) throws Exception {
		UniversalObject universalObject = new UniversalObject(reqDataBusData);
		HashMap<String, Object> requestParamMap = (HashMap<String, Object>)reqDataBusData.get("hashMap");
		requestParamMap.put("bankid", universalObject.getBankid());
		Map<String, Object> result =
				OverdueInfoCommon.sumBusinessHisOverdueAmt(requestParamMap, universalObject.getSession());
		respDataBusData.setRespEntity("hashMap", result.get("data"));
		respDataBusData.setRespCode(result.get("code") + "");
		respDataBusData.setRespDesc(result.get("msg") + "");
		return respDataBusData;
	}
}
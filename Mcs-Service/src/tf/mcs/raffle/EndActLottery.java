package tf.mcs.raffle;

import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.hibernate.Session;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.InvestigationUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 终止抽奖活动
 * <AUTHOR>
 * @Date: 2023/7/4/15:56
 */
public class EndActLottery implements CommonHandlerStub {
    private Logger log = Logger.getLogger(getClass());

    @Override
    public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

        RespDataBusData rdbd = new RespDataBusData();
        // 获取session
        DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
        Session session=arg0.getReqDBPoolservice().getTransSession();
        //公共域获取
        CommonDBT cdbt = arg0.getCommDC();
        String operid = cdbt.getOpTel();
        String workDate = cdbt.getWorkDate();
        String workTime = cdbt.getReqTime();

        // 前台参数获取
        HashMap hashMap = (HashMap) arg0.get("hashMap");
        String lotteryId= MapUtils.getString(hashMap,"lotteryId");//活动编号

        String sql ="";
        try {
            String lotteryEndTime = workDate+" "+workTime;
            Map setMap = new HashMap();
            setMap.put("lottery_sts","3");
            setMap.put("lottery_end_time",lotteryEndTime);
            Map whereMap = new HashMap();
            whereMap.put("lottery_id",lotteryId);
            InvestigationUtil.updateCommonFields(daoTemplate,"act_lottery_info",setMap,whereMap);

            rdbd.setRespCode("0000");
            rdbd.setRespDesc("终止抽奖活动执行成功！");
        }catch (Exception e){
            rdbd.setRespCode("7777");
            rdbd.setRespDesc("终止抽奖活动执行异常！");
            e.printStackTrace();
        }
        return rdbd;
    }
}

package tf.mcs.market.handler;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplate;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;
import tf.mcs.dao.MarketResultDao;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

public class GetPotentialCustResult implements CommonHandlerStub {
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());

	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		RespDataBusData rdbd = new RespDataBusData();
		UniversalObject uo;
		try {
			uo = new UniversalObject(arg0);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespDesc("微贷站点初始化时发生错误");
			rdbd.setRespCode("8888");
			return rdbd;
		}

		Session externalSession = uo.getSession();
		MarketResultDao marketResultDao = new MarketResultDao();
		marketResultDao.setExternalSession(externalSession);

		long bankid = uo.getBankid().intValue();

		DaoTemplate daoTemplate = arg0.getReqDBPoolservice().getDaoTemplate();

		Pager pager = (Pager) arg0.get("pager");
		// modified by 张晓明 2017年10月18日 修改查询条件接收方式
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		whereMap.put("#SQL#", " 1=1 order by t1.cifid DESC");
//    HashMap hashMap = (HashMap)arg0.get("hashMap");
//    String cifid = (String)hashMap.get("cifid");
		try {
			String mainSQL = "SELECT t1.* FROM market_result t1 WHERE t1.bankid=" + bankid;
			daoTemplate.getMapListByLikeSql(mainSQL, whereMap, pager);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9000");
			rdbd.setRespDesc("获取潜在客户营销记录信息时发生错误");
			return rdbd;
		}
		// modified end by 张晓明 2017年10月18日
		for (int i = 0; i < pager.getList().size(); ++i) {
			HashMap hMap = (HashMap) pager.getList().get(i);
			hMap.put("marketManname", Tools.getSessionOpername(bankid, String.valueOf(hMap.get("marketMan"))));
		}
		arg1.setRespCode("0000");
		arg1.setRespDesc("获取潜在客户营销记录信息执行成功！");
		arg1.setRespEntity("pager", pager);
		return arg1;
	}
}
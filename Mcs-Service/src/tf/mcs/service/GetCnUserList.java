package tf.mcs.service;

import java.util.HashMap;
import java.util.Map;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;
/**
 * 根据cn_user_post查询当前岗位的用户信息表
 * <AUTHOR>
 * @date 2017-6-12
 * @ClassName GetCnUserList.java
 * @Company tfrunning
 *
 */
public class GetCnUserList implements CommonHandlerStub {
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");   
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		// 公用域获取
		CommonDBT cdbt = arg0.getCommDC();
		long bankid=cdbt.getiBanks();
		String operid = cdbt.getOpTel();
		String appendsql="";
		HashMap hash=(HashMap)arg0.get("hashMap");
		String postid=String.valueOf(hash.get("postid"));
		String inst=String.valueOf(hash.get("instcode"));
		if(null!=postid&&!"null".equals(postid)&&!"".equals(postid)){
			appendsql +=" and t2.postid='"+postid+"' ";
		}
		if(null!=inst&&!"null".equals(inst)&&!"".equals(inst)){
			appendsql +=" and t2.instcode='"+inst+"' ";//以cn_user_post 表配置的机构为准
		}		
		whereMap.put("#SQL#", "1=1 order by t1.operid");
		
		String getSql = "SELECT t2.operid,t2.postid,t1.opername,t2.instcode,t3.postname FROM  CN_USER t1,cn_user_post t2,cn_post_define t3 " +
				" where t1.status='1' and t1.bankid="+bankid+" and t1.bankid=t2.bankid and t2.bankid=t3.bankid and t1.operid=t2.operid " +
				" and t2.postid=t3.postid and t1.operid<>'"+operid+"' "+appendsql;
		daoTemplate.getMapListByLikeSql(getSql, whereMap, pager);
		
		for(int i=0;i<pager.getList().size();i++){
			HashMap hMap = (HashMap) pager.getList().get(i);
			String instcode=(String)hMap.get("instcode");
			hMap.put("instname", Tools.getSessionInstname(bankid,instcode));
		}
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取柜员信息列表执行成功!");
		return rdbd;
	}
}

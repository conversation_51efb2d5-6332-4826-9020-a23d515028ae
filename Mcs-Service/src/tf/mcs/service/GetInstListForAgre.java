package tf.mcs.service;

import java.util.HashMap;
import java.util.Map;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;

public class GetInstListForAgre implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg) throws Exception {

		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");
		//HashMap hm=(HashMap) arg0.get("hashMap");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		
		CommonDBT cdbt = arg0.getCommDC();
		long bankid=cdbt.getiBanks();
		whereMap.put("#SQL#", " 1=1 order by t1.instcode asc");
		//String postid=(String) hm.get("postid");
		daoTemplate.getMapListBySql("select * from cn_basicinfo t1 where t1.bankid="+bankid,whereMap, pager);
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取机构列表执行成功!");
		return rdbd;
	}

}

package tf.mcs.service;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.CnApprovalCustrelDao;
import tf.mcs.model.CnApprovalCustrel;
import tf.mcs.model.CnApprovalDef;
import tf.tools.StringTools;
/**
 * @desc 新增或修改审贷权限人员关联
 * <AUTHOR>
 * @date ********
 */
public class UpdateApprovalCustrel implements CommonHandlerStub{

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		DaoTemplateImpl daoImpl = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Session session = arg0.getReqDBPoolservice().getTransSession();
		CommonDBT cdbt = arg0.getCommDC();
		//获取前台参数
		long bankid = cdbt.getiBanks();
		CnApprovalCustrel cnApprovalCustrel = (CnApprovalCustrel) arg0.get("cnApprovalCustrel");
		//业务处理
		CnApprovalCustrelDao approvalCustrelDao = new CnApprovalCustrelDao();
		approvalCustrelDao.setExternalSession(session);
		cnApprovalCustrel.setBankid(bankid);
		
		CnApprovalCustrel cnApprovalCustrelOld = new CnApprovalCustrel();
		try {
			cnApprovalCustrelOld = approvalCustrelDao.getEntityByPK(cnApprovalCustrel);
		} catch (Exception e) {

			StringTools.stackLog(e);
			arg1.setRespCode("9998");
			arg1.setRespDesc("获取权限人员关联失败！");
			return arg1;
			
		}
		String desc ="";
		if(cnApprovalCustrelOld==null){
			approvalCustrelDao.insert(cnApprovalCustrel);
			desc ="新增审贷权限人员关联成功！";
		}else {
			tf.core.utils.func.DomainHelper.copyProperty(cnApprovalCustrel, cnApprovalCustrelOld);
			approvalCustrelDao.update(cnApprovalCustrelOld);
			desc ="修改审贷权限人员关联成功！";
		}
		arg1.setRespCode("0000");
		arg1.setRespDesc(desc);
		return arg1;
	}

}

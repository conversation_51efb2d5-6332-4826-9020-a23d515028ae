package tf.mcs.service;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.dao.CnBasicinfoDao;
import tf.mcs.model.CnBasicinfo;

public class DeleteBasicinfo implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		CnBasicinfoDao basicinfoDao=new CnBasicinfoDao();
		basicinfoDao.setExternalSession(arg0.getReqDBPoolservice().getTransSession());
		CnBasicinfo cnBasicinfo=(CnBasicinfo)arg0.get("cnBasicinfo");
		
		CommonDBT cdbt = arg0.getCommDC();
		long bankid=cdbt.getiBanks();
		cnBasicinfo.setBankid(bankid);
		basicinfoDao.delete(cnBasicinfo);
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("删除执行成功!");
		return rdbd;
	}

}

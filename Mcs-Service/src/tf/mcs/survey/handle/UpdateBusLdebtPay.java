package tf.mcs.survey.handle;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.PublicMethods;
import tf.mcs.dao.BusLdebtPayDao;
import tf.mcs.model.BusLdebtPay;

/**
 * @description 
 * <AUTHOR>
 * @date 2020年7月10日
 */
public class UpdateBusLdebtPay implements CommonHandlerStub {
	
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		BRC.getLog().info(arg0);
		RespDataBusData rdbd = new RespDataBusData();
		// 获取session
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		BusLdebtPay busLdebtPay = (BusLdebtPay)arg0.get("busLdebtPay");
		String applyno = busLdebtPay.getApplyno();
		String cifid = busLdebtPay.getCifid();
		if (busLdebtPay==null||applyno==null||cifid==null) {
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取前台表单信息失败!");
			return rdbd;
		}
		BusLdebtPayDao busLdebtPayDao = new BusLdebtPayDao();
		busLdebtPayDao.setExternalSession(externalSession);
		
		String serid = (String)busLdebtPay.getSerid();
		if ("".equals(serid)||serid==null) {
			// 获取公共参数
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();
			String workDate = cdbt.getWorkDate();
			Map<String, String> templateMap = new HashMap<String, String>();
			templateMap.put("date", workDate);
			// 流水号
			serid = PublicMethods.getCnSeqnumByTemplate(bankid,"smebus", externalSession, templateMap);		
			busLdebtPay.setSerid(serid);
			try {
				busLdebtPayDao.insert(busLdebtPay);
			} catch (Exception e) {
				e.printStackTrace();
				rdbd.setRespCode("9999");
				rdbd.setRespDesc("新增企业长期负债应付/预收信息失败!");
				return rdbd;
			}
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("新增企业长期负债应付/预收信息成功!");
			return rdbd;
		}
		try {
			BusLdebtPay queryResult = busLdebtPayDao.getEntityByPK(busLdebtPay);
			if (queryResult==null) {
				busLdebtPayDao.insert(busLdebtPay);
				rdbd.setRespCode("0000");
				rdbd.setRespDesc("新增企业长期负债应付/预收信息成功!");
				return rdbd;
			}else {
				String mainSql = "update bus_ldebt_pay set ldebtitem='"+busLdebtPay.getLdebtitem()+"',"
						+ " ldebtsum='"+busLdebtPay.getLdebtsum()+"',"
						+ " lrepaydate='"+busLdebtPay.getLrepaydate()+"',"
						+ " lgetvaluedesc='"+busLdebtPay.getLgetvaluedesc()+"'"
						+ " where applyno='"+applyno+"'"
						+ " and serid='"+serid+"'";
				daoTemplate.executeSql(mainSql);
				rdbd.setRespCode("0000");
				rdbd.setRespDesc("编辑企业长期负债应付/预收信息成功!");
				return rdbd;
			}
		} catch (Exception e) {
			e.printStackTrace();
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("编辑企业短期负债应付/预收信息失败!");
			return rdbd;
		}
	}

}

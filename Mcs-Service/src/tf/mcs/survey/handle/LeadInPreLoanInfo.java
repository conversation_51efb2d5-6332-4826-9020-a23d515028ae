package tf.mcs.survey.handle;

import org.apache.log4j.Logger;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.dao.DaoTemplate;
import tf.ifms.rpc.handler.IfmsTool;
import tf.tools.UniversalObject;

/**
 * SME贷前报告审批复录-引入贷前录入信息
 * <AUTHOR>
 * @date 2023-11-09
 */
public class LeadInPreLoanInfo extends HibernateDaoSupport implements CommonHandlerStub {

	private final Logger log = Logger.getLogger(LeadInPreLoanInfo.class);

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData reqDataBusData, RespDataBusData respDataBusData) {
		RespDataBusData result = new RespDataBusData();

		try{
			String applyNo = reqDataBusData.get("applyno") + "";//申请编号

			if(IfmsTool.isNotNull(applyNo)){
				UniversalObject universalObject = new UniversalObject(reqDataBusData);
				long bankId = universalObject.getBankid();
				DaoTemplate daoTemplate = universalObject.Dao();

				//1.检查是否有贷前检查信息
				String countBusLoanCheckSql = "SELECT COUNT(1)\n" +
						"FROM BUS_LOAN_CHECK\n" +
						"WHERE BANKID = " + bankId + "\n" +
						"AND APPLYNO = '" + applyNo + "'\n";
				int busLoanCheckNum = daoTemplate.getUniqueValueBySql(countBusLoanCheckSql, Integer.class);

				//因为是主键查询,所以直接判断是否有一条数据即可
				if(1 == busLoanCheckNum){

					//2.删除-BUS_LOAN_CHECK_APPR
					delTableDataByApplyNo("BUS_LOAN_CHECK_APPR", bankId, applyNo, daoTemplate);

					//3.同步-BUS_LOAN_CHECK_APPR
					String addLoanCheckApproveSql = "INSERT INTO BUS_LOAN_CHECK_APPR(\n" +
							getLoanCheckFieldSql() +
							")SELECT\n" +
							getLoanCheckFieldSql() +
							"FROM BUS_LOAN_CHECK\n" +
							"WHERE BANKID = " + bankId + "\n" +
							"AND APPLYNO = '" + applyNo + "'";
					daoTemplate.executeSql(addLoanCheckApproveSql);

					//4.删除-BUS_DOE_OWNER_APPR
					delTableDataByApplyNo("BUS_DOE_OWNER_APPR", bankId, applyNo, daoTemplate);

					//5.同步-BUS_DOE_OWNER_APPR
					String addDoeOwnerApproveSql = "INSERT INTO BUS_DOE_OWNER_APPR(\n" +
							getDoeOwnerFieldSql() +
							")SELECT\n" +
							getDoeOwnerFieldSql() +
							"FROM BUS_DOE_OWNER\n" +
							"WHERE BANKID = " + bankId + "\n" +
							"AND APPLYNO = '" + applyNo + "'\n" +
							"AND ISPRELOAN = '1'\n";
					daoTemplate.executeSql(addDoeOwnerApproveSql);

					//6.删除-BUS_DEBT_PAY_APPR
					delTableDataByApplyNo("BUS_DEBT_PAY_APPR", bankId, applyNo, daoTemplate);

					//7.同步-BUS_DEBT_PAY_APPR
					String addDebtPayApproveSql = "INSERT INTO BUS_DEBT_PAY_APPR(\n" +
							getDebtPayFieldSql() +
							")SELECT\n" +
							getDebtPayFieldSql() +
							"FROM BUS_DEBT_PAY\n" +
							"WHERE BANKID = " + bankId + "\n" +
							"AND APPLYNO = '" + applyNo + "'\n" +
							"AND ISPRELOAN = '1'\n";
					daoTemplate.executeSql(addDebtPayApproveSql);

					//8.删除-BUS_OTH_DEBT_APPR
					delTableDataByApplyNo("BUS_OTH_DEBT_APPR", bankId, applyNo, daoTemplate);

					//9.同步-BUS_OTH_DEBT_APPR
					String addOthDebtApproveSql = "INSERT INTO BUS_OTH_DEBT_APPR(\n" +
							getOthDebtFieldSql() +
							")SELECT\n" +
							getOthDebtFieldSql() +
							"FROM BUS_OTH_DEBT\n" +
							"WHERE BANKID = " + bankId + "\n" +
							"AND APPLYNO = '" + applyNo + "'\n" +
							"AND ISPRELOAN = '1'\n";
					daoTemplate.executeSql(addOthDebtApproveSql);

					//10.删除-FAM_DEBT_MSG_APPR
					delTableDataByApplyNo("FAM_DEBT_MSG_APPR", bankId, applyNo, daoTemplate);

					//11.同步-FAM_DEBT_MSG_APPR
					String addFamDebtMsgApproveSql = "INSERT INTO FAM_DEBT_MSG_APPR(\n" +
							getFamDebtMsgFieldSql() +
							")SELECT\n" +
							getFamDebtMsgFieldSql() +
							"FROM FAM_DEBT_MSG\n" +
							"WHERE BANKID = " + bankId + "\n" +
							"AND APPLYNO = '" + applyNo + "'\n" +
							"AND ISPRELOAN = '1'\n";
					daoTemplate.executeSql(addFamDebtMsgApproveSql);
					result.setRespCode("0000");
					result.setRespDesc("引入贷前录入信息成功！");
				}else{
					result.setRespCode("9999");
					result.setRespDesc("未查询到贷前检查数据,引入贷前录入信息失败！");
				}
			}else{
				result.setRespCode("8888");
				result.setRespDesc("引入贷前录入信息参数缺失！");
			}
		}catch (Exception e) {
			e.printStackTrace();
			log.error("引入贷前录入信息服务器异常{}", e);
			result.setRespCode("7777");
			result.setRespDesc("引入贷前录入信息失败！");
		}
		return result;
	}

	/**
	 * 根据申请编号删除表数据
	 * @param tableName			表名称
	 * @param bankId			法人编号
	 * @param applyNo			申请编号
	 * @param daoTemplate		数据持久对象
	 */
	private void delTableDataByApplyNo(String tableName, long bankId, String applyNo, DaoTemplate daoTemplate){
		String delSql = "DELETE FROM " + tableName + "\n" +
				"WHERE BANKID = " + bankId + "\n" +
				"AND APPLYNO = '" + applyNo + "'";
		daoTemplate.executeSql(delSql);
	}

	/**
	 * 获取贷前调查表字段
	 * @return
	 */
	private String getLoanCheckFieldSql(){
		return "\tBANKID, APPLYNO, CIFID, IPT_USR, IPT_BRNO,\n" +
				"\tIPT_DATE, PRDT_NO, ISLOANPURPOSE, ISREALEXIST,\n" +
				"\tISOPREQUIRE, ISHOUSE, CROPNAME, ISGUARHOUSE,\n" +
				"\tISCOLEXIST, CERREAL, INVESTADVICE, INDUSTRYRACE,\n" +
				"\tBUSDEVBRF, ENFORCERECORD, GUARHOUSERECORD,\n" +
				"\tCORPORATEREL, BUSCHANGEBRF, OTHINSTRUCTION,\n" +
				"\tASSETSDEBTRATE, BUSASSETSDEBTRATE, FAMASSETSDEBTRATE,\n" +
				"\tAFTALLDEBTRATE, ASSETSCHANGE, MOPERATEINCOME,\n" +
				"\tMCOSTTOTAL, MPAYFEE, MLOANPAY, MNETINCOME,\n" +
				"\tVERINCOMEDESC, VERCOSTDESC, PAYFEEDESC,\n" +
				"\tOTH_INCOMEDESC, MUSEANALYSIS, PERSONALEVAL,\n" +
				"\tSTABILITYDESC, LOANPURPOSE, APPSUM, APPTERM,\n" +
				"\tREQFUNDTOTAL, PJFSBLANALYSIS, ADVICESUM,\n" +
				"\tADVICETERM, MSUPPLYSUM, MSUPPLYRATE, PRELOANADVICE,\n" +
				"\tSTABILITYBRF, SURVEY_DATE, LOAN_TIMES, OVERDUEDAYSTOTAL,\n" +
				"\tDOE_CLINAME, LEGALPERSON, LEGALPERSONHOUSEHOLD,\n" +
				"\tOPER_DATE, OPER_ADDR_SPETERM, REG_SUM, PAID_SUM,\n" +
				"\tMAIN_BUS, DOEADDR, IS_MAR, AGE, BUS_SPETERM,\n" +
				"\tCASHDEPOSITTOTAL, CASHDESC, RECEIVETOTAL, RECEIVEDESC,\n" +
				"\tSTOCKTOTAL, STOCKDESC, ADVPAYTOTAL, ADVPAYDESC,\n" +
				"\tFIXEDPRICE_SUM, FIXEDPRICEDESC, ALL_EQM_SUM, EQMDESC,\n" +
				"\tCARPRICE_SUM, CARPRICEDESC, OTHTOTAL, OTHTOTALDESC,\n" +
				"\tBUSASSETSTOTAL, HOUSE_SUM, HOUSR_SUMDESC, OTHER_SUM,\n" +
				"\tOTHER_SUMDESC, FAM_ASSETS, UNVERIFIED_ASSETS,\n" +
				"\tSETTLEMENTBUS_DEBTDESC, BUSDEBTTOTAL, BUSDEBTCHANGE,\n" +
				"\tFAMDEBTCHANGE, SETTLEMENTFAM_DEBTDESC, ALLBAL,\n" +
				"\tALLREPAYSUM, ALLASSETSTOTAL, ALLDEBTTOTAL, INCOMETOTAL,\n" +
				"\tINCOMETOTALDESC, COSTTOTAL, COSTTOTALDESC, FEE_TOTAL,\n" +
				"\tMONTHLY_LOAN_REPAYMENT, BUSMONTHFEEDESC, PAY_SUM,\n" +
				"\tFAMDEBT_PAY, FAMMONTHFEEDESC, OTH_INCOME, REPAYTYPE,\n" +
				"\tLOAN_RATE, PRE_LOAN_OPERNAME, CSTYPE, ADVISEGUARTYPE,\n" +
				"\tISCOBOHOUSE, NORMAL_BUSINESS_HOURS, IS_TEMP,\n" +
				"\tCOLLATERAL_NAME, PURPOSE_DETAIL, OTH_OPERATING_COST,\n" +
				"\tOTH_OPERATING_COSTDESC, PRELOAN_MESSAGE\n";
	}

	/**
	 * 获取业务经营实体表字段
	 * @return
	 */
	private String getDoeOwnerFieldSql(){
		return "\tBANKID, APPLYNO, CIFID, OWN_CIFID, OWN_OUTCIFID,\n" +
				"\tCLINAME, CSTYPE, INDUSTRYTYPE, SHARE_RATIO, SHARE_SUM,\n" +
				"\tSHARE_DATE, SERID, SHAREHOLDER_NAME, POSITION, REMARK,\n" +
				"\tCREDITINFO, CERTNO, CERTTYPE, MTEL, SHARETYPE,\n" +
				"\tSUBSCRIBED_CAPITAL, SUBSCRIPTION_DATE, ISPRELOAN\n";
	}

	/**
	 * 获取企业负债应付/预收表字段
	 * @return
	 */
	private String getDebtPayFieldSql(){
		return "\tBANKID, APPLYNO, CIFID, SERID, DEBTITEM, CREDITOR,\n" +
				"\tDEBTSUM, REPAYDATE, BUSLITIGATION_SUM, TERM_TYPE,\n" +
				"\tSTATE, ISPRELOAN, BRF\n";
	}

	/**
	 * 获取企业负债金融机构/民间负债表字段
	 * @return
	 */
	private String getOthDebtFieldSql(){
		return "\tBANKID, APPLYNO, CIFID, SERID, BORROWER, FSINSTCODE,\n" +
				"\tRECOVERSUM, BEGDATE, ENDDATE, BALSUM, SUPPLYSUM, RISK_TYPE,\n" +
				"\tTERM_TYPE, GUARTYPE, SUPPLEMENTARY_DESC, ISPRELOAN, BRF,\n" +
				"\tBANKNAME, FROMCREDIT, COINSERID, ISOPERATE, CLASSIFY,\n" +
				"\tTWENTYFOURMONTHS_OVERTIMES, TWENTYFOURMONTHS_MAXSTAGE, OVER_SUM\n";
	}

	/**
	 * 获取SME家庭负债信息表字段
	 * @return
	 */
	private String getFamDebtMsgFieldSql(){
		return "\tBANKID, APPLYNO, CIFID, LOANBRNO, LOANTYPE, LOANSUM,\n" +
				"\tBEGDATE, ENDDATE, BALSUM, MREPAYSUM, SERID, GUARTYPE,\n" +
				"\tCLASSIFY, DEBT_TYPE, DEBTOR, ISPRELOAN, BRF, BANKNAME,\n" +
				"\tFROMCREDIT, COINSERID, ISOPERATE, RISK_TYPE, OVER_SUM,\n" +
				"\tTWENTYFOURMONTHS_OVERTIMES, TWENTYFOURMONTHS_MAXSTAGE\n";
	}
}
package tf.mcs.comm;

import java.util.HashMap;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.utils.hb.model.Pager;
import tf.tools.StringTools;

/**
 * <AUTHOR>
 * @function 查询通讯流水记录列表
 * @date ********
 * @version v1.0
 */
public class GetTrsCommRecordList implements CommonHandlerStub{

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		BRC.getLog().info("进入查询通讯流水记录列表" + this.getClass().getName() + "类");
		CommonDBT cdbt = arg0.getCommDC();
		long bankid = cdbt.getiBanks();
		HashMap inputMap = (HashMap) arg0.get("hashMap");
		Pager pager = (Pager) arg0.get("pager");
		String serid = inputMap.get("serid").toString();
		DaoTemplate daoTemplate = arg0.getReqDBPoolservice().getDaoTemplate();
		String getTrsCommRecordSql = "select t1.*, decode(site,'1','微贷总账','2','核心系统') as site_name from trs_comm_record t1 where bankid = " + bankid +" and serid = '" + serid + "'";
		
		try {
			daoTemplate.getMapListBySql(getTrsCommRecordSql, pager);
		} catch (Exception e) {
			StringTools.stackLog(e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("查询通讯流水记录列表" + this.getClass().getName() + "执行失败");
			return arg1;
		}
		arg1.setRespEntity("pager", pager);
		arg1.setRespCode("0000");
		arg1.setRespDesc("查询通讯流水记录列表执行成功");
		return arg1;
	}

}

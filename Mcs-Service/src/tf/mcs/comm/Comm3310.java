
package tf.mcs.comm;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.gl.impl.ParmsofTrsDefinition;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.flowmanage.BusinessProcessManager;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.AcLnrepayschdDao;
import tf.mcs.dao.AfLnrepayschdDao;
import tf.mcs.dao.AfMainInfoDao;
import tf.mcs.dao.BusHandmaderepayDao;
import tf.mcs.dao.ComRateDao;
import tf.mcs.model.AcBusinessvch;
import tf.mcs.model.AcLnrepayschd;
import tf.mcs.model.AfLnrepayschd;
import tf.mcs.model.AfMainInfo;
import tf.mcs.model.ComRate;
import tf.tools.Arith;
import tf.tools.EarlyRepaySchedulebk;
import tf.tools.McsWorkTools;
import tf.tools.ScheduleBuild;
import tf.tools.ScheduleCheck;
import tf.tools.ScheduleRecord;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * 功能说明：贷款利率变更业务，1.向还款计划表（ac_lnrepayschd）新增还款计划；2. 备份还款计划表af_lnrepayschd
 * 
 * <AUTHOR>
 * 
 * @date 20150729
 * @version v1.3 wangwei 20151013 适用于铁岭
 */
public class Comm3310 implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		BRC.getLog().info("传入参数为" + arg0);
		BRC.getLog().info("=========进入" + this.getClass().getName() + ",传入参数为：" + arg0);
		// 定义返回值
		RespDataBusData rdbd = new RespDataBusData();
		HashMap rtnMap = new HashMap();
		// 定义通用对象
		UniversalObject uo;
		try {
			// 完全初始化通用对象
			uo = new UniversalObject(arg0);
		} catch (Exception e2) {
			e2.printStackTrace();
			log.error(e2.getMessage(), e2);
			rdbd.setRespCode("8888");
			rdbd.setRespDesc("微贷站点初始化时发生错误");
			return rdbd;
		}
	
		try {
			// 校验当前系统状态是否可以发生记账交易
			uo.logInfo("----------校验系统记账状态开始----------");
			uo.checkSystemSts();
			uo.logInfo("----------校验系统记账状态成功----------");
		} catch (Exception e3) {
			uo.logError("----------校验系统记账状态失败,不允许发生记账交易----------");
			e3.printStackTrace();
			log.error(e3.getMessage(), e3);
			rdbd.setRespCode("7000");
			rdbd.setRespDesc("当前系统状态不允许发生记账交易");
			return rdbd;
		}
		//变量定义
		long bankid = uo.getBankid();
		String workDate = uo.getServiceDate();
		String operid = uo.getOptUser();
		String brno = uo.getOptBrno();
		String accountno="";//还款账号
		double rate = 0d;//执行利率
		double changerRate = 0d;//变更后利率
		String vchno ="";//借据编号
		double overDuerate=0d;//原逾期利率
		AfMainInfo afMainInfo = new AfMainInfo();	
		ParmsofTrsDefinition rtnPtd;	
//		uo.initialTrsCommLog(this.getClass().getName());
		// 业务处理
		try {
			// 变量初始化
			String applyno = "";
			String aut = "";
			// 前台参数获取
			try {
				HashMap hashMap = (HashMap) arg0.get("hashMap");
				applyno = hashMap.get("applyno").toString();
				aut = (String) hashMap.get("aut");
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("9000");
				rdbd.setRespDesc("获取前台信息发生错误");
				return rdbd;
			}
			// 获取session
			Session externalSession = uo.getSession();
			AcLnrepayschdDao acLnrepayschdDao = new AcLnrepayschdDao();
			acLnrepayschdDao.setExternalSession(externalSession);
			AfLnrepayschdDao afLnrepayschdDao = new AfLnrepayschdDao();
			afLnrepayschdDao.setExternalSession(externalSession);
			AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
			acBusinessvchDao.setExternalSession(externalSession);
			AfMainInfoDao afMainInfoDao = new AfMainInfoDao();
			afMainInfoDao.setExternalSession(externalSession);
			BusHandmaderepayDao busHandmaderepayDao = new BusHandmaderepayDao();
			busHandmaderepayDao.setExternalSession(externalSession);
			DaoTemplate daoTemplate = uo.Dao();
			
			// 定义当期还款计划表变量
			AcLnrepayschd currAcLnrepayschd = new AcLnrepayschd();
			double overRateFlt= 0d;
			double cmpdFloat= 0d;
			try {
				afMainInfo.setBankid(bankid);
				afMainInfo.setApplyno(applyno);
				afMainInfo = afMainInfoDao.getEntityByPK(afMainInfo);
				vchno = afMainInfo.getVchno();
				changerRate = afMainInfo.getExtrate();
				overRateFlt=Double.valueOf(afMainInfo.getOverDuefloat());
				cmpdFloat=Double.valueOf(afMainInfo.getCmpdFloat());
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("9400");
				rdbd.setRespDesc("获取贷后变更申请主表数据执行失败");
				return rdbd;
			}
			
			// 初始化交易信息表
			AcBusinessvch acBusinessvch = new AcBusinessvch();
			acBusinessvch.setBankid(bankid);
			acBusinessvch.setVchno(vchno);
			try{
				acBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
			} catch (Exception e2) {
				e2.printStackTrace();
				log.error(e2.getMessage(), e2);
				arg1.setRespCode("9990");
				arg1.setRespDesc("获取借据信息时发生错误");
				return arg1;
			}
			String dcbrno = uo.getDcBrno(acBusinessvch.getBalInstcode());
		
			// 判断流程是否结束，如果结束，则无法落实变更
			String checkFlowSql =
					"select count(t1.bankid) from de_flowrecord t1 where t1.bankid='" + bankid + "' and t1.applyno ='" + applyno
							+ "' and ifnull(t1.enddate,'')=''";
			try {
				int checkFlowCount = Integer.parseInt(daoTemplate.getUniqueValueBySql(checkFlowSql).toString());
				if (checkFlowCount == 0) {
					rdbd.setRespCode("8100");
					rdbd.setRespDesc("本次业务变更流程已结束，不可落实变更!");
					return rdbd;
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("8200");
				rdbd.setRespDesc("判断流程是否结束时发生错误！");
				return rdbd;
			}

			//获取操作日所处于期次
			String sqlCurrcnt ="select nvl(min(currcnt),0) from ac_lnrepayschd where bankid = " + bankid + " and vchno ='" + afMainInfo.getVchno()+ "' and updcurrrepdate > '" + workDate + "' and REPAYSTATE!='1'  and state ='1'";
			long currcnt = Integer.parseInt(daoTemplate.getUniqueValueBySql(sqlCurrcnt).toString());

			//删除原来还款计划表需变化期次和插入变更后的还款计划表
			// 处理之后几期的还款计划表
			try{
				//king 2017/03/02 处理当期还款计划
				currAcLnrepayschd.setBankid(bankid);
				currAcLnrepayschd.setVchno(vchno);
				currAcLnrepayschd.setCurrcnt(currcnt);
				currAcLnrepayschd = acLnrepayschdDao.getEntityByPK(currAcLnrepayschd);
				if(currAcLnrepayschd==null){
					rdbd.setRespCode("9800");
					rdbd.setRespDesc("无法获取相应还款期次 ");
					return rdbd;
				}


				
				List<Map<String,Object>> rtnScheduleList = new ArrayList<Map<String,Object>>();
				ParmsofTrsDefinition ptd = new ParmsofTrsDefinition();
				ptd.setStringParm("txDate", workDate);
				Map<String, Object> inputMap = new HashMap<>();
				ScheduleBuild scheduleBuild = new ScheduleBuild();
				double amt = Arith.add(currAcLnrepayschd.getRepaybal(), currAcLnrepayschd.getUpdcurramt());
				inputMap.put("calType", acBusinessvch.getCalType());//// 利息计算方式：1按实际天数计息，2月份按30天计息
				inputMap.put("amt", amt);// 剩余本金
				inputMap.put("begDate", currAcLnrepayschd.getRepbegindate());// 当前日期
				inputMap.put("endDate", acBusinessvch.getEnddate());// 到期日
				BigDecimal arate = new BigDecimal(afMainInfo.getExtrate()).divide(new BigDecimal("100"),6,BigDecimal.ROUND_HALF_UP);//执行年利率
				inputMap.put("arate", arate);// 执行年利率
//				String repayday = acBusinessvch.getRepayday();
//				if (repayday != null && repayday.length() == 1) {
//					repayday = "0" + repayday;
//				}
				String repayType=acBusinessvch.getRepayType();
				inputMap.put("repayday", currAcLnrepayschd.getRepbegindate());// 还款日
				inputMap.put("repayType", repayType);// 还款方式
				inputMap.put("repayFreq", acBusinessvch.getRepayFreq());// 还款间隔
				//净息还款 额外传入字段
			    inputMap.put("intTerm", acBusinessvch.getSpeTerm());
				inputMap.put("intType", acBusinessvch.getSpeType());
	            //气球贷 额外传入字段
				inputMap.put("calTerm", acBusinessvch.getSpeTerm());//气球贷计算期次
				inputMap.put("calRepayType", acBusinessvch.getSpeType());//气球贷还款方式-只允许1等额本息2等额本金
				//暂只处理手工编制
				List<Map> repayList = new ArrayList<Map>();
				if("6".equals(repayType)) {
					repayList = daoTemplate.getMapListBySql("select currcnt as assign_no,updcurrrepdate as assign_enddate,updCURRAMT as assign_amt from ac_lnrepayschd where vchno='"+vchno+"' and currcnt>='"+currcnt+"' and bankid='"+bankid+"' ");
					inputMap.put("repayList", repayList);
				}
				if(repayList.size()>0||!"6".equals(repayType)) {
					inputMap.put("beginAbsFlag", acBusinessvch.getBeginAbsFlag()); //暂不考虑
					inputMap.put("endAbsFlag", acBusinessvch.getEndAbsFlag());  //暂不考虑
					inputMap.put("txDate", workDate);
					inputMap.put("daoTemplate", daoTemplate);
					inputMap.put("currterm", "0");
					List<ScheduleRecord> schdList = scheduleBuild.bulid(inputMap);	
					ScheduleCheck scheduleCheck = new ScheduleCheck();
					//scheduleCheck.check(schdList, inputMap,ptd);
					for (int i = 0; i < schdList.size(); i++) {
						ScheduleRecord scheduleRecord = schdList.get(i);
						Map<String, Object> termRecord = new HashMap<>();
						termRecord.put("currcnt", scheduleRecord.getCurrcnt() + currcnt-1);
						termRecord.put("curramt", scheduleRecord.getCurramt());
						termRecord.put("currint", scheduleRecord.getCurrint());
						termRecord.put("currsum", scheduleRecord.getCurrsum());
						termRecord.put("repaybal", scheduleRecord.getRepaybal());
						termRecord.put("repbegindate", scheduleRecord.getRepbegindate());
						termRecord.put("rependdate", scheduleRecord.getRependdate());
						termRecord.put("payamt", BigDecimal.ZERO);
						termRecord.put("payint", BigDecimal.ZERO);
						termRecord.put("repaystate", "0");
						termRecord.put("provint", BigDecimal.ZERO);
						termRecord.put("bookedprovint", BigDecimal.ZERO);
						termRecord.put("outProvInt", BigDecimal.ZERO);
						rtnScheduleList.add(termRecord);
					}
				}
				String deleteSql ="delete from ac_lnrepayschd where bankid = " + bankid + " and vchno = '" + vchno + "' and currcnt >='" + currcnt+ "'";
				daoTemplate.executeSql(deleteSql);
				for(int i=0 ;i<rtnScheduleList.size();i++) {
					Map<String,Object> scheMap = rtnScheduleList.get(i);
					AcLnrepayschd acLnrepayschdtmp = new AcLnrepayschd();
					acLnrepayschdtmp.setAcid(currAcLnrepayschd.getAcid());
					acLnrepayschdtmp.setAcSeqn(currAcLnrepayschd.getAcSeqn());
					acLnrepayschdtmp.setBankid(currAcLnrepayschd.getBankid());
					acLnrepayschdtmp.setAcno(currAcLnrepayschd.getAcno());
					acLnrepayschdtmp.setContno(currAcLnrepayschd.getContno());
					acLnrepayschdtmp.setVchno(currAcLnrepayschd.getVchno());
					acLnrepayschdtmp.setInstcode(currAcLnrepayschd.getInstcode());
					acLnrepayschdtmp.setOperid(currAcLnrepayschd.getOperid());
					acLnrepayschdtmp.setOpdate(workDate);
					acLnrepayschdtmp.setIntstSts("1");
					acLnrepayschdtmp.setState("1");
					acLnrepayschdtmp.setRepaystate("0");
					acLnrepayschdtmp.setLoInd("0");
					acLnrepayschdtmp.setInOutFlg(currAcLnrepayschd.getInOutFlg());
					acLnrepayschdtmp.setPaysum(0d);
					acLnrepayschdtmp.setPayint(0d);
					double Intst=0;
					double provAcm = currAcLnrepayschd.getProvInt(); //当期计提基数
					double provBal = currAcLnrepayschd.getProvBal(); //当期计提利息金额
					if(i==0) {
						BigDecimal currint=new BigDecimal(McsWorkTools.sub_ln_Intst(arate.doubleValue(), "0", amt, workDate, scheMap.get("rependdate").toString(), 0)).add(new BigDecimal(currAcLnrepayschd.getProvBal()));
						Intst = currint.doubleValue();	
						acLnrepayschdtmp.setProvInt(provAcm); //计提利息积数
						acLnrepayschdtmp.setProvBal(provBal); //计提利息金额
					}else {
						Intst = Double.parseDouble(scheMap.get("currint").toString());
						acLnrepayschdtmp.setProvInt(0d); //计提利息积数
						acLnrepayschdtmp.setProvBal(0d); //计提利息金额
					}
					acLnrepayschdtmp.setCurrcnt(Long.valueOf(scheMap.get("currcnt").toString()));
					acLnrepayschdtmp.setCurramt(Double.parseDouble(scheMap.get("curramt").toString()));
					acLnrepayschdtmp.setCurrint(Intst);
					acLnrepayschdtmp.setCurrsum(Arith.add(acLnrepayschdtmp.getCurramt(),acLnrepayschdtmp.getCurrint()));
					acLnrepayschdtmp.setRepaybal(Double.parseDouble(scheMap.get("repaybal").toString()));
					acLnrepayschdtmp.setRepbegindate(scheMap.get("repbegindate").toString());
					acLnrepayschdtmp.setRependdate(scheMap.get("rependdate").toString());
					acLnrepayschdtmp.setUpdcurramt(acLnrepayschdtmp.getCurramt());
					acLnrepayschdtmp.setUpdcurrint(acLnrepayschdtmp.getCurrint());
					acLnrepayschdtmp.setUpdcurrrepdate(acLnrepayschdtmp.getRependdate());
					acLnrepayschdtmp.setUpdcurrsum(acLnrepayschdtmp.getCurrsum());
					acLnrepayschdtmp.setCurrrepdate(acLnrepayschdtmp.getRependdate());
					acLnrepayschdDao.insert(acLnrepayschdtmp);
				}
			}catch(Exception e){
				log.error(e.getMessage(), e);
				rdbd.setRespCode("8100");
				rdbd.setRespDesc("处理还款计划表错误");
				return rdbd;
			}
			//处理借据信息
			double rateFloat=0d;
			try{
				AcBusinessvch oldAcBusinessvch = new AcBusinessvch();
				oldAcBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
				// 更新执行利率
				acBusinessvch.setArate(changerRate);
				//king 2017/04/04 计算浮动比例
				rateFloat = afMainInfo.getRateFloat();//浮动比例
				DecimalFormat df = new DecimalFormat("#0.00");
				rateFloat=Double.parseDouble(df.format(rateFloat));//浮动比例格式化
				acBusinessvch.setRateFloat(rateFloat);
				//更新逾期利率 overRateFlt 

				double overRate = Arith.add(changerRate,Arith.div(Arith.mul(changerRate,overRateFlt),100));
				acBusinessvch.setOverDuefloat(overRateFlt);
				acBusinessvch.setOverDuerate(overRate);
				//更新挪用利率
				double fineRate = Arith.add(changerRate,Arith.div(Arith.mul(changerRate,acBusinessvch.getFineFloat()),100));
				acBusinessvch.setFineRate(fineRate);
				//更新挤占利率
				double occRate = Arith.add(changerRate,Arith.div(Arith.mul(changerRate,acBusinessvch.getOccFloat()),100));
				acBusinessvch.setOccRate(occRate);
				//更新复利利率cmpdFloat
				double cmpdRate = Arith.add(changerRate,Arith.div(Arith.mul(changerRate,cmpdFloat),100));	
				acBusinessvch.setCmpdRate(cmpdRate);
				acBusinessvch.setCmpdFloat(cmpdFloat);
				// 更新ipr利率基点
				acBusinessvch.setRatePoint(afMainInfo.getRatePoint());
				acBusinessvch.setFloatType(afMainInfo.getRateChgInd());
				tf.core.utils.func.DomainHelper.copyProperty(acBusinessvch, oldAcBusinessvch);
				acBusinessvchDao.update(oldAcBusinessvch);
			}catch(Exception e){
				log.error(e.getMessage(), e);
				rdbd.setRespCode("8000");
				rdbd.setRespDesc("更新借据信息时发生错误");
				return rdbd;
			}

			// 定义总账传值结构体
			ParmsofTrsDefinition ptd = new ParmsofTrsDefinition();
			/********* 参数赋值 *********/
			ptd.setTrsCode("3310");
			ptd.setStringParm("bankid", String.valueOf(bankid));
			ptd.setStringParm("workdate", workDate);
			ptd.setStringParm("txdate", workDate);
			ptd.setStringParm("optuser", operid);
			ptd.setStringParm("chkuser", operid);
			ptd.setStringParm("authuser", null == aut?"":aut);
			ptd.setStringParm("acno", acBusinessvch.getLoanacNo());// 贷款账号
			ptd.setStringParm("chkloind", "1");// 是否进行逾期校验			
			ptd.setStringParm("extrate", String.valueOf(afMainInfo.getExtrate()));// 变更利率			
			ptd.setStringParm("opnbrno", uo.getDcBrno(acBusinessvch.getBalInstcode()));// 开户机构号
			ptd.setStringParm("txbrno", dcbrno);
			ptd.setStringParm("curr", acBusinessvch.getCurr());
			ptd.setStringParm("acname", acBusinessvch.getCliname());
			ptd.setStringParm("vchno", vchno);
			ptd.setStringParm("contno", acBusinessvch.getContno());
			ptd.setStringParm("rateChgInd", afMainInfo.getRateChgInd());
			ptd.setStringParm("rateFloat", String.valueOf(rateFloat));
			ptd.setStringParm("ctind", "2");
			ptd.setStringParm("currcnt", String.valueOf(currcnt));
			ptd.setStringParm("brno", brno);// 微贷交易机构
			ptd.setStringParm("txtime",uo.getRealTime());	
			ptd.setStringParm("overRatefloat",String.valueOf(overRateFlt));	//lyy 20171012	修改逾期，复利利率浮动比传入
			ptd.setStringParm("cmpdfloat",String.valueOf(cmpdFloat));	
			try {
				// 完成变更后更具流程表让流程表结束流程
				BusinessProcessManager bpm = new BusinessProcessManager(arg0);
				bpm.finishFlowByAgree(applyno);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				rdbd.setRespCode("8300");
				rdbd.setRespDesc("更新流程记录时发生错误！");
				return rdbd;
			}
			DealTrsRecord dtr = new DealTrsRecord(uo);
			try{
				HashMap<String, Object> hMap = new HashMap<String, Object>();// 交易流水记录前准备
				hMap.put("repayamt", 0d);
				hMap.put("repayintst", 0d);
				hMap.put("repayoverintst", 0d);
				hMap.put("repaycmpdintst", 0d);
				hMap.put("fee",0d);
				hMap.put("repacNo", acBusinessvch.getReppriacNo());
				hMap.put("repacId", null);
				hMap.put("repacSeq", acBusinessvch.getReppriacSeq());
				hMap.put("repacName", acBusinessvch.getReppriacna());
				hMap.put("chk", null);
				hMap.put("aut", aut);
				hMap.put("repcardNo", acBusinessvch.getReppriaccardno());
				hMap.put("applyno", applyno);
				hMap.put("busbal", acBusinessvch.getBusBal());
				hMap.put("dcbrno", dcbrno);
				hMap.put("extRate", changerRate);//新利率
				hMap.put("extOverDueRate", acBusinessvch.getOverDuerate());//新逾期利率
				hMap.put("totalAmt", Arith.sub(acBusinessvch.getBusSum(),acBusinessvch.getBusBal()));//累计还本金额
				hMap.put("loAmt", acBusinessvch.getBusBal());//尚欠本金（还款后余额）
				hMap.put("beginDate", acBusinessvch.getBegindate());//贷款起始日期
				hMap.put("endDate", acBusinessvch.getEnddate());//贷款到期日
				hMap.put("arate", rate);//贷款执行利率(月) //edit by jinping 2017/5/23 改为更新前利率
				hMap.put("overDuerate", overDuerate);//逾期利率(月)//edit by jinping 2017/5/24 改为更新前逾期利率
				dtr.initialTrsRecordLog(acBusinessvch,ptd.getTrsCode(),hMap);
			}catch(Exception e){
				log.error(e.getMessage(), e);
				rdbd.setRespCode("8600");
				rdbd.setRespDesc("利率变更更新日志记录时发生错误");
				return rdbd;
			}
			
			// 定义接收GL返回结果结构体,调用通用对象的GL通讯外层封装
			rtnPtd = uo.GLHelper(ptd,dtr);
			
			// 解析GL返回值
			if (!rtnPtd.getRespCode().startsWith("0")) {
				rdbd.setRespCode(rtnPtd.getRespCode());
				rdbd.setRespDesc((String) rtnPtd.getResultValuebyName("respdesc"));
				return rdbd;
			}

			try {
				// 更新trs_record_log中的状态
				dtr.updateTrsRecordLog();
														
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				throw new Exception(e.getMessage());//e.getMessage基本都是空
			}
			//返回操作成功流水号
			rtnMap.put("traceNo", rtnPtd.getResultValuebyName("traceno"));//edit by jinping 2017/4/11 修改返回参数类型
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("利率变更Comm3310执行时发生错误");
			return rdbd;
		}
		
		rdbd.setRespCode("0000");
		rdbd.setRespMap("hashMap", rtnMap);
		rdbd.setRespDesc("生成利率变更还款计划表Comm3310执行成功");
		return rdbd;
	}

}
package tf.mcs.zged.handler;

import java.util.HashMap;

import org.hibernate.Session;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.mcs.dao.CustInfoDao;
import tf.mcs.model.CustInfo;

/**反显客户名称,连ecif系统查询
 * <AUTHOR>
 * @file QueryCustInfoforCliname.java
 * @date 2017-2-20
 * @version v1.0
 * @company:tfrunning
 */
public class QueryCustInfoforCliname implements CommonHandlerStub{

	/* (non-Javadoc)
	 * @see tf.brc.txhandler.CommonHandlerStub#subTxDealer(tf.brc.databus.ReqDataBusData, tf.brc.databus.RespDataBusData)
	 */
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0); 
		BRC.getLog().info("=============进入QueryCustInfoforCliname类=======");
		//获取Session
		DBPoolFactory dbpf=arg0.getReqDBPoolservice();
		Session externalSession=dbpf.getTransSession();
		CustInfoDao custInfoDao =new CustInfoDao();
		custInfoDao.setExternalSession(externalSession);
		//获取前台数据
		 HashMap queryMap = (HashMap) arg0.get("hashMap");
		 String certtype=(String) queryMap.get("certtype");
		 String certno=(String) queryMap.get("certno");
		// 公用域获取
		CommonDBT cdbt = arg0.getCommDC();
		long bankid = cdbt.getiBanks();
		RespDataBusData rdbd = new RespDataBusData();
		//需要先与ecif系统对接，如果查询ecif客户信息失败，则报该证件类型及证件号不存在ecif系统中，如果在ecif系统中存在，则更新cust_info表内信息
		//如果ecif中不存在，cust_info存在？
		
		
		 CustInfo custInfo=new CustInfo();
		 custInfo.setCerttype(certtype);
		 custInfo.setCertno(certno);
		 custInfo=custInfoDao.findUniqueByEntity(custInfo);
		 //
		if(null==custInfo||custInfo.equals("")){
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("该客户不在当前系统内！");
			return rdbd;
		}
		
		rdbd.setRespEntity("custInfo", custInfo);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("反显客户名字成功!");
		return rdbd;
	}

}

package tf.mcs.account.handler;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.PublicMethods;
import tf.mcs.model.CnProductdefine;

public class GetGccontnoInfoList implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		BRC.getLog().info(arg0); 
		
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0
				.getReqDBPoolservice().getDaoTemplate();
		//前台参数获取
		Pager pager=(Pager) arg0.get("pager");
		Map<String, Object> whereMap=(Map<java.lang.String, Object>) arg0.get("whereMap");
		//业务处理
		whereMap.put("#SQL#", " 1=1 order by t1.contno desc");
		// 公用域获取
		
		CommonDBT cdbt = arg0.getCommDC(); 
		long bankid = cdbt.getiBanks();	
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		try {
			daoTemplate.getMapListBySql("select t1.vercontno,t1.guarcurr,t1.guarsum,t1.guartype,t1.begindate,t1.enddate,t2.contno,t3.cliname,t3.bus_sum,t3.bus_bal,t3.prdt_no,t1.contno as gccontno,t1.certtype,t1.certno,t1.cifid,t1.cliname as dbcliname from ac_guarcont t1,bus_gcc_relative t2,ac_businesscont t3 where   t1.bankid="+bankid+" and t1.bankid=t2.bankid and t1.contno=t2.gccontno and t2.status='20' and t1.status='10' and t3.contno=t2.contno",whereMap, pager);
			for(int i=0;i<pager.getList().size();i++){
				HashMap hMap = (HashMap) pager.getList().get(i);
				hMap.put("prdtNa",tf.mcs.Tools.getProductName(bankid,(String)hMap.get("prdtNo")));
				
			}
		}
	    catch (Exception e) {
	    	e.printStackTrace();
	    	log.error(e.getMessage(), e);
	    	throw new Exception(e.toString());
	   }
				
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取借据信息执行成功!");
		return rdbd;
	}

}


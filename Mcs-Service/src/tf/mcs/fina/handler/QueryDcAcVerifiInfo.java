package tf.mcs.fina.handler;

import java.util.HashMap;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.utils.hb.model.Pager;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * @file QueryDcAcVerifiInfo.java
 * <AUTHOR>
 * @Date 20160105
 * @description 核销台账获取核销明细信息
 * 
 */
public class QueryDcAcVerifiInfo implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		BRC.getLog().info(
				"--------------进入" + this.getClass().getName()
						+ "类--------------传入参数为：" + arg0);
		// 初始化
		UniversalObject uo = new UniversalObject(arg0);
		RespDataBusData rdbd = new RespDataBusData();
		// 接收前台参数
		Pager pager = (Pager) arg0.get("pager");
		HashMap hashMap = (HashMap) arg0.get("hashMap");
		String vchno = (String) hashMap.get("vchno");
		// 公共参数赋值
		DaoTemplate dao = uo.Dao();
		long bankid = uo.getBankid();// 法人机构号
		String operid = uo.getOptUser();// 操作员编号
		String brno = uo.getOptBrno();// 操作机构号
		String sql = "select a.tx_date,a.trace_no,a.trace_cnt,b.cifid,b.cliname,a.acc_hrt,c.acc_name  " +
				" from dc_log_rz a,ac_verifi_info b,com_item c " +
				"where a.tx_trace_no in (select trace_no from trace_log_rz where bankid ='"+bankid+"' and tx_code='5200' " +
				"and pact_no in(select vchno from ac_verifi_info where VERIFI_FLAG='1' ))" +
				"and a.tx_code='5200' and a.acc_hrt in('**********','**********','**********')" +
				" and b.vchno  ='"+vchno+"' and a.acc_hrt=c.acc_hrt"+
				" union"+
				" select a.tx_date,a.trace_no,a.trace_cnt,b.cifid,b.cliname,a.acc_hrt,c.acc_name  " +
				" from dc_log a,ac_verifi_info b,com_item c " +
				"where a.tx_trace_no in (select trace_no from trace_log where bankid ='"+bankid+"' and tx_code='5200' " +
				"and pact_no in(select vchno from ac_verifi_info where VERIFI_FLAG='1' ))" +
				"and a.tx_code='5200' and a.acc_hrt in('**********','**********','**********')" +
				" and b.vchno  ='"+vchno+"' and a.acc_hrt=c.acc_hrt";
		try {
			BRC.getLog().info(sql);
			dao.getMapListBySql(sql, pager);
		} catch (Exception e) {
			StringTools.stackLog(e);// 异常日志
			rdbd.setRespCode("9100");
			rdbd.setRespDesc("已核销贷款核销明细查询出现错误");
			return rdbd;
		}
		rdbd.setRespEntity("pager", pager);// 返回到前台
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("已核销贷款核销明细查询执行成功");
		return rdbd;
	}

}

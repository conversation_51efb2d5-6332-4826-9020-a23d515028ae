package tf.mcs.print.handler;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import org.hibernate.Session;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.LnBatAjPayfileDao;
import tf.mcs.model.AcBusinessvch;
import tf.tools.CreatZipFile;
import tf.tools.DateTools;
import tf.tools.PrintTools;
import tf.tools.UniversalObject;
import tf.tools.WorkTools;
/**
 * 日终生成批量扣款凭证文件
 * <AUTHOR>
 * @date 2017-6-16
 * @ClassName LnBatAjPayfileBatchforZipPrint.java
 * @Company tfrunning
 *
 */
public class LnBatAjPayfileBatchforZipPrint implements CommonHandlerStub{
	// 获取日志
		private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0,RespDataBusData arg1) throws Exception {

		UniversalObject uo = new UniversalObject(arg0);
		BRC.getLog().info(arg0);
		BRC.getLog().info("##########进入LnBatAjPayfileBatchforZipPrint类############");
		long bankid=uo.getBankid();
        String workDate = uo.getServiceDate();
        String brno = uo.getOptBrno();
		String operid = uo.getOptUser();
		DateTools tool = new DateTools();
		String lastDate= tool.addData(workDate, "day", -1);//"********";
		// session
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		//借据表
		AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
		acBusinessvchDao.setExternalSession(externalSession);
		//日终批量扣款表
		LnBatAjPayfileDao lnBatAjPayfileDao = new LnBatAjPayfileDao();
		lnBatAjPayfileDao.setExternalSession(externalSession);
		ArrayList<File> fileList=new ArrayList<File>();
		PrintTools pt=new PrintTools(); 
		DecimalFormat moneyFormat = new DecimalFormat("#,##0.00");
		DecimalFormat rateFormat = new DecimalFormat("0.00####");
		BRC.getLog().info("select t1.*,t1.PAY_AMT+t1.PAY_INTST as paysum from ln_bat_aj_payfile t1 , ac_businessvch t2 where t1.vchno=t2.vchno and " +
				" t1.bankid=t2.bankid and t1.result='2' and t2.bal_instcode='"+brno+"' and t1.txdate='"+lastDate+"' and (t1.PAY_AMT+t1.PAY_INTST)>0");
		List aList =daoTemplate.getMapListBySql("select t1.*,t1.PAY_AMT+t1.PAY_INTST as paysum from ln_bat_aj_payfile t1 , ac_businessvch t2 where t1.vchno=t2.vchno and " +
				" t1.bankid=t2.bankid and t1.result='2' and t2.bal_instcode='"+brno+"' and t1.txdate='"+lastDate+"' and (t1.PAY_AMT+t1.PAY_INTST)>0");
		String txbrnoname=String.valueOf(daoTemplate.getUniqueValueBySql("select INSTNAME from cn_basicinfo where instcode='"+brno+"' and bankid='"+bankid+"'"));
		File file=null;
		 String tranceno="";
		if(null==aList||aList.size()<=0){
			arg1.setRespCode("8000");
			arg1.setRespDesc("该操作员所在机构昨日没有批量扣款记录！");
			return arg1;
		}
		for(int i=0;i<aList.size();i++){
			HashMap hash=new HashMap();
			hash=(HashMap)aList.get(i);
			String vchno=String.valueOf(hash.get("vchno"));
			AcBusinessvch acBusinessvch=new AcBusinessvch();
			acBusinessvch.setBankid(bankid);
			acBusinessvch.setVchno(vchno);
			acBusinessvch = acBusinessvchDao.getEntityByPK(acBusinessvch);
			String corrprdt=String.valueOf(daoTemplate.getUniqueValueBySql("select l1.TITLE  from  LN_PARM l1 where l1.prdt_no='"+acBusinessvch.getCorePrdtNo()+"' and  l1.BANKID='"+bankid+"'"));
			double paysum=Double.valueOf(String.valueOf(hash.get("paysum")));
			HashMap dataHm=new HashMap();
			dataHm.put("txbrnoname",dealNull(brno)+" "+ dealNull(txbrnoname));
			dataHm.put("txdate", dealNull(lastDate));
			dataHm.put("serid", dealNull(hash.get("traceNo").toString()));
			dataHm.put("acno", dealNull(hash.get("acNo").toString()));
			dataHm.put("vchno", dealNull(vchno));
			dataHm.put("cliname", dealNull(acBusinessvch.getCliname()));
			dataHm.put("prdtName", dealNull(corrprdt));
			dataHm.put("reppriacNo", dealNull(hash.get("reppriacNo").toString()));
			dataHm.put("busSumChi", String.valueOf(WorkTools.toChineseNumerals(new BigDecimal(String.valueOf(paysum)))));//金 额大写
			dataHm.put("busbal", moneyFormat.format(paysum));
			dataHm.put("payamt", moneyFormat.format(Double.valueOf(String.valueOf(hash.get("payAmt")))));
			dataHm.put("payinst", moneyFormat.format(Double.valueOf(String.valueOf(hash.get("payNorIntst")))));
			dataHm.put("paypunInst", moneyFormat.format(Double.valueOf(String.valueOf(hash.get("payPunIntst")))));
			dataHm.put("begindate", dealNull(acBusinessvch.getBegindate()));
			dataHm.put("enddate", dealNull(acBusinessvch.getEnddate()));
			dataHm.put("arate", rateFormat.format(acBusinessvch.getArate())+"‰");
			dataHm.put("overDuerate",  rateFormat.format(acBusinessvch.getOverDuerate())+"‰");
			dataHm.put("busSum", moneyFormat.format(acBusinessvch.getBusSum()));
			dataHm.put("amtBal", moneyFormat.format(Double.valueOf(String.valueOf(hash.get("amtBal")))));			
			 try{
				 tranceno=String.valueOf(daoTemplate.getUniqueValueBySql("select to_char(current_timestamp,'yyyymmddhh24missff') tstamp from dual"));
				 file=pt.GetHtmlToPdfNotUpload(dataHm, acBusinessvch.getContno()+"_"+tranceno ,"Inajkkhd.html");
				}catch(Exception e){
					for(File file1:fileList){//没有打成压缩包的文件都存在newfile里，如果失败，删之前生成的pdf
						file1.delete();
					}
					file.delete();
					e.printStackTrace();
					log.error("AC_ID:"+String.valueOf(hash.get("acId"))+"AC_SEQN:"+String.valueOf(hash.get("acSeqn"))
							+"TERM:"+String.valueOf(hash.get("term"))+e.getMessage(), e);
					arg1.setRespCode("9999");
					arg1.setRespDesc("生成批量凭证文件失败信息执行失败！");
					return arg1;
				}
			 fileList.add(file);
		}
		String filepath="";
		CreatZipFile cz = new CreatZipFile();
		filepath=cz.creatZip(fileList, brno+tranceno);
		for(File file1:fileList){
			file1.delete();
		}
		HashMap map=new HashMap();
		map.put("filePath", filepath);
		arg1.setRespCode("0000");
		arg1.setRespDesc("生成批量凭证文件失败信息执行成功！");
		arg1.setRespMap("hashMap", map);
		return arg1;
	}
	public String dealNull(String s){
		if ("".equals(s) || null == s || ("null").equals(s)) {
			return  "";
		}else{
			return s;
		}
	}
}

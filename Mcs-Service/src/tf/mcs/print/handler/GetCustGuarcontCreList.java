package tf.mcs.print.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.core.utils.hb.model.Pager;
import tf.tools.UniversalObject;
/**
 * 功能描述：获取单一合同项下担保合同信息列表
 * <AUTHOR>
 *
 */
public class GetCustGuarcontCreList implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0,RespDataBusData arg1) throws Exception {

		UniversalObject uo=new UniversalObject(arg0);
		//BRCLog
		uo.logInfo(arg0);
		//公共参数获取
		String brno=uo.getOptBrno();
		String operid=uo.getOptUser();
		String workDate=uo.getServiceDate();
		long bankid=uo.getBankid();
		//获取前台参数
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		HashMap reqMap=(HashMap) arg0.get("hashMap");
		String contno=String.valueOf(reqMap.get("contno"));
		//业务处理
		whereMap.put("#SQL#", " 1=1 ORDER BY t1.contno DESC");
		String mainSql="SELECT t1.contno,t1.guartype,t1.cliname,t1.certno,t1.status,t2.fileurl FROM " +
				"(SELECT a.contno,a.guartype,a.cliname,a.certno,b.status " +
						"FROM ac_guarcont a,bus_gcc_relative b " +
						"WHERE a.bankid=b.bankid AND a.bankid='"+bankid+"' AND b.contno='"+contno+"' AND a.contno=b.gccontno and b.status!='30') t1 " +
				"LEFT JOIN " +
				"(SELECT a.relserid,a.fileurl FROM document_filing a WHERE a.bankid='"+bankid+"' AND a.file_sts='1'  AND a.file_type like '2%') t2 " +
				"ON t1.contno=t2.relserid " +
				"WHERE 1=1";
		uo.Dao().getMapListByLikeSql(mainSql,whereMap, pager);
		arg1.setRespEntity("pager", pager);
		arg1.setRespCode("0000");
		arg1.setRespDesc("获取已存在合同客户信息列表执行成功!");
		return arg1;
	}
}
package tf.mcs.print.handler;

import org.apache.log4j.Logger;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.DateTool;
import tf.ifms.rpc.handler.IfmsTool;
import tf.mcs.PublicMethods;
import tf.mcs.comm.IndBaseCommon;
import tf.mcs.comm.UserAndBasicCommon;
import tf.mcs.dao.AcBusinesscontDao;
import tf.mcs.dao.WbDocumentInfoDao;
import tf.mcs.dto.DecryptDto;
import tf.mcs.enums.AppType;
import tf.mcs.enums.ContractNameEnum;
import tf.mcs.enums.TextType;
import tf.mcs.model.AcBusinesscont;
import tf.mcs.model.WbDocumentInfo;
import tf.mcs.service.SecuritySensitiveHandle;
import tf.tools.SignToos;
import tf.tools.UniversalObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预览和生成重组担保人商业文件信函和诉讼文书的送达地址确认书
 * <AUTHOR>
 * @date 20210823
 */
public class PreviewAndLoanConfirmationAddressRest extends HibernateDaoSupport implements CommonHandlerStub {

	private final Logger log = Logger.getLogger(getClass());

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		UniversalObject uo = new UniversalObject(arg0);
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		daoTemplate.setExternalSession(externalSession);
		long bankid = uo.getBankid();
		String operid = uo.getOptUser();
		String workdate = uo.getServiceDate();
		HashMap gethashMap = (HashMap) arg0.get("hashMap");
		String loanCifid = String.valueOf(gethashMap.get("cifid"));
		String applyno = String.valueOf(gethashMap.get("applyno"));
		String flag = String.valueOf(gethashMap.get("flag"));//是否预览
		String isonlinesign = String.valueOf(gethashMap.get("isonlineSign"));//线上线下签约
		String loanCliname = String.valueOf(daoTemplate.getUniqueValueBySql("select cliname from ind_base where bankid = " + bankid + " and cifid ='" + loanCifid + "' "));
		String contno = String.valueOf(daoTemplate.getUniqueValueBySql("select contno from af_main_info where bankid = " + bankid + " and applyno = '" + applyno + "' "));
		AcBusinesscont ac = new AcBusinesscont();
		AcBusinesscontDao acDao = new AcBusinesscontDao();
		acDao.setExternalSession(externalSession);
		ac.setBankid(bankid);
		ac.setContno(contno);
		ac = acDao.getEntityByPK(ac);
		String fileType = "80006";
		String updateSql = "update document_filing set file_sts = '0' " +
				"where relserid = '" + applyno + "' " +
				"and cifid = '" + loanCifid + "' " +
				"and file_type = '" + fileType + "' " +
				"and bankid = " + bankid;
		daoTemplate.executeSql(updateSql);

		updateSql = "update wb_document_info set file_sts = '0' " +
				"where relserid = '" + applyno + "' " +
				"and cifid = '" + loanCifid + "' " +
				"and file_type = '" + fileType + "' " +
				"and bankid = " + bankid;
		daoTemplate.executeSql(updateSql);

		HashMap printMap = new HashMap();//封装借款人信息
		HashMap loopMap = new HashMap();//封装集合信息
		HashMap<String, Boolean> showFlagHm = new HashMap<>();//是否显示参数
		List<Map> list = new ArrayList<>();
		List<Map> guarList;
		boolean signFlag = true;//共同签约true，false各自签约

		//获取公司相关信息
		String sql = "select sinstname from cn_basicinfo  where bankid = " + bankid + " and instcode = '" + ac.getManageInstcode() + "'";
		String instname = daoTemplate.getUniqueValueBySql(sql, String.class);
		printMap.put("company", instname);//封装公司名称信息

		try {

			//校验是否签字人都确认签字方式
			String printType = "select cifid, cliname, isonline_sign from ac_businesscont where applyno = '" + ac.getApplyno() + "' and con_sts = '10' " +
					"union " +
					"select cifid, cliname, isonline_sign from bus_survey_coborrower where bankid = " + bankid + " and applyno = '" + applyno + "' and is_temp = '1' and state = '1' " +
					"union " +
					"select cifid, cliname, isonline_sign from bus_survey_guarantor where bankid = " + bankid + " and applyno = '" + applyno + "' and is_temp = '1' and state = '1' ";
			List<Map> printTypeList = daoTemplate.getMapListBySql(printType);

			for (Map printTypeMap : printTypeList) {
				String cliname = String.valueOf(printTypeMap.get("cliname"));
				String isonlineSign = String.valueOf(printTypeMap.get("isonlineSign"));

				if (IfmsTool.isNull(isonlineSign)) {
					arg1.setRespCode("9999");
					arg1.setRespDesc("客户名称为【" + cliname + "】的客户没有选择签约方式！");
					return arg1;
				} else if ("1".equals(isonlineSign) && signFlag) {
					signFlag = false;
				}
			}

			//封装担保人相关信息。此处的意思是剔除原合同的担保人，只查询重组新增的担保人信息
			guarList = daoTemplate.getMapListBySql("select CAST(@rowno\\:=@rowno+1 AS CHAR) as rowno, cliname as guarantee, cifid, " +
					"case cstype when 'A01' then workaddr else address end communication, " +
					"case cstype when 'A01' then workcorptel else tel end phone " +
					"from bus_survey_guarantor a, (select @rowno\\:=0) t " +
					"where bankid = " + bankid + " " +
					"and applyno = '" + applyno + "' " +
					"and is_temp = '1' " +
					"and state = '1' " +
					"and cifid not in (" +
					"select cifid from bus_survey_guarantor where bankid = " + bankid + " and applyNo = '" + ac.getApplyno() + "'" +
					")");
			List<String> allCiphertextPhoneList = new ArrayList<>();
			String mainFamAddr = null;

			for (int i = 0; i < guarList.size(); i++) {
				Map mapi = guarList.get(i);
				String phone = String.valueOf(mapi.get("phone"));
				String communication = String.valueOf(mapi.get("communication"));
				allCiphertextPhoneList.add(phone);

				if(IfmsTool.isNull(communication)) {

					if(IfmsTool.isNull(mainFamAddr)) {
						String queryMainAddressSql = "select famaddr from ind_base where bankid = " + bankid + " " +
								"and cifid = (" +
								"select cifid from af_main_info where bankid = " + bankid + " and applyno = '" + applyno + "'" +
								")";
						mainFamAddr = daoTemplate.getUniqueValueBySql(queryMainAddressSql, String.class);
					}
					mapi.put("communication", mainFamAddr);
				}
			}
			DecryptDto decryptDto = new DecryptDto();
			decryptDto.setOperid(operid);
			decryptDto.setOpername(UserAndBasicCommon.getOperNameByOperId(bankid, operid, daoTemplate));
			decryptDto.setAppType(AppType.PC);
			decryptDto.setServiceId(arg0.getReqNo());
			decryptDto.setCiphertextList(allCiphertextPhoneList);
			decryptDto.setDecryptMethod(false);//全解
			decryptDto.setTextType(TextType.PHONE);
			decryptDto.setBusType("1");
			decryptDto.setBusDesc("预览和生成重组担保人商业文件信函和诉讼文书的送达地址确认书");
			decryptDto.setBusCode(ac.getApplyno());
			DecryptDto plaintextDto = SecuritySensitiveHandle.decrypt(decryptDto);
			List<String> plaintextList = plaintextDto.getPlaintextList();

			if (null != plaintextList && !plaintextList.isEmpty()) {

				for (int i = 0; i < guarList.size(); i++) {//担保人
					guarList.get(i).put("phone", plaintextList.get(i));
				}
			}
			showFlagHm.put("show0", true);
			showFlagHm.put("show1", true);
			loopMap.put("array0", list);
			loopMap.put("array1", guarList);
			loopMap.put("array2", guarList);
			printMap.put("contno", ac.getContno());//封装合同号
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9998");
			arg1.setRespDesc("参数准备失败！");
			return arg1;
		}
		String byear = workdate.substring(0, 4);
		String bmonth = workdate.substring(4, 6);
		String bday = workdate.substring(6, 8);
		printMap.put("byear", byear);
		printMap.put("bmonth", bmonth);
		printMap.put("bday", bday);

		if ("1".equals(isonlinesign) && "0".equals(flag)) {

			try {

				if (guarList.size() > 4) {
					arg1.setRespCode("9997");
					arg1.setRespDesc("担保人数量超出担保人线上签约人数，请选择线下签约！");
					return arg1;
				}
				Map<String, String> templateMap = new HashMap<String, String>();
				templateMap.put("date", workdate);
				String wbserid = PublicMethods.getCnSeqnumByTemplate(bankid, "wbseid", uo.getSession(), templateMap);
				HashMap textValueInfo = new HashMap();
				SignToos stool = new SignToos();
				HashMap hashMap = new HashMap();
				String signLocation = "Signature";

				for (int i = 0; i < guarList.size(); i++) {
					HashMap tMap = (HashMap) guarList.get(i);
					String guarcifid = String.valueOf(tMap.get("cifid"));

					int j = i + 1;
					textValueInfo.put("guarantee" + j, tMap.get("guarantee"));
					textValueInfo.put("phone" + j, jugStr(String.valueOf(tMap.get("phone"))));
					textValueInfo.put("communication" + j, tMap.get("communication"));

					if (guarcifid.equals(loanCifid)) {
						textValueInfo.put("F" + j, loanCliname);
						signLocation += j;
					}
				}
				textValueInfo.put("contno", contno);
				textValueInfo.put("company", instname);
				textValueInfo.put("y1", byear + "-" + bmonth + "-" + bday);
				String tembrno = ac.getManageInstcode().substring(0, 4);
				String parmCode;

				if ("1010".equals(tembrno)) {//南充美兴
					parmCode = "REST1010";
				} else {//四川美兴
					parmCode = "REST1020";
				}
				String templateId = daoTemplate.getUniqueValueBySql("select val from com_parm a where a.bankid = " + bankid + " " +
						"and a.parm_code = '" + parmCode + "' and a.parm_seqn = 5", String.class);
				String userId = stool.indCfcaAcct(loanCifid, ac.getManageInstcode(), externalSession, daoTemplate);
				String contractName = "商业文件信函和诉讼文书的送达地址确认书";
				hashMap.put("brno", ac.getManageInstcode());
				hashMap.put("templateId", templateId);
				hashMap.put("contractName", contractName);
				hashMap.put("signLocation", signLocation);

				HashMap<String, Object> signInfoMap = new HashMap<>();
				signInfoMap.put("authorizationTime", "**************");//授权时间
				signInfoMap.put("isCheckProjectCode", "0");//是否检查项目编号
				signInfoMap.put("isCopy", "0");//是否抄送
				signInfoMap.put("location", "192.168.116.25");//授权地点
				signInfoMap.put("projectCode", wbserid);//项目编号
				signInfoMap.put("isProxySign", "0");//是否代签
				signInfoMap.put("sealId", "");//印章ID
				signInfoMap.put("sealColor", "");//印章颜色
				signInfoMap.put("signLocation", signLocation);//签名域的标签值
				signInfoMap.put("userId", userId);//用户ID
				ArrayList<HashMap<String, Object>> signInfos = new ArrayList<>();
				signInfos.add(signInfoMap);
				String contractNo = stool.createContract(hashMap, textValueInfo, signInfos);//创建合同

				if (IfmsTool.isNull(contractNo)) {
					throw new Exception("调用安心签生成电子合同异常 !");
				}
				HashMap haMap = stool.downContract(contractNo, ac.getManageInstcode());
				String rtnFliePath = (String) haMap.get("fileUrl");
				String fileName = (String) haMap.get("fileName");

				if (IfmsTool.isNull(fileName)) {
					throw new Exception("调用安心签合同异常下载 !");
				}
				String seqSql = "select t1.file_seq from wb_document_info t1 " +
						"where t1.relserid = '" + applyno + "' " +
						"and t1.cifid = '" + ac.getCifid() + "' " +
						"and t1.file_type = '" + fileType + "' " +
						"and t1.file_sts = '1' " +
						"and t1.bankid = " + bankid;
				String strSeq = daoTemplate.getUniqueValueBySql(seqSql, String.class);
				int fileSeq = 0;

				if (IfmsTool.isNotNull(strSeq)) {
					fileSeq = Integer.parseInt(strSeq);
				}
				int newFileSeq = fileSeq + 1;
				WbDocumentInfo wbDocumentInfo = new WbDocumentInfo();
				WbDocumentInfoDao wbDocumentInfoDao = new WbDocumentInfoDao();
				wbDocumentInfoDao.setExternalSession(externalSession);
				wbDocumentInfo.setBankid(bankid);
				wbDocumentInfo.setSerid(wbserid);
				wbDocumentInfo.setFileSeq((long) newFileSeq);
				wbDocumentInfo.setRelserid(applyno);
				wbDocumentInfo.setFieldname("商业文件信函和诉讼文书的送达地址确认书");
				wbDocumentInfo.setFileType(fileType);
				wbDocumentInfo.setFilename(contractNo + ".pdf");
				wbDocumentInfo.setFileurl(rtnFliePath);
				wbDocumentInfo.setOperid(ac.getManageOperid());
				wbDocumentInfo.setInstcode(ac.getManageInstcode());
				wbDocumentInfo.setOperationDate(workdate);
				wbDocumentInfo.setFileSts("1");
				wbDocumentInfo.setCifid(loanCifid);
				wbDocumentInfo.setCliname(loanCliname);
				wbDocumentInfo.setFiledescription("地址送达确认书");
				wbDocumentInfo.setFlag("0");
				wbDocumentInfo.setSignsts("0");
				wbDocumentInfo.setExdate(workdate);
				wbDocumentInfo.setEccontno(contractNo);
				wbDocumentInfo.setSignlocation(signLocation);
				wbDocumentInfo.setStartdate(DateTool.getCurrentTime("yyyyMMdd HHmmss"));
				wbDocumentInfoDao.insert(wbDocumentInfo);

				//2024-07-03 yk 增加共同借款人担保人的几项微信通知
				IndBaseCommon.sendWechatContractSignNotice(loanCifid, ContractNameEnum.addressConfirmation, applyno, daoTemplate);
				arg1.setRespCode("0000");
				arg1.setRespDesc("电子商业文件信函和诉讼文书的送达地址确认书文件已推送客户为微信公众号！");
			} catch (Exception e2) {
				e2.printStackTrace();
				log.error(e2.getMessage(), e2);
				arg1.setRespCode("9998");
				arg1.setRespDesc("生成电子商业文件信函和诉讼文书的送达地址确认书文件失败！");
			}
			return arg1;
		} else {//线下
			Map<String, String> templateMap = new HashMap<>();
			templateMap.put("date", workdate);
			String serid = PublicMethods.getCnSeqnumByTemplate(bankid, "normal", externalSession, templateMap);

			try {//7。文件表数据更新
				String queryFilePathSql = "select val from com_parm " +
						"where bankid = " + bankid + " " +
						"and parm_code = 'crsReporturl' " +
						"and parm_seqn = 1";
				String prefilepath = daoTemplate.getUniqueValueBySql(queryFilePathSql, String.class);
				HashMap<String, Object> respMap;

				if ("1".equals(flag)) {
					respMap = uo.previewLoopHtmlToPdfs(printMap, loopMap, "restAddress.html",
							ac.getContno() + "_" + serid, loanCifid + "/" + ac.getContno() + "/",
							true, showFlagHm, fileType, prefilepath);
					arg1.setRespDesc("预览重组商业文件信函和诉讼文书的送达地址确认书文件执行成功！");
				} else {
					respMap = uo.GetLoopHtmlToPdfs(printMap, loopMap, "restAddress.html",
							ac.getContno() + "_" + serid, loanCifid + "/" + ac.getContno() + "/",
							true, showFlagHm, fileType);
					arg1.setRespDesc("生成重组商业文件信函和诉讼文书的送达地址确认书文件执行成功！");
				}

				if (respMap == null) {
					arg1.setRespCode("9998");
					arg1.setRespDesc("生成合同文件执行失败！");
				}else{
					arg1.setRespCode("0000");
				}
				arg1.setRespMap("hashMap", respMap);
				return arg1;
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				arg1.setRespCode("9999");
				arg1.setRespDesc("生成合同文件执行失败！");
				return arg1;
			}
		}
	}

	public String jugStr(String str) {

		if (IfmsTool.isNull(str)) {
			return "";
		}
		return str;
	}
}
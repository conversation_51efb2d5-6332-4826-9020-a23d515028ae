package tf.mcs.print.handler;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.ifms.rpc.handler.IfmsTool;
import tf.mcs.PublicMethods;
import tf.mcs.comm.MyDefineException;
import tf.mcs.comm.UserAndBasicCommon;
import tf.mcs.dao.AcBusinesscontDao;
import tf.mcs.dao.AcGuarcontDao;
import tf.mcs.dao.DocumentFilingDao;
import tf.mcs.dto.DecryptDto;
import tf.mcs.enums.AppType;
import tf.mcs.enums.TextType;
import tf.mcs.model.AcBusinesscont;
import tf.mcs.model.AcGuarcont;
import tf.mcs.model.DocumentFiling;
import tf.mcs.service.SecuritySensitiveHandle;
import tf.tax.util.EnumYesOrNo;
import tf.tools.SignToos;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 最高额抵押（房产抵押）合同打印
 * <AUTHOR>
 * @date 2020-8-10
 * @ClassName MaxAcGuarcontPrint.java
 * @Company tfrunning
 *
 */
public class MaxAcGuarcontPrint extends HibernateDaoSupport implements CommonHandlerStub{

	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		UniversalObject uo = new UniversalObject(arg0);
		uo.logInfo(arg0);
		uo.logInfo("###########进入"+this.getClass().getName()+"类############");
		// session
		DecimalFormat moneyFormat = new DecimalFormat("###,###.00");
		//1、获取查询条件相关参数
		//公共域获取
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		daoTemplate.setExternalSession(externalSession);
		long bankid = uo.getBankid();
		String operid = uo.getOptUser();
		String workdate = uo.getServiceDate();
		String brno = uo.getOptBrno();
		SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
		DecimalFormat df = new DecimalFormat("#.00");
		//获取前台传入参数
		HashMap repMap = (HashMap) arg0.get("hashMap");
		String contno=(String)repMap.get("contno");
		String applyno=(String)repMap.get("applyno");
		String gccontno=(String)repMap.get("gccontno");
		AcBusinesscont acBusinesscont = new AcBusinesscont();
		AcBusinesscontDao acBusinesscontDao = new AcBusinesscontDao();
		acBusinesscontDao.setExternalSession(externalSession);
		acBusinesscont.setBankid(bankid);
		acBusinesscont.setContno(contno);
		acBusinesscont = acBusinesscontDao.getEntityByPK(acBusinesscont);
		if(acBusinesscont==null) {
			arg1.setRespCode("9998");
			arg1.setRespDesc("不存在业务合同信息，无法生成最高额抵押合同！");
			return arg1;
		}

		AcGuarcont acGuarcont=new AcGuarcont();
		AcGuarcontDao acGuarcontDao=new AcGuarcontDao();
		acGuarcontDao.setExternalSession(externalSession);
		acGuarcont.setBankid(bankid);
		acGuarcont.setContno(gccontno);
		acGuarcont=acGuarcontDao.getEntityByPK(acGuarcont);
		if(acGuarcont==null) {
			arg1.setRespCode("9997");
			arg1.setRespDesc("不存在担保合同信息，无法生成最高额抵押合同！");
			return arg1;
		}

		//封装借款人信息
		HashMap printMap = new HashMap();
		//封装集合信息
		HashMap loopMap = new HashMap();
		//整理统计信息
		HashMap<String, Boolean> showFlagHm =  new HashMap<String, Boolean>();//是否显示参数
		try {
			printMap.put("gccontno", gccontno);
			//抵押人
//			printMap.put("cliname1", acGuarcont.getCliname());
//			printMap.put("certno", acGuarcont.getCertno());
			//获取公司相关信息
			String mainsql = "select a.sinstname,a.instmgr,a.address from cn_basicinfo a INNER join cn_basicinfo b on a.instcode=b.sinstcode where b.instcode='"+acBusinesscont.getManageInstcode()+"'";
			Map map = (Map)daoTemplate.getUniqueMapRowBySql(mainsql);
			//抵押权人(乙方)
			printMap.put("managename", (String)map.get("sinstname"));
			printMap.put("manager", (String)map.get("instmgr"));
			printMap.put("instcodeaddr", (String)map.get("address"));
			//dyc_20240923_MIS-1372_合同金额需要去作价后的
			String sumGuarSql="select sum(ifnull(b.com_sum,b.guar_sum_max)) guarsum  from bus_affrim_collateral a,guar_base b where a.guar_no=b.guar_no and a.bankid=b.bankid and a.applyno='"+applyno+"' and a.gccontno='"+gccontno+"'";
			Double sumGuarSum=daoTemplate.getUniqueValueBySql(sumGuarSql, Double.class);
			printMap.put("guarSum", String.valueOf(sumGuarSum));
			//金额整数部分
			String bigguarSum = SignToos.digitUppercase(sumGuarSum);
			printMap.put("bigguarSum", bigguarSum);

			//取当前贷款借款人、共借人、担保人
			String clinam2=acBusinesscont.getCliname();
			String certno2=acBusinesscont.getCertno();
			clinam2 = clinam2+"("+certno2+")";
			String otherPersons = "";
			List<String> cifids = new ArrayList<>();
			ArrayList  borrowerlist = (ArrayList) uo.Dao().getMapListBySql("select CAST(@rowno\\:=@rowno+1 AS CHAR) as rowno,cliname borcliname,certtype borcerttype,certno borcertno,cstype,cifid from bus_survey_coborrower,(select @rowno\\:=0) t where bankid='"+bankid+"' and applyno='"+applyno+"'");
			for (int a = 0; a < borrowerlist.size(); a++) {
				HashMap hMap = (HashMap) borrowerlist.get(a);
				String borcliname=String.valueOf(hMap.get("borcliname")) ;
				String borcertno=String.valueOf(hMap.get("borcertno")) ;
				clinam2=clinam2+"/"+borcliname+"("+borcertno+")";
				String cifid=String.valueOf(hMap.get("cifid")) ;
				cifids.add(cifid);
			}
			// 处理多个担保合同情况下获取担保合同号,保证人
			String gccSql ="select CAST(@rowno\\:=@rowno+1 AS CHAR) as rowno,cliname,cifid,certno from bus_survey_guarantor,(select @rowno\\:=0) t "
					+ " where bankid='"+bankid+"' and applyno='"+applyno+"'";
			ArrayList gccList = (ArrayList) uo.Dao().getMapListBySql(gccSql);
			for (int a = 0; a < gccList.size(); a++) {
				HashMap hMap = (HashMap) gccList.get(a);
				String cliname=String.valueOf(hMap.get("cliname")) ;
				String certno=String.valueOf(hMap.get("certno")) ;
				clinam2=clinam2+"/"+cliname+"("+certno+")";
				String cifid=String.valueOf(hMap.get("cifid")) ;
				cifids.add(cifid);
			}
			//处理实体主借人,如果实体作为主借人签字了，就查询出来
			String isBorsign = acBusinesscont.getIsBorsign();
			if(EnumYesOrNo.YES.getValue().equals(isBorsign)){
				String sqlDoeBase = "select CAST(@rowno\\:=@rowno+1 AS CHAR) as rowno,cliname,cifid,certno from bus_doe_base,(select @rowno\\:=0) t "
						+ " where bankid='"+bankid+"' and applyno='"+applyno+"'";
				ArrayList doeBaseList = (ArrayList) uo.Dao().getMapListBySql(sqlDoeBase);
				for (int a = 0; a < doeBaseList.size(); a++) {
					HashMap hMap = (HashMap) doeBaseList.get(a);
					String cliname=String.valueOf(hMap.get("cliname")) ;
					String certno=MapUtils.getString(map, "certno");
					if(IfmsTool.isNull(certno)){
						certno="";
					}
					clinam2=clinam2+"/"+cliname+"("+certno+")";
				}
			}
			printMap.put("clinam2", clinam2);

			//抵押人（财产共有人）
			String sql="select DISTINCT c.cifid,c.cliname bcliname,c.certno bcertno,c.ownertype from bus_affrim_collateral a,guar_comown_info c where  a.guar_no=c.guar_no and a.bankid=c.bankid"
					+" and a.applyno='"+applyno+"' and a.gccontno='"+gccontno+"'";
			List<Map> repList=daoTemplate.getMapListBySql(sql);

			/**
			 * 2024-02-06 MIS-1130 最高额抵押合同取值优化 合同抬头和联系人列表需要分开取值
			 * 最高额抵押合同抬头,之前抬头和合同中 “甲方的联系信息如下” 列表一致,现在需要分开取值
			 */
			List<Map> headerDataList = new ArrayList<>();
			List<Map> tableDataList = new ArrayList<>();

			for(int i = 0; i < repList.size(); i++) {
				Map repDataMap = repList.get(i);

				int cnt = i + 1;
				String certype = String.valueOf(repDataMap.get("ownertype"));
				String cifid = MapUtils.getString(repDataMap, "cifid");
				String ownertype;

				if("A01".equals(certype)) {
					ownertype = "统一社会信用代码";
				}else {
					ownertype = "公民身份号码";
				}
				repDataMap.put("ownertype", ownertype);
				repDataMap.put("cnt", String.valueOf(cnt));

				Map headerDataMap = new HashMap(repDataMap);
				Map tableDataMap = new HashMap(repDataMap);
				headerDataMap.putAll(getCustomerInfo(daoTemplate, cifid, 1, applyno, bankid));
				tableDataMap.putAll(getCustomerInfo(daoTemplate, cifid, 0, applyno, bankid));

				List<String> allCiphertextPhoneList = new ArrayList<>();
				allCiphertextPhoneList.add(String.valueOf(headerDataMap.get("btel")));
				allCiphertextPhoneList.add(String.valueOf(tableDataMap.get("btel")));

				DecryptDto decryptDto = new DecryptDto();
				decryptDto.setOperid(operid);
				decryptDto.setOpername(UserAndBasicCommon.getOperNameByOperId(bankid, operid, daoTemplate));
				decryptDto.setAppType(AppType.PC);
				decryptDto.setServiceId(arg0.getReqNo());
				decryptDto.setCiphertextList(allCiphertextPhoneList);
				decryptDto.setDecryptMethod(false);//全解
				decryptDto.setTextType(TextType.PHONE);
				decryptDto.setBusType("1");
				decryptDto.setBusDesc("打印最高额担保合同");
				decryptDto.setBusCode(applyno);
				DecryptDto plaintextDto = SecuritySensitiveHandle.decrypt(decryptDto);
				List<String> plaintextList = plaintextDto.getPlaintextList();

				if(null != plaintextList && !plaintextList.isEmpty()){
					headerDataMap.put("btel", plaintextList.get(0));
					tableDataMap.put("btel", plaintextList.get(1));
				}
				headerDataList.add(headerDataMap);
				tableDataList.add(tableDataMap);
				cifids.add(cifid);
			}
			showFlagHm.put("show0", headerDataList.size()==0?false:true);
			loopMap.put("array0", headerDataList);

			showFlagHm.put("show2", tableDataList.size()==0?false:true);
			loopMap.put("array2", tableDataList);

			//2024-02-06 MIS-1130 最高额抵押合同取值优化 修改此功能时,发现该代码没有作用,注释
			//甲方(包含借款人)联系人列表
			/*HashSet<String> cifidSet = new HashSet<>(cifids);
			List<Map> contacts=new ArrayList<>();
			int m=0;
			for(String cifid:cifidSet){
				m=m+1;
				Map<String, Object> customer = getCustomerInfo(daoTemplate, cifid);
				customer.put("cnt", String.valueOf(m));
				contacts.add(customer);
			}
			showFlagHm.put("show1", contacts.size()==0?false:true);
			loopMap.put("array1", contacts);*/
			//String owner="";
			//抵押物清单
			/*String guarSql="select b.col_na guar_type,b.be_situated guar_adrr,cast(a.guarsum as char) guarsum,b.receipt_no war_no,(SELECT GROUP_CONCAT(c.cliname) FROM guar_comown_info c WHERE c.bankid=a.bankid and c.guar_no=a.guar_no) owner  from bus_affrim_collateral a,guar_base b where a.guar_no=b.guar_no and a.bankid=b.bankid "
					+" and a.applyno='"+applyno+"' and a.gccontno='"+gccontno+"'";*/
			String guarSql="select b.col_na guar_type,b.be_situated guar_adrr,ifnull(b.com_sum,b.guar_sum_max) guarsum,b.receipt_no war_no,(SELECT GROUP_CONCAT(c.cliname) FROM guar_comown_info c WHERE c.bankid=a.bankid and c.guar_no=a.guar_no) owner  from bus_affrim_collateral a,guar_base b where a.guar_no=b.guar_no and a.bankid=b.bankid "
					+" and a.applyno='"+applyno+"' and a.gccontno='"+gccontno+"'";
			List<Map> guarList=daoTemplate.getMapListBySql(guarSql);
			for(int i=0;i<guarList.size();i++) {
				int cnt=i+1;
				HashMap gaurMap=(HashMap)guarList.get(i);
				String warNo=String.valueOf(gaurMap.get("warNo"));
				String guarAdrr=String.valueOf(gaurMap.get("guarAdrr"));
				String guarsum=String.valueOf(gaurMap.get("guarsum"));
				gaurMap.put("guarsum", StringTools.isNull(guarsum)?"0.00":guarsum);
				//String cliname=String.valueOf(gaurMap.get("cliname"));
//				if( "".equals(owner)) {
//					owner=cliname;
//				}else {
//					owner=owner+"/"+cliname;
//				}
				if("null".equals(warNo) || "".equals(warNo)|| null==warNo) {
					warNo="";
				}
				if("null".equals(guarAdrr) || "".equals(guarAdrr)|| null==guarAdrr) {
					guarAdrr="";
				}
				gaurMap.put("cnt", String.valueOf(cnt));
				gaurMap.put("warNo", warNo);
				gaurMap.put("guarAdrr", guarAdrr);
				//gaurMap.put("owner", owner);
			}
			showFlagHm.put("show3", guarList.size()==0?false:true);
			loopMap.put("array3", guarList);
		}catch (MyDefineException e){
			arg1.setRespCode("9999");
			arg1.setRespDesc(e.getMessage());
			return arg1;
		}catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9998");
			arg1.setRespDesc("参数准备失败！");
			return arg1;
		}
//		获取日期
		String begindate = acGuarcont.getBegindate();
		String enddate = acGuarcont.getEnddate();
		if(StringUtils.isEmpty(begindate) || StringUtils.isEmpty(enddate)){
			arg1.setRespCode("9999");
			arg1.setRespDesc("打印合同前必须做核保认定！");
			return arg1;
		}
		String byear = begindate.substring(0, 4);
		String bmonth = begindate.substring(4, 6);
		String bday = begindate.substring(6, 8);
		String eyear = enddate.substring(0, 4);
		String emonth = enddate.substring(4, 6);
		String eday = enddate.substring(6, 8);
//		日期处理：yyyMMdd 改为 yyyy年M月d日
		SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd");
		SimpleDateFormat format2 = new SimpleDateFormat("yyyy年M月d日");
		begindate = format2.format(format1.parse(begindate));
		enddate = format2.format(format1.parse(enddate));

		printMap.put("bdate", begindate);
		printMap.put("edate", enddate);
		printMap.put("byear", byear);
		printMap.put("bmonth", bmonth);
		printMap.put("bday", bday);
		printMap.put("eyear", eyear);
		printMap.put("emonth", emonth);
		printMap.put("eday", eday);
		//6、打印html为pdf
		// 根据传入机构产品编号及打印类型查找相应打印要素
		String mainSql = "SELECT t1.* FROM cn_credence t1 " + "WHERE t1.bankid='" + bankid + "' AND t1.instcode='"
				+ acBusinesscont.getManageInstcode() + "' " + "AND t1.prdt_no='" + acBusinesscont.getPrdtNo()+ "' AND t1.cre_type='24' "
				+ "AND t1.begindate<='" + workdate + "' AND t1.enddate  >= '" + workdate + "'";
		@SuppressWarnings("unchecked")
		HashMap<String, Object> sqlMap = (HashMap<String, Object>) daoTemplate.getUniqueMapRowBySql(mainSql);
		if (sqlMap == null) {
			arg1.setRespCode("9999");
			arg1.setRespDesc("<br />生成合同文件执行失败！<br />未找到机构产品所对应的业务合同打印配置信息！");
			return arg1;
		}
		HashMap<String, String> hashMap = new HashMap();
		hashMap.put("groupInst", "LEFT");
		hashMap.put("printDate", "LEFT");
		Map<String, String> templateMap = new HashMap<String, String>();
		templateMap.put("date", workdate);
		String serid = PublicMethods.getCnSeqnumByTemplate(Long.valueOf(bankid), "normal",externalSession, templateMap);
		BRC.getLog().info(printMap);
		BRC.getLog().info(String.valueOf("----------"+sqlMap.get("fileName")));
		BRC.getLog().info("**********************"+serid);
		BRC.getLog().info(String.valueOf("----------"+acGuarcont.getCifid()));
		BRC.getLog().info("**********************"+ acGuarcont.getContno());
		HashMap<String, Object> respMap=new HashMap();
		//7。文件表数据更新
		try {
			HashMap<String, Object> signDataMap = new HashMap<String, Object>();
			signDataMap.put("UniversalObject", uo);
			String filetype="24";
			respMap = uo.GetLoopHtmlToPdfs(printMap,loopMap, String.valueOf(sqlMap.get("fileName")), acGuarcont.getContno() +"_"+serid,
					acGuarcont.getCifid() + "/" +  acGuarcont.getContno() + "/",true,showFlagHm,filetype);
			System.out.println("respMap =" + respMap.toString());
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("生成最高额抵押合同文件执行失败！");
			return arg1;
		}
		if (respMap == null) {
			arg1.setRespCode("9998");
			arg1.setRespMap("hashMap", respMap);
			arg1.setRespDesc("生成最高额抵押合同文件执行失败！");
			return arg1;
		} else {
			// 更新业务文件数据表
			String seqSql = "SELECT t1.file_seq FROM document_filing t1 WHERE t1.bankid='" + bankid + "'"
					+ " AND t1.relserid='"
					+  gccontno + "' AND t1.file_type='24' AND t1.file_sts='1' ";
			String strSeq = String.valueOf(uo.Dao().getUniqueValueBySql(seqSql));
			int fileSeq = 0;
			if (!"null".equals(strSeq)) {
				fileSeq = Integer.valueOf(strSeq);
			}
			int newFileSeq = fileSeq + 1;
			DocumentFiling documentFiling = new DocumentFiling();
			documentFiling.setBankid(bankid);
			documentFiling.setSerid(serid);
			documentFiling.setFileSeq((long) newFileSeq);
			documentFiling.setRelserid(acGuarcont.getContno());
			documentFiling.setFieldname("最高额抵押合同");
			documentFiling.setFileType("24");
			documentFiling.setFilename(String.valueOf(respMap.get("destFile")));
			documentFiling.setFileurl(String.valueOf(respMap.get("filePath")));
			documentFiling.setOperid(operid);
			documentFiling.setInstcode(brno);
			documentFiling.setOperationDate(workdate);
			documentFiling.setFileSts("1");
			documentFiling.setCifid(acGuarcont.getCifid());
			documentFiling.setFiledescription("最高额抵押合同");
			documentFiling.setExdate(workdate);
			// 将原有业务文件数据置失效
			uo.Dao().executeSql(
					"UPDATE document_filing SET file_sts='0' WHERE file_type='24' and relserid='" +  acGuarcont.getContno()
							+ "' AND file_seq='" + fileSeq + "' AND bankid='" + bankid + "'");
			// 合同凭证留痕表
			DocumentFilingDao documentFilingDao = new DocumentFilingDao();
			documentFilingDao.setExternalSession(uo.getSession());
			documentFilingDao.insert(documentFiling);
			arg1.setRespCode("0000");
			arg1.setRespMap("hashMap", respMap);
			arg1.setRespDesc("生成最高额抵押合同执行成功！");
			return arg1;
		}
	}

	/**
	 * 查询权属人信息
	 * @param daoTemplate	数据库持久对象
	 * @param cifid			客户编号
	 * @param isHeader		是否为抬头:0-否、1-是
	 * @param applyNo		申请编号
	 * @param bankId		法人编号
	 * @return
	 */
	private Map<String, Object> getCustomerInfo(
			DaoTemplateImpl daoTemplate, String cifid, int isHeader, String applyNo, Object bankId) throws MyDefineException{

		//2024-02-06 MIS-1130 最高额抵押合同取值优化 取共借人、担保人信息的时候未添加申请号，会取到其他贷款中的信息
		Map<String, Object> result = new HashMap<>();
		String sql = "SELECT\n" +
				"\tD.CLINAME BCLINAME, D.MTEL BTEL, D.FAMADDR BADDRESS,\n" +
				"\tD.CERTTYPE, D.FAMADDR WORKADDR, D.MTEL LEGALTEL, D.CLINAME LEGALPERSON,\n" +
				"\tD.MTEL WORKCORPTEL\n" +
				"FROM IND_BASE D\n" +
				"WHERE D.BANKID = " + bankId + "\n" +
				"AND D.CIFID = '" + cifid + "'\n" +
				"UNION ALL\n" +
				"SELECT\n" +
				"\tD.CLINAME BCLINAME, D.TEL BTEL, D.ADDRESS BADDRESS,\n" +
				"\tD.CERTTYPE, D.WORKADDR, D.LEGALTEL, D.LEGALPERSON,\n" +
				"\tD.WORKCORPTEL\n" +
				"FROM BUS_SURVEY_COBORROWER D\n" +
				"WHERE D.BANKID = " + bankId + "\n" +
				"AND D.APPLYNO = '" + applyNo + "'\n" +
				"AND D.CIFID = '" + cifid + "'\n" +
				"UNION ALL\n" +
				"SELECT\n" +
				"\tD.CLINAME BCLINAME, D.TEL BTEL, D.ADDRESS BADDRESS,\n" +
				"\tD.CERTTYPE, D.WORKADDR, D.LEGALTEL, D.LEGALPERSON,\n" +
				"\tD.WORKCORPTEL\n" +
				"FROM BUS_SURVEY_GUARANTOR D\n" +
				"WHERE D.BANKID = " + bankId + "\n" +
				"AND D.APPLYNO = '" + applyNo + "'\n" +
				"AND D.CIFID = '" + cifid + "'\n";
		List<Map> listMap = daoTemplate.getMapListBySql(sql);

		if(listMap != null && listMap.size()>0){
			result = listMap.get(0);
		}
		String bcliname = MapUtils.getString(result, "bcliname");
		String btel = MapUtils.getString(result, "btel");
		String baddress = MapUtils.getString(result, "baddress");
		String workaddr = MapUtils.getString(result, "workaddr");
		String certtype = MapUtils.getString(result, "certtype");
		String workCorpTel = MapUtils.getString(result, "workcorptel");
		String legalTel = MapUtils.getString(result, "legaltel");//法人电话
		String legalPerson = MapUtils.getString(result, "legalperson");//法人姓名

		if(IfmsTool.isNull(bcliname)) {
			bcliname = "";
		}

		if(IfmsTool.isNull(btel)) {
			btel = "";
		}

		if(IfmsTool.isNull(baddress)) {
			baddress = "";
		}

		if(IfmsTool.isNull(workaddr)) {
			workaddr = "";
		}

		if(IfmsTool.isNull(workCorpTel)) {
			workCorpTel = "";
		}

		if(IfmsTool.isNull(legalTel)) {
			legalTel = "";
		}

		if(IfmsTool.isNull(legalPerson)) {
			legalPerson = "";
		}

		if(certtype != null && (certtype.equals("110") || certtype.equals("111"))){
			result.put("bcliname", bcliname);
			result.put("baddress", baddress);
			result.put("btel", btel);
		}else{

			if(1 == isHeader){
				//20240206 (MIS-1130-最高额抵押合同取值优化) 合同开头,抵押物权属人为公司客户，取值为企业名称
				result.put("bcliname", bcliname);
				result.put("baddress", workaddr);
				result.put("btel", workCorpTel);
			}else{

				//20231221 (MIS-1021-最高额抵押合同-取值调整) 由于在此之前法人联系电话不是必输，所以强校验必填
				if(IfmsTool.isNull(legalPerson)){
					log.info("法人名称为空,客户编号：" + cifid);
					throw new MyDefineException("法人名称为空");
				}

				if(IfmsTool.isNull(legalTel)){
					log.info("法人联系电话为空,客户编号：" + cifid);
					throw new MyDefineException("法人联系电话为空");
				}
				result.put("bcliname", legalPerson);
				result.put("baddress", workaddr);
				result.put("btel", legalTel);
			}
		}
		return result;
	}

}











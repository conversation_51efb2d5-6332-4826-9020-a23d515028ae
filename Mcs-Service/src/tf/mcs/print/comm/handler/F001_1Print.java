package tf.mcs.print.comm.handler;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.mcs.Tools;
import tf.mcs.dao.AcBusinessvchDao;
import tf.mcs.dao.AcTepaymentDao;
import tf.mcs.dao.TrsRecordLogDao;
import tf.mcs.model.AcTepayment;
import tf.mcs.model.TrsRecordLog;
import tf.tools.PrintTools;
import tf.tools.StringTools;
import tf.tools.UniversalObject;
import tf.tools.WorkTools;
/**
 * 账务中心受托支付凭证打印(跨行转账费用)
 * <AUTHOR>
 * @date 2018-11-26
 * @Company tfrunning
 *
 */
public class F001_1Print{
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	public ArrayList doOperation(HashMap hm,String printName,UniversalObject uo) throws Exception {
		ArrayList list = new ArrayList();
		try {
			DaoTemplate daoTemplate = (DaoTemplate) hm.get("daoTemplate");
			Session externalSession = (Session) hm.get("externalSession");
			CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
			BRC.getLog().info("###########进入"+this.getClass().getName()+"类############");
			// session
			AcBusinessvchDao acBusinessvchDao = new AcBusinessvchDao();
			acBusinessvchDao.setExternalSession(externalSession);
			TrsRecordLogDao trsRecordLogDao =new TrsRecordLogDao();
			trsRecordLogDao.setExternalSession(externalSession);
			//返回hashMap
			HashMap rtnmap=new HashMap();
			// 公用域获取 
			String brno = cdbt.getOpBr();
			String operid = cdbt.getOpTel();
			long bankid = cdbt.getiBanks();
			String workDate = cdbt.getWorkDate();
			RespDataBusData rdbd = new RespDataBusData();
			//前台获取
			TrsRecordLog trsRecordLog =new TrsRecordLog();
			long traceNo=Long.valueOf(hm.get("traceNo").toString());//交易流水号（必输）
			String txdate=(String)hm.get("txdate");//交易日期（必输）
			//String vchno=(String) hm.get("vchno");//改为交易流水号
			//业务处理
			trsRecordLog.setBankid(bankid);
			trsRecordLog.setTraceNo(traceNo);
			trsRecordLog.setTxdate(txdate);//交易日期
			trsRecordLog.setSts("1");//交易成功
			trsRecordLog.setTxCode(hm.get("txCode").toString());//交易码
			trsRecordLog=trsRecordLogDao.findUniqueByEntity(trsRecordLog);
			
			if(trsRecordLog==null) {//自主支付返回空文件路径
				return list;
			}
			/*放款时受托支付只打印行内转账，行外的放款时只进行冻结不进行转账，需要在账务中心中打印*/
			AcTepaymentDao acTepaymentDao = new AcTepaymentDao();
			acTepaymentDao.setExternalSession(uo.getSession());
			AcTepayment acTepayment = new AcTepayment();
			acTepayment.setBankid(bankid);
			acTepayment.setVchno(trsRecordLog.getVchno());
			acTepayment.setSerid(trsRecordLog.getRelserid());
			acTepayment = acTepaymentDao.findUniqueByEntity(acTepayment);
			double freeAmt = 0l;
			if(acTepayment.getFeeAmt()!=null) {
				 freeAmt = acTepayment.getFeeAmt();
			}
		   
		    String busTraceNo = String.valueOf(acTepayment.getTraceNo());
		    String accType = String.valueOf(acTepayment.getAccType());
		    if("1".equals(accType)) {
		    	return list;
		    }
			/*****************************dataHm打印替换参数**************************/
			DecimalFormat moneyFormat = new DecimalFormat("#,##0.00");
			DecimalFormat rateFormat = new DecimalFormat("0.00####");
			HashMap dataHm =new HashMap();
			dataHm.put("instcode",String.valueOf(trsRecordLog.getTxbrno()));//机构名称
			dataHm.put("txdate",StringTools.strToDateFormat(trsRecordLog.getTxdate()+" "+trsRecordLog.getTxtime()));//交易时间
			dataHm.put("hxtraceno",String.valueOf(trsRecordLog.getTraceNo()));//交易流水
			dataHm.put("chkopid",String.valueOf(trsRecordLog.getTel()));//经办/复合
			dataHm.put("chkopna","");//经办/复合
			dataHm.put("txcode",String.valueOf(trsRecordLog.getTxCode()));//交易代码
			dataHm.put("trname",String.valueOf(trsRecordLog.getTxName()));//交易名称
			dataHm.put("deacNo",String.valueOf(trsRecordLog.getRepacNo()));//付费帐号
			dataHm.put("deacNa",String.valueOf(trsRecordLog.getRepacName()));//付费户名
			dataHm.put("freeAmt","￥"+moneyFormat.format(freeAmt));//行内不收手续费，行外需要收手续费在账务中心处理
			dataHm.put("freesum", "￥"+moneyFormat.format(freeAmt));//收费合计
			dataHm.put("freechisum",WorkTools.toChineseNumerals(new BigDecimal(freeAmt)));//汇划金额大写
			dataHm.put("jboperna", Tools.getSessionOpername(bankid, String.valueOf(trsRecordLog.getTel())));//经办人编号
			dataHm.put("jboperid", String.valueOf(trsRecordLog.getTel()));//经办人名称
			dataHm.put("autid", String.valueOf(trsRecordLog.getAut()));//授权人编号
			dataHm.put("autna", Tools.getSessionOpername(bankid, String.valueOf(trsRecordLog.getAut())));//授权人名称
			Iterator it = dataHm.entrySet().iterator(); 
			while (it.hasNext()) { 
			    Map.Entry entry = (Map.Entry) it.next(); 
			    Object key = entry.getKey(); 
			    Object val = entry.getValue(); 
			    if("null".equals(val)){
			    	entry.setValue("");
			    }
			}
			PrintTools pt=new PrintTools(); 
			HashMap<String, Object> signDataMap = new HashMap<String, Object>();
			signDataMap.put("UniversalObject", uo);
			signDataMap.put("sendPalessFlag", String.valueOf(hm.get("sendPalessFlag")));
			signDataMap.put("branch_id", String.valueOf(trsRecordLog.getTxbrno()));
			/*获取机构章*/
			signDataMap.put("OPE_TYPE", "7");//获取机构章图片以及业务验证码
			signDataMap.put("SERIAL_NO",  String.valueOf(trsRecordLog.getTraceNo()));//交易流水号
			signDataMap.put("OPE_TIME", String.valueOf(trsRecordLog.getTxdate()));//交易时间
			signDataMap.put("TRADE_CODE", String.valueOf(trsRecordLog.getTxCode()));//交易码
			signDataMap.put("TYPE", "1");//1业务专用章，2回单专用章，3受理专用章
			/*无纸化准备参数*/
			signDataMap.put("branchname", Tools.getSessionInstname(bankid, trsRecordLog.getTxbrno()));
			signDataMap.put("tellerid", trsRecordLog.getTel());
			signDataMap.put("tellername", Tools.getSessionOpername(bankid, trsRecordLog.getTel()));
			signDataMap.put("txdate", trsRecordLog.getTxdate());
			signDataMap.put("txname", trsRecordLog.getTxName());
			signDataMap.put("txcode", trsRecordLog.getTxCode());
			signDataMap.put("flowid", trsRecordLog.getTraceNo());
			signDataMap.put("vouchertype", "2");
			String snedFlag=String.valueOf(hm.get("printFlag"));
			HashMap<String, Object> rtmap = pt.GetHtmlToPdf(dataHm, trsRecordLog.getVchno()+"_"+workDate+"_"+traceNo+"_"+"fee"+snedFlag, printName, "/"+trsRecordLog.getCifid()+"/",signDataMap);
			list.add(rtmap);
			return list;
		}catch(Exception e) {
			log.error(e.getMessage(), e);
			throw new Exception("受托支付打印发生异常");
		}	
		
	}

}

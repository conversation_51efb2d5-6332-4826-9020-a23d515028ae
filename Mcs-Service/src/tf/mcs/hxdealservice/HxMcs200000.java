package tf.mcs.hxdealservice;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;

import tf.core.dao.DaoTemplate;
import tf.tools.StringTools;
import tf.tools.UniversalObject;
import tf.tools.WorkTools;
/**
 * 核心交易功能描述:00010000200000_账户信息查询
 * 
 * <AUTHOR>
 * @date 2018-10-22
 * @version 1.0
 */
public class HxMcs200000 {
	private static org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(HxMcs200000.class);
	private static String hxDealCode=HxMcsNumConfig.hxMcs200000;
	/**
	 * @param sendMap  必传字段  帐号_ACCT-NO 核心机构号_branch_id
	 * @return rtnMap
	 * @throws Exception
	 */
	public static HashMap<String, Object> dealMethod(UniversalObject uo,HashMap<String, Object> sendMap) throws Exception{
		try {
			if(sendMap==null || uo==null) {
				throw new Exception("核心交易："+hxDealCode+"_账户信息查询失败，所需参数为空！");
			}
			/*交易参数*/
			String serviceSn = StringTools.getServiceSn(uo);//交易流水
			String serviceTime = uo.getServiceDate() + new SimpleDateFormat("HHmmss").format(Calendar.getInstance().getTime());//交易时间 YYYYMMDDHHmmss。
			String branchId = String.valueOf(sendMap.get("branch_id"));//核心机构号
			HashMap hashMap = (HashMap) WorkTools.com_parm_result("ESBID", uo.Dao());
			String esbid = hashMap.get("val1").toString();//系统代码|渠道号|版本号
			String makStr = hashMap.get("val2").toString();// 密钥标签
			String[] esb = esbid.trim().split("\\|");
			String requesterId = esb[0];// 系统代码
			String channelId = esb[1];// 渠道号
			String versionId = esb[2];// 版本号
			String acctNo = String.valueOf(sendMap.get("ACCT-NO"));//帐号
			DaoTemplate daoTemplate=uo.Dao();
			String groupflg=(String)daoTemplate.getUniqueValueBySql("select nvl(groupflg,'0') from cn_basicinfo where instcode='"+branchId+"' and bankid="+uo.getBankid());
			if("1".contentEquals(groupflg)) {//如果是直营中心，则与核心通讯时使用总行营业部
				branchId="*********";
			}
			HashMap<String, Object>  sendHash = new HashMap<String, Object>();
			/*报必要参数配置*/
			sendHash.put("service_sn", serviceSn);
			sendHash.put("requester_id", requesterId);
			sendHash.put("channel_id", channelId);
			sendHash.put("version_id", versionId);
			sendHash.put("service_id", hxDealCode);
			sendHash.put("branch_id", branchId);
			sendHash.put("service_time",serviceTime);
			sendHash.put("T_KEY_LABEL", makStr);
			/*交易参数配置*/
			sendHash.put("FUNC", "9");//固定位9表示查询
			sendHash.put("ACCT-NO", acctNo);
			/*报文mac加密*/
			String requestStr = "9"+acctNo;//报文加密所需字符串
			byte[] macData = StringTools.getMACBlock(serviceSn,serviceTime,requestStr);
			String mac = StringTools.getMacStr(macData,makStr);
			sendHash.put("MAC", mac);
			HashMap<String, Object>  rtMap = uo.commHelper(hxDealCode, sendHash);
			if("S000A000".equals(String.valueOf(rtMap.get("code")))){
				rtMap.put("SA-BAL", WorkTools.getCoreNagetiveNumber(String.valueOf(rtMap.get("SA-BAL"))));//余额
				rtMap.put("SA-AVL-BAL", WorkTools.getCoreNagetiveNumber(String.valueOf(rtMap.get("SA-AVL-BAL"))));//可用余额
				rtMap.put("SA-OD-AMT", WorkTools.getCoreNagetiveNumber(String.valueOf(rtMap.get("SA-OD-AMT"))));//透支金额
				rtMap.put("SA-ACCT-BALL", WorkTools.getCoreNagetiveNumber(String.valueOf(rtMap.get("SA-ACCT-BAL"))));//账户余额
			}
			return rtMap;
		}catch(Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			throw new Exception("核心交易："+hxDealCode+"_账户信息查询，发生异常！");
		}
	}
	
}

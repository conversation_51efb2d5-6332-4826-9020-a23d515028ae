package tf.mcs.cust.handler;

import java.util.HashMap;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.DeFlowrecordDao;
import tf.mcs.dao.DoeChangeInfoDao;
import tf.mcs.model.DoeChangeInfo;

/**
 * @description 对应业务-客户经理自动取消实体变更申请
 * <AUTHOR>
 * @date 2020-06-11
 */
public class DeleteDoeChangeInfo implements CommonHandlerStub {
		// 获取日志
		private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());

		@Override
		public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
			BRC.getLog().info(arg0);
			// 获取session
			RespDataBusData rdbd = new RespDataBusData();
			DBPoolFactory dbpf = arg0.getReqDBPoolservice();
			Session externalSession = dbpf.getTransSession();
			
			DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
			
			DoeChangeInfoDao doeChangeDao = new DoeChangeInfoDao();
			doeChangeDao.setExternalSession(externalSession);
			
			DeFlowrecordDao deFlowrecordDao = new DeFlowrecordDao();
			deFlowrecordDao.setExternalSession(externalSession);
			
			// 前台参数获取
			HashMap hMap=(HashMap) arg0.get("hashMap");
			String applyno =(String) hMap.get("applyno");
			String cifid =(String) hMap.get("cifid");
			// 公共域获取
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();
			// 业务处理
			DoeChangeInfo doeChange = new DoeChangeInfo();
			DoeChangeInfo dataToDelete = new DoeChangeInfo();
			try {
				doeChange.setBankid(bankid);
				doeChange.setApplyno(applyno);
				doeChange.setCifid(cifid);
				dataToDelete = doeChangeDao.getEntityByPK(doeChange);
				doeChangeDao.delete(dataToDelete);
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("9999");
				rdbd.setRespDesc("删除实体变更申请信息执行失败！");
				return rdbd;
			}
			String mainSql="Delete from de_flowrecord "
					+ "where bankid='"
					+ bankid
					+ "' and applyno='"
					+ applyno
					+ "' and phaseno like '0%'";
			try {
				daoTemplate.executeSql(mainSql);
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("9999");
				rdbd.setRespDesc("删除流程记录信息执行失败！");
				return rdbd;
			}
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("删除实体变更申请信息执行成功！");
			return rdbd;
		}
}

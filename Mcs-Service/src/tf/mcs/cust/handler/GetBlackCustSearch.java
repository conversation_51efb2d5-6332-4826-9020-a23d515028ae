package tf.mcs.cust.handler;
import java.util.HashMap;
import java.util.Map;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;
/**
 * @version v1.1 edited by wuzhujun 20160527 梳理代码，删除无用代码
 */
public class GetBlackCustSearch extends HibernateDaoSupport implements CommonHandlerStub {
		@Override
		public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {// 获取所有客户列表
			BRC.getLog().info(arg0); 
			// 获取前台参数
			DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
			Pager pager = (Pager) arg0.get("pager");   
			Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
			// 公用域获取
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();
			whereMap.put("#EQUAL#t1.bankid", bankid);
			try {
				String mainSql="select t1.* from cust_blacklist t1 where  t1.cifid not in (select a.cifid from apply_blackinfo a,de_flowrecord b    where a.bankid=b.bankid and a.applyno=b.applyno and b.enddate is null and a.bankid='"+bankid+"') ";// ******** wuzhujun 修改bankid从总线取值
				daoTemplate.getMapListByLikeSql(mainSql, whereMap, pager); 
				for (int i = 0; i < pager.getList().size(); i++) {
					HashMap hMap = (HashMap) pager.getList().get(i);
					hMap.put("iptUsrName", Tools.getSessionOpername(bankid,(String) hMap.get("iptUsr")));
					hMap.put("iptBrnoName", Tools.getSessionInstname(bankid,(String) hMap.get("iptBrno")));
				}		
			} catch (Exception e) {
				e.printStackTrace();
				throw new Exception(e.toString());
			}
			RespDataBusData rdbd = new RespDataBusData();
			rdbd.setRespEntity("pager", pager);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("获取客户列表GetBlackCustSearch执行成功!");
			return rdbd;
		}
	}

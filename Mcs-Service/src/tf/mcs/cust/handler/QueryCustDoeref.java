package tf.mcs.cust.handler;

import java.util.HashMap;

import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.DoeBaseDao;
import tf.mcs.model.DoeBase;

public class QueryCustDoeref implements CommonHandlerStub  {
	@Override
public  RespDataBusData subTxDealer(ReqDataBusData arg0,RespDataBusData arg1) throws Exception{
	   //获取前台参数 
		HashMap hash=(HashMap) arg0.get("hashMap");
		String  certtype=(String) hash.get("certtype");
		String  certno=(String) hash.get("certno");
		//公共域获取
		BRC.getLog().info(arg0);
		
		CommonDBT cdbt=arg0.getCommDC();
		long bankid=cdbt.getiBanks();
		String  operid=cdbt.getOpTel();
		Session  externalSession=arg0.getReqDBPoolservice().getTransSession();
		DoeBase doeBase=new  DoeBase();
		DoeBaseDao doeBaseDao=new DoeBaseDao();
		doeBaseDao.setExternalSession(externalSession);
	    DaoTemplateImpl  daoTemplateImpl=(DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
	    String  getSql="select t1.* from  doe_base t1 where certno='"+certno+"' and certtype='"+certtype+"' and bankid="+bankid+"";
	    Object entity=daoTemplateImpl.getUniqueMapRowBySql(getSql);
	   
	    if(entity!=null){
	    	HashMap hMap=(HashMap) entity;
	    	doeBase.setCliname((String) hMap.get("cliname"));
	    	doeBase.setDoetype((String) hMap.get("doetype"));
	    	doeBase.setIndustrytype((String) hMap.get("industrytype"));
	    	doeBase.setIndustryname((String)hMap.get("industryname"));
	    	doeBase.setOperDate((String)hMap.get("operDate"));
	    	doeBase.setLegalperson((String)hMap.get("legalperson"));
	    	doeBase.setLegalcertno((String)hMap.get("legalcertno"));
	    	doeBase.setDoeaddr((String)hMap.get("doeaddr"));
	    	doeBase.setCerttype(certtype);
	    	doeBase.setCertno(certno);
	    	doeBase.setBankid(bankid);
	    	doeBase.setCifid((String) hMap.get("cifid"));
	    }
	    
		RespDataBusData rdbd =new RespDataBusData();
		rdbd.setRespCode("0000");
		rdbd.setRespEntity("doeBase", doeBase);
		rdbd.setRespDesc("查询经营实体信息成功!");
		return rdbd;
		
}
}

package tf.mcs.eod.handler;

import java.util.ArrayList;
import java.util.HashMap;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import tf.brc.eod.tools.IOperInstNoInstcode;
import tf.cbs.brc.io.FilesTools;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.dao.DaoTemplateImpl;
import tf.tools.StringTools;
/**
 * 描述 日终获取担保圈信息
 * <AUTHOR>
 * @date ********
 * @version v1.0 
 *
 */
public class EodGuaranteeCircleCheck implements IOperInstNoInstcode {

	@Override
	public void doOperation(HashMap hm) throws Exception {
		BRC.getLog().info("----------------进入EodGuaranteeCircleCheck类--------------------");
		DaoTemplate dao = (DaoTemplate) hm.get("daoTemplate");
		long bankid = Long.valueOf((String)hm.get("bankid").toString()).longValue();
		String sysDate = (String)hm.get("txdate");
		/**
		 * 抽取互保借款人信息(已放款)
		 */
		//获取已放款借款人信息及合同信息
		String busiSql = "select contno,cifid,cliname from ac_businessvch where bankid= " + bankid + " and account_type='1' and vch_sts='10' " +
				" union select contno,cifid,cliname from bus_affrim_coborrower where bankid= " + bankid + " and state='1' ";
		ArrayList busiList = (ArrayList) dao.getMapListBySql(busiSql);
		for(int i=0;i<busiList.size();i++){
			HashMap busiHm = (HashMap) busiList.get(i);
			String busiContno = (String) busiHm.get("contno");
			String busiCifid = (String) busiHm.get("cifid");
			String busiCliname = (String) busiHm.get("cliname");
			BRC.getLog().info("借款人A【"+busiContno+","+busiCifid+","+busiCliname+"】");
			// 获取担保人信息
			String guarSql = "select contno,cifid,cliname from bus_gcc_relative t1,bus_affrim_guarantor t2 " +
					"where t1.bankid = t2.bankid and t1.bankid = " + bankid + " and t1.gccontno = t2.gccontno " +
					"and t1.status='20' and t2.state='1' and t1.contno='"+busiContno+"'";
			ArrayList guarList = (ArrayList) dao.getMapListBySql(guarSql);
			if(guarList.size()==0){
				BRC.getLog().info("借款人无担保人");
			}
			for(int j=0;j<guarList.size();j++){
				HashMap guarHm = (HashMap) guarList.get(j);
				String guarContno = (String) guarHm.get("contno");
				String guarCifid = (String) guarHm.get("cifid");
				String guarCliname = (String) guarHm.get("cliname");
				BRC.getLog().info("担保人B【"+guarContno+","+guarCifid+","+guarCliname+"】");
				// 担保人是否借款
				String guarBusiSql = "select contno,cifid,cliname from ac_businessvch where bankid= " + bankid + " " +
						"and account_type='1' and vch_sts='10' and cifid = '"+guarCifid+"' " +
						" union select contno,cifid,cliname from bus_affrim_coborrower where bankid= " + bankid + " " +
						"and state='1' and cifid = '"+guarCifid+"'";
				ArrayList guarBusiList = (ArrayList) dao.getMapListBySql(guarBusiSql);
				if(guarBusiList.size()==0){
					BRC.getLog().info("担保人无借款");
				}
				for(int z=0;z<guarBusiList.size();z++){
					HashMap guarBusiHm = (HashMap) guarBusiList.get(z);
					String guarBusiContno = (String) guarBusiHm.get("contno");
					String guarBusiCifid = (String) guarBusiHm.get("cifid");
					String guarBusiCliname = (String) guarBusiHm.get("cliname");
					BRC.getLog().info("借款人B【"+guarBusiContno+","+guarBusiCifid+","+guarBusiCliname+"】");
					// 借款人与担保人是否互保
					String resultSql = "select contno,cifid,cliname from bus_gcc_relative t1,bus_affrim_guarantor t2 " +
							"where t1.bankid = t2.bankid and t1.bankid = " + bankid + " and t1.gccontno = t2.gccontno " +
							"and t1.status='20' and t2.state='1' and t2.cifid='"+busiCifid+"' and t1.contno='"+guarBusiContno+"'";
					ArrayList resultList = (ArrayList) dao.getMapListBySql(resultSql);
					if(resultList.size()!=0){
						BRC.getLog().info(">>>>>>合同【"+busiContno+","+busiCifid+","+busiCliname+"与"+guarBusiContno+","+guarBusiCifid+","+guarBusiCliname+"】互保<<<<<<");
					}else{
						BRC.getLog().info("合同【"+busiContno+"与"+guarBusiContno+"】未发生互保");
					}
				}
			}
			BRC.getLog().info("---------------------------------------------");
		}
		/**
		 * 抽取多人互保借款人信息(已放款)
		 */
		ArrayList list = new ArrayList();
		ArrayList inList = new ArrayList();
		HashMap hm1 = new HashMap();
		inList.add(0, hm1);
		list.add(0, inList);
		/**
		 * 申请时互保信息推送
		 */
	}
	public static void main(String[] args){
		ClassPathXmlApplicationContext applicationContext= new ClassPathXmlApplicationContext("applicationContext.xml");
		applicationContext.start();
		BRC.setBrcContext(applicationContext);
		BRC.setRunMode(1);
		// 初始化操作
		BRC.getLog().debug("开始日终");
		String sAppHome = System.getProperty("user.dir");
		System.out.println("\n 当前路径是:" + sAppHome);
		
		BRC.setProperty("APPHOME", sAppHome);
		java.io.File cf = FilesTools.findFile("brc.xml");
		if (cf != null) {
			BRC.init(cf);
		} else {
		}
		//日终检查和初始化初始化
		SessionFactory sessionFactory = (SessionFactory) BRC.getBrcContext().getBean("sf");
		Session psessCheck=sessionFactory.openSession();		
		EodGuaranteeCircleCheck ed= new EodGuaranteeCircleCheck();
		HashMap hm = new HashMap();
		DaoTemplateImpl daoTemplate = new DaoTemplateImpl();
		daoTemplate.setExternalSession(psessCheck);
		psessCheck.beginTransaction();
		hm.put("daoTemplate", daoTemplate);
		hm.put("txdate", "********");
		hm.put("bankid", 100000);
		hm.put("externalSession", psessCheck);

		try {
			ed.doOperation(hm);
			psessCheck.getTransaction().commit();
			psessCheck.close();
		} catch (Exception e) {
			psessCheck.getTransaction().rollback();
			psessCheck.close();
			StringTools.stackLog(e);
		}
		
	}

}

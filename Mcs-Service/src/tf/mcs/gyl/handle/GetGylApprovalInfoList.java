package tf.mcs.gyl.handle;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.model.CnProduct;

/**
 * @funtion 获取供应链已完成申请列表
 * <AUTHOR>
 * @date 20170822
 * @version 1.0
 */
public class GetGylApprovalInfoList extends HibernateDaoSupport implements
		CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		BRC.getLog().info("########进入GetGylApprovalInfoList类#########");
		RespDataBusData rdbd = new RespDataBusData();
		// 获取session
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		// 公共域获取
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pager");
		HashMap hashMap = (HashMap) arg0.get("hashMap");
		String bustype = (String) hashMap.get("bustype");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 业务处理
		if(bustype.equals("60")){
			whereMap.put("#SQL#", " 1=1 and t1.bustype = '60' order by t1.applyno desc");
		}
		try {
			String mainSql="select t1.*,t3.agre_highsum,t2.phasename,t2.opername  ,t4.begindate,t4.enddate,t4.bus_sum,nvl(t4.bus_bal,0) bus_bal ,t2.phaseno as recphaseno from app_main_info t1,de_flowrecord t2,app_bus_info t3 ,ac_businesscont t4 where t1.bankid='"
						+ bankid
						+ "' and t1.bankid=t2.bankid and t1.applyno=t2.applyno and t1.bankid=t3.bankid and t1.applyno=t3.applyno and t1.bankid=t4.bankid and t1.applyno=t4.applyno  and t1.app_usr='"
						+ operid + "' and t2.phasechoice='agree' and nvl(t2.enddate,null) is not null and t1.prdt_no like 'D%'";
			daoTemplate.getMapListByLikeSql(mainSql, whereMap, pager);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("获取业务申请信息已批准信息GetGylApprovalInfoList失败!");
			return rdbd;
		}

		//业务品种编码名称
		for (int i = 0; i < pager.getList().size(); i++) {
			HashMap hMap = (HashMap) pager.getList().get(i);
			hMap.put("prdtNa", "[" + (String)hMap.get("prdtNo") + "]" + tf.mcs.Tools.getProductName(bankid,(String)hMap.get("prdtNo")));
		}
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取业务申请信息已批准信息GetGylApprovalInfoList执行成功!");
		return rdbd;
	}
}
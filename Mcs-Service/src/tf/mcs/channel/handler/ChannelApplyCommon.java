package tf.mcs.channel.handler;

import org.apache.log4j.Logger;
import tf.brc.databus.RespDataBusData;
import tf.core.dao.DaoTemplate;
import tf.ifms.rpc.handler.InvestigationUtil;
import tf.mcs.comm.MyDefineException;
import tf.mcs.model.AppChannelInfo;

/**
 * @Description:
 * @date: 2024/11/13
 * @author: wl
 **/
public class ChannelApplyCommon {
    // 获取日志
    private static Logger log = Logger.getLogger(ChannelApplyCommon.class);


    /**
     * 渠道申请在途申请校验
     * @param daoTemplate
     * @param masterName
     * @param channelName
     * @param masterIdcard
     * @param oprateType 操作类型  addApply新增 updApply更新申请 reApply重新申请
     * @return
     * @throws Exception
     */
    public static RespDataBusData checkChannelOnWayApply(DaoTemplate daoTemplate,String masterName,String channelName,String masterIdcard,String applyno,String oprateType){
        RespDataBusData respTfData = new RespDataBusData();
        try {
            String chkSql = "select count(1) from app_channel_info aci,de_flowrecord dfr \n" +
                    " where aci.applyno = dfr.applyno and aci.bankid = '100000' \n" +
                    " and aci.master_name = '"+masterName+"' and aci.channel_name='"+channelName+"'\n" +
                    " and aci.master_idcard='"+masterIdcard+"' and ifnull(dfr.enddate,'') = '' " ;

            if("updApply".equals(oprateType)){//更新操作
                chkSql+=" and aci.applyno != '"+applyno+"' ";
            }
            String cnt = InvestigationUtil.getStringValueBySql(daoTemplate,chkSql);
            log.info("当前渠道名称为："+channelName+",负责人为："+masterName+"存在 "+cnt+" 笔在途的渠道申请记录!");
            if(!"0".equals(cnt)){
                respTfData.setRespCode("7777");
                respTfData.setRespDesc("当前渠道信息存在在途的渠道申请记录,请检查！");
                return respTfData;
            }
        }catch (Exception e){
            respTfData.setRespCode("7777");
            respTfData.setRespDesc("校验渠道信息申请在途业务错误！");
            return respTfData;
        }
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("校验渠道信息申请在途业务成功");
        return respTfData;
    }
}

package tf.mcs.perf.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lowagie.text.pdf.hyphenation.TernaryTree.Iterator;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.dao.PfBasewagesDao;
import tf.mcs.model.PfBasewages;
import tf.tools.UniversalObject;
/**
 * 功能描述： 获取固定工资列表
 * 
 * <AUTHOR>
 * @date 20141217
 */
public class QueryPfBaseWages implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		UniversalObject uo;
		try {
			// 完全初始化通用对象
			uo = new UniversalObject(arg0);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("8888");
			arg1.setRespDesc("微贷站点初始化时发生错误");
			return arg1;
		}
		uo.logInfo("############进入  QueryPfBaseWages ##################");
		// 公共域获取
		Long bankid = uo.getBankid().longValue();
		String workdate = uo.getServiceDate();
		String updUsr = uo.getOptUser();
		String opername = uo.getOptUserName();
		//获取前台数据
		HashMap hashMap = (HashMap) arg0.get("hashMap");
		String operid=hashMap.get("operid").toString();
		//获取PfBaseWages表中一条的数据
		PfBasewages pfBasewages=new PfBasewages();
		pfBasewages.setBankid(bankid);
		pfBasewages.setOperid(operid);
		PfBasewagesDao pfBasewagesDao=new PfBasewagesDao();
		pfBasewagesDao.setExternalSession(uo.getSession());
		try {
			pfBasewages=pfBasewagesDao.getEntityByPK(pfBasewages);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("收集数据时失败，无法获取信息! ");
			return arg1;
		}
		HashMap resMap = new HashMap();
		//获取岗位名称和用户名称
		String cnsql="select a2.opername,a2.staname,a4.instname from "
		+"(select instcode,bankid,operid,postid from cn_user_post where operid='"+operid+"' and bankid='"+bankid
		+"' and postid='"+pfBasewages.getPostid()+"') a1"
		+" left join cn_user a2 on a1.bankid=a2.bankid and a1.instcode=a2.instcode and a1.operid=a2.operid"
		+" left join cn_basicinfo a4 on a1.bankid=a4.bankid and a1.instcode=a4.instcode";
		try {	
		HashMap sqlMap= (HashMap) uo.Dao().getUniqueMapRowBySql(cnsql);
		if(sqlMap!=null){
		    HashMap hm=(HashMap)sqlMap;
		    resMap.put("opername", (String)hm.get("opername"));
		    resMap.put("staname", (String)hm.get("staname"));
		    resMap.put("instname", (String)hm.get("instname"));
		}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("收集数据时失败，无法获取信息! ");
			return arg1;		
		}
		// 获取职位数据字典
		String postsql = "select postid,postname from cn_post_define where bankid='"
				+ bankid + "'";
		ArrayList postList;
		try {
			postList = (ArrayList) uo.Dao().getMapListBySql(postsql);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("收集数据时失败，无法获取职位信息! ");
			return arg1;
		}
		ArrayList resPostList = new ArrayList();
		for (int i = 0; i < postList.size(); i++) {
			HashMap iteratorMap = (HashMap) postList.get(i);
			HashMap comboMap = new HashMap();
			comboMap.put("key", iteratorMap.get("postid"));
			comboMap.put("value", iteratorMap.get("postname"));
			resPostList.add(comboMap);
		}
		// 返回系统时间，当前操作员编号和姓名
		resMap.put("updDate", workdate);
		resMap.put("updUsr", updUsr);
		resMap.put("updUsrName", opername);
		// 返回职位数据字典和所需的信息
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespList("postList", resPostList);
		rdbd.setRespMap("hashMap", resMap);
		rdbd.setRespEntity("pfBasewages", pfBasewages);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取申请信息QueryAppBusInfo执行成功!");
		return rdbd;
	}
}
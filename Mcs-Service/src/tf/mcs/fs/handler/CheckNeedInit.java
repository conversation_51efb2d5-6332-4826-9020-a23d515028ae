package tf.mcs.fs.handler;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import tf.core.el.expression.LinkExp;
import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.PublicMethods;
import tf.mcs.dao.FsCatalogDao;
import tf.mcs.dao.FsDataDao;
import tf.mcs.dao.FsModelBodyDao;
import tf.mcs.dao.FsModelHeadDao;
import tf.mcs.dao.FsRecordMenuInfoDao;
import tf.mcs.dao.FsRecordOthInfoDao;
import tf.mcs.model.FsCatalog;
import tf.mcs.model.FsData;
import tf.mcs.model.FsModelBody;
import tf.mcs.model.FsModelHead;
import tf.mcs.model.FsRecordMenuInfo;
import tf.mcs.model.FsRecordOthInfo;
//检查报表是否需要初始画
public class CheckNeedInit implements CommonHandlerStub {
	private LinkExp leWFC;
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		BRC.getLog().info(arg0);
		this.leWFC = new LinkExp(arg0.getCommDC());
		// 获取session
		DBPoolFactory dbpf = arg0.getReqDBPoolservice();
		Session externalSession = dbpf.getTransSession();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		FsRecordMenuInfoDao fsRecordMenuInfoDao = new FsRecordMenuInfoDao();
		fsRecordMenuInfoDao.setExternalSession(externalSession);
		FsCatalogDao fsCatalogDao = new FsCatalogDao();
		fsCatalogDao.setExternalSession(externalSession);
		FsModelBodyDao fsModelBodyDao = new FsModelBodyDao();
		fsModelBodyDao.setExternalSession(externalSession);
		FsDataDao fsDataDao = new FsDataDao();
		fsDataDao.setExternalSession(externalSession);
		FsModelHeadDao fsModelHeadDao = new FsModelHeadDao();
		fsModelHeadDao.setExternalSession(externalSession);
		FsRecordOthInfoDao fsRecordOthInfoDao=new FsRecordOthInfoDao();
		fsRecordOthInfoDao.setExternalSession(externalSession);
		
		RespDataBusData rdbd = new RespDataBusData();

		
		// 获取前台参数
		HashMap<String, Object> queryMap = (HashMap<String, Object>) arg0.get("hashMap");
		String fsno = (String) queryMap.get("fsno");
		String fsmno = (String) queryMap.get("fsmno");
		String cifid = (String) queryMap.get("cifid");
		String readonly = (String) queryMap.get("readonly");//只读标志
		// 公用域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		long bankid = cdbt.getiBanks();
		String optel=cdbt.getOpTel();
		String brno=cdbt.getOpBr();
		String workDate=cdbt.getWorkDate();
		//业务操作
		String fsindexno="";
		String needInit="0";//是否需要初始化报表模版（ 需要选择已经填写过的模版）
		String curver="";
		HashMap rtnHashMap=new HashMap(); //返回的map
		
		FsRecordMenuInfo fsRecordMenuInfo=new FsRecordMenuInfo();
		fsRecordMenuInfo.setBankid(bankid);
		fsRecordMenuInfo.setFsno(fsno);
		fsRecordMenuInfo.setFsmno(fsmno);
		//fsRecordMenuInfo.setFsindexno(fsindexno);
		fsRecordMenuInfo=fsRecordMenuInfoDao.findUniqueByEntity(fsRecordMenuInfo);
		if(fsRecordMenuInfo==null){
			if(StringUtils.isNotEmpty(readonly)&&readonly.equals("1")){
				//查看只读，则不会赋值系统默认模版
				needInit="1";
				rtnHashMap.put("needInit", needInit);
				rdbd.setRespMap("hashMap", rtnHashMap);
				rdbd.setRespCode("0000");
				rdbd.setRespDesc("获取报表数据执行成功!");
				return rdbd;
			}
			needInit="1";
		}else{
			needInit="0";
			fsindexno=fsRecordMenuInfo.getFsindexno();
			curver=fsRecordMenuInfo.getCurver();
		}
		rtnHashMap.put("fsindexno", fsindexno);
		rtnHashMap.put("needInit", needInit);
		rtnHashMap.put("curver", curver);
		rdbd.setRespMap("hashMap", rtnHashMap);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取报表数据执行成功!");
		return rdbd;
	}
	public static String shiftFirst(String str) {
		char[] chs = str.toCharArray();
		if (chs[0] <= 'z' && chs[0] >= 'a') {
			chs[0] = (char) (chs[0] - 32);
		}
		return new String(chs);
	}
	private String exeLinkExpInit(String script, HashMap htab) {
		String rtnvalue = null;
		try {

			this.leWFC.genValueHash(htab);
			rtnvalue = (String) this.leWFC.getFinValue(script);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return rtnvalue;
	}
}

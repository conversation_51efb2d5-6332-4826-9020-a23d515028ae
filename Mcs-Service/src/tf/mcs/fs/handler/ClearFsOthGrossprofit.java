package tf.mcs.fs.handler;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.tools.UniversalObject;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
/**
 * <AUTHOR>
 * @file ClearFsOthGrossprofit.java
 * @Date 2017-2-15
 * @description 清空毛利润信息。
 */

public class ClearFsOthGrossprofit implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		BRC.getLog().info("------进入ClearFsOthGrossprofit类-----");
		BRC.getLog().info("----传入参数为---"+arg0);
		RespDataBusData rdbd = new RespDataBusData();
		String cifid = (String) arg0.get("cifid");
		String fsno = (String) arg0.get("fsno");
		UniversalObject uo = new UniversalObject(arg0);
		long bankid = uo.getBankid();
		DaoTemplate daoTemplate = uo.Dao();
		String sql = "delete from fs_oth_grossprofit where bankid="+bankid+" and cifid='"+cifid+"' and fsno='"+fsno+"'";
		//删除存货明细
		try{
			daoTemplate.executeSql(sql);
		}catch(Exception e){
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("清空毛利润清单表时发生错误!");
			return rdbd;
		}
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("清空毛利润清单表ClearFsOthGrossprofit成功!");
		return rdbd;
	}
}
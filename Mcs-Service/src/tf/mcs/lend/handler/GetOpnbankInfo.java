package tf.mcs.lend.handler;



import java.util.HashMap;
import java.util.Map;
 
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;

import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager; 

/**
 * 获取开户行信息
 * <AUTHOR>
 * @date 2020-06-28
 */
@Transactional
public class GetOpnbankInfo extends HibernateDaoSupport implements CommonHandlerStub {
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
	//获取前台参数
	Pager pager = (Pager) arg0.get("pager");

	//业务操作及检查
	Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
	HashMap map=(HashMap) arg0.get("hashMap");
	whereMap.put("#SQL#", "1=1 order by payeebk_no");
	String payeebkName=(String)map.get("payeeName");//支付行名
	DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
	String paystr[]=payeebkName.split(" ", -1);
	String sql="where ";
	for(int i=0;i<paystr.length;i++){
		String str=paystr[i];
		if("".equals(str)){
			continue;
		}else{
			if(i==paystr.length-1){
				sql=sql+" instr(payeebk_name,'"+str+"')>0   ";
			}else{
				sql=sql+" instr(payeebk_name,'"+str+"')>0  and ";
			}
			
			
		}
		
	}
	try {
		daoTemplate.getMapListByLikeSql("select * from cn_cpcn_bankinfo t1 "+sql, whereMap, pager);
	} catch (Exception e) {
		e.printStackTrace();
		log.error(e.getMessage(), e);
		throw new Exception(e.toString());
	}
	
	RespDataBusData rdbd = new RespDataBusData();
	rdbd.setRespEntity("pager", pager);
	rdbd.setRespCode("0000");
	rdbd.setRespDesc("获取开户行信息列表GetOpnbankInfo执行成功!");
	return rdbd;
}
}

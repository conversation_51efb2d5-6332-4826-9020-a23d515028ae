package tf.mcs.lend.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AcBusinesscontDao;
import tf.mcs.hxdealservice.HxMcs000101;
import tf.mcs.hxdealservice.HxMcs175500;
import tf.mcs.model.AcBusinesscont;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * 贷后发送双录系统
 * <AUTHOR>
 * 2019年6月24日
 */
public class SendAftbusInfo extends HibernateDaoSupport implements CommonHandlerStub {
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		BRC.getLog().info(arg0);
		BRC.getLog().info("#############进入SendAftbusInfo类################");
		// 获取session
		Session externalSession = (Session) arg0.getReqDBPoolservice().getTransSession();
		DaoTemplateImpl dao = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String workDate = cdbt.getWorkDate();
		String workTime = cdbt.getReqTime();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		// 前台参数获取
		HashMap hashMap = (HashMap) arg0.get("hashMap");
		String applyno=(String)hashMap.get("applyno");//申请号
		String contno=(String)hashMap.get("contno");//合同号
		String changerType=(String)hashMap.get("changerType");//业务变更类型
		RespDataBusData rdbd = new RespDataBusData();	
		UniversalObject uo;
		try {
			// 完全初始化通用对象
			uo = new UniversalObject(arg0);
		} catch (Exception e2) {
			StringTools.stackLog(e2);
			e2.printStackTrace();
			rdbd.setRespCode("8888");
			rdbd.setRespDesc("微贷站点初始化时发生错误");
			return rdbd;
		}
		
		String sql="select val from com_parm where parm_code='SL'";
		String sts=(String)dao.getUniqueValueBySql(sql);//双录检查是否启用标志
		if(!"1".equals(sts)) {	
			rdbd.setRespCode("8585");
			rdbd.setRespDesc("双录系统未启用，无需发送双录！");
			return rdbd;
		}
		
		AcBusinesscontDao acBusinesscontDao = new AcBusinesscontDao();
		acBusinesscontDao.setExternalSession(externalSession);
		AcBusinesscont acBusinesscont = new AcBusinesscont();
		acBusinesscont.setBankid(bankid);
		acBusinesscont.setContno(contno);
		acBusinesscont=acBusinesscontDao.findUniqueByEntity(acBusinesscont);
		if(acBusinesscont==null) {//合同信息未登记
			rdbd.setRespCode("7878");
			rdbd.setRespDesc("请先登记合同信息后，再发送双录系统");
			return rdbd;
		}
		
		String chksql="";
		List<Map> list=new ArrayList();
		if("01".equals(changerType)) {//担保人变更
			chksql="select distinct cifid,cliname,certno,cstype,type,certtype from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'3' as type from bus_survey_guarantor a , "
					+ "cust_info b where a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
					+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") "
					+ " and not exists (select 1 from bus_gcc_relative c , ac_guarcont d where c.bankid=d.bankid and d.guartype ='30' and c.gccontno=d.contno and a.cifid=d.cifid and c.contno='"+contno+"')";
			list=dao.getMapListBySql(chksql);
		}else if("02".equals(changerType)) {//共借人变更
			chksql="select distinct cifid,cliname,certno,cstype,type,certtype from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'2' as type from bus_survey_coborrower a , "
					+ "cust_info b where a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
					+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") "
							+ " and not exists(select 1 from bus_affrim_coborrower c where c.bankid="+bankid+" and c.cifid=a.cifid and c.contno='"+contno+"' )";
			list=dao.getMapListBySql(chksql);
		}else if("03".equals(changerType)) {//抵质押押品变更
			String typesql="select substr(col_no,1,1) as type from bus_survey_collateral where bankid="+bankid+" and applyno='"+applyno+"'";
			List<Map> typelist=dao.getMapListBySql(typesql);
			for(int i=0;i<typelist.size();i++) {
				Map map=(Map)typelist.get(i);
				String type=(String)map.get("type");
				if("A".equals(type)) {//质押
					chksql="select distinct cifid,cliname,certno,cstype,type,certtype from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'5' as type"
							+ " from bus_survey_collateral a , cust_info b where "
							+ "a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
							+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") and "
							+ "not exists (select 1 from bus_gcc_relative c , ac_guarcont d where c.bankid=d.bankid and d.guartype ='20' and c.gccontno=d.contno and a.cifid=d.cifid and c.contno='"+contno+"')";
					List<Map> list1=dao.getMapListBySql(chksql);
					if(list1.size()>0) {	
						list.addAll(list1);
					}
				}else if("B".equals(type)) {//抵押
					chksql="select distinct cifid,cliname,certno,cstype,type,certtype from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'4' as type"
							+ " from bus_survey_collateral a , cust_info b where "
							+ "a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
							+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") and "
							+ "not exists (select 1 from bus_gcc_relative c , ac_guarcont d where c.bankid=d.bankid and d.guartype ='10' and c.gccontno=d.contno and a.cifid=d.cifid and c.contno='"+contno+"')";
					List<Map> list1=dao.getMapListBySql(chksql);
					if(list1.size()>0) {	
						list.addAll(list1);
					}
				}
			}
		}
		
		if(list.size()==0) {
			rdbd.setRespCode("8383");
			rdbd.setRespDesc("当前业务下所有客户相关资料已发送双录系统，无须重复发送");
			return rdbd;
		}
		//发送双录
		try{
			//数据准备
			HashMap sendMap=new HashMap();
			sendMap.put("ContractNumber", contno);// 合同编号
			sendMap.put("ManagerID", operid);// 客户经理工号
			sendMap.put("Counts", list.size());// 记录数
			ArrayList<Map> array = new ArrayList<Map>();
			for (Map map : list) {
				HashMap tmp = new HashMap();
				tmp.put("UserIdentity", map.get("type"));// 客户身份
				tmp.put("CustomerName", map.get("cliname"));// 客户姓名
				tmp.put("CertID", map.get("certno"));
				tmp.put("CustomerID", map.get("cifid"));// 客户号
				if ("110".equals(map.get("certtype"))) {// 身份证
					tmp.put("CertType", "101");
				} else if ("210".equals(map.get("certtype"))) {
					tmp.put("CertType", "201");
				} else {
					continue;
				}
				array.add(tmp);
			}
			sendMap.put("array", array);
			//开始通讯
			HxMcs000101 hxMcs000101 = new HxMcs000101();
			HashMap rtnHMap = hxMcs000101.dealMethod(uo, sendMap);
			String returnsts=String.valueOf(rtnHMap.get("code"));
			if(!"0000".equals(returnsts)){
				String desc = "发送双录系统失败原因：【"+String.valueOf(rtnHMap.get("errorMsg")).trim()+"】";
				arg1.setRespCode("5000");
				arg1.setRespDesc(desc);
				return arg1;
			}
		}catch(Exception e){
			log.error(e.getMessage(), e);
			BRC.getLog().error("双录通讯异常:" + e.getStackTrace());
			arg1.setRespCode("9950");
			arg1.setRespDesc("双录通讯异常:");
			return arg1;
		}
		
		//登记双录信息表
		String insersql="";
		if("01".equals(changerType)) {//担保人变更
			insersql="insert into AC_BUS_MEMBER(BANKID,RELSERID,RELTYPE,CIFID,CLINAME,CERTNO,CERTTYPE,CSTYPE,STS,contno) "
					+ "select distinct "+bankid+",'"+applyno+"',type,cifid,cliname,certno,certtype,cstype,'0','"+contno+"' "
					+ "from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'3' as type from bus_survey_guarantor a , "
					+ "cust_info b where a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
					+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+")";
			log.info("登记双录信息表："+insersql);
			dao.executeSql(insersql);
		}else if("02".equals(changerType)) {//共借人变更
			insersql="insert into AC_BUS_MEMBER(BANKID,RELSERID,RELTYPE,CIFID,CLINAME,CERTNO,CERTTYPE,CSTYPE,STS,contno) "
					+ "select distinct "+bankid+",'"+applyno+"',type,cifid,cliname,certno,certtype,cstype,'0','"+contno+"' "
					+ "from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'2' as type from bus_survey_coborrower a , "
					+ "cust_info b where a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
					+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+")";
			log.info("登记双录信息表："+insersql);
			dao.executeSql(insersql);
		}else if("03".equals(changerType)) {//抵质押押品变更
			String typesql="select substr(col_no,1,1) as type from bus_survey_collateral where bankid="+bankid+" and applyno='"+applyno+"'";
			List<Map> typelist=dao.getMapListBySql(typesql);
			for(int i=0;i<typelist.size();i++) {
				Map map=(Map)typelist.get(i);
				String type=(String)map.get("type");
				if("A".equals(type)) {//质押
					insersql="insert into AC_BUS_MEMBER(BANKID,RELSERID,RELTYPE,CIFID,CLINAME,CERTNO,CERTTYPE,CSTYPE,STS,contno) "
							+ "select distinct "+bankid+",'"+applyno+"',type,cifid,cliname,certno,certtype,cstype,'0','"+contno+"'"
							+ " from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'5' as type from bus_survey_collateral a , cust_info b where "
							+ "a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
							+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") and "
							+ "not exists (select 1 from bus_gcc_relative c , ac_guarcont d where c.bankid=d.bankid and d.guartype ='20' and c.gccontno=d.contno and a.cifid=d.cifid and c.contno='"+contno+"')";
					log.info("登记双录信息表："+insersql);
					dao.executeSql(insersql);
				}else if("B".equals(type)) {//抵押
					insersql="insert into AC_BUS_MEMBER(BANKID,RELSERID,RELTYPE,CIFID,CLINAME,CERTNO,CERTTYPE,CSTYPE,STS,contno) "
							+ "select select distinct "+bankid+",'"+applyno+"',type,cifid,cliname,certno,certtype,cstype,'0','"+contno+"'"
							+ "from (select a.cifid,b.cliname,a.certno,b.cstype,b.certtype,'4' as type"
							+ " from bus_survey_collateral a , cust_info b where "
							+ "a.cifid=b.cifid and a.applyno='"+applyno+"' and a.bankid="+bankid+") a "
							+ "where not exists(select 1 from  AC_BUS_MEMBER b where a.cifid=b.cifid and a.type=b.reltype and b.relserid='"+applyno+"' and b.bankid="+bankid+") and "
							+ "not exists (select 1 from bus_gcc_relative c , ac_guarcont d where c.bankid=d.bankid and d.guartype ='10' and c.gccontno=d.contno and a.cifid=d.cifid and c.contno='"+contno+"')";
					log.info("登记双录信息表："+insersql);
					dao.executeSql(insersql);
				}
			}
		}
		
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("发送双录系统执行成功!");
		return rdbd;
		
	
	}
}
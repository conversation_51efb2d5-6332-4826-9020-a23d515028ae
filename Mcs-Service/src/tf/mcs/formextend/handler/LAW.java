package tf.mcs.formextend.handler;

import java.math.BigDecimal;
import java.util.HashMap;

import org.hibernate.Session;

import tf.brc.databus.impl.CommonDBT;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AppDoeInfoDao;
import tf.mcs.dao.AppMainInfoDao;
import tf.mcs.dao.BusDoeBaseDao;
import tf.mcs.dao.CustInfoDao;
import tf.mcs.datacollection.handler.DealR203;
import tf.mcs.model.AppDoeInfo;
import tf.mcs.model.AppMainInfo;
import tf.mcs.model.BusDoeBase;
import tf.mcs.model.CustInfo;

/**
 * 企业涉诉信息
 * <AUTHOR>
 * 2018年11月15日
 */

public class LAW {
	private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());

/**
 * 近 3 年有涉诉信息
 * <AUTHOR>
 * 2018年11月15日
 * @param hm
 * @return
 * @throws Exception
 */
	public HashMap getLAWCheck1(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		String cliname = "";
		String cifid="";
		String msg = "";
		String cstype = "";
		String entname ="";
		Session session=daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			CustInfo custinfo= new CustInfo();
			CustInfoDao custinfoDao = new CustInfoDao();
			custinfoDao.setExternalSession(daoTemplate.getSession());
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				cifid = appMainInfo.getCifid();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			
			custinfo.setCifid(cifid);
			custinfo = custinfoDao.getEntityByPK(custinfo);
			cstype=custinfo.getCstype();
			cliname=custinfo.getCliname();
			if("A01".equals(cstype)) {
				entname=cliname;
				HashMap dr203map = new HashMap();
				dr203map.put("EntName", entname);
				//调用外部通讯类获取对外风险探测数据
				DealR203 dealR203 = new DealR203();
				dealR203.deal(cdbt, dr203map, daoTemplate);
			}else if("C01".equals(cstype)) {	
				BusDoeBase busDoeBase = new BusDoeBase();
				BusDoeBaseDao busDoeBaseDao=new BusDoeBaseDao();
				busDoeBaseDao.setExternalSession(session);
				busDoeBase.setBankid(bankid);
				busDoeBase.setApplyno(applyno);
				busDoeBase=busDoeBaseDao.findUniqueByEntity(busDoeBase);
				if(busDoeBase!=null&&busDoeBase.getCliname().length()>5) {
					entname=busDoeBase.getCliname();
					HashMap dr203map = new HashMap();
					dr203map.put("EntName", entname);
					//调用外部通讯类获取对外风险探测数据
					DealR203 dealR203 = new DealR203();
					dealR203.deal(cdbt, dr203map, daoTemplate);
				}
			}
			String mainSql = "select count(*) from R203 where entname='"+entname+"' and seq<>'-1' and to_char(add_months(to_date(PDATE,'YYYYMMDD'),36),'YYYYMMDD')>='"+cdbt.getWorkDate()+"' " ;
			BigDecimal size = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			if (size.compareTo(new BigDecimal(5))>0) {			
				restrbuf.append("<font color=#EEC900>");
				msg = "<span style=font-weight:bold;>近 3 年内，当前企业有诉讼公告达到 "+size+"条！</span></font><br>";
				restrbuf.append(msg);
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "30");
				hm.put("ReturnMessage", "近 3 年有涉诉信息存在风险！");
				hm.put("returnDetailMessage",retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "近 3 年有涉诉信息检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>近 3 年有涉诉信息检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "近 3 年有涉诉信息检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>近 3 年有涉诉信息检查发生错误！"+e.getMessage()+"</span></font>");
		}
		return hm;
	}

	/**
	 * 近 1 年存在负面经济类案件
	 * <AUTHOR>
	 * 2018年11月15日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getLAWCheck2(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		String cliname = "";
		String cifid="";
		String msg = "";
		String cstype = "";
		String entname ="";
		Session session=daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			CustInfo custinfo= new CustInfo();
			CustInfoDao custinfoDao = new CustInfoDao();
			custinfoDao.setExternalSession(daoTemplate.getSession());
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				cifid = appMainInfo.getCifid();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			
			custinfo.setCifid(cifid);
			custinfo = custinfoDao.getEntityByPK(custinfo);
			cstype=custinfo.getCstype();
			cliname=custinfo.getCliname();
			if("A01".equals(cstype)) {
				entname=cliname;
//				HashMap dr203map = new HashMap();
//				dr203map.put("EntName", entname);
//				//调用外部通讯类获取对外风险探测数据
//				DealR203 dealR203 = new DealR203();
//				dealR203.deal(cdbt, dr203map, daoTemplate);
			}else if("C01".equals(cstype)) {	
				BusDoeBase busDoeBase = new BusDoeBase();
				BusDoeBaseDao busDoeBaseDao=new BusDoeBaseDao();
				busDoeBaseDao.setExternalSession(session);
				busDoeBase.setBankid(bankid);
				busDoeBase.setApplyno(applyno);
				busDoeBase=busDoeBaseDao.findUniqueByEntity(busDoeBase);
				if(busDoeBase!=null) {
					entname=busDoeBase.getCliname();
//					HashMap dr203map = new HashMap();
//					dr203map.put("EntName", entname);
//					//调用外部通讯类获取对外风险探测数据
//					DealR203 dealR203 = new DealR203();
//					dealR203.deal(cdbt, dr203map, daoTemplate);
				}
			}
			String mainSql = "select count(*) from R203 where entname='"+entname+"' and seq<>'-1'and to_char(add_months(to_date(PDATE,'YYYYMMDD'),12),'YYYYMMDD')>='"+cdbt.getWorkDate()+"'"
					+ " and CASEREASON in ('缔约过失责任纠纷','确认合同效力纠纷','债权人代位权纠纷','债权人撤销权纠纷','债权转让合同纠纷' "
					+ ",'债务转移合同纠纷','债权债务概括转移合同纠纷','悬赏广告纠纷','买卖合同纠纷','招标投标买卖合同纠纷','拍卖合同纠纷',"
					+ "'建设用地使用权合同纠纷','临时用地合同纠纷','探矿权转让合同纠纷','采矿权转让合同纠纷','房地产开发经营合同纠纷',"
					+ "'房屋买卖合同纠纷','房屋拆迁安置补偿合同纠纷','供用电合同纠纷','供用水合同纠纷','供用气合同纠纷','供用热力合同纠纷',"
					+ "'赠与合同纠纷','借款合同纠纷','保证合同纠纷','抵押合同纠纷','质押合同纠纷','定金合同纠纷','进出口押汇纠纷','、储蓄存款合同纠纷','银行卡纠纷',"
					+ "'租赁合同纠纷','融资租赁合同纠纷','承揽合同纠纷','建设工程合同纠纷','运输合同纠纷','保管合同纠纷','仓储合同纠纷','委托合同纠纷','委托理财合同纠纷',"
					+ "'行纪合同纠纷','居间合同纠纷','补偿贸易纠纷','借用合同纠纷','典当纠纷','合伙协议纠纷','种植、养殖回收合同纠纷','彩票、奖券纠纷',"
					+ "'中外合作勘探开发自然资源合同纠纷','农业承包合同纠纷','林业承包合同纠纷','渔业承包合同纠纷','牧业承包合同纠纷',"
					+ "'农村土地承包合同纠纷','服务合同纠纷','演出合同纠纷','劳务合同纠纷','离退休人员返聘合同纠纷','广告合同纠纷','展览合同纠纷',"
					+ "'追偿权纠纷','请求确认人民调解协议效力','企业出资人权益确认纠纷','侵害企业出资人权益纠纷','企业公司制改造合同纠纷',"
					+ "'企业股份合作制改造合同纠纷','企业债权转股权合同纠纷','企业分立合同纠纷','企业租赁经营合同纠纷','企业出售合同纠纷',"
					+ "'挂靠经营合同纠纷','企业兼并合同纠纷','联营合同纠纷','企业承包经营合同纠纷','中外合资经营企业合同纠纷','中外合作经营企业合同纠纷',"
					+ "'股东资格确认纠纷','股东名册记载纠纷','请求变更公司登记纠纷','股东出资纠纷','新增资本认购纠纷','股东知情权纠纷',"
					+ "'请求公司收购股份纠纷','股权转让纠纷','公司决议纠纷','公司设立纠纷','公司证照返还纠纷','发起人责任纠纷','公司盈余分配纠纷',"
					+ "'损害股东利益责任纠纷','损害公司利益责任纠纷','股东损害公司债权人利益责任纠纷','公司关联交易损害责任纠纷','公司合并纠纷',"
					+ "'公司分立纠纷','公司减资纠纷','公司增资纠纷','公司解散纠纷','申请公司清算','清算责任纠纷','上市公司收购纠纷','入伙纠纷','退伙纠纷',"
					+ "'合伙企业财产份额转让纠纷','申请破产清算','申请破产重整','申请破产和解','请求撤销个别清偿行为纠纷','请求确认债务人行为无效纠纷',"
					+ "'对外追收债权纠纷','追收未缴出资纠纷','追收抽逃出资纠纷','追收非正常收入纠纷','破产债权确认纠纷','取回权纠纷','破产抵销权纠纷',"
					+ "'别除权纠纷','破产撤销权纠纷','损害债务人利益赔偿纠纷','管理人责任纠纷','证券权利确认纠纷','证券交易合同纠纷','金融衍生品种交易纠纷'"
					+ ",'证券承销合同纠纷','证券投资咨询纠纷','证券资信评级服务合同纠纷','证券回购合同纠纷','证券上市合同纠纷','证券交易代理合同纠纷',"
					+ "'证券上市保荐合同纠纷','证券发行纠纷','证券返还纠纷','证券欺诈责任纠纷','证券托管纠纷','证券登记、存管、结算纠纷','融资融券交易纠纷',"
					+ "'客户交易结算资金纠纷','期货经纪合同纠纷','期货透支交易纠纷','期货强行平仓纠纷','期货实物交割纠纷','期货保证合约纠纷',"
					+ "'期货交易代理合同纠纷','侵占期货交易保证金纠纷','期货欺诈责任纠纷','操纵期货交易市场责任纠纷','期货内幕交易责任纠纷',"
					+ "'期货虚假信息责任纠纷','民事信托纠纷','营业信托纠纷','公益信托纠纷') and WINSTAFF not like '%"+entname+"%' and PARTY  like '%"+entname+"%'" ;
			BigDecimal size = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			if (size.compareTo(new BigDecimal(0))>0) {			
				restrbuf.append("<font color=red>");
				msg = "<span style=font-weight:bold;>近 1 年内，存在非原告、未胜诉的经济纠纷案件数"+size+"条！</span></font><br>";
				restrbuf.append(msg);
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "20");
				hm.put("ReturnMessage", "近 1 年存在负面经济类案件风险！");
				hm.put("returnDetailMessage",retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "近 1 年存在负面经济类案件检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>近 1 年存在负面经济类案件检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "近 1 年存在负面经济类案件检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>近 1 年存在负面经济类案件检查发生错误！"+e.getMessage()+"</span></font>");
		}
		return hm;
	}
	
	/**
	 * 存在破产清算记录
	 * <AUTHOR>
	 * 2018年11月15日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getLAWCheck3(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		String cliname = "";
		String cifid="";
		String msg = "";
		String cstype = "";
		String entname ="";
		Session session=daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			CustInfo custinfo= new CustInfo();
			CustInfoDao custinfoDao = new CustInfoDao();
			custinfoDao.setExternalSession(daoTemplate.getSession());
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				cifid = appMainInfo.getCifid();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			
			custinfo.setCifid(cifid);
			custinfo = custinfoDao.getEntityByPK(custinfo);
			cstype=custinfo.getCstype();
			cliname=custinfo.getCliname();
			if("A01".equals(cstype)) {
				entname=cliname;
//				HashMap dr203map = new HashMap();
//				dr203map.put("EntName", entname);
//				//调用外部通讯类获取对外风险探测数据
//				DealR203 dealR203 = new DealR203();
//				dealR203.deal(cdbt, dr203map, daoTemplate);
			}else if("C01".equals(cstype)) {	
				BusDoeBase busDoeBase = new BusDoeBase();
				BusDoeBaseDao busDoeBaseDao=new BusDoeBaseDao();
				busDoeBaseDao.setExternalSession(session);
				busDoeBase.setBankid(bankid);
				busDoeBase.setApplyno(applyno);
				busDoeBase=busDoeBaseDao.findUniqueByEntity(busDoeBase);
				if(busDoeBase!=null) {
					entname=busDoeBase.getCliname();
//					HashMap dr203map = new HashMap();
//					dr203map.put("EntName", entname);
//					//调用外部通讯类获取对外风险探测数据
//					DealR203 dealR203 = new DealR203();
//					dealR203.deal(cdbt, dr203map, daoTemplate);
				}
			}
			String mainSql = "select count(*) from R203 where entname='"+entname+"' and seq<>'-1'and to_char(add_months(to_date(PDATE,'YYYYMMDD'),36),'YYYYMMDD')>='"+cdbt.getWorkDate()+"' "
					+ " and PARTY like '%"+entname+"%' and PTYPE in('19','23') " ;
			BigDecimal size = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			if (size.compareTo(new BigDecimal(0))>0) {			
				restrbuf.append("<font color=red>");
				msg = "<span style=font-weight:bold;>当前企业存在破产公告/清算公告记录，且为破产人/清算当事人！</span></font><br>";
				restrbuf.append(msg);
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "20");
				hm.put("ReturnMessage", "存在破产清算记录存在风险！");
				hm.put("returnDetailMessage",retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "存在破产清算记录检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>存在破产清算记录检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "存在破产清算记录检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>存在破产清算记录检查发生错误！"+e.getMessage()+"</span></font>");
		}
		return hm;
	}
	
	/**
	 * 近3天发生诉讼记录
	 * <AUTHOR>
	 * 2018年11月15日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getLAWCheck4(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		String cliname = "";
		String certno="";
		String cifid="";
		String occurtype = "";
		String msg = "";
		String cstype = "";
		String entname ="";
		String indname ="";
		Session session=daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			CustInfo custinfo= new CustInfo();
			CustInfoDao custinfoDao = new CustInfoDao();
			custinfoDao.setExternalSession(daoTemplate.getSession());
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				occurtype = appMainInfo.getOccurtype();
				cifid = appMainInfo.getCifid();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			
			custinfo.setCifid(cifid);
			custinfo = custinfoDao.getEntityByPK(custinfo);
			certno=custinfo.getCertno();
			cstype=custinfo.getCstype();
			cliname=custinfo.getCliname();
			if("A01".equals(cstype)) {
				entname=cliname;
//				HashMap dr203map = new HashMap();
//				dr203map.put("EntName", entname);
//				//调用外部通讯类获取对外风险探测数据
//				DealR203 dealR203 = new DealR203();
//				dealR203.deal(cdbt, dr203map, daoTemplate);
			}else if("C01".equals(cstype)) {	
				BusDoeBase busDoeBase = new BusDoeBase();
				BusDoeBaseDao busDoeBaseDao=new BusDoeBaseDao();
				busDoeBaseDao.setExternalSession(session);
				busDoeBase.setBankid(bankid);
				busDoeBase.setApplyno(applyno);
				busDoeBase=busDoeBaseDao.findUniqueByEntity(busDoeBase);
				if(busDoeBase!=null) {
					entname=busDoeBase.getCliname();
//					HashMap dr203map = new HashMap();
//					dr203map.put("EntName", entname);
//					//调用外部通讯类获取对外风险探测数据
//					DealR203 dealR203 = new DealR203();
//					dealR203.deal(cdbt, dr203map, daoTemplate);
				}
			}
			String mainSql = "select count(*) num from R203 where entname='"+entname+"' and seq<>'-1' and   to_char(to_date(PDATE,'yyyymmdd')+3,'YYYYMMDD')>='"+cdbt.getWorkDate()+"' and PDATE<='"+cdbt.getWorkDate()+"' " ;
			BigDecimal size = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			if (size.compareTo(new BigDecimal(0))>0) {			
				restrbuf.append("<font color=red>");
				msg = "<span style=font-weight:bold;>近3天发生 "+size+"（案由）诉讼公告！</span></font><br>";
				restrbuf.append(msg);
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "20");
				hm.put("ReturnMessage", "近3天发生诉讼记录存在风险！");
				hm.put("returnDetailMessage",retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "近3天发生诉讼记录检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>近3天发生诉讼记录检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "近3天发生诉讼记录检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>近3天发生诉讼记录检查发生错误！"+e.getMessage()+"</span></font>");
		}
		return hm;
	}
	
	/**
	 *  业务流程内发生诉讼记录
	 * <AUTHOR>
	 * 2018年11月15日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getLAWCheck5(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		String cliname = "";
		String certno="";
		String cifid="";
		String occurtype = "";
		String msg = "";
		String cstype = "";
		String entname ="";
		String indname ="";
		Session session=daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			CustInfo custinfo= new CustInfo();
			CustInfoDao custinfoDao = new CustInfoDao();
			custinfoDao.setExternalSession(daoTemplate.getSession());
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				occurtype = appMainInfo.getOccurtype();
				cifid = appMainInfo.getCifid();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			
			custinfo.setCifid(cifid);
			custinfo = custinfoDao.getEntityByPK(custinfo);
			certno=custinfo.getCertno();
			cstype=custinfo.getCstype();
			cliname=custinfo.getCliname();
			if("A01".equals(cstype)) {
				entname=cliname;
//				HashMap dr203map = new HashMap();
//				dr203map.put("EntName", entname);
//				//调用外部通讯类获取对外风险探测数据
//				DealR203 dealR203 = new DealR203();
//				dealR203.deal(cdbt, dr203map, daoTemplate);
			}else if("C01".equals(cstype)) {	
				BusDoeBase busDoeBase = new BusDoeBase();
				BusDoeBaseDao busDoeBaseDao=new BusDoeBaseDao();
				busDoeBaseDao.setExternalSession(session);
				busDoeBase.setBankid(bankid);
				busDoeBase.setApplyno(applyno);
				busDoeBase=busDoeBaseDao.findUniqueByEntity(busDoeBase);
				if(busDoeBase!=null) {
					entname=busDoeBase.getCliname();
//					HashMap dr203map = new HashMap();
//					dr203map.put("EntName", entname);
//					//调用外部通讯类获取对外风险探测数据
//					DealR203 dealR203 = new DealR203();
//					dealR203.deal(cdbt, dr203map, daoTemplate);
				}
			}
			String objectsql="select nvl(ipt_date,'********') from de_flowobject where applyno='"+applyno+"' and bankid='"+bankid+"' ";
			String iptdate=(String)daoTemplate.getUniqueValueBySql(objectsql);
			String mainSql = "select count(*) num from R203 where entname='"+entname+"' and seq<>'-1' and   PDATE>='"+iptdate+"' " ;
			BigDecimal size = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			if (size.compareTo(new BigDecimal(0))>0) {			
				restrbuf.append("<font color=red>");
				msg = "<span style=font-weight:bold;>流程期间内，发生诉讼公告记录"+size+"条！</span></font><br>";
				restrbuf.append(msg);
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "20");
				hm.put("ReturnMessage", "业务流程内发生诉讼记录存在风险！");
				hm.put("returnDetailMessage",retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "业务流程内发生诉讼记录检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>业务流程内发生诉讼记录检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "业务流程内发生诉讼记录检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>业务流程内发生诉讼记录检查发生错误！"+e.getMessage()+"</span></font>");
		}
		return hm;
	}
}

package tf.mcs.formextend.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;

import org.hibernate.Session;

import tf.brc.databus.impl.CommonDBT;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AppMainInfoDao;
import tf.mcs.dao.BusIndInfoDao;
import tf.mcs.dao.IndBaseDao;
import tf.mcs.model.AppMainInfo;
import tf.mcs.model.BusIndInfo;
import tf.mcs.model.IndBase;
import tf.mcs.tencent.handler.DealTxF001;
import tf.mcs.tencent.handler.DealTxF002;
import tf.mcs.tencent.handler.DealTxF003;
import tf.mcs.tencent.handler.DealTxF004;
import tf.mcs.tencent.handler.DealTxF0078;
import tf.mcs.tencent.handler.DealTxF009;
import tf.mcs.tencent.handler.DealTxR001;
import tf.mcs.tencent.handler.DealTxR002;
import tf.mcs.tencent.handler.DealTxR003;
import tf.tools.WorkTools;

/**
 * 功能描述:腾讯外部数据风险探测
 * 
 * <AUTHOR>
 * @date 2019-03-08
 * @version 1.0
 *
 */

public class TENCENT {
	private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());

	/**
	 * 功能描述:腾讯天御反欺诈f001
	 * 
	 * <AUTHOR>
	 * @date 2019-03-09
	 * @version 1.0
	 *
	 */
	public HashMap getf001Chk(HashMap hm) throws Exception {
		String chkscores = "60";// 欺诈分标准值
		String chkscores1 = "90";// 欺诈分标准值上限
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}

			// 调用腾讯外部通讯类获取对外风险探测数据
			HashMap f001map = new HashMap();
			f001map.put("id_no", certno);
			f001map.put("cliname", cliname);
			f001map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF001 f001 = new DealTxF001();
			f001.deal(f001map, daoTemplate, workDate);

			String mainSql = "select * from txf001 where id_no='" + certno + "' and name = '" + cliname
					+ "' and seq<>'-1'";
			HashMap resultMap = (HashMap) daoTemplate.getUniqueMapRowBySql(mainSql);
			if (resultMap != null) {
				BigDecimal score = (BigDecimal) resultMap.get("score");// 欺诈分
				BigDecimal chkscore = new BigDecimal(chkscores);
				BigDecimal chkscore1 = new BigDecimal(chkscores1);
				String[] risk = String.valueOf(resultMap.get("risk")).split(",");// 风险项
				String riskinfo=String.valueOf(resultMap.get("risk"));
				StringBuffer restrbuf = new StringBuffer();
				if ((score.compareTo(chkscore) > 0&&score.compareTo(chkscore1) <= 0) || (risk.length >= 1&&risk.length <3&&!"null".equals(riskinfo)&&!"".equals(riskinfo))) {
					if (score.compareTo(chkscore) > 0) {// 腾讯天御欺诈分大于指定值则提示
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>欺诈分为" + score + ",大于标准"
								+ chkscore + ",存在风险</span></font><br>");
					}
					if (risk.length >=1&&!"null".equals(riskinfo)&&!"".equals(riskinfo)) {// 存在腾讯天御疑似项则提示
						for (int i = 0; i < risk.length; i++) {
							String risktype = WorkTools.getOptLabel("TX00001", risk[i]);
							restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>风险项:" + risktype
									+ "</span></font><br>");
						}
					}
					String retmsg = String.valueOf(restrbuf);

					hm.put("Result", "30");
					hm.put("ReturnMessage", "腾讯天御反欺诈检查存在风险！");
					hm.put("returnDetailMessage", retmsg);
				} else if(score.compareTo(chkscore1) > 0 || risk.length >=3) {
					if (score.compareTo(chkscore1) > 0) {// 腾讯天御欺诈分大于指定值则提示
						restrbuf.append("<font color=red><span style=font-weight:bold;>欺诈分为" + score + ",大于标准"
								+ chkscore1 + ",存在风险</span></font><br>");
					}
					if (risk.length >=3) {// 存在腾讯天御疑似项则提示
						for (int i = 0; i < risk.length; i++) {
							String risktype = WorkTools.getOptLabel("TX00001", risk[i]);
							restrbuf.append("<font color=red><span style=font-weight:bold;>风险项:" + risktype
									+ "</span></font><br>");
						}
					}
					String retmsg = String.valueOf(restrbuf);

					hm.put("Result", "20");
					hm.put("ReturnMessage", "腾讯天御反欺诈检查存在风险！");
					hm.put("returnDetailMessage", retmsg);
				} else {
					hm.put("Result", "10");
					hm.put("ReturnMessage", "腾讯天御反欺诈检查成功！");
					hm.put("returnDetailMessage",
							"<font color=green><span style=font-weight:bold;>腾讯天御反欺诈检查通过！</span></font>");
				}
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "腾讯天御反欺诈检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>腾讯天御反欺诈检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "腾讯天御反欺诈检查发生错误！");
			hm.put("returnDetailMessage",
					"<font color=red><span style=font-weight:bold;>腾讯天御反欺诈检查发生错误！" + e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:腾讯金融机构逾期黑名单f002
	 * 
	 * <AUTHOR>
	 * @date 2019-03-09
	 * @version 1.0
	 *
	 */
	public HashMap getf002Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}

			// 调用腾讯外部通讯类获取对外风险探测数据
			HashMap f002map = new HashMap();
			f002map.put("id_no", certno);
			f002map.put("cliname", cliname);
			f002map.put("mobile", mobile);
			DealTxF002 f002 = new DealTxF002();
			f002.deal(f002map, daoTemplate, workDate);

			String mainSql = "select * from TXF002 where name='" + cliname + "' and id_no='" + certno
					+ "' and seq<>'-1'";
			ArrayList txf002List = (ArrayList) daoTemplate.getMapListBySql(mainSql);
			StringBuffer restrbuf = new StringBuffer();
			for (int i = 0; i < txf002List.size(); i++) {
				HashMap listMap = (HashMap) txf002List.get(i);
				String bad = String.valueOf(listMap.get("bad"));
				String overdue = String.valueOf(listMap.get("overdue"));
				String fraud = String.valueOf(listMap.get("fraud"));
				String lost = String.valueOf(listMap.get("lost"));
				String refuse = String.valueOf(listMap.get("refuse"));
				String datatype = String.valueOf(listMap.get("dataType"));
				String dataStr = "";
				if("id".equals(datatype)) {
					dataStr="身份证命中";
				}else  if("cell".equals(datatype)) {
					dataStr="手机号命中";
				}
				String loanType = WorkTools.getOptLabel("TX00002", String.valueOf(listMap.get("loanType")));
				if (!"".equals(lost) || !"".equals(refuse) || ("0".equals(bad) || "1".equals(bad))
						|| ("0".equals(overdue) || "1".equals(overdue)) || ("0".equals(fraud) || "1".equals(fraud))) {
					if ("0".equals(lost)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,本人命中高风险项,存在风险</span></font><br>");
					}
					if ("1".equals(lost)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,一度关系人命中高风险项,存在风险</span></font><br>");
					}
					if ("2".equals(lost)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,二度关系人命中高风险项,存在风险</span></font><br>");
					}
					if ("0".equals(refuse)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,本人命中拒绝项,存在风险</span></font><br>");
					}
					if ("1".equals(refuse)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,一度关系人命中拒绝项,存在风险</span></font><br>");
					}
					if ("2".equals(refuse)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,二度关系人命中拒绝项,存在风险</span></font><br>");
					}
					if ("0".equals(bad)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,本人命中中风险项,存在风险</span></font><br>");
					}
					if ("1".equals(bad)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,一度关系人命中中风险项,存在风险</span></font><br>");
					}
					if ("0".equals(overdue)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,本人命中一般风险项,存在风险</span></font><br>");
					}
					if ("1".equals(overdue)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,一度关系人命中一般风险项,存在风险</span></font><br>");
					}
					if ("0".equals(fraud)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,本人命中资信不佳项,存在风险</span></font><br>");
					}
					if ("1".equals(fraud)) {
						restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>" +dataStr+ loanType
								+ "类,一度关系人命中资信不佳项,存在风险</span></font><br>");
					}
				}

			}
			if (restrbuf.length() > 0) {
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "30");
				hm.put("ReturnMessage", "腾讯金融机构逾期黑名单检查存在风险！");
				hm.put("returnDetailMessage", retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "金融机构逾期黑名单检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>金融机构逾期黑名单检查通过！</span></font>");
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "腾讯金融机构逾期黑名单检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>腾讯金融机构逾期黑名单检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:f004互联网高危行为风险评分规则
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getf004Chk(HashMap hm) throws Exception {
		String chkscore = "580";
		String chkscore1 = "500";
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f004map = new HashMap();
			f004map.put("id_no", certno);
			f004map.put("cliname", cliname);
			f004map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF004 f004 = new DealTxF004();
			f004.deal(f004map, daoTemplate, workDate);

			String mainSql = "select score from txf004 where id_no='" + certno + "' and name='" + cliname
					+ "' and seq!='-1'";
			BigDecimal score = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			BigDecimal sco = new BigDecimal(chkscore);
			BigDecimal sco1 = new BigDecimal(chkscore1);
			StringBuffer restrbuf = new StringBuffer();
			if (score != null) {
				if (score.compareTo(sco) == -1&&score.compareTo(sco1)>=0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>检测互联网高危行为风险评分为" + score
							+ ",低于标准分数" + sco + ",存在风险</span></font><br>");
					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "30");
					hm.put("ReturnMessage", "互联网高危行为风险评分存在风险！");
					hm.put("returnDetailMessage", retmsg);
				} else if (score.compareTo(sco1)<0) {
					restrbuf.append("<font color=red><span style=font-weight:bold;>检测互联网高危行为风险评分为" + score
							+ ",低于标准分数" + sco1 + ",存在风险</span></font><br>");
					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "20");
					hm.put("ReturnMessage", "互联网高危行为风险评分存在风险！");
					hm.put("returnDetailMessage", retmsg);
				} else {
					hm.put("Result", "10");
					hm.put("ReturnMessage", "互联网高危行为风险评分检查成功！");
					hm.put("returnDetailMessage",
							"<font color=green><span style=font-weight:bold;>互联网高危行为风险评分检查通过！</span></font>");
				}
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "互联网高危行为风险评分检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>互联网高危行为风险评分检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "互联网高危行为风险评分检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>互联网高危行为风险评分检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:f007直接联系人涉黑情况规则
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getf007Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f0078map = new HashMap();
			f0078map.put("id_no", certno);
			f0078map.put("cliname", cliname);
			f0078map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF0078 f0078 = new DealTxF0078();
			f0078.deal(f0078map, daoTemplate, workDate);

			String mainSql = "select nvl(sum(ratio),0)*100  from txf007_008 where id_no='" + certno + "' and name='" + cliname
					+ "' and contact_type='1' and seq!='-1'";
			BigDecimal txf0078Num = (BigDecimal)daoTemplate.getUniqueValueBySql(mainSql);
			BigDecimal minf0078Num = new BigDecimal("0.2").multiply(new BigDecimal("100"));//校验标准值
			StringBuffer restrbuf = new StringBuffer();
			if(txf0078Num.compareTo(minf0078Num)>0) {
				restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>直接联系人黑号数占比:"+txf0078Num+"%,超过标准"+minf0078Num+"%,存在风险</span></font><br>");
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "30");
				hm.put("ReturnMessage", "腾讯直接联系人涉黑情况存在风险！");
				hm.put("returnDetailMessage", retmsg);
			}else {	
				hm.put("Result", "10");
				hm.put("ReturnMessage", "腾讯直接联系人涉黑情况检查成功！");
				hm.put("returnDetailMessage","<font color=green><span style=font-weight:bold;>腾讯直接联系人涉黑情况检查通过！</span></font>");
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "腾讯直接联系人涉黑情况检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>腾讯直接联系人涉黑情况检查发生错误！"+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:f008间接联系人涉黑情况规则
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getf008Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}

			HashMap f0078map = new HashMap();
			f0078map.put("id_no", certno);
			f0078map.put("cliname", cliname);
			f0078map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF0078 f0078 = new DealTxF0078();
			f0078.deal(f0078map, daoTemplate, workDate);

			String mainSql = "select nvl(sum(ratio),0)*100 from txf007_008 where id_no='" + certno + "' and name='" + cliname
					+ "' and contact_type='2' and seq!='-1'";
			BigDecimal txf0078Num = (BigDecimal)daoTemplate.getUniqueValueBySql(mainSql);
			BigDecimal minf0078Num = new BigDecimal("0.5").multiply(new BigDecimal("100"));//校验标准值
			StringBuffer restrbuf = new StringBuffer();
			if(txf0078Num.compareTo(minf0078Num)>0) {
				restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>间接联系人黑号数占比:"+txf0078Num+"%,超过标准"+minf0078Num+"%,存在风险</span></font><br>");
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "30");
				hm.put("ReturnMessage", "腾讯间接联系人涉黑情况存在风险！");
				hm.put("returnDetailMessage", retmsg);
			}else {	
				hm.put("Result", "10");
				hm.put("ReturnMessage", "腾讯间接联系人涉黑情况检查成功！");
				hm.put("returnDetailMessage","<font color=green><span style=font-weight:bold;>腾讯间接联系人涉黑情况检查通过！</span></font>");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "腾讯间接联系人涉黑情况检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>腾讯间接联系人涉黑情况检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:f009联系人黑产黑中介关联分规则
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getf009Chk(HashMap hm) throws Exception {
		String chkscore = "50";// 校验标准值
		String chkscore1 = "80";// 校验标准值
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f009map = new HashMap();
			f009map.put("id_no", certno);
			f009map.put("cliname", cliname);
			f009map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF009 f009 = new DealTxF009();
			f009.deal(f009map, daoTemplate, workDate);

			String mainSql = "select score from txf009 where id_no='" + certno + "' and name='" + cliname
					+ "' and seq!='-1'";
			BigDecimal score = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			BigDecimal sco = new BigDecimal(chkscore);
			BigDecimal sco1 = new BigDecimal(chkscore1);
			if (score != null) {
				StringBuffer restrbuf = new StringBuffer();
				if (score.compareTo(sco) == 1&&score.compareTo(sco1) <=0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>当前联系人黑产黑中介关联分为" + score
							+ ",超过标准分数" + sco + ",存在风险</span></font><br>");
					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "30");
					hm.put("ReturnMessage", "腾讯联系人黑产黑中介关联分存在风险！");
					hm.put("returnDetailMessage", retmsg);

				} else if (score.compareTo(sco1) >0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>当前联系人黑产黑中介关联分为" + score
							+ ",超过标准分数" + sco1 + ",存在风险</span></font><br>");
					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "20");
					hm.put("ReturnMessage", "腾讯联系人黑产黑中介关联分存在风险！");
					hm.put("returnDetailMessage", retmsg);

				} else {
					hm.put("Result", "10");
					hm.put("ReturnMessage", "腾讯联系人黑产黑中介关联分检查成功！");
					hm.put("returnDetailMessage",
							"<font color=green><span style=font-weight:bold;>腾讯联系人黑产黑中介关联分检查通过！</span></font>");
				}
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "腾讯联系人黑产黑中介关联分检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>腾讯联系人黑产黑中介关联分检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "腾讯联系人黑产黑中介关联分检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>腾讯联系人黑产黑中介关联分检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:r002多头借贷报告
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getr002Chk(HashMap hm) throws Exception {
		String chkm1 = "18";// 一个月校验标准值
		String chkm2 = "26";// 一个月校验标准值
		String chkm3 = "36";// 三个月校验标准值
		String chkm4 = "48";// 三个月校验标准值
		String chkm6 = "48";// 六个月校验标准值
		String chkm5 = "68";// 六个月校验标准值
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap r002map = new HashMap();
			r002map.put("id_no", certno);
			r002map.put("cliname", cliname);
			r002map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxR002 r002 = new DealTxR002();
			r002.deal(r002map, daoTemplate, workDate);

			String mainSql = "select nvl(sum(LOAN_NUM),0) as apnum,DATA_TYPE from TXR002 where name='" + cliname
					+ "' and id_no='" + certno + "' and seq<>'-1' group by DATA_TYPE";
			ArrayList txr002List = (ArrayList) daoTemplate.getMapListBySql(mainSql);
			BigDecimal minM1times = new BigDecimal(chkm1);
			BigDecimal minM2times = new BigDecimal(chkm2);
			BigDecimal minM3times = new BigDecimal(chkm3);
			BigDecimal minM4times = new BigDecimal(chkm4);
			BigDecimal minM6times = new BigDecimal(chkm6);
			BigDecimal minM5times = new BigDecimal(chkm5);
			StringBuffer restrbuf = new StringBuffer();
			for (int i = 0; i < txr002List.size(); i++) {
				HashMap txr002Map = (HashMap) txr002List.get(i);
				String dataType = String.valueOf(txr002Map.get("dataType"));
				BigDecimal apTimes = new BigDecimal(String.valueOf(txr002Map.get("apnum")));
				if ("M1".equals(dataType) && apTimes.compareTo(minM1times) > 0&&apTimes.compareTo(minM2times) <= 0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>近1个月内总查询次数为" + apTimes + ",超过标准"
							+ minM1times + "次,存在风险</span></font><br>");
				}else if("M1".equals(dataType) && apTimes.compareTo(minM2times) > 0) {
					String msg="<font color=red><span style=font-weight:bold;>近1个月内总查询次数为" + apTimes + ",超过标准"
							+ minM2times + "次,存在风险</span></font><br>";
					hm.put("ReturnMessage", "多头借贷报告查询次数检查存在风险！");
					hm.put("Result", "20");
					hm.put("returnDetailMessage", msg);
					return hm;
					
				}
				if ("M3".equals(dataType) && apTimes.compareTo(minM3times) > 0&&apTimes.compareTo(minM4times) <= 0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>近3个月内总查询次数为" + apTimes + ",超过标准"
							+ minM3times + "次,存在风险</span></font><br>");
				}else if ("M3".equals(dataType) && apTimes.compareTo(minM4times) > 0) {
					String msg="<font color=red><span style=font-weight:bold;>近1个月内总查询次数为" + apTimes + ",超过标准"
							+ minM4times + "次,存在风险</span></font><br>";
					hm.put("ReturnMessage", "多头借贷报告查询次数检查存在风险！");
					hm.put("Result", "20");
					hm.put("returnDetailMessage", msg);
					return hm;
				}
				if ("M6".equals(dataType) && apTimes.compareTo(minM6times) > 0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>近6个月内总查询次数为" + apTimes + ",超过标准"
							+ minM6times + "次,存在风险</span></font><br>");
				}else if("M6".equals(dataType)&& apTimes.compareTo(minM5times) > 0) {
					String msg="<font color=FFB90F><span style=font-weight:bold;>近6个月内总查询次数为" + apTimes + ",超过标准"
							+ minM5times + "次,存在风险</span></font><br>";
					hm.put("ReturnMessage", "多头借贷报告查询次数检查存在风险！");
					hm.put("Result", "20");
					hm.put("returnDetailMessage", msg);
					return hm;
				}
			}
			if (restrbuf.length() > 0) {
				String retmsg = String.valueOf(restrbuf);
				hm.put("ReturnMessage", "多头借贷报告查询次数检查存在风险！");
				hm.put("Result", "30");
				hm.put("returnDetailMessage", retmsg);
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "多头借贷报告查询次数检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>多头借贷报告查询次数检查通过！</span></font>");
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "多头借贷报告查询次数检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>多头借贷报告查询次数检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 功能描述:r003历史履约能力评分规则
	 * 
	 * <AUTHOR>
	 * @date ********
	 */
	public HashMap getr003Chk(HashMap hm) throws Exception {
		String chkscore = "0.53";// 校验标准值
		String chkscore1 = "0.6";// 校验标准值
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}

			HashMap r003map = new HashMap();
			r003map.put("id_no", certno);
			r003map.put("cliname", cliname);
			r003map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxR003 r003 = new DealTxR003();
			r003.deal(r003map, daoTemplate, workDate);

			String mainSql = "select score from txr003 where id_no='" + certno + "' and name='" + cliname
					+ "' and seq!='-1'";
			BigDecimal score = (BigDecimal) daoTemplate.getUniqueValueBySql(mainSql);
			BigDecimal chkscores = new BigDecimal(chkscore);// 校验标准
			BigDecimal chkscores1 = new BigDecimal(chkscore1);// 校验标准
			if (score != null) {
				StringBuffer restrbuf = new StringBuffer();
				if (score.compareTo(chkscores) == 1&&score.compareTo(chkscores1)<=0) {
					restrbuf.append("<font color=FFB90F><span style=font-weight:bold;>历史履约能力评分为" + score + ",高于标准"
							+ chkscores + ",存在风险</span></font><br>");

					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "30");
					hm.put("ReturnMessage", "历史履约能力评分存在风险！");
					hm.put("returnDetailMessage", retmsg);

				}else if(score.compareTo(chkscores1)>0) {
					restrbuf.append("<font color=read><span style=font-weight:bold;>历史履约能力评分为" + score + ",高于标准"
							+ chkscores1 + ",存在风险</span></font><br>");

					String retmsg = String.valueOf(restrbuf);
					hm.put("Result", "20");
					hm.put("ReturnMessage", "历史履约能力评分存在风险！");
					hm.put("returnDetailMessage", retmsg);
				} else {
					hm.put("Result", "10");
					hm.put("ReturnMessage", "历史履约能力评分检查成功！");
					hm.put("returnDetailMessage",
							"<font color=green><span style=font-weight:bold;>历史履约能力评分检查通过！</span></font>");
				}
			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "历史履约能力评分检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>历史履约能力评分检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "历史履约能力评分检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>历史履约能力评分检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}

	/**
	 * 公安、法院失信、政府公示黑名单
	 * <AUTHOR>
	 * 2019年7月18日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getf010Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f010map = new HashMap();
			f010map.put("id_no", certno);
			f010map.put("cliname", cliname);
			f010map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF003 dlTxF003 = new DealTxF003();
			dlTxF003.deal(f010map, daoTemplate, workDate);

			String mainSql = "select count(*) from TXF003A where name='"+cliname+"' and id_no='"+certno+"' and seq<>'-1'";
			BigDecimal txf003ANum=(BigDecimal)daoTemplate.getUniqueValueBySql(mainSql);
			mainSql = "select count(*) from TXF003B where name='"+cliname+"' and id_no='"+certno+"' and seq<>'-1'";
			BigDecimal txf003BNum=(BigDecimal)daoTemplate.getUniqueValueBySql(mainSql);
			if ((txf003ANum.add(txf003BNum)).compareTo(new BigDecimal(0))>0) {
				StringBuffer restrbuf = new StringBuffer();
				
				restrbuf.append("<font color=red><span style=font-weight:bold;>公安、法院失信、政府公示黑名单失败,该客户被列入于黑名单,存在风险</span></font><br>");
				String retmsg = String.valueOf(restrbuf);
				hm.put("Result", "20");
				hm.put("ReturnMessage", "公安、法院失信、政府公示黑名单存在风险！");
				hm.put("returnDetailMessage", retmsg);

			} else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "公安、法院失信、政府公示黑名单检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>公安、法院失信、政府公示黑名单检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "公安、法院失信、政府公示黑名单检查发生错误！");
			hm.put("returnDetailMessage", "<font color=red><span style=font-weight:bold;>公安、法院失信、政府公示黑名单检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}
	
	/**
	 * 金融机构逾期黑名单拒绝
	 * <AUTHOR>
	 * 2019年7月18日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getf011Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f011map = new HashMap();
			f011map.put("id_no", certno);
			f011map.put("cliname", cliname);
			f011map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxF002 dlTxF002 = new DealTxF002();
			dlTxF002.deal(f011map, daoTemplate, workDate);
			String mainSql = "select * from TXF002 where name='"+cliname+"' and id_no='"+certno+"' and seq<>'-1'";
			ArrayList txf002List = (ArrayList) daoTemplate.getMapListBySql(mainSql);
			if(txf002List.size()>0) {
				for(int i=0;i<txf002List.size();i++) {
					HashMap listMap = (HashMap) txf002List.get(i);
					String  bad = String.valueOf(listMap.get("bad"));
					String  overdue = String.valueOf(listMap.get("overdue"));
					String  fraud = String.valueOf(listMap.get("fraud"));
					String  lost = String.valueOf(listMap.get("lost"));
					String  refuse = String.valueOf(listMap.get("refuse"));
					String  loanType = WorkTools.getOptLabel("TX00002",String.valueOf(listMap.get("loanType")));
					
					if("1".equals(lost)||"0".equals(lost)) {

						String retmsg = "<font color=red><span style=font-weight:bold;>"+loanType+"本人或一度关系命中高风险，拒绝申请</span></font>";
						hm.put("Result", "20");
						hm.put("ReturnMessage", "金融机构逾期黑名单存在风险！");
						hm.put("returnDetailMessage", retmsg);
						return hm;
					}
					if("1".equals(refuse)||"0".equals(refuse)) {
						String retmsg = "<font color=red><span style=font-weight:bold;>"+loanType+"本人或一度关系命中拒绝，拒绝申请</span></font>";
						hm.put("Result", "20");
						hm.put("ReturnMessage", "金融机构逾期黑名单存在风险！");
						hm.put("returnDetailMessage", retmsg);
						return hm;
					}
					if("0".equals(bad)) {
						String retmsg = "<font color=red><span style=font-weight:bold;>"+loanType+ "本人命中中风险，拒绝申请</span></font>";
						hm.put("Result", "20");
						hm.put("ReturnMessage", "金融机构逾期黑名单存在风险！");
						hm.put("returnDetailMessage", retmsg);
						return hm;
					}
					if("0".equals(overdue)) {
						String retmsg = "<font color=red><span style=font-weight:bold;>"+loanType+"本人命中一般风险，拒绝申请</span></font>";
						hm.put("Result", "20");
						hm.put("ReturnMessage", "金融机构逾期黑名单存在风险！");
						hm.put("returnDetailMessage", retmsg);
						return hm;
					}
					if("0".equals(fraud)) {
						String retmsg = "<font color=red><span style=font-weight:bold;>"+loanType+ "本人命中资信不佳，拒绝申请</span></font>";
						hm.put("Result", "20");
						hm.put("ReturnMessage", "金融机构逾期黑名单存在风险！");
						hm.put("returnDetailMessage", retmsg);
						return hm;
					}
				}
			}else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "金融机构逾期黑名单检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>金融机构逾期黑名单检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "金融机构逾期黑名单检查发生错误！");
			hm.put("returnDetailMessage", "<font color=FFB90F><span style=font-weight:bold;>金融机构逾期黑名单检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}
	
	/**
	 * 腾讯多头风险评分
	 * <AUTHOR>
	 * 2019年7月18日
	 * @param hm
	 * @return
	 * @throws Exception
	 */
	public HashMap getf012Chk(HashMap hm) throws Exception {
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) hm.get("daoTemplate");
		long bankid = (Long) hm.get("bankid");
		String applyno = (String) hm.get("applyno");
		CommonDBT cdbt = (CommonDBT) hm.get("cdbt");
		String workDate = cdbt.getWorkDate();// 交易日期
		String cliname = "";
		String certno = "";
		String mobile = "";
		String cifid = "";
		Session session = daoTemplate.getSession();
		try {
			AppMainInfo appMainInfo = new AppMainInfo();
			AppMainInfoDao appMainInfoDao = new AppMainInfoDao();
			appMainInfoDao.setExternalSession(session);
			appMainInfo.setBankid(bankid);
			appMainInfo.setApplyno(applyno);
			appMainInfo = appMainInfoDao.getEntityByPK(appMainInfo);
			BusIndInfo busIndInfo = new BusIndInfo();
			BusIndInfoDao busIndInfoDao = new BusIndInfoDao();
			busIndInfoDao.setExternalSession(session);
			
			if (appMainInfo != null) {
				cliname = appMainInfo.getCliname();
				certno = appMainInfo.getCertno();
				cifid = appMainInfo.getCifid();
				busIndInfo.setBankid(bankid);
				busIndInfo.setApplyno(applyno);
				busIndInfo = busIndInfoDao.getEntityByPK(busIndInfo);
				mobile = busIndInfo.getMtel();
			} else {
				throw new Exception("未找到该申请号所对应的申请信息!");
			}
			HashMap f012map = new HashMap();
			f012map.put("id_no", certno);
			f012map.put("cliname", cliname);
			f012map.put("mobile", mobile);
			// 调用外部通讯类获取对外风险探测数据
			DealTxR001 dlTxR001 = new DealTxR001();
			dlTxR001.deal(f012map, daoTemplate, workDate);
			String mainSql = "select * from TXR001 where name='"+cliname+"' and id_no='"+certno+"' and seq<>'-1'";
			HashMap txr001Map = (HashMap) daoTemplate.getUniqueMapRowBySql(mainSql);
			if(txr001Map!=null) {
				String decision = String.valueOf(txr001Map.get("decision"));
				String r001Score = String.valueOf(txr001Map.get("score"));
				if("reject".equals(decision)) {
					hm.put("Result", "30");
					hm.put("ReturnMessage", "多头风险评分存在风险！");
					hm.put("returnDetailMessage",
							"<font color=FFB90F><span style=font-weight:bold;>多头风险评分"+r001Score+"分！</span></font>");
				}else {	
					hm.put("Result", "10");
					hm.put("ReturnMessage", "多头风险评分检查成功！");
					hm.put("returnDetailMessage",
							"<font color=green><span style=font-weight:bold;>多头风险评分检查通过！</span></font>");
				}
			}else {
				hm.put("Result", "10");
				hm.put("ReturnMessage", "多头风险评分检查成功！");
				hm.put("returnDetailMessage",
						"<font color=green><span style=font-weight:bold;>多头风险评分检查通过！</span></font>");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			hm.put("Result", "30");
			hm.put("ReturnMessage", "多头风险评分检查发生错误！");
			hm.put("returnDetailMessage", "<font color=FFB90F><span style=font-weight:bold;>多头风险评分检查发生错误！"
					+ e.getMessage() + "</span></font>");
		}
		return hm;
	}
}

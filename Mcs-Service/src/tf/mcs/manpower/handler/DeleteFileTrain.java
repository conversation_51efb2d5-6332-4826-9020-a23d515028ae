package tf.mcs.manpower.handler;

import org.hibernate.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.dao.FileAttachedDao;
import tf.mcs.dao.ManpowerTrainDao;
import tf.mcs.model.FileAttached;
import tf.mcs.model.ManpowerTrain;
import tf.tools.StringTools;
import tf.tools.UniversalObject;
/**
 * 描述：删除人员培训上传附件
 * 
 * @file DeleteFileTrain
 * <AUTHOR>
 * @company tfrunning
 * @date 20150911
 * @version v1.0
 */
public class DeleteFileTrain  extends HibernateDaoSupport implements
CommonHandlerStub{

	@SuppressWarnings("unused")
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		UniversalObject uo = new UniversalObject(arg0);
		// 输出类信息
		uo.logInfo("------------进入删除人员培训上传附件DeleteFileTrain类-----------------"+arg0);
		// 获取session对象
		Session externalSession = uo.getSession();
		// 数据库操作Dao对象
		FileAttachedDao fileAttachedDao = new FileAttachedDao();// 附件实体类操作对象
		fileAttachedDao.setExternalSession(externalSession);
		ManpowerTrainDao manpowerTrainDao = new ManpowerTrainDao();// 人员培训实体类操作对象
		manpowerTrainDao.setExternalSession(externalSession);
		// 公共域获取
		long bankid = uo.getBankid();
		// 前台参数获取
		FileAttached fileAttached = (FileAttached) arg0.get("fileAttached");// 获取文件实体信息
		String relserid = fileAttached.getRelserid();// 上传文件关联编号
		ManpowerTrain manpowerTrain = (ManpowerTrain) arg0.get("manpowerTrain");// 人员考评表
		// 业务处理-附件删除
		fileAttached.setBankid(bankid);
		try {
			fileAttached = fileAttachedDao.getEntityByPK(fileAttached);
		} catch (Exception e) {
			StringTools.stackLog(e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("获取唯一附件信息失败！");
			return arg1;
		}
		try {
			fileAttachedDao.delete(fileAttached);
		} catch (Exception e) {
			StringTools.stackLog(e);
			arg1.setRespCode("9999");
			arg1.setRespDesc("删除附件表失败！");
			return arg1;
		}
		// 业务处理-更新人员招聘上传附件关联编号为空
		try {
			manpowerTrain.setBankid(bankid);
			manpowerTrain = manpowerTrainDao.findUniqueByEntity(manpowerTrain);
		} catch (Exception e) {
			StringTools.stackLog(e);
			arg1.setRespCode("9998");
			arg1.setRespDesc("获取人员培训表manpowerTrain执行失败！");
			return arg1;
		}
		try {
			manpowerTrain.setBankid(bankid);
		} catch (Exception e) {
			StringTools.stackLog(e);
			arg1.setRespCode("9998");
			arg1.setRespDesc("删除人员培训表信息时更新人员考评表manpowerTrain执行失败！");
			return arg1;
		}
		arg1.setRespCode("0000");
		arg1.setRespDesc("删除人员培训表附件信息DeleteFileTrain执行成功！");
		return arg1;
	}
}
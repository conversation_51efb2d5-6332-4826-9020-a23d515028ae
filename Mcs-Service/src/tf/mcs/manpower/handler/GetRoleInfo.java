package tf.mcs.manpower.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.utils.model.Page;
import tf.core.utils.model.Pager;
import tf.core.utils.model.TResult;
import tf.mcs.Tools;
import tf.tcc.model.TccRole;
import tf.tcc.service.ITccSsoService;
import tf.tools.UniversalObject;

/**
 * @Describe：获取角色列表信息
 * @ClassName: GetRoleInfo.java
 * <AUTHOR>
 * @Date 2016-5-5
 * @version 1.0
 */
public class GetRoleInfo implements CommonHandlerStub{
	// 获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		ITccSsoService tccSsoService = (ITccSsoService)BRC.getBrcContext().getBean("tccSsoService");
		TResult<TccRole> tccRoleappResult;
		// 前台参数获取
		Pager pager = (Pager) arg0.get("pPager");
		tf.core.utils.hb.model.Pager pager1 = new tf.core.utils.hb.model.Pager();
		RespDataBusData rdbd = new RespDataBusData();
		List<TccRole> tccRoleList= new ArrayList();
		//获取角色信息表
		TccRole tccRole =new TccRole();
		tccRole.setSysId("0013");
		tccRole.setRoleIsenable("1");
		tccRoleList.add(tccRole);
		try{
			tccRoleappResult = tccSsoService.getTccRoleList(tccRole, pager);
			Page rolePage=(Page) tccRoleappResult.getRefObj();
			List<TccRole> roleList=rolePage.getResult();
			ArrayList arrayList = new ArrayList();
			for(int i=0; i<roleList.size();i++){
			HashMap<String, String> hMap=new HashMap<String, String>();
			hMap.put("roleId",String.valueOf(roleList.get(i).getRoleId()));
			hMap.put("roleName",roleList.get(i).getRoleName());
			arrayList.add(i, hMap);
			pager1.setList(arrayList);
			}
		}catch(Exception e){
			e.printStackTrace();
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9700");
			rdbd.setRespDesc("获取用户角色列表时发生错误");
			return rdbd;
		}
		rdbd.setRespEntity("pager", pager1);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取角色列表信息执行成功!");
		return rdbd;
	}
}

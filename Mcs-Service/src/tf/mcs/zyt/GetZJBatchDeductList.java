package tf.mcs.zyt;

import java.util.HashMap;
import java.util.Map;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplate;
import tf.core.utils.hb.model.Pager;
import tf.ifms.rpc.handler.IfmsTool;

/**
 * 获取中金批量扣款列表
 * <AUTHOR>
 * @date 20210519
 */
public class GetZJBatchDeductList implements CommonHandlerStub {
	
	//获取日志
	private org.apache.log4j.Logger log=org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		RespDataBusData rdbd = new RespDataBusData();
		String desc = "获取当前操作员中金批量扣款列表";
		try {
			BRC.getLog().info(desc + "入参:" + arg0);
			DaoTemplate daoTemplate = arg0.getReqDBPoolservice().getDaoTemplate();
			
			//公共域获取
			CommonDBT cdbt = arg0.getCommDC();
			int bankid = cdbt.getiBanks();
			
			//获取前台参数
			Pager pager = (Pager) arg0.get("pager");
			Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
			String txDate = (String) whereMap.get("#EQUAL#t1.txDate");
			String vchnotest = (String) whereMap.get("#EQUAL#t1.vchno");
			String cifid = (String) whereMap.get("#LIKE#c.cifid");
			String cliname = (String) whereMap.get("#LIKE#c.cliname");
			
			//判断查询条件是否全部为空如果全部为空则不返回结果
			IfmsTool ifmsTool = new IfmsTool();
			if (ifmsTool.isNull(vchnotest) && ifmsTool.isNull(cifid) && ifmsTool.isNull(cliname) && ifmsTool.isNull(txDate)) {
				rdbd.setRespEntity("pager", pager);
				rdbd.setRespCode("0000");
				rdbd.setRespDesc(desc + "成功!");
				return rdbd;
			}
			
			//查询主要信息
			String queryInitBatch = " select t1.vchno,t1.tx_date,t1.cifid,t1.opn_br_no,t1.pay_term,t1.pay_seqn,t1.sum_amt,t1.pay_sum_amt,t1.result,t1.cpcn_serid,c.cliname," + 
					"(select instname from cn_basicinfo where bankid='"+bankid+"' and instcode=t1.opn_br_no) instname," + 
					"if(ifnull(t1.sum_amt,0)-ifnull(t1.pay_sum_amt,0)-ifnull(t1.deposit_amt,0)<0,0,ifnull(t1.sum_amt,0)-ifnull(t1.pay_sum_amt,0)-ifnull(t1.deposit_amt,0)) as topayamt," + 
					"(select ifnull(bal,0) from cs_mst where ac_id='1' and cifid=t1.cifid and opn_br_no=t1.opn_br_no) ac_bal," + 
					"(select cpcn_rep_acno from ac_businessvch where bankid='"+bankid+"' and vchno=t1.vchno) cpcn_rep_acno," + 
					"(select payeebk_name from cn_cpcn_bankinfo where payeebk_no=t1.cpcn_rep_bankno) payeebk_name," + 
					"(select contexts from taxes_msgres where bankid='"+bankid+"' and creditapplyno=t1.cpcn_serid ) failreason " +
					"from ln_mx_repayrecord t1,ind_base c where t1.bankid = c.bankid and t1.cifid = c.cifid " + 
					"AND t1.bankid='"+bankid+"' and t1.pay_way='2' ";
			
			String queryInitSingle = " select y1.vchno,y1.txdate tx_date,y1.cifid,y1.instcode opn_br_no,ifnull(y1.currcnt,'') pay_term,'' pay_seqn,y1.repaysum sum_amt," +
					"if( y1.sts = '30',y1.repaysum,'0') pay_sum_amt ," + 
					"y1.glsts result,y1.tx_sn cpcn_serid,c.cliname," + 
					"(select instname from cn_basicinfo where bankid='"+bankid+"' and instcode=y1.instcode) instname ," + 
					"if( y1.sts <> '30',y1.repaysum,'0') topayamt ," + 
					"(select ifnull(bal,0) from cs_mst where ac_id='1' and cifid=y1.cifid and opn_br_no=y1.instcode) ac_bal," + 
					"y1.account_number cpcn_rep_acno," + 
					"(select payeebk_name from cn_cpcn_bankinfo where payeebk_no=d.cpcn_rep_bankno) payeebk_name,  " + 		
					"y1.cpcnfailreason failreason " + 
					"from cpcndebit_record_log y1 ,ind_base c ,ac_businessvch d " + 
					"where y1.bankid = c.bankid and y1.cifid = c.cifid  and y1.bankid = d.bankid and y1.vchno = d.vchno " + 
					"AND y1.bankid='"+bankid+"' and y1.tx_code = '2011' and y1.feeprdtno='2011' ";
			
			//增加查询条件
			if(IfmsTool.isNotNull(txDate)) {
				queryInitBatch = queryInitBatch + "and t1.tx_date = '" + txDate + "' ";
				queryInitSingle = queryInitSingle + "and y1.txdate = '" + txDate + "' ";
			}
			if(IfmsTool.isNotNull(vchnotest)) {
				queryInitBatch = queryInitBatch + "and t1.vchno = '" + vchnotest + "' ";
				queryInitSingle = queryInitSingle + "and y1.vchno = '" + vchnotest + "' ";
			}
			if(IfmsTool.isNotNull(cifid)) {
				queryInitBatch = queryInitBatch + "and t1.cifid = '" + cifid + "' ";
				queryInitSingle = queryInitSingle + "and y1.cifid = '" + cifid + "' ";
			}
			if(IfmsTool.isNotNull(cliname)) {
				queryInitBatch = queryInitBatch + " and c.cliname like '%"+ cliname + "%' ";
				queryInitSingle = queryInitSingle + " and y1.cliname like '%" + cliname + "%' ";
			}
			//合并sql
			String queryInitSql = queryInitBatch + " union " + queryInitSingle ;
				
			BRC.getLog().info("获取当前操作员中金批量扣款列表SQL is:" + queryInitSql);
			daoTemplate.getMapListBySql(queryInitSql, pager);
			
			//由于响应时间的数据量太大，用遍历查询
			for (int i = 0; i < pager.getList().size(); i++) {
				HashMap hMap = (HashMap) pager.getList().get(i);
				String cpcnSerid = (String) hMap.get("cpcnSerid");	
				Map reqtimeMap = daoTemplate
						.getUniqueMapRowBySql("select max(reqtime) reqtime from log_cpcn where txsn='"+cpcnSerid+"' ");				
				if (reqtimeMap != null) {
				hMap.put("reqtime", reqtimeMap.get("reqtime"));
				}	
			}

			rdbd.setRespEntity("pager", pager);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc(desc + "成功!");
			return rdbd;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc(desc + "失败!");
			return rdbd;
		}
	}
}

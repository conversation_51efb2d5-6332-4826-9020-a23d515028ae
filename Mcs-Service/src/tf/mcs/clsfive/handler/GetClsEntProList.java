package tf.mcs.clsfive.handler;

import java.util.HashMap;
import java.util.Map;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;
import tf.mcs.model.CnProductdefine;

@Transactional
public class GetClsEntProList extends HibernateDaoSupport implements
		CommonHandlerStub {
	// 获取五级分类公司类贷款待处理
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);

		// 获取前台参数
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0
				.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0
				.get("whereMap");
		// 公用域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks();
		whereMap.put("#SQL#", "1=1 order by t1.misn_no");
		// 业务处理
		try {
			daoTemplate
					.getMapListByLikeSql(
							"select distinct T1.*,T2.curr,T2.bus_sum,T2.certtype,t2.applyno,t2.manage_operid,t2.manage_instcode,case when (adjust_cls is null or lst_clsfive=adjust_cls) then '0' else '1' end ischange from af_clsbase T1 ,AC_BUSINESSCONT T2 where "
									+ "T1.contno=T2.contno and (pkg_misn_no is null or is_finish='2') and t1.prdt_no like 'A1%' and t1.prdt_no not like 'A201%' and (NOT EXISTS (SELECT 1 FROM DE_FLOWOBJECT t5 WHERE  t5.OBJECTTYPE ='AFCLASS' and  T1.MISN_NO=t5.APPLYNO and t5.phaseno not like'0%' and t1.bankid=t5.bankid) ) and t1.bankid=t2.bankid and t1.bankid="+bankid,
							whereMap, pager);

			for (int i = 0; i < pager.getList().size(); i++) {
				HashMap hMap = (HashMap) pager.getList().get(i);
				CnProductdefine cnProductdef = Tools.getProductdefine(bankid,String.valueOf(hMap.get("prdtNo"))) ;

				if (cnProductdef != null) {
					hMap.put("prdtNa", cnProductdef.getPrdtNa());
					hMap.put("afbcTreeId", cnProductdef.getAfbcTreeId());
					hMap.put("aplyTreeId", cnProductdef.getAplyTreeId());
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e.toString());
		}
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取五级分类公司类贷款待处理XYGetClsEntProList执行成功!");
		return rdbd;
	}
}

package tf.mcs.clsfive.handler;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.mcs.Tools;
import tf.mcs.model.CnProductdefine;




public class GetNotInTaskContList implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {

		BRC.getLog().info(arg0); 
		
		// 获取前台参数
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
		Pager pager = (Pager) arg0.get("pager");   
		Map<String, Object> whereMap = (Map<java.lang.String, Object>) arg0.get("whereMap");
		// 公用域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr(); 
		String operid = cdbt.getOpTel();
		long bankid = cdbt.getiBanks(); 
		//whereMap.put("#EQUAL#t1.cstype", brno);
		whereMap.put("#SQL#", "1=1 order by BEGINDATE");
		try {
			daoTemplate.getMapListByLikeSql("select * from AC_BUSINESSCONT t1 where t1.con_sts='10'  and manage_operid='"+operid+"' "
					+ " AND PRDT_NO NOT LIKE 'C%' and not exists (select 1 from af_clsbase t2 where t1.contno=t2.contno and t2.is_finish not in ('1','9'))", whereMap, pager);
			for(int i=0;i<pager.getList().size();i++){
				HashMap hMap = (HashMap) pager.getList().get(i);
				CnProductdefine cnProductdef = Tools.getProductdefine(bankid,String.valueOf(hMap.get("prdtNo"))) ;
				if(cnProductdef!=null){
					hMap.put("prdtNa", cnProductdef.getPrdtNa());
					hMap.put("afbcTreeId", cnProductdef.getAfbcTreeId());
					hMap.put("aplyTreeId", cnProductdef.getAplyTreeId());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e.toString());
		}
		RespDataBusData rdbd = new RespDataBusData();
		rdbd.setRespEntity("pager", pager);
		rdbd.setRespCode("0000");
		rdbd.setRespDesc("获取未结清业务列表(五级分类用)XYGetNotInTaskContList执行成功!");
		return rdbd;
	}
}

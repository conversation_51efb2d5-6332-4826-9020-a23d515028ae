package tf.mcs.apply.handle;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.core.utils.hb.model.Pager;
import tf.ifms.rpc.handler.IfmsTool;
import tf.ifms.rpc.handler.InvestigationUtil;
import tf.tools.StringTools;

/**
 * @Description: 业务详情-PC实调获取征信查询列表 
 * <AUTHOR>
 * @date 2020年12月22日
 */
public class GetCreditInquiryListForPC implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());
		@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
			BRC.getLog().info(arg0);
			RespDataBusData rdbd = new RespDataBusData();

			log.info("进入——>GetCreditInquiryListForPC类");
			// 获取session
			DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();
			// 获取前台参数
			Pager pager = (Pager) arg0.get("pager");
			String applyno = (String) arg0.get("applyno");
			// 公用域获取
			CommonDBT cdbt = arg0.getCommDC();
			long bankid = cdbt.getiBanks();

			try {
//				String cifid = String.valueOf(daoTemplate.getUniqueValueBySql("select cifid from app_main_info where applyno='"+applyno+"' and bankid='"+bankid+"'"));
//
//				String sql = " select if(ifnull(a.cust_id,'')='',0,1) isbound,a.cifid loan_cifid,a.cliname loan_cliname,'10' loan_role from cust_info a where cifid='"+cifid+"' "
//						   + " union select if(ifnull(a.cust_id,'')='',0,1) isbound,a.cifid loan_cifid,a.cliname loan_cliname,'20' loan_role from cust_info a inner join bus_survey_coborrower b on a.cifid=b.cifid and b.applyno='"+applyno+"' "
//						   + " union select if(ifnull(a.cust_id,'')='',0,1) isbound,a.cifid loan_cifid,a.cliname loan_cliname,'30' loan_role from cust_info a inner join bus_survey_guarantor b on a.cifid=b.cifid and b.applyno='"+applyno+"' ";

				//******** 查询该笔申请所有征信查询记录


				String contno=String.valueOf(daoTemplate.getUniqueValueBySql("select contno from ac_businessvch where applyno='"+applyno+"'"));

				String sql="";
				//******** MIS-1308

				if(StringTools.isNotNull(contno))
				{
					List<String> applynoList=new ArrayList<>();

					List list = daoTemplate.getMapListBySql("select applyno from ac_businessvch where contno='"+contno+"'");

					for (int i = 0; i < list.size(); i++) {
						Map map = (Map)list.get(i);
						applynoList.add("'"+String.valueOf(map.get("applyno"))+"'");
					}

					list = daoTemplate.getMapListBySql("select applyno from af_main_info where contno='"+contno+"' and changer_type='45'");

					for (int i = 0; i < list.size(); i++) {
						Map map = (Map)list.get(i);
						applynoList.add("'"+String.valueOf(map.get("applyno"))+"'");
					}

					String appList= StringUtils.join(applynoList,",");

					log.info("进入——>GetCreditInquiryListForPC类 appList:"+appList);

				sql="select serid,flag sign_method,zxsignature_url,(case when report_htm_url is null then zxreport_url else report_htm_url end) as zxreport_url,zxquery_state,zxrefuse_reason,(case when signsts='1' then '30' else '20' end) sign_state, " +
							"(select fileurl from file_attached where serid = (select max(serid) from file_attached where relserid=w.serid and filetype='001')) as zxsignphoto_url " +
							",w.cifid as loan_cifid,w.cliname as loan_cliname,w.loan_role,if(ifnull(c.cust_id,'')='',0,1) isbound,w.operation_date as querydate,w.query_reason " +
							"from wb_document_info w  left join cust_info c on w.cifid=c.cifid  " +
							"where relserid in ("+appList+") and file_type in('60','90') and file_sts='1' order by operation_date desc";
				}else
				{
					sql="select serid,flag sign_method,zxsignature_url,(case when report_htm_url is null then zxreport_url else report_htm_url end) as zxreport_url,zxquery_state,zxrefuse_reason,(case when signsts='1' then '30' else '20' end) sign_state, " +
							"(select fileurl from file_attached where serid = (select max(serid) from file_attached where relserid=w.serid and filetype='001')) as zxsignphoto_url " +
							",w.cifid as loan_cifid,w.cliname as loan_cliname,w.loan_role,if(ifnull(c.cust_id,'')='',0,1) isbound,w.operation_date as querydate,w.query_reason " +
							"from wb_document_info w  left join cust_info c on w.cifid=c.cifid  " +
							"where relserid='"+applyno+"' and file_type in('60','90') and file_sts='1' order by operation_date desc";
				}

				daoTemplate.getMapListBySql(sql, pager);

				for (int i = 0; i < pager.getList().size(); i++) {
					HashMap hMap = (HashMap) pager.getList().get(i);
//					String loanCifid = String.valueOf(hMap.get("loanCifid"));
//					sql = " select serid,flag sign_method,zxsignature_url,zxreport_url,zxquery_state,zxrefuse_reason,(case when signsts='1' then '30' else '20' end) sign_state, "
//						+ " (select fileurl from file_attached where serid = (select max(serid) from file_attached where relserid=w.serid and filetype='001')) as zxsignphoto_url "
//						+ " from wb_document_info w where relserid='"+applyno+"' and cifid='"+loanCifid+"' and file_sts='1' and file_type='60' order by serid desc limit 1 ";
					//Map rMap = daoTemplate.getUniqueMapRowBySql(sql);
					//if (rMap != null) {
						//hMap.putAll(rMap);
						String zxsignphotoUrl = String.valueOf(hMap.get("zxsignphotoUrl"));
						String zxsignatureUrl = String.valueOf(hMap.get("zxsignatureUrl"));
						String zxqueryState = String.valueOf(hMap.get("zxqueryState"));
						String zxrefuseReason = String.valueOf(hMap.get("zxrefuseReason"));
						if (IfmsTool.isNotNull(zxsignphotoUrl)) {
							hMap.put("photoname", zxsignphotoUrl.substring(zxsignphotoUrl.lastIndexOf("/")+1));
						}
						if (IfmsTool.isNotNull(zxsignatureUrl)) {
							hMap.put("naturename", zxsignatureUrl.substring(zxsignatureUrl.lastIndexOf("/")+1)+".png");
						}
						hMap.put("zxqueryState", getZxqueryState(zxqueryState, zxrefuseReason));
						hMap.put("zxQuery", zxqueryState);
//					}else {
////						10 待发起  20 待反馈  30 成功
//						hMap.put("signState", "10");
//					}
					String phaseno = String.valueOf(daoTemplate.getUniqueValueBySql("select phaseno from de_flowrecord where applyno='"+applyno+"' and serid=(select max(serid) from de_flowrecord where applyno='"+applyno+"')"));
					hMap.put("phaseno", phaseno);


					hMap.put("queryReason", queryReason(String.valueOf(hMap.get("queryReason"))));
				}

//				String cifid = "";
//				List<Map> list = new ArrayList<Map>();
//				
//				String mainSql = "select cifid as loan_cifid,cliname as loan_cliname"
//						+ " from app_main_info where applyno = '"+applyno+"'";
//				List<Map> clinameList =  new ArrayList<Map>();
//				InvestigationUtil.addDataToList(daoTemplate, clinameList, mainSql);
//				for (Map clinameMap : clinameList) {
//					String zxqueryState = "";
//					String signState = "";
//					String signMethod = "";
//					// 贷款角色
//					clinameMap.put("loanRole", "10");// 借款人
//					cifid = InvestigationUtil.caseMapValueToString(clinameMap, "loanCifid");
//					String isbound = InvestigationUtil.getIsboundByCifid(daoTemplate, cifid);
//					clinameMap.put("isbound", isbound);
//					String count = InvestigationUtil.countWbDocumentNum(daoTemplate, applyno, cifid);
//					if (!"0".equals(count)) {
//						// 获取签署方式 0线上1线下
//						signMethod = InvestigationUtil.getSignMethod(daoTemplate, applyno, cifid);
//						// 判断签署状态
//						signState = InvestigationUtil.getSignState(daoTemplate, applyno, cifid, clinameList);
//					}
//					clinameMap.put("signMethod", signMethod);
//					clinameMap.put("signState", signState);
//					// 查询状态
//					if ("30".equals(signState)) {
//						zxqueryState = InvestigationUtil.getZxqueryState(daoTemplate, applyno, cifid);
//					}
//					clinameMap.put("zxqueryState", zxqueryState);
//					// 返回征信报告
//					String zxreportUrl = "";
//					String url = InvestigationUtil.getZxreportUrl(daoTemplate, applyno, cifid);
//					if (IfmsTool.isNotNull(url)) {
//						zxreportUrl = url;
//					}
//					clinameMap.put("zxreportUrl", zxreportUrl);
//				}
//				
//				mainSql = "select cifid as loan_cifid,cliname as loan_cliname"
//						+ " from bus_survey_coborrower where applyno ='"+applyno+"' and cstype = 'C01'";// 仅获取对私共借人
//				List<Map> coList =  new ArrayList<Map>();
//				InvestigationUtil.addDataToList(daoTemplate, coList, mainSql);
//				for (Map coMap : coList) {
//					String zxqueryState = "";
//					String signState = "";
//					String signMethod = "";
//					// 贷款角色
//					coMap.put("loanRole", "20");// 共借人
//					cifid = InvestigationUtil.caseMapValueToString(coMap, "loanCifid");
//					String isbound =InvestigationUtil.getIsboundByCifid(daoTemplate, cifid);
//					coMap.put("isbound", isbound);
//					String count = InvestigationUtil.countWbDocumentNum(daoTemplate, applyno, cifid);
//					if (!"0".equals(count)) {
//						// 获取签署方式 0线上1线下
//						signMethod = InvestigationUtil.getSignMethod(daoTemplate, applyno, cifid);
//						// 判断签署状态
//						signState = InvestigationUtil.getSignState(daoTemplate, applyno, cifid, coList);
//					}
//					coMap.put("signMethod", signMethod);
//					coMap.put("signState", signState);
//					// 拼接查询状态
//					if ("30".equals(signState)) {
//						zxqueryState = InvestigationUtil.getZxqueryState(daoTemplate, applyno, cifid);
//					}
//					coMap.put("zxqueryState", zxqueryState);
//					// 返回征信报告
//					String zxreportUrl = "";
//					String url = InvestigationUtil.getZxreportUrl(daoTemplate, applyno, cifid);
//					if (IfmsTool.isNotNull(url)) {
//						zxreportUrl = url;
//					}
//					coMap.put("zxreportUrl", zxreportUrl);
//					
//				}
//				
//				mainSql = "select cifid as loan_cifid,cliname as loan_cliname"
//						+ " from bus_survey_guarantor where applyno ='"+applyno+"'";
//				List<Map> guarList =  new ArrayList<Map>();
//				InvestigationUtil.addDataToList(daoTemplate, guarList, mainSql);
//				for (Map guarMap : guarList) {
//					String zxqueryState = "";
//					String signState = "";
//					String signMethod = "";
//					// 贷款角色
//					guarMap.put("loanRole", "30");// 担保人
//					cifid = InvestigationUtil.caseMapValueToString(guarMap, "loanCifid");
//					String isbound = InvestigationUtil.getIsboundByCifid(daoTemplate, cifid);
//					guarMap.put("isbound", isbound);
//					String count = InvestigationUtil.countWbDocumentNum(daoTemplate, applyno, cifid);
//					if (!"0".equals(count)) {
//						// 获取签署方式 0线上1线下
//						signMethod = InvestigationUtil.getSignMethod(daoTemplate, applyno, cifid);
//						// 判断签署状态
//						signState = InvestigationUtil.getSignState(daoTemplate, applyno, cifid, guarList);
//					}
//					guarMap.put("signMethod", signMethod);
//					guarMap.put("signState", signState);
//					// 拼接查询状态
//					if ("30".equals(signState)) {
//						zxqueryState = InvestigationUtil.getZxqueryState(daoTemplate, applyno,cifid);
//					}
//					guarMap.put("zxqueryState", zxqueryState);
//					// 返回征信报告
//					String zxreportUrl = "";
//					String url = InvestigationUtil.getZxreportUrl(daoTemplate, applyno, cifid);
//					if (IfmsTool.isNotNull(url)) {
//						zxreportUrl = url;
//					}
//					guarMap.put("zxreportUrl", zxreportUrl);
//				}
//				
//				list = InvestigationUtil.addDataToList(list, clinameList);
//				list = InvestigationUtil.addDataToList(list, coList);
//				list = InvestigationUtil.addDataToList(list, guarList);
//				
//				pager.setList(list);
//				
//				for (int i = 0; i < pager.getList().size(); i++) {
//					HashMap hMap = (HashMap) pager.getList().get(i);
//					Map uniqueMapRowBySql = daoTemplate.getUniqueMapRowBySql("select zxsignphoto_url,zxsignature_url,zxquery_state zx_query from wb_document_info where relserid = '"+applyno+"' and cifid = '"+(String)hMap.get("loanCifid")+"' and file_sts = '1'");
//					if (uniqueMapRowBySql != null) {
//						String zxsignphotoUrl = String.valueOf(uniqueMapRowBySql.get("zxsignphotoUrl"));
//						String zxsignatureUrl = String.valueOf(uniqueMapRowBySql.get("zxsignatureUrl"));
//						String zxQuery = String.valueOf(uniqueMapRowBySql.get("zxQuery"));
//						if (IfmsTool.isNotNull(zxsignphotoUrl)) {
//							hMap.put("photoname", zxsignphotoUrl.substring(zxsignphotoUrl.lastIndexOf("/")+1));
//						}
//						if (IfmsTool.isNotNull(zxsignatureUrl)) {
//							hMap.put("naturename", zxsignatureUrl.substring(zxsignatureUrl.lastIndexOf("/")+1)+".png");
//						}
//						hMap.put("zxQuery", zxQuery);
//						hMap.putAll(uniqueMapRowBySql);
//					}
//					String phaseno = String.valueOf(daoTemplate.getUniqueValueBySql("select phaseno from de_flowrecord where applyno='"+applyno+"' and serid=(select max(serid) from de_flowrecord where applyno='"+applyno+"')"));
//					hMap.put("phaseno", phaseno);
//				}
				rdbd.setRespEntity("pager", pager);
				rdbd.setRespCode("0000");
				rdbd.setRespDesc("获取信息成功!");
			} catch (Exception e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				rdbd.setRespCode("9999");
				rdbd.setRespDesc("获取对应表记录失败");
				return rdbd;
			}
			return rdbd;
		}

	private String getZxqueryState(String zxqueryState,String zxrefuseReason) {
		String result1 = "";
		String result2 = "";
		switch (zxqueryState) {
			case "10":// 查询中
				result1 = "查询中";
				break;
			case "20":// 查询成功
				result1 = "查询成功";
				break;
			case "30":// 拒绝
				switch (zxrefuseReason) {
					case "1":
						result2 = "客户名字错误";
						break;
					case "2":
						result2 = "身份证正反面缺失";
						break;
					case "3":
						result2 = "重复提交";
						break;
					case "4":
						result2 = "因网络故障导致无法进行征信查询";
						break;
					case "5":
						result2 = "其他，请与征信部门联系";
						break;
					/*case "6":
						result2 = "授权书查询选项勾选错误";
						break;
					case "7":
						result2 = "授权书日期填写错误";
						break;
					case "8":
						result2 = "授权书有涂改痕迹";
						break;
					case "9":
						result2 = "授权书未按手印";
						break;*/
					case "10":
						result2 = "客户未签字";
						break;
					case "11":
						result2 = "照片模糊";
						break;
					case "12":
						result2 = "人像识别不一致";
						break;
					case "13":
						result2 = "身份证号码错误";
						break;
					case "14":
						result2 = "身份证过期";
						break;
					case "15":
						result2 = "查询原因不一致";
						break;
					case "16":
						result2 = "授权日期错误";
						break;
					case "17":
						result2 = "授权人的身份证信息不一致";
						break;
					case "18":
						result2 = "授权书未按手印";
						break;
					default:
						break;
				}
			result1 = "拒绝{" + result2 + "}";
			break;

			default:
			break;
		}
		return result1;
	}

	public static String queryReason(String reasonCode)
	{
		if ("3003228".equals(reasonCode)) {
			return "贷款审批"; //贷款审批
		} else if ("3003230".equals(reasonCode)) {
			return "担保资格审查"; //担保资格审查
		}else if ("3003227".equals(reasonCode)) {
			return "贷后管理"; //担保资格审查
		}
		return null;
	}

}
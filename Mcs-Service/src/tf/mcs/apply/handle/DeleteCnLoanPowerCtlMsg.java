package tf.mcs.apply.handle;

import org.hibernate.Session;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.mcs.dao.CnLoanpowerCtlDao;
import tf.mcs.model.CnLoanpowerCtl;

public class DeleteCnLoanPowerCtlMsg  implements CommonHandlerStub {
	// 获取日志
	private org.apache.log4j.Logger log = org.apache.log4j.Logger.getLogger(getClass());
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1) throws Exception {
		
		log.info("进入——>DelectCnLoanPowerCtlMsg类");
		RespDataBusData rdbd = new RespDataBusData();
		BRC.getLog().info(arg0);
		
		Session externalSession = arg0.getReqDBPoolservice().getTransSession();
		
		// 获取前台参数
		CommonDBT cdbt=arg0.getCommDC();
		long  bankid=cdbt.getiBanks();
		CnLoanpowerCtl cnLoanpowerCtl = (CnLoanpowerCtl)arg0.get("cnLoanpowerCtl");
		CnLoanpowerCtlDao cnLoanpowerCtlDao = new CnLoanpowerCtlDao();
		cnLoanpowerCtlDao.setExternalSession(externalSession);
		cnLoanpowerCtl.setBankid(bankid);
		try {
			CnLoanpowerCtl entityByPK = cnLoanpowerCtlDao.getEntityByPK(cnLoanpowerCtl);
			if (entityByPK != null) {
				cnLoanpowerCtlDao.delete(entityByPK);
			}
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("删除对应记录成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("删除对应记录失败");
		}
		return rdbd;
	}
}
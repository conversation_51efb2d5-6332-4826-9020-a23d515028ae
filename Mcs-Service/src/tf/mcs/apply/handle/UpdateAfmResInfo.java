package tf.mcs.apply.handle;

import java.text.DecimalFormat;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.dao.AfmResInfoDao;
import tf.mcs.dao.AppBusInfoDao;
import tf.mcs.dao.AppMainInfoDao;
import tf.mcs.dao.BusSurveyResultDao;
import tf.mcs.model.AfmResInfo;
import tf.mcs.model.AppBusInfo;
import tf.mcs.model.AppMainInfo;
import tf.mcs.model.BusSurveyResult;
import tf.tools.Arith;

public class UpdateAfmResInfo extends HibernateDaoSupport implements CommonHandlerStub {

	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {

		BRC.getLog().info(arg0);
		BRC.getLog().info("############进入UpdateAfmResInfo类#############");
		//获取session
		RespDataBusData rdbd=new RespDataBusData();
		DaoTemplateImpl daoTemplate = (DaoTemplateImpl) arg0.getReqDBPoolservice().getDaoTemplate();

		AfmResInfoDao afmResInfoDao=new AfmResInfoDao();
		afmResInfoDao.setExternalSession(arg0.getReqDBPoolservice().getTransSession());
		
		AppMainInfoDao appMainInfoDao=new AppMainInfoDao();
		appMainInfoDao.setExternalSession(arg0.getReqDBPoolservice().getTransSession());
		//公共域获取
		
		CommonDBT cdbt = arg0.getCommDC();
		String brno = cdbt.getOpBr();
		String operid = cdbt.getOpTel();
		String workDate=cdbt.getWorkDate();
		long bankid = cdbt.getiBanks();
		//获取前台参数
		AfmResInfo afmResInfo=(AfmResInfo) arg0.get("afmResInfo");
		BRC.getLog().info(">>>>>>>>>"+afmResInfo.getDguarLtv()+">>>>>>>>>"+afmResInfo.getZguarLtv());
		
		//edit by wpz ******** 决议页面贷款种类改为只读，判断后修改该值。
		Long term=afmResInfo.getTerm();
		String purp=afmResInfo.getPurpose();
		if(term!=null&&purp!=null){
			if(term<=12){
				if(purp.equals("1")){
					afmResInfo.setBusPrdtType("11");
				}else{
					afmResInfo.setBusPrdtType("19");
				}
			}else if(term>12&&term<=60) {
				if(purp.equals("1")){
					afmResInfo.setBusPrdtType("21");
				}else{
				afmResInfo.setBusPrdtType("29");
				}
			}else{
				afmResInfo.setBusPrdtType("39");
			}
		}
		
		//EDIT BY GCL 建议否决时，可不填决议信息，做相应修改。PS：产品可以改但是不允许清空。
		String prdtNo=afmResInfo.getPrdtNo();
		if(prdtNo == null || prdtNo.equals("")){
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("更新上会决议信息执行失败：产品不允许为空！");
			return rdbd;
		}
		
		int num = Integer.parseInt(String.valueOf(daoTemplate.getUniqueValueBySql("select count(*) from bus_survey_collateral where applyno='" +afmResInfo.getApplyno()+
				"' and bankid='"+bankid+"'and status='1'")));		
		afmResInfo.setBankid(bankid);
		afmResInfo.setIptDate(workDate);
		afmResInfo.setIptUsr(operid);
		afmResInfo.setIptBrno(brno);
		
		AppMainInfo appMainInfo=new AppMainInfo();
		appMainInfo.setBankid(bankid);
		appMainInfo.setApplyno(afmResInfo.getApplyno());
		AppMainInfo oldAppMainInfo=new AppMainInfo();
		oldAppMainInfo=appMainInfoDao.getEntityByPK(appMainInfo);
		String isRepayDate=afmResInfo.getIsRepayDate();
		if(isRepayDate!=null&&isRepayDate.equals("0")){
			afmResInfo.setRepayDate("");
		}
		if(oldAppMainInfo==null){
			
		}else{
			oldAppMainInfo.setAudNum(String.valueOf(afmResInfo.getAudNum()));
			oldAppMainInfo.setAudOperid(operid);
			oldAppMainInfo.setAffrimRepayType(afmResInfo.getRepayType());
			oldAppMainInfo.setAffrimSum(afmResInfo.getBusSum());
			oldAppMainInfo.setAffrimTermType(afmResInfo.getTermType());
			oldAppMainInfo.setAffrimTerm(afmResInfo.getTerm());
			oldAppMainInfo.setGuarType(afmResInfo.getGuarType());
			oldAppMainInfo.setOthGuarType(afmResInfo.getOthGuarType());//edit 增加担保方式的copy ssh ********
			oldAppMainInfo.setPrdtNo(afmResInfo.getPrdtNo());
			tf.core.utils.func.DomainHelper.copyProperty(appMainInfo, oldAppMainInfo);
			appMainInfoDao.update(oldAppMainInfo);
		}
//		afmResInfo.setOccurtype(oldAppMainInfo.getOccurtype());//保存业务发生类型
		//--------协议项下贷款校验---------//
		/*if(!"0".equals(afmResInfo.getIsTemp())){//edited by jinran ******** 针对暂存无法获取到决议类型时，不进行协议项下贷款校验
		if(!afmResInfo.getAffrimtype().equals("3")){
			HashMap mapInfo=(HashMap)daoTemplate.getUniqueMapRowBySql("select bustype,relacno from app_main_info where applyno='"+afmResInfo.getApplyno()+"' and bankid='"+bankid+"'");
			String bustype=mapInfo.get("bustype").toString();
			if(bustype.equals("20")){//协议项下
				String relacno=mapInfo.get("relacno").toString();
				Double appSum=afmResInfo.getBusSum()==null?0:afmResInfo.getBusSum();
				//协议未使用金额
				String unusedSumSql="select nvl(unused_sum,0) from ac_businesscont where contno='"+relacno+"' and bankid="+bankid ;
				Double unusedSum=Double.parseDouble(daoTemplate.getUniqueValueBySql(unusedSumSql).toString());
				//其他在申请的金额
				String onSiteAppSumSql="select nvl(sum(t1.app_sum),0) from app_main_info t1 where relacno='"+relacno+"' and applyno<>'"+afmResInfo.getApplyno()+"' and bankid="+bankid+" and exists (select 1 from de_flowrecord where bankid="+bankid+" and applyno=t1.applyno and (enddate is null or enddate='')) and not exists (select 1 from ac_businesscont where bankid="+bankid+" and applyno=t1.applyno and con_sts='10')" ;
				Double onSiteAppSum=Double.parseDouble(daoTemplate.getUniqueValueBySql(onSiteAppSumSql).toString());
				if(appSum>(unusedSum-onSiteAppSum)){
					rdbd.setRespCode("9997");
					rdbd.setRespDesc("批准金额大于申请金额，导致协议可用额度不足");
					return rdbd;
				}
				//第二步，校验是否超过协议单笔最大授信额度
				String agreHighsumSql="select agre_highsum from ac_businesscont where contno='"+relacno+"' and bankid="+bankid;
				Double agreHighsum=Double.parseDouble(daoTemplate.getUniqueValueBySql(agreHighsumSql).toString());
				if(appSum>agreHighsum){
					rdbd.setRespCode("9998");
					rdbd.setRespDesc("批准金额大于协议规定的单笔最高授信额度");
					return rdbd;
				}
			}
			
		}
		}*/
		//--------协议项下贷款校验结束---------//
		//add by jinping 2017/3/3 如果是最高额授信，则决议信息表插入申请信息表中的信息
		String getbustype ="select bustype from app_main_info  where bankid='"+bankid+"'  and applyno='"+afmResInfo.getApplyno()+"'";
		String bustype=(String) daoTemplate.getUniqueValueBySql(getbustype);
		AppBusInfoDao appBusInfoDao=new AppBusInfoDao();
		appBusInfoDao.setExternalSession(arg0.getReqDBPoolservice().getTransSession());
		AppBusInfo appBusInfo=new AppBusInfo();
		appBusInfo.setBankid(bankid);
		appBusInfo.setApplyno(afmResInfo.getApplyno());
		appBusInfo=appBusInfoDao.getEntityByPK(appBusInfo);
		if(bustype.equals("40")){
			//afmResInfo.setAgreHighsum(appBusInfo.getAgreHighsum());//单笔最高授信额度  edit by lyy 屏蔽单笔最高 ********
			afmResInfo.setAgreIsInstead(appBusInfo.getAgreIsInstead());//是否代偿
			afmResInfo.setAgreInsteadBeg(appBusInfo.getAgreInsteadBeg());//代偿起始日
			afmResInfo.setAgreInsteadEnd(appBusInfo.getAgreInsteadEnd());//代偿终止日
			afmResInfo.setAgreIsAutopay(appBusInfo.getAgreIsAutopay());//代偿是否自动扣款
		}
		//add end by jinping
		AfmResInfo oldAfmResInfo=new AfmResInfo();
		oldAfmResInfo=afmResInfoDao.getEntityByPK(afmResInfo);
		//add by lyy ******** 增加抵质押率重新计算
		DecimalFormat df = new DecimalFormat("######0.00####");

		if(num>0&&afmResInfo.getBusSum() != null){
			String assessSumSql="select nvl(sum(assess_sum),0) from bus_survey_collateral where bankid='"+bankid+"'and applyno='"+afmResInfo.getApplyno()+"'and status='1'"; 
			String dAssessSumSql="select nvl(sum(assess_sum),0) from bus_survey_collateral where applyno='"+afmResInfo.getApplyno()+"'and bankid='"+bankid+"'and col_no like 'B%' and status='1'";
	    	String zAsessSumSql="select nvl(sum(assess_sum),0) from bus_survey_collateral where applyno='"+afmResInfo.getApplyno()+"'and bankid='"+bankid+"'and col_no like 'A%' and status='1'";
	    	Object obj1=daoTemplate.getUniqueValueBySql(dAssessSumSql);
			Object obj2=daoTemplate.getUniqueValueBySql(zAsessSumSql);
	    	double assessSum=Double.valueOf(String.valueOf(daoTemplate.getUniqueValueBySql(assessSumSql)));
			double bussum=afmResInfo.getBusSum();
			double guarLtv=Arith.div(bussum, assessSum)*100;
			guarLtv=Double.valueOf(df.format(guarLtv));
			afmResInfo.setGuarLtv(guarLtv);
			
			Double dAssessSum=Double.valueOf(String.valueOf(obj1));//抵押金额
	    	if(dAssessSum>0){
	    		Double dguarLtv=Arith.div(bussum, dAssessSum)*100;//抵押率
		    	dguarLtv=Double.valueOf(df.format(dguarLtv));
		    	afmResInfo.setDguarLtv(dguarLtv);
	    	}else{
		    	afmResInfo.setDguarLtv(0.00);
	    	}
	    	Double zAssessSum=Double.valueOf(String.valueOf(obj2));//质押金额
	    	if(zAssessSum>0){
	    		Double zguarLtv=Arith.div(bussum, zAssessSum)*100;//质押率
	    		zguarLtv=Double.valueOf(df.format(zguarLtv));
	    		afmResInfo.setZguarLtv(zguarLtv);
	    	}else{
	    		afmResInfo.setZguarLtv(0.00);
	    	}
		}else{
			afmResInfo.setGuarLtv(0.00);
			afmResInfo.setDguarLtv(0.00);
			afmResInfo.setZguarLtv(0.00);
		}
//		afmResInfo.setOccurtype(oldAppMainInfo.getOccurtype());
		if(oldAfmResInfo==null){
			afmResInfoDao.insert(afmResInfo);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("新增上会决议信息成功！");
		}else{
			oldAfmResInfo.setOthGuarType(afmResInfo.getOthGuarType());
			tf.core.utils.func.DomainHelper.copyProperty(afmResInfo, oldAfmResInfo);
			afmResInfoDao.update(oldAfmResInfo);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("更新上会决议信息成功！");
		}
		return rdbd;
	}
}
package tf.mcs.apply.handle;

import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import tf.brc.databus.DataBusfactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.databus.impl.CommonDBT;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.maintance.BRC;
import tf.core.dao.DaoTemplateImpl;
import tf.mcs.PublicMethods;
import tf.mcs.Tools;
import tf.mcs.dao.AuditCommRelDao;
import tf.mcs.model.AuditCommRel;
/**
 * <AUTHOR>
 * @version v1.1 edited by wuzhujun 20160510 调整代码格式,删除无用代码
 */
public class BatchAudStrChk extends HibernateDaoSupport implements CommonHandlerStub {
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)throws Exception {
		BRC.getLog().info(arg0);
		BRC.getLog().info("############进入BatchAuditCommRel类#############");
		RespDataBusData rdbd=new RespDataBusData();
		//获取前台参数
		ArrayList list=(ArrayList) arg0.get("list");
		//业务处理
		String connStr="";
		double totalUpSum=0.00;
		double totalFlSum=0.00;
		if(list.size()==0){
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("提交失败！</b>未获取到任何人员信息！");
			return rdbd;
		}else if(list.size()>3||list.size()<2){
			rdbd.setRespCode("9999");
			rdbd.setRespDesc("提交失败！</b>审贷会小组成员最少2人最多3人！");
			return rdbd;
		}else{
			int i;
			for(i=0; i<list.size(); i++){
				HashMap listMap=(HashMap) list.get(i);
				String commOperid=listMap.get("operid").toString();
				String commOpername=listMap.get("opername").toString();
				double approvalUpperVal=Double.valueOf(String.valueOf(listMap.get("approvalUpperVal")));
				double approvalFloorVal=Double.valueOf(String.valueOf(listMap.get("approvalFloorVal")));
				if(i%3==2 && i!=0){
					connStr+="["+commOperid+"]"+commOpername+","+"\n";
				}else{
					connStr+="["+commOperid+"]"+commOpername+",";
				}
				//根据算法匹配小组审贷范围(当前匹配被选柜员上限及下限的最大值)
				if(totalUpSum<approvalUpperVal){
					totalUpSum=approvalUpperVal;
				}
				if(totalFlSum<approvalFloorVal){
					totalFlSum=approvalFloorVal;
				}
			}
			if(i%3==0 && i!=0){
				connStr=connStr.substring(0, connStr.length()-2);
			}else{
				connStr=connStr.substring(0, connStr.length()-1);
			}	
			HashMap resMap=new HashMap();
			resMap.put("connStr", connStr);
			resMap.put("upSum", totalUpSum);
			resMap.put("flSum", totalFlSum);
			rdbd.setRespCode("0000");
			rdbd.setRespDesc("提交贷审会委员BatchAudStrChk执行成功！");
			rdbd.setRespMap("hashMap", resMap);
			return rdbd;
		}
	}
}

package tf.mcs.persona.handler;

import java.util.HashMap;

import org.hibernate.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.mcs.dao.UpClitagwtDao;
import tf.mcs.model.UpClitagwt;
import tf.tools.UniversalObject;

/**
 * @Description:新增标签全重定义
 * @ClassName: AddTagWtDefine.java
 * @author: mazhuang
 * @Date: 2016-10-18 下午16:55
 * @version: V1.0
 * @company: Tfrunning
 */
public class AddTagWtDefine implements CommonHandlerStub {
	private final Logger log = LoggerFactory.getLogger(getClass());
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
		// 定义通用对象
				UniversalObject uo;
				try {
					uo = new UniversalObject(arg0);
				} catch (Exception e) {
					log.error("初始化通用对象错误", e);
					arg1.setRespCode("9999");
					arg1.setRespDesc("初始化通用对象错误");
					return arg1;
				}
				// 获取前台传入参数
				UpClitagwt upClitagwt=(UpClitagwt) arg0.get("upClitagwt");
				uo.logInfo("---------------------"+upClitagwt);
				
				//获取session
				Session dmpsession=uo.getSession();
				UpClitagwtDao upClitagwtDao=new UpClitagwtDao();
				upClitagwtDao.setExternalSession(dmpsession);
				
				// 获取标签归属
				String tagcode=upClitagwt.getTagcode();
				String cifid=upClitagwt.getCifid();
				String evaldate=upClitagwt.getEvaldate();
				String tagname=upClitagwt.getTagname().replaceAll(" +",""); //trim();
				String tagobjcode=upClitagwt.getTagobjcode().trim();
			    //int a=tagname.length();
				HashMap hashMap = new HashMap();
				
				//判断数据库中是否已经存在该标签归属
				String mainSql = "select * from up_clitagwt t1 where t1.evaldate='"+evaldate+"' and t1.cifid='"+cifid+"' and t1.tagcode ='"
						+ tagcode + "'";
				uo.logInfo("-----------------------" + mainSql);
				// 编辑标签
				uo.Dao().setExternalSession(dmpsession);
				if (uo.Dao().getMapListBySql(mainSql).size() > 0) {
					try {
						upClitagwtDao.update(upClitagwt);
					} catch (Exception e) {
						e.printStackTrace();
					}
					hashMap.put("isused", 1);
				}
				// 新增标签
				else {
					try {
						upClitagwt.setBankid(uo.getBankid().longValue());
						upClitagwt.setManuiptusr(uo.getOptUser().trim());
						upClitagwt.setIptBrno(uo.getOptBrno().trim());
						upClitagwt.setIptBrnoname(uo.getOptBrnoName().trim());
						upClitagwt.setTagobjcode(tagobjcode);
						upClitagwt.setTagname(tagname); 
						upClitagwtDao.insert(upClitagwt);
						hashMap.put("isused", 0);
					} catch (Exception e) {
						log.error("提交申请失败", e);
						arg1.setRespCode("9999");
						arg1.setRespDesc("提交申请失败");
						// return arg1;
					}
				}
				arg1.setRespCode("0000");
				arg1.setRespEntity("upClitagwt", upClitagwt);
				arg1.setRespMap("hashMap", hashMap);
				arg1.setRespDesc("执行成功");
				return arg1;
	}

}

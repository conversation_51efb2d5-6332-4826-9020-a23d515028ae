package tf.mcs.persona.handler;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tf.brc.databus.ReqDataBusData;
import tf.brc.databus.RespDataBusData;
import tf.brc.txhandler.CommonHandlerStub;
import tf.cbs.brc.sql.DBPoolFactory;
import tf.mcs.PublicMethods;
import tf.mcs.dao.UpTagvalmanualDao;
import tf.mcs.model.UpTagvalmanual;
import tf.tools.StringTools;
import tf.tools.UniversalObject;

/**
 * @Description:新增取值定义对象信息
 * @ClassName: AddValObj.java
 * @author: machao
 * @Date: 2016-10-20 
 * @version: V1.0
 * @company: Tfrunning
 */
public class AddValObj implements CommonHandlerStub{
	private final Logger log = LoggerFactory.getLogger(getClass());
	public RespDataBusData subTxDealer(ReqDataBusData arg0, RespDataBusData arg1)
			throws Exception {
				UniversalObject uo;
				try {
					uo = new UniversalObject(arg0);
				} catch (Exception e) {
					log.error("初始化uo对象失败", e);
					arg1.setRespCode("9999");
					arg1.setRespDesc("初始化uo对象失败!");
					return arg1;
				}
				// 输出类信息
				uo.logInfo("进入新增取值定义对象信息----AddValObj-----类");
				uo.logInfo("总线获取参数信息-------------------------" + arg0);
				// 获取前台传过的对象
				UpTagvalmanual upTagvalmanual = (UpTagvalmanual) arg0.get("upTagvalmanual");
				// 根据编号生成规则获取当前取值对象编号
				Map<String, String> templateMap = new HashMap<String, String>();
				String tagobjcode = "root";
				String valcode = "";
				
				try {
					
					valcode = PublicMethods.getCnSeqnumByTemplate(uo.getBankid()
							.longValue(), "tagobjcode", uo.getSession(), templateMap);
					
				} catch (Exception e) {
					StringTools.stackLog(e, arg0.getCommDC().getiBanks());
					arg1.setRespCode("9900");
					arg1.setRespDesc("生成叶子节点归属号、规则编号发生错误");
					return arg1;
				}
				// 获取数据库会话
				 DBPoolFactory dbpf = arg0.getReqDBPoolservice();
				 Session dmpsession = dbpf.getTransSession();
				 
				UpTagvalmanualDao upTagvalmanualDao = new UpTagvalmanualDao();
				upTagvalmanualDao.setExternalSession(dmpsession);
							
				upTagvalmanual.setTagobjcode(tagobjcode);

			    upTagvalmanual.setValcode(valcode);
			    String transmit=upTagvalmanual.getTagobjname();
			    upTagvalmanual.setTagobjname(upTagvalmanual.getVal());
			    upTagvalmanual.setVal(transmit);
				
				try {
					upTagvalmanualDao.insert(upTagvalmanual);
				} catch (Exception e) {
					log.error("新增失败", e);
					arg1.setRespCode("9999");
					arg1.setRespDesc("新增失败");
					return arg1;

				}
				//新增根节点下取值对象
				//uo.speDao("mcs");//强行赋回原来的数据库dao
				upTagvalmanual.setTagobjcode(valcode);
				valcode = PublicMethods.getCnSeqnumByTemplate(uo.getBankid()
						.longValue(), "valcode", dmpsession, templateMap);
				 upTagvalmanual.setValcode(valcode.trim());
				 
				 upTagvalmanual.setVal(upTagvalmanual.getTagobjname());
				 upTagvalmanual.setTagobjname(transmit);
				 
				try {
					upTagvalmanualDao.insert(upTagvalmanual);
				} catch (Exception e) {
					log.error("新增失败", e);
					arg1.setRespCode("9999");
					arg1.setRespDesc("新增失败");
					return arg1;

				}
				
				arg1.setRespCode("0000");
				arg1.setRespDesc("新增成功");
				return arg1;
			}

		}